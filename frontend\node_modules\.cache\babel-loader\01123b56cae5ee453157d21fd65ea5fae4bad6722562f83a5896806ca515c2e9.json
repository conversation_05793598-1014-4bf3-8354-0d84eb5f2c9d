{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toNodeArray from \"rc-util/es/Children/toArray\";\nimport warning, { noteOnce } from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { isMultiple } from \"../BaseSelect\";\nimport { toArray } from \"./commonUtil\";\nimport { convertChildrenToData } from \"./legacyUtil\";\nfunction warningProps(props) {\n  var mode = props.mode,\n    options = props.options,\n    children = props.children,\n    backfill = props.backfill,\n    allowClear = props.allowClear,\n    placeholder = props.placeholder,\n    getInputElement = props.getInputElement,\n    showSearch = props.showSearch,\n    onSearch = props.onSearch,\n    defaultOpen = props.defaultOpen,\n    autoFocus = props.autoFocus,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    inputValue = props.inputValue,\n    optionLabelProp = props.optionLabelProp;\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = showSearch !== undefined ? showSearch : multiple || mode === 'combobox';\n  var mergedOptions = options || convertChildrenToData(children);\n\n  // `tags` should not set option as disabled\n  warning(mode !== 'tags' || mergedOptions.every(function (opt) {\n    return !opt.disabled;\n  }), 'Please avoid setting option to disabled in tags mode since user can always type text as tag.');\n\n  // `combobox` & `tags` should option be `string` type\n  if (mode === 'tags' || mode === 'combobox') {\n    var hasNumberValue = mergedOptions.some(function (item) {\n      if (item.options) {\n        return item.options.some(function (opt) {\n          return typeof ('value' in opt ? opt.value : opt.key) === 'number';\n        });\n      }\n      return typeof ('value' in item ? item.value : item.key) === 'number';\n    });\n    warning(!hasNumberValue, '`value` of Option should not use number type when `mode` is `tags` or `combobox`.');\n  }\n\n  // `combobox` should not use `optionLabelProp`\n  warning(mode !== 'combobox' || !optionLabelProp, '`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly.');\n\n  // Only `combobox` support `backfill`\n  warning(mode === 'combobox' || !backfill, '`backfill` only works with `combobox` mode.');\n\n  // Only `combobox` support `getInputElement`\n  warning(mode === 'combobox' || !getInputElement, '`getInputElement` only work with `combobox` mode.');\n\n  // Customize `getInputElement` should not use `allowClear` & `placeholder`\n  noteOnce(mode !== 'combobox' || !getInputElement || !allowClear || !placeholder, 'Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`.');\n\n  // `onSearch` should use in `combobox` or `showSearch`\n  if (onSearch && !mergedShowSearch && mode !== 'combobox' && mode !== 'tags') {\n    warning(false, '`onSearch` should work with `showSearch` instead of use alone.');\n  }\n  noteOnce(!defaultOpen || autoFocus, '`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed.');\n  if (value !== undefined && value !== null) {\n    var values = toArray(value);\n    warning(!labelInValue || values.every(function (val) {\n      return _typeof(val) === 'object' && ('key' in val || 'value' in val);\n    }), '`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`');\n    warning(!multiple || Array.isArray(value), '`value` should be array when `mode` is `multiple` or `tags`');\n  }\n\n  // Syntactic sugar should use correct children type\n  if (children) {\n    var invalidateChildType = null;\n    toNodeArray(children).some(function (node) {\n      if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n        return false;\n      }\n      var _ref = node,\n        type = _ref.type;\n      if (type.isSelectOption) {\n        return false;\n      }\n      if (type.isSelectOptGroup) {\n        var allChildrenValid = toNodeArray(node.props.children).every(function (subNode) {\n          if (! /*#__PURE__*/React.isValidElement(subNode) || !node.type || subNode.type.isSelectOption) {\n            return true;\n          }\n          invalidateChildType = subNode.type;\n          return false;\n        });\n        if (allChildrenValid) {\n          return false;\n        }\n        return true;\n      }\n      invalidateChildType = type;\n      return true;\n    });\n    if (invalidateChildType) {\n      warning(false, \"`children` should be `Select.Option` or `Select.OptGroup` instead of `\".concat(invalidateChildType.displayName || invalidateChildType.name || invalidateChildType, \"`.\"));\n    }\n    warning(inputValue === undefined, '`inputValue` is deprecated, please use `searchValue` instead.');\n  }\n}\n\n// value in Select option should not be null\n// note: OptGroup has options too\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      var inGroup = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Select options should not be `null`.');\n          return true;\n        }\n        if (!inGroup && Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options], true)) {\n          break;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;", "map": {"version": 3, "names": ["_typeof", "toNodeArray", "warning", "noteOnce", "React", "isMultiple", "toArray", "convertChildrenToData", "warningProps", "props", "mode", "options", "children", "backfill", "allowClear", "placeholder", "getInputElement", "showSearch", "onSearch", "defaultOpen", "autoFocus", "labelInValue", "value", "inputValue", "optionLabelProp", "multiple", "mergedShowSearch", "undefined", "mergedOptions", "every", "opt", "disabled", "hasNumberValue", "some", "item", "key", "values", "val", "Array", "isArray", "invalidateChildType", "node", "isValidElement", "type", "_ref", "isSelectOption", "isSelectOptGroup", "allChil<PERSON>n<PERSON><PERSON>", "subNode", "concat", "displayName", "name", "warningNullOptions", "fieldNames", "recursiveOptions", "optionsList", "inGroup", "arguments", "length", "i", "option"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/utils/warningPropsUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toNodeArray from \"rc-util/es/Children/toArray\";\nimport warning, { noteOnce } from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { isMultiple } from \"../BaseSelect\";\nimport { toArray } from \"./commonUtil\";\nimport { convertChildrenToData } from \"./legacyUtil\";\nfunction warningProps(props) {\n  var mode = props.mode,\n    options = props.options,\n    children = props.children,\n    backfill = props.backfill,\n    allowClear = props.allowClear,\n    placeholder = props.placeholder,\n    getInputElement = props.getInputElement,\n    showSearch = props.showSearch,\n    onSearch = props.onSearch,\n    defaultOpen = props.defaultOpen,\n    autoFocus = props.autoFocus,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    inputValue = props.inputValue,\n    optionLabelProp = props.optionLabelProp;\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = showSearch !== undefined ? showSearch : multiple || mode === 'combobox';\n  var mergedOptions = options || convertChildrenToData(children);\n\n  // `tags` should not set option as disabled\n  warning(mode !== 'tags' || mergedOptions.every(function (opt) {\n    return !opt.disabled;\n  }), 'Please avoid setting option to disabled in tags mode since user can always type text as tag.');\n\n  // `combobox` & `tags` should option be `string` type\n  if (mode === 'tags' || mode === 'combobox') {\n    var hasNumberValue = mergedOptions.some(function (item) {\n      if (item.options) {\n        return item.options.some(function (opt) {\n          return typeof ('value' in opt ? opt.value : opt.key) === 'number';\n        });\n      }\n      return typeof ('value' in item ? item.value : item.key) === 'number';\n    });\n    warning(!hasNumberValue, '`value` of Option should not use number type when `mode` is `tags` or `combobox`.');\n  }\n\n  // `combobox` should not use `optionLabelProp`\n  warning(mode !== 'combobox' || !optionLabelProp, '`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly.');\n\n  // Only `combobox` support `backfill`\n  warning(mode === 'combobox' || !backfill, '`backfill` only works with `combobox` mode.');\n\n  // Only `combobox` support `getInputElement`\n  warning(mode === 'combobox' || !getInputElement, '`getInputElement` only work with `combobox` mode.');\n\n  // Customize `getInputElement` should not use `allowClear` & `placeholder`\n  noteOnce(mode !== 'combobox' || !getInputElement || !allowClear || !placeholder, 'Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`.');\n\n  // `onSearch` should use in `combobox` or `showSearch`\n  if (onSearch && !mergedShowSearch && mode !== 'combobox' && mode !== 'tags') {\n    warning(false, '`onSearch` should work with `showSearch` instead of use alone.');\n  }\n  noteOnce(!defaultOpen || autoFocus, '`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed.');\n  if (value !== undefined && value !== null) {\n    var values = toArray(value);\n    warning(!labelInValue || values.every(function (val) {\n      return _typeof(val) === 'object' && ('key' in val || 'value' in val);\n    }), '`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`');\n    warning(!multiple || Array.isArray(value), '`value` should be array when `mode` is `multiple` or `tags`');\n  }\n\n  // Syntactic sugar should use correct children type\n  if (children) {\n    var invalidateChildType = null;\n    toNodeArray(children).some(function (node) {\n      if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n        return false;\n      }\n      var _ref = node,\n        type = _ref.type;\n      if (type.isSelectOption) {\n        return false;\n      }\n      if (type.isSelectOptGroup) {\n        var allChildrenValid = toNodeArray(node.props.children).every(function (subNode) {\n          if (! /*#__PURE__*/React.isValidElement(subNode) || !node.type || subNode.type.isSelectOption) {\n            return true;\n          }\n          invalidateChildType = subNode.type;\n          return false;\n        });\n        if (allChildrenValid) {\n          return false;\n        }\n        return true;\n      }\n      invalidateChildType = type;\n      return true;\n    });\n    if (invalidateChildType) {\n      warning(false, \"`children` should be `Select.Option` or `Select.OptGroup` instead of `\".concat(invalidateChildType.displayName || invalidateChildType.name || invalidateChildType, \"`.\"));\n    }\n    warning(inputValue === undefined, '`inputValue` is deprecated, please use `searchValue` instead.');\n  }\n}\n\n// value in Select option should not be null\n// note: OptGroup has options too\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      var inGroup = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Select options should not be `null`.');\n          return true;\n        }\n        if (!inGroup && Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options], true)) {\n          break;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,OAAO,IAAIC,QAAQ,QAAQ,oBAAoB;AACtD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,qBAAqB,QAAQ,cAAc;AACpD,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,eAAe,GAAGP,KAAK,CAACO,eAAe;IACvCC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,YAAY,GAAGZ,KAAK,CAACY,YAAY;IACjCC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,eAAe,GAAGf,KAAK,CAACe,eAAe;EACzC,IAAIC,QAAQ,GAAGpB,UAAU,CAACK,IAAI,CAAC;EAC/B,IAAIgB,gBAAgB,GAAGT,UAAU,KAAKU,SAAS,GAAGV,UAAU,GAAGQ,QAAQ,IAAIf,IAAI,KAAK,UAAU;EAC9F,IAAIkB,aAAa,GAAGjB,OAAO,IAAIJ,qBAAqB,CAACK,QAAQ,CAAC;;EAE9D;EACAV,OAAO,CAACQ,IAAI,KAAK,MAAM,IAAIkB,aAAa,CAACC,KAAK,CAAC,UAAUC,GAAG,EAAE;IAC5D,OAAO,CAACA,GAAG,CAACC,QAAQ;EACtB,CAAC,CAAC,EAAE,8FAA8F,CAAC;;EAEnG;EACA,IAAIrB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;IAC1C,IAAIsB,cAAc,GAAGJ,aAAa,CAACK,IAAI,CAAC,UAAUC,IAAI,EAAE;MACtD,IAAIA,IAAI,CAACvB,OAAO,EAAE;QAChB,OAAOuB,IAAI,CAACvB,OAAO,CAACsB,IAAI,CAAC,UAAUH,GAAG,EAAE;UACtC,OAAO,QAAQ,OAAO,IAAIA,GAAG,GAAGA,GAAG,CAACR,KAAK,GAAGQ,GAAG,CAACK,GAAG,CAAC,KAAK,QAAQ;QACnE,CAAC,CAAC;MACJ;MACA,OAAO,QAAQ,OAAO,IAAID,IAAI,GAAGA,IAAI,CAACZ,KAAK,GAAGY,IAAI,CAACC,GAAG,CAAC,KAAK,QAAQ;IACtE,CAAC,CAAC;IACFjC,OAAO,CAAC,CAAC8B,cAAc,EAAE,mFAAmF,CAAC;EAC/G;;EAEA;EACA9B,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACc,eAAe,EAAE,uFAAuF,CAAC;;EAEzI;EACAtB,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACG,QAAQ,EAAE,6CAA6C,CAAC;;EAExF;EACAX,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACM,eAAe,EAAE,mDAAmD,CAAC;;EAErG;EACAb,QAAQ,CAACO,IAAI,KAAK,UAAU,IAAI,CAACM,eAAe,IAAI,CAACF,UAAU,IAAI,CAACC,WAAW,EAAE,iIAAiI,CAAC;;EAEnN;EACA,IAAIG,QAAQ,IAAI,CAACQ,gBAAgB,IAAIhB,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC3ER,OAAO,CAAC,KAAK,EAAE,gEAAgE,CAAC;EAClF;EACAC,QAAQ,CAAC,CAACgB,WAAW,IAAIC,SAAS,EAAE,kIAAkI,CAAC;EACvK,IAAIE,KAAK,KAAKK,SAAS,IAAIL,KAAK,KAAK,IAAI,EAAE;IACzC,IAAIc,MAAM,GAAG9B,OAAO,CAACgB,KAAK,CAAC;IAC3BpB,OAAO,CAAC,CAACmB,YAAY,IAAIe,MAAM,CAACP,KAAK,CAAC,UAAUQ,GAAG,EAAE;MACnD,OAAOrC,OAAO,CAACqC,GAAG,CAAC,KAAK,QAAQ,KAAK,KAAK,IAAIA,GAAG,IAAI,OAAO,IAAIA,GAAG,CAAC;IACtE,CAAC,CAAC,EAAE,kHAAkH,CAAC;IACvHnC,OAAO,CAAC,CAACuB,QAAQ,IAAIa,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE,6DAA6D,CAAC;EAC3G;;EAEA;EACA,IAAIV,QAAQ,EAAE;IACZ,IAAI4B,mBAAmB,GAAG,IAAI;IAC9BvC,WAAW,CAACW,QAAQ,CAAC,CAACqB,IAAI,CAAC,UAAUQ,IAAI,EAAE;MACzC,IAAI,EAAE,aAAarC,KAAK,CAACsC,cAAc,CAACD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACE,IAAI,EAAE;QAC3D,OAAO,KAAK;MACd;MACA,IAAIC,IAAI,GAAGH,IAAI;QACbE,IAAI,GAAGC,IAAI,CAACD,IAAI;MAClB,IAAIA,IAAI,CAACE,cAAc,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IAAIF,IAAI,CAACG,gBAAgB,EAAE;QACzB,IAAIC,gBAAgB,GAAG9C,WAAW,CAACwC,IAAI,CAAChC,KAAK,CAACG,QAAQ,CAAC,CAACiB,KAAK,CAAC,UAAUmB,OAAO,EAAE;UAC/E,IAAI,EAAE,aAAa5C,KAAK,CAACsC,cAAc,CAACM,OAAO,CAAC,IAAI,CAACP,IAAI,CAACE,IAAI,IAAIK,OAAO,CAACL,IAAI,CAACE,cAAc,EAAE;YAC7F,OAAO,IAAI;UACb;UACAL,mBAAmB,GAAGQ,OAAO,CAACL,IAAI;UAClC,OAAO,KAAK;QACd,CAAC,CAAC;QACF,IAAII,gBAAgB,EAAE;UACpB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb;MACAP,mBAAmB,GAAGG,IAAI;MAC1B,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAIH,mBAAmB,EAAE;MACvBtC,OAAO,CAAC,KAAK,EAAE,wEAAwE,CAAC+C,MAAM,CAACT,mBAAmB,CAACU,WAAW,IAAIV,mBAAmB,CAACW,IAAI,IAAIX,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAC3L;IACAtC,OAAO,CAACqB,UAAU,KAAKI,SAAS,EAAE,+DAA+D,CAAC;EACpG;AACF;;AAEA;AACA;AACA,OAAO,SAASyB,kBAAkBA,CAACzC,OAAO,EAAE0C,UAAU,EAAE;EACtD,IAAI1C,OAAO,EAAE;IACX,IAAI2C,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAE;MAC5D,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK9B,SAAS,GAAG8B,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACvF,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC3C,IAAIC,MAAM,GAAGL,WAAW,CAACI,CAAC,CAAC;QAC3B,IAAIC,MAAM,CAACP,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC/B,KAAK,CAAC,KAAK,IAAI,EAAE;UAC7FpB,OAAO,CAAC,KAAK,EAAE,iDAAiD,CAAC;UACjE,OAAO,IAAI;QACb;QACA,IAAI,CAACsD,OAAO,IAAIlB,KAAK,CAACC,OAAO,CAACqB,MAAM,CAACP,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC1C,OAAO,CAAC,CAAC,IAAI2C,gBAAgB,CAACM,MAAM,CAACP,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC1C,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE;UAC/N;QACF;MACF;IACF,CAAC;IACD2C,gBAAgB,CAAC3C,OAAO,CAAC;EAC3B;AACF;AACA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
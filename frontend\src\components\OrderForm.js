import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Table,
  Row,
  Col,
  Card,
  Divider,
  message,
  Space,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  UserAddOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { orderAPI, customerAPI, productAPI } from '../services/api';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

const OrderForm = ({ visible, order, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [customerForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 状态管理
  const [orderItems, setOrderItems] = useState([]);
  const [totals, setTotals] = useState({
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0
  });
  const [customerModalVisible, setCustomerModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);

  // 获取客户列表
  const { data: customersData } = useQuery({
    queryKey: ['customers', { limit: 1000 }],
    queryFn: () => customerAPI.getCustomers({ limit: 1000 })
  });

  // 获取产品列表
  const { data: productsData } = useQuery({
    queryKey: ['products', { limit: 1000 }],
    queryFn: () => productAPI.getProducts({ limit: 1000 })
  });

  // 创建订单
  const createOrderMutation = useMutation({
    mutationFn: orderAPI.createOrder,
    onSuccess: () => {
      message.success('订单创建成功');
      onSuccess();
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '创建失败');
    }
  });

  // 更新订单
  const updateOrderMutation = useMutation({
    mutationFn: ({ id, data }) => orderAPI.updateOrder(id, data),
    onSuccess: () => {
      message.success('订单更新成功');
      onSuccess();
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '更新失败');
    }
  });

  // 创建客户
  const createCustomerMutation = useMutation({
    mutationFn: customerAPI.createCustomer,
    onSuccess: (response) => {
      message.success('客户创建成功');
      setCustomerModalVisible(false);
      customerForm.resetFields();
      // 刷新客户列表
      queryClient.invalidateQueries(['customers']);
      // 自动选择新创建的客户
      form.setFieldValue('customer_id', response.data.customer.id);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '创建客户失败');
    }
  });

  // 初始化表单数据
  useEffect(() => {
    if (order) {
      // 编辑模式
      form.setFieldsValue({
        customer_id: order.customer_id,
        delivery_date: order.delivery_date ? dayjs(order.delivery_date) : null,
        priority: order.priority,
        discount_amount: order.discount_amount,
        shipping_cost: order.shipping_cost,
        payment_method: order.payment_method,
        shipping_address: order.shipping_address,
        tracking_number: order.tracking_number,
        notes: order.notes
      });
      
      if (order.items) {
        setOrderItems(order.items.map(item => ({
          key: item.id,
          product_id: item.product_id,
          product_name: item.product?.name,
          product_code: item.product?.product_code,
          unit: item.product?.unit,
          quantity: item.quantity,
          unit_price: Number(item.unit_price),
          unit_cost: Number(item.unit_cost),
          discount_rate: Number(item.discount_rate),
          discount_amount: Number(item.discount_amount),
          tax_rate: Number(item.tax_rate),
          tax_amount: Number(item.tax_amount),
          line_total: Number(item.line_total),
          notes: item.notes
        })));
      }
    } else {
      // 新建模式
      form.resetFields();
      setOrderItems([]);
      form.setFieldsValue({
        priority: 'normal',
        discount_amount: 0,
        shipping_cost: 0
      });
    }
  }, [order, form]);

  // 计算订单总额
  const calculateTotals = (items, discountAmount = 0, shippingCost = 0) => {
    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxAmount = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + shippingCost - discountAmount;
    
    setTotals({
      subtotal,
      taxAmount,
      totalAmount
    });
    
    return { subtotal, taxAmount, totalAmount };
  };

  // 计算行总计
  const calculateLineTotal = (item) => {
    const { quantity = 0, unit_price = 0, discount_rate = 0, tax_rate = 0 } = item;
    const subtotal = quantity * unit_price;
    const discountAmount = subtotal * (discount_rate / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (tax_rate / 100);
    const lineTotal = afterDiscount + taxAmount;
    
    return {
      discount_amount: discountAmount,
      tax_amount: taxAmount,
      line_total: lineTotal
    };
  };

  // 添加订单项
  const addOrderItem = () => {
    const newItem = {
      key: Date.now(),
      product_id: null,
      product_name: '',
      product_code: '',
      unit: '',
      quantity: 1,
      unit_price: 0,
      unit_cost: 0,
      discount_rate: 0,
      discount_amount: 0,
      tax_rate: 0,
      tax_amount: 0,
      line_total: 0,
      notes: ''
    };
    
    const newItems = [...orderItems, newItem];
    setOrderItems(newItems);
    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));
  };

  // 删除订单项
  const removeOrderItem = (key) => {
    const newItems = orderItems.filter(item => item.key !== key);
    setOrderItems(newItems);
    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));
  };

  // 更新订单项
  const updateOrderItem = (key, field, value) => {
    const newItems = orderItems.map(item => {
      if (item.key === key) {
        const updatedItem = { ...item, [field]: value };
        
        // 如果是产品选择，自动填充产品信息
        if (field === 'product_id' && value) {
          const product = productsData?.data?.products?.find(p => p.id === value);
          if (product) {
            updatedItem.product_name = product.name;
            updatedItem.product_code = product.product_code;
            updatedItem.unit = product.unit;
            updatedItem.unit_price = Number(product.selling_price || 0);
            updatedItem.unit_cost = Number(product.cost_price || 0);
          }
        }
        
        // 重新计算行总计
        if (['quantity', 'unit_price', 'discount_rate', 'tax_rate'].includes(field)) {
          const calculated = calculateLineTotal(updatedItem);
          Object.assign(updatedItem, calculated);
        }
        
        return updatedItem;
      }
      return item;
    });
    
    setOrderItems(newItems);
    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (orderItems.length === 0) {
        message.error('请至少添加一个订单项');
        return;
      }
      
      // 验证订单项
      const invalidItems = orderItems.filter(item =>
        (!item.product_id && !item.product_name) ||
        item.quantity <= 0 ||
        item.unit_price < 0
      );

      if (invalidItems.length > 0) {
        message.error('请检查订单项信息是否完整（产品名称、数量、单价）');
        return;
      }
      
      const orderData = {
        ...values,
        delivery_date: values.delivery_date?.format('YYYY-MM-DD'),
        items: orderItems.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          product_code: item.product_code,
          unit: item.unit,
          quantity: item.quantity,
          unit_price: item.unit_price,
          unit_cost: item.unit_cost,
          discount_rate: item.discount_rate,
          tax_rate: item.tax_rate,
          notes: item.notes
        }))
      };
      
      if (order) {
        updateOrderMutation.mutate({ id: order.id, data: orderData });
      } else {
        createOrderMutation.mutate(orderData);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 订单项表格列定义
  const itemColumns = [
    {
      title: '产品',
      dataIndex: 'product_id',
      width: 200,
      render: (value, record) => (
        <Space.Compact style={{ width: '100%' }}>
          <Select
            value={value}
            placeholder="选择产品"
            style={{ width: '85%' }}
            showSearch
            optionFilterProp="children"
            allowClear
            onChange={(val) => updateOrderItem(record.key, 'product_id', val)}
          >
            {productsData?.data?.products?.map(product => (
              <Option key={product.id} value={product.id}>
                {product.product_code} - {product.name}
              </Option>
            ))}
          </Select>
          <Tooltip title="手动编辑产品信息">
            <Button
              icon={<EditOutlined />}
              style={{ width: '15%' }}
              onClick={() => setEditingProduct(record.key)}
            />
          </Tooltip>
        </Space.Compact>
      )
    },
    {
      title: '产品名称',
      dataIndex: 'product_name',
      width: 150,
      render: (value, record) => (
        editingProduct === record.key ? (
          <Input
            value={value}
            placeholder="产品名称"
            onChange={(e) => updateOrderItem(record.key, 'product_name', e.target.value)}
            onBlur={() => setEditingProduct(null)}
            autoFocus
          />
        ) : (
          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>
            {value || '点击编辑'}
          </span>
        )
      )
    },
    {
      title: '产品编码',
      dataIndex: 'product_code',
      width: 120,
      render: (value, record) => (
        editingProduct === record.key ? (
          <Input
            value={value}
            placeholder="产品编码"
            onChange={(e) => updateOrderItem(record.key, 'product_code', e.target.value)}
            onBlur={() => setEditingProduct(null)}
          />
        ) : (
          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>
            {value || '点击编辑'}
          </span>
        )
      )
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 80,
      render: (value, record) => (
        editingProduct === record.key ? (
          <Input
            value={value}
            placeholder="单位"
            onChange={(e) => updateOrderItem(record.key, 'unit', e.target.value)}
            onBlur={() => setEditingProduct(null)}
          />
        ) : (
          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>
            {value || '件'}
          </span>
        )
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      render: (value, record) => (
        <InputNumber
          value={value}
          min={1}
          style={{ width: '100%' }}
          onChange={(val) => updateOrderItem(record.key, 'quantity', val || 1)}
        />
      )
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      width: 120,
      render: (value, record) => (
        <InputNumber
          value={value}
          min={0}
          precision={2}
          style={{ width: '100%' }}
          onChange={(val) => updateOrderItem(record.key, 'unit_price', val || 0)}
        />
      )
    },
    {
      title: '折扣率(%)',
      dataIndex: 'discount_rate',
      width: 100,
      render: (value, record) => (
        <InputNumber
          value={value}
          min={0}
          max={100}
          precision={2}
          style={{ width: '100%' }}
          onChange={(val) => updateOrderItem(record.key, 'discount_rate', val || 0)}
        />
      )
    },
    {
      title: '税率(%)',
      dataIndex: 'tax_rate',
      width: 100,
      render: (value, record) => (
        <InputNumber
          value={value}
          min={0}
          max={100}
          precision={2}
          style={{ width: '100%' }}
          onChange={(val) => updateOrderItem(record.key, 'tax_rate', val || 0)}
        />
      )
    },
    {
      title: '行总计',
      dataIndex: 'line_total',
      width: 120,
      render: (value) => `¥${Number(value || 0).toFixed(2)}`
    },
    {
      title: '操作',
      width: 80,
      render: (_, record) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeOrderItem(record.key)}
        />
      )
    }
  ];

  return (
    <Modal
      title={order ? '编辑订单' : '新建订单'}
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={createOrderMutation.isLoading || updateOrderMutation.isLoading}
          onClick={handleSubmit}
        >
          {order ? '更新' : '创建'}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          priority: 'normal',
          discount_amount: 0,
          shipping_cost: 0
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="customer_id"
              label={
                <Space>
                  客户
                  <Tooltip title="快速创建客户">
                    <Button
                      type="link"
                      size="small"
                      icon={<UserAddOutlined />}
                      onClick={() => setCustomerModalVisible(true)}
                    >
                      新建客户
                    </Button>
                  </Tooltip>
                </Space>
              }
              rules={[{ required: true, message: '请选择客户' }]}
            >
              <Select
                placeholder="选择客户"
                showSearch
                optionFilterProp="children"
              >
                {customersData?.data?.customers?.map(customer => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.company_name} - {customer.contact_person}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="delivery_date" label="交付日期">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="priority" label="优先级">
              <Select>
                <Option value="low">低</Option>
                <Option value="normal">普通</Option>
                <Option value="high">高</Option>
                <Option value="urgent">紧急</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>订单明细</Divider>
        
        <div style={{ marginBottom: 16 }}>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addOrderItem}
            style={{ width: '100%' }}
          >
            添加订单项
          </Button>
        </div>

        <Table
          columns={itemColumns}
          dataSource={orderItems}
          pagination={false}
          scroll={{ x: 800 }}
          size="small"
        />

        <Divider>费用信息</Divider>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="discount_amount" label="订单折扣">
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                precision={2}
                addonBefore="¥"
                onChange={(value) => {
                  calculateTotals(orderItems, value || 0, form.getFieldValue('shipping_cost'));
                }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="shipping_cost" label="运费">
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                precision={2}
                addonBefore="¥"
                onChange={(value) => {
                  calculateTotals(orderItems, form.getFieldValue('discount_amount'), value || 0);
                }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="payment_method" label="付款方式">
              <Select placeholder="选择付款方式">
                <Option value="cash">现金</Option>
                <Option value="bank_transfer">银行转账</Option>
                <Option value="credit_card">信用卡</Option>
                <Option value="check">支票</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="shipping_address" label="收货地址">
              <TextArea rows={3} placeholder="请输入收货地址" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="tracking_number" label="物流单号">
              <Input placeholder="请输入物流单号" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="notes" label="备注">
          <TextArea rows={2} placeholder="请输入备注信息" />
        </Form.Item>

        {/* 订单总计 */}
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Row gutter={16}>
            <Col span={6}>
              <div>小计: ¥{totals.subtotal.toFixed(2)}</div>
            </Col>
            <Col span={6}>
              <div>税额: ¥{totals.taxAmount.toFixed(2)}</div>
            </Col>
            <Col span={6}>
              <div>运费: ¥{(form.getFieldValue('shipping_cost') || 0).toFixed(2)}</div>
            </Col>
            <Col span={6}>
              <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                总计: ¥{totals.totalAmount.toFixed(2)}
              </div>
            </Col>
          </Row>
        </Card>
      </Form>

      {/* 客户创建Modal */}
      <Modal
        title="快速创建客户"
        open={customerModalVisible}
        onCancel={() => {
          setCustomerModalVisible(false);
          customerForm.resetFields();
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setCustomerModalVisible(false);
            customerForm.resetFields();
          }}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={createCustomerMutation.isLoading}
            onClick={() => {
              customerForm.validateFields().then(values => {
                createCustomerMutation.mutate(values);
              }).catch(error => {
                console.error('客户表单验证失败:', error);
              });
            }}
          >
            创建客户
          </Button>
        ]}
      >
        <Form
          form={customerForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="company_name"
                label="公司名称"
                rules={[{ required: true, message: '请输入公司名称' }]}
              >
                <Input placeholder="请输入公司名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="contact_person"
                label="联系人"
                rules={[{ required: true, message: '请输入联系人' }]}
              >
                <Input placeholder="请输入联系人姓名" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="mobile" label="手机号">
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="customer_type" label="客户类型" initialValue="retail">
                <Select>
                  <Option value="retail">零售</Option>
                  <Option value="wholesale">批发</Option>
                  <Option value="distributor">分销商</Option>
                  <Option value="online">线上</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="payment_terms" label="付款期限(天)" initialValue={30}>
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="address" label="地址">
            <TextArea rows={2} placeholder="请输入详细地址" />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <TextArea rows={2} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default OrderForm;

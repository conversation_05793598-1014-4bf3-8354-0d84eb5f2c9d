{"ast": null, "code": "import { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\n\n/**\n * Trigger `callback` immediately when `condition` is `true`.\n * But trigger `callback` in next frame when `condition` is `false`.\n */\nexport default function useLockEffect(condition, callback) {\n  var delayFrames = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var callbackRef = React.useRef(callback);\n  callbackRef.current = callback;\n  useLayoutUpdateEffect(function () {\n    if (condition) {\n      callbackRef.current(condition);\n    } else {\n      var id = raf(function () {\n        callbackRef.current(condition);\n      }, delayFrames);\n      return function () {\n        raf.cancel(id);\n      };\n    }\n  }, [condition]);\n}", "map": {"version": 3, "names": ["useLayoutUpdateEffect", "raf", "React", "useLockEffect", "condition", "callback", "delayFrames", "arguments", "length", "undefined", "callback<PERSON><PERSON>", "useRef", "current", "id", "cancel"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useLockEffect.js"], "sourcesContent": ["import { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\n\n/**\n * Trigger `callback` immediately when `condition` is `true`.\n * But trigger `callback` in next frame when `condition` is `false`.\n */\nexport default function useLockEffect(condition, callback) {\n  var delayFrames = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var callbackRef = React.useRef(callback);\n  callbackRef.current = callback;\n  useLayoutUpdateEffect(function () {\n    if (condition) {\n      callbackRef.current(condition);\n    } else {\n      var id = raf(function () {\n        callbackRef.current(condition);\n      }, delayFrames);\n      return function () {\n        raf.cancel(id);\n      };\n    }\n  }, [condition]);\n}"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,kCAAkC;AACxE,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACzD,IAAIC,WAAW,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACvF,IAAIG,WAAW,GAAGR,KAAK,CAACS,MAAM,CAACN,QAAQ,CAAC;EACxCK,WAAW,CAACE,OAAO,GAAGP,QAAQ;EAC9BL,qBAAqB,CAAC,YAAY;IAChC,IAAII,SAAS,EAAE;MACbM,WAAW,CAACE,OAAO,CAACR,SAAS,CAAC;IAChC,CAAC,MAAM;MACL,IAAIS,EAAE,GAAGZ,GAAG,CAAC,YAAY;QACvBS,WAAW,CAACE,OAAO,CAACR,SAAS,CAAC;MAChC,CAAC,EAAEE,WAAW,CAAC;MACf,OAAO,YAAY;QACjBL,GAAG,CAACa,MAAM,CAACD,EAAE,CAAC;MAChB,CAAC;IACH;EACF,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
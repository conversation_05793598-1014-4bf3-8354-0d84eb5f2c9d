{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nvar uniquePrefix = Math.random().toFixed(5).toString().slice(2);\nvar internalId = 0;\nexport default function useUUID(id) {\n  var _useMergedState = useMergedState(id, {\n      value: id\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    uuid = _useMergedState2[0],\n    setUUID = _useMergedState2[1];\n  React.useEffect(function () {\n    internalId += 1;\n    var newId = process.env.NODE_ENV === 'test' ? 'test' : \"\".concat(uniquePrefix, \"-\").concat(internalId);\n    setUUID(\"rc-menu-uuid-\".concat(newId));\n  }, []);\n  return uuid;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useMergedState", "uniquePrefix", "Math", "random", "toFixed", "toString", "slice", "internalId", "useUUID", "id", "_useMergedState", "value", "_useMergedState2", "uuid", "setUUID", "useEffect", "newId", "process", "env", "NODE_ENV", "concat"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-menu@9.16.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-menu/es/hooks/useUUID.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nvar uniquePrefix = Math.random().toFixed(5).toString().slice(2);\nvar internalId = 0;\nexport default function useUUID(id) {\n  var _useMergedState = useMergedState(id, {\n      value: id\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    uuid = _useMergedState2[0],\n    setUUID = _useMergedState2[1];\n  React.useEffect(function () {\n    internalId += 1;\n    var newId = process.env.NODE_ENV === 'test' ? 'test' : \"\".concat(uniquePrefix, \"-\").concat(internalId);\n    setUUID(\"rc-menu-uuid-\".concat(newId));\n  }, []);\n  return uuid;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,IAAIC,YAAY,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;AAC/D,IAAIC,UAAU,GAAG,CAAC;AAClB,eAAe,SAASC,OAAOA,CAACC,EAAE,EAAE;EAClC,IAAIC,eAAe,GAAGV,cAAc,CAACS,EAAE,EAAE;MACrCE,KAAK,EAAEF;IACT,CAAC,CAAC;IACFG,gBAAgB,GAAGd,cAAc,CAACY,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC/Bb,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1BR,UAAU,IAAI,CAAC;IACf,IAAIS,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,CAACC,MAAM,CAACnB,YAAY,EAAE,GAAG,CAAC,CAACmB,MAAM,CAACb,UAAU,CAAC;IACtGO,OAAO,CAAC,eAAe,CAACM,MAAM,CAACJ,KAAK,CAAC,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useBaseProps } from 'rc-select';\nimport * as React from 'react';\nimport RawOptionList from \"./List\";\nvar RefOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var baseProps = useBaseProps();\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(RawOptionList, _extends({}, props, baseProps, {\n    ref: ref\n  }));\n});\nexport default RefOptionList;", "map": {"version": 3, "names": ["_extends", "useBaseProps", "React", "RawOptionList", "RefOptionList", "forwardRef", "props", "ref", "baseProps", "createElement"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/OptionList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useBaseProps } from 'rc-select';\nimport * as React from 'react';\nimport RawOptionList from \"./List\";\nvar RefOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var baseProps = useBaseProps();\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(RawOptionList, _extends({}, props, baseProps, {\n    ref: ref\n  }));\n});\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,QAAQ;AAClC,IAAIC,aAAa,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,SAAS,GAAGP,YAAY,CAAC,CAAC;;EAE9B;EACA,OAAO,aAAaC,KAAK,CAACO,aAAa,CAACN,aAAa,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAEE,SAAS,EAAE;IACpFD,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
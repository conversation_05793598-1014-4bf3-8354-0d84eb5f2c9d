{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction buildTreeStructure(nodes, config) {\n  var id = config.id,\n    pId = config.pId,\n    rootPId = config.rootPId;\n  var nodeMap = new Map();\n  var rootNodes = [];\n  nodes.forEach(function (node) {\n    var nodeKey = node[id];\n    var clonedNode = _objectSpread(_objectSpread({}, node), {}, {\n      key: node.key || nodeKey\n    });\n    nodeMap.set(nodeKey, clonedNode);\n  });\n  nodeMap.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = nodeMap.get(parentKey);\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    } else if (parentKey === rootPId || rootPId === null) {\n      rootNodes.push(node);\n    }\n  });\n  return rootNodes;\n}\n\n/**\n * 将 `treeData` 或 `children` 转换为格式化的 `treeData`。\n * 如果 `treeData` 或 `children` 没有变化，则不会重新计算。\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      if (simpleMode) {\n        var config = _objectSpread({\n          id: 'id',\n          pId: 'pId',\n          rootPId: null\n        }, _typeof(simpleMode) === 'object' ? simpleMode : {});\n        return buildTreeStructure(treeData, config);\n      }\n      return treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}", "map": {"version": 3, "names": ["_typeof", "_objectSpread", "React", "convertChildrenToData", "buildTreeStructure", "nodes", "config", "id", "pId", "rootPId", "nodeMap", "Map", "rootNodes", "for<PERSON>ach", "node", "nodeKey", "clonedNode", "key", "set", "parent<PERSON><PERSON>", "parent", "get", "children", "push", "useTreeData", "treeData", "simpleMode", "useMemo"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree-select@5.27.0_react_436633a6a2f00ab1ebd4c6e59066bdb2/node_modules/rc-tree-select/es/hooks/useTreeData.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction buildTreeStructure(nodes, config) {\n  var id = config.id,\n    pId = config.pId,\n    rootPId = config.rootPId;\n  var nodeMap = new Map();\n  var rootNodes = [];\n  nodes.forEach(function (node) {\n    var nodeKey = node[id];\n    var clonedNode = _objectSpread(_objectSpread({}, node), {}, {\n      key: node.key || nodeKey\n    });\n    nodeMap.set(nodeKey, clonedNode);\n  });\n  nodeMap.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = nodeMap.get(parentKey);\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    } else if (parentKey === rootPId || rootPId === null) {\n      rootNodes.push(node);\n    }\n  });\n  return rootNodes;\n}\n\n/**\n * 将 `treeData` 或 `children` 转换为格式化的 `treeData`。\n * 如果 `treeData` 或 `children` 没有变化，则不会重新计算。\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      if (simpleMode) {\n        var config = _objectSpread({\n          id: 'id',\n          pId: 'pId',\n          rootPId: null\n        }, _typeof(simpleMode) === 'object' ? simpleMode : {});\n        return buildTreeStructure(treeData, config);\n      }\n      return treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzC,IAAIC,EAAE,GAAGD,MAAM,CAACC,EAAE;IAChBC,GAAG,GAAGF,MAAM,CAACE,GAAG;IAChBC,OAAO,GAAGH,MAAM,CAACG,OAAO;EAC1B,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB,IAAIC,SAAS,GAAG,EAAE;EAClBP,KAAK,CAACQ,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC5B,IAAIC,OAAO,GAAGD,IAAI,CAACP,EAAE,CAAC;IACtB,IAAIS,UAAU,GAAGf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1DG,GAAG,EAAEH,IAAI,CAACG,GAAG,IAAIF;IACnB,CAAC,CAAC;IACFL,OAAO,CAACQ,GAAG,CAACH,OAAO,EAAEC,UAAU,CAAC;EAClC,CAAC,CAAC;EACFN,OAAO,CAACG,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC9B,IAAIK,SAAS,GAAGL,IAAI,CAACN,GAAG,CAAC;IACzB,IAAIY,MAAM,GAAGV,OAAO,CAACW,GAAG,CAACF,SAAS,CAAC;IACnC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAACE,QAAQ,IAAI,EAAE;MACvCF,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACT,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAIK,SAAS,KAAKV,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpDG,SAAS,CAACW,IAAI,CAACT,IAAI,CAAC;IACtB;EACF,CAAC,CAAC;EACF,OAAOF,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA,eAAe,SAASY,WAAWA,CAACC,QAAQ,EAAEH,QAAQ,EAAEI,UAAU,EAAE;EAClE,OAAOxB,KAAK,CAACyB,OAAO,CAAC,YAAY;IAC/B,IAAIF,QAAQ,EAAE;MACZ,IAAIC,UAAU,EAAE;QACd,IAAIpB,MAAM,GAAGL,aAAa,CAAC;UACzBM,EAAE,EAAE,IAAI;UACRC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE;QACX,CAAC,EAAET,OAAO,CAAC0B,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC,CAAC;QACtD,OAAOtB,kBAAkB,CAACqB,QAAQ,EAAEnB,MAAM,CAAC;MAC7C;MACA,OAAOmB,QAAQ;IACjB;IACA,OAAOtB,qBAAqB,CAACmB,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACA,QAAQ,EAAEI,UAAU,EAAED,QAAQ,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
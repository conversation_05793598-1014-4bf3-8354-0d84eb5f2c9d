{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nvar useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var extractValues = function extractValues(values) {\n      return values.map(function (_ref) {\n        var value = _ref.value;\n        return value;\n      });\n    };\n    var checkedKeys = extractValues(rawLabeledValues);\n    var halfCheckedKeys = extractValues(rawHalfCheckedValues);\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    var finalCheckedKeys = checkedKeys;\n    var finalHalfCheckedKeys = halfCheckedKeys;\n    if (treeConduction) {\n      var conductResult = conductCheck(checkedKeys, true, keyEntities);\n      finalCheckedKeys = conductResult.checkedKeys;\n      finalHalfCheckedKeys = conductResult.halfCheckedKeys;\n    }\n    return [Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(finalCheckedKeys)))), finalHalfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n};\nexport default useCheckedKeys;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "conduct<PERSON>heck", "useCheckedKeys", "rawLabeledValues", "rawHalfCheckedValues", "treeConduction", "keyEntities", "useMemo", "extractValues", "values", "map", "_ref", "value", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "<PERSON><PERSON><PERSON><PERSON>", "filter", "key", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalHalfChecked<PERSON>eys", "conductResult", "Array", "from", "Set", "concat"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree-select@5.27.0_react_436633a6a2f00ab1ebd4c6e59066bdb2/node_modules/rc-tree-select/es/hooks/useCheckedKeys.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nvar useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var extractValues = function extractValues(values) {\n      return values.map(function (_ref) {\n        var value = _ref.value;\n        return value;\n      });\n    };\n    var checkedKeys = extractValues(rawLabeledValues);\n    var halfCheckedKeys = extractValues(rawHalfCheckedValues);\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    var finalCheckedKeys = checkedKeys;\n    var finalHalfCheckedKeys = halfCheckedKeys;\n    if (treeConduction) {\n      var conductResult = conductCheck(checkedKeys, true, keyEntities);\n      finalCheckedKeys = conductResult.checkedKeys;\n      finalHalfCheckedKeys = conductResult.halfCheckedKeys;\n    }\n    return [Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(finalCheckedKeys)))), finalHalfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n};\nexport default useCheckedKeys;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,gBAAgB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,WAAW,EAAE;EAChH,OAAON,KAAK,CAACO,OAAO,CAAC,YAAY;IAC/B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAE;MACjD,OAAOA,MAAM,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;QAChC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QACtB,OAAOA,KAAK;MACd,CAAC,CAAC;IACJ,CAAC;IACD,IAAIC,WAAW,GAAGL,aAAa,CAACL,gBAAgB,CAAC;IACjD,IAAIW,eAAe,GAAGN,aAAa,CAACJ,oBAAoB,CAAC;IACzD,IAAIW,aAAa,GAAGF,WAAW,CAACG,MAAM,CAAC,UAAUC,GAAG,EAAE;MACpD,OAAO,CAACX,WAAW,CAACW,GAAG,CAAC;IAC1B,CAAC,CAAC;IACF,IAAIC,gBAAgB,GAAGL,WAAW;IAClC,IAAIM,oBAAoB,GAAGL,eAAe;IAC1C,IAAIT,cAAc,EAAE;MAClB,IAAIe,aAAa,GAAGnB,YAAY,CAACY,WAAW,EAAE,IAAI,EAAEP,WAAW,CAAC;MAChEY,gBAAgB,GAAGE,aAAa,CAACP,WAAW;MAC5CM,oBAAoB,GAAGC,aAAa,CAACN,eAAe;IACtD;IACA,OAAO,CAACO,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,EAAE,CAACC,MAAM,CAACzB,kBAAkB,CAACgB,aAAa,CAAC,EAAEhB,kBAAkB,CAACmB,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAEC,oBAAoB,CAAC;EACxI,CAAC,EAAE,CAAChB,gBAAgB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,WAAW,CAAC,CAAC;AAC3E,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
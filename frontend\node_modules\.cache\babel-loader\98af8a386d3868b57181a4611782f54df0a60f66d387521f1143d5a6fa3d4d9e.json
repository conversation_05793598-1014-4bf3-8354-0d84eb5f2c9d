{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initFadeMotion } from '../../style/motion';\n// =========================== Motion ===========================\nconst genMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const uploadAnimateInlineIn = new Keyframes('uploadAnimateInlineIn', {\n    from: {\n      width: 0,\n      height: 0,\n      padding: 0,\n      opacity: 0,\n      margin: token.calc(token.marginXS).div(-2).equal()\n    }\n  });\n  const uploadAnimateInlineOut = new Keyframes('uploadAnimateInlineOut', {\n    to: {\n      width: 0,\n      height: 0,\n      padding: 0,\n      opacity: 0,\n      margin: token.calc(token.marginXS).div(-2).equal()\n    }\n  });\n  const inlineCls = `${componentCls}-animate-inline`;\n  return [{\n    [`${componentCls}-wrapper`]: {\n      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseInOutCirc,\n        animationFillMode: 'forwards'\n      },\n      [`${inlineCls}-appear, ${inlineCls}-enter`]: {\n        animationName: uploadAnimateInlineIn\n      },\n      [`${inlineCls}-leave`]: {\n        animationName: uploadAnimateInlineOut\n      }\n    }\n  }, {\n    [`${componentCls}-wrapper`]: initFadeMotion(token)\n  }, uploadAnimateInlineIn, uploadAnimateInlineOut];\n};\nexport default genMotionStyle;", "map": {"version": 3, "names": ["Keyframes", "initFadeMotion", "genMotionStyle", "token", "componentCls", "uploadAnimateInlineIn", "from", "width", "height", "padding", "opacity", "margin", "calc", "marginXS", "div", "equal", "uploadAnimateInlineOut", "to", "inlineCls", "animationDuration", "motionDurationSlow", "animationTimingFunction", "motionEaseInOutCirc", "animationFillMode", "animationName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/upload/style/motion.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nimport { initFadeMotion } from '../../style/motion';\n// =========================== Motion ===========================\nconst genMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const uploadAnimateInlineIn = new Keyframes('uploadAnimateInlineIn', {\n    from: {\n      width: 0,\n      height: 0,\n      padding: 0,\n      opacity: 0,\n      margin: token.calc(token.marginXS).div(-2).equal()\n    }\n  });\n  const uploadAnimateInlineOut = new Keyframes('uploadAnimateInlineOut', {\n    to: {\n      width: 0,\n      height: 0,\n      padding: 0,\n      opacity: 0,\n      margin: token.calc(token.marginXS).div(-2).equal()\n    }\n  });\n  const inlineCls = `${componentCls}-animate-inline`;\n  return [{\n    [`${componentCls}-wrapper`]: {\n      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseInOutCirc,\n        animationFillMode: 'forwards'\n      },\n      [`${inlineCls}-appear, ${inlineCls}-enter`]: {\n        animationName: uploadAnimateInlineIn\n      },\n      [`${inlineCls}-leave`]: {\n        animationName: uploadAnimateInlineOut\n      }\n    }\n  }, {\n    [`${componentCls}-wrapper`]: initFadeMotion(token)\n  }, uploadAnimateInlineIn, uploadAnimateInlineOut];\n};\nexport default genMotionStyle;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,cAAc,QAAQ,oBAAoB;AACnD;AACA,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,qBAAqB,GAAG,IAAIL,SAAS,CAAC,uBAAuB,EAAE;IACnEM,IAAI,EAAE;MACJC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;IACnD;EACF,CAAC,CAAC;EACF,MAAMC,sBAAsB,GAAG,IAAIhB,SAAS,CAAC,wBAAwB,EAAE;IACrEiB,EAAE,EAAE;MACFV,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;IACnD;EACF,CAAC,CAAC;EACF,MAAMG,SAAS,GAAG,GAAGd,YAAY,iBAAiB;EAClD,OAAO,CAAC;IACN,CAAC,GAAGA,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGc,SAAS,YAAYA,SAAS,WAAWA,SAAS,QAAQ,GAAG;QAC/DC,iBAAiB,EAAEhB,KAAK,CAACiB,kBAAkB;QAC3CC,uBAAuB,EAAElB,KAAK,CAACmB,mBAAmB;QAClDC,iBAAiB,EAAE;MACrB,CAAC;MACD,CAAC,GAAGL,SAAS,YAAYA,SAAS,QAAQ,GAAG;QAC3CM,aAAa,EAAEnB;MACjB,CAAC;MACD,CAAC,GAAGa,SAAS,QAAQ,GAAG;QACtBM,aAAa,EAAER;MACjB;IACF;EACF,CAAC,EAAE;IACD,CAAC,GAAGZ,YAAY,UAAU,GAAGH,cAAc,CAACE,KAAK;EACnD,CAAC,EAAEE,qBAAqB,EAAEW,sBAAsB,CAAC;AACnD,CAAC;AACD,eAAed,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
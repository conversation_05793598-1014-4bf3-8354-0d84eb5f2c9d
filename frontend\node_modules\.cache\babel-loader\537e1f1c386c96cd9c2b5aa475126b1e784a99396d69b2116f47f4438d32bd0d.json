{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// ================================== Cache ==================================\n\nexport function sameDerivativeOption(left, right) {\n  if (left.length !== right.length) {\n    return false;\n  }\n  for (var i = 0; i < left.length; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nvar ThemeCache = /*#__PURE__*/function () {\n  function ThemeCache() {\n    _classCallCheck(this, ThemeCache);\n    _defineProperty(this, \"cache\", void 0);\n    _defineProperty(this, \"keys\", void 0);\n    _defineProperty(this, \"cacheCallTimes\", void 0);\n    this.cache = new Map();\n    this.keys = [];\n    this.cacheCallTimes = 0;\n  }\n  _createClass(ThemeCache, [{\n    key: \"size\",\n    value: function size() {\n      return this.keys.length;\n    }\n  }, {\n    key: \"internalGet\",\n    value: function internalGet(derivativeOption) {\n      var _cache2, _cache3;\n      var updateCallTimes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var cache = {\n        map: this.cache\n      };\n      derivativeOption.forEach(function (derivative) {\n        if (!cache) {\n          cache = undefined;\n        } else {\n          var _cache;\n          cache = (_cache = cache) === null || _cache === void 0 || (_cache = _cache.map) === null || _cache === void 0 ? void 0 : _cache.get(derivative);\n        }\n      });\n      if ((_cache2 = cache) !== null && _cache2 !== void 0 && _cache2.value && updateCallTimes) {\n        cache.value[1] = this.cacheCallTimes++;\n      }\n      return (_cache3 = cache) === null || _cache3 === void 0 ? void 0 : _cache3.value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(derivativeOption) {\n      var _this$internalGet;\n      return (_this$internalGet = this.internalGet(derivativeOption, true)) === null || _this$internalGet === void 0 ? void 0 : _this$internalGet[0];\n    }\n  }, {\n    key: \"has\",\n    value: function has(derivativeOption) {\n      return !!this.internalGet(derivativeOption);\n    }\n  }, {\n    key: \"set\",\n    value: function set(derivativeOption, value) {\n      var _this = this;\n      // New cache\n      if (!this.has(derivativeOption)) {\n        if (this.size() + 1 > ThemeCache.MAX_CACHE_SIZE + ThemeCache.MAX_CACHE_OFFSET) {\n          var _this$keys$reduce = this.keys.reduce(function (result, key) {\n              var _result = _slicedToArray(result, 2),\n                callTimes = _result[1];\n              if (_this.internalGet(key)[1] < callTimes) {\n                return [key, _this.internalGet(key)[1]];\n              }\n              return result;\n            }, [this.keys[0], this.cacheCallTimes]),\n            _this$keys$reduce2 = _slicedToArray(_this$keys$reduce, 1),\n            targetKey = _this$keys$reduce2[0];\n          this.delete(targetKey);\n        }\n        this.keys.push(derivativeOption);\n      }\n      var cache = this.cache;\n      derivativeOption.forEach(function (derivative, index) {\n        if (index === derivativeOption.length - 1) {\n          cache.set(derivative, {\n            value: [value, _this.cacheCallTimes++]\n          });\n        } else {\n          var cacheValue = cache.get(derivative);\n          if (!cacheValue) {\n            cache.set(derivative, {\n              map: new Map()\n            });\n          } else if (!cacheValue.map) {\n            cacheValue.map = new Map();\n          }\n          cache = cache.get(derivative).map;\n        }\n      });\n    }\n  }, {\n    key: \"deleteByPath\",\n    value: function deleteByPath(currentCache, derivatives) {\n      var cache = currentCache.get(derivatives[0]);\n      if (derivatives.length === 1) {\n        var _cache$value;\n        if (!cache.map) {\n          currentCache.delete(derivatives[0]);\n        } else {\n          currentCache.set(derivatives[0], {\n            map: cache.map\n          });\n        }\n        return (_cache$value = cache.value) === null || _cache$value === void 0 ? void 0 : _cache$value[0];\n      }\n      var result = this.deleteByPath(cache.map, derivatives.slice(1));\n      if ((!cache.map || cache.map.size === 0) && !cache.value) {\n        currentCache.delete(derivatives[0]);\n      }\n      return result;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(derivativeOption) {\n      // If cache exists\n      if (this.has(derivativeOption)) {\n        this.keys = this.keys.filter(function (item) {\n          return !sameDerivativeOption(item, derivativeOption);\n        });\n        return this.deleteByPath(this.cache, derivativeOption);\n      }\n      return undefined;\n    }\n  }]);\n  return ThemeCache;\n}();\n_defineProperty(ThemeCache, \"MAX_CACHE_SIZE\", 20);\n_defineProperty(ThemeCache, \"MAX_CACHE_OFFSET\", 5);\nexport { ThemeCache as default };", "map": {"version": 3, "names": ["_slicedToArray", "_classCallCheck", "_createClass", "_defineProperty", "sameDerivativeOption", "left", "right", "length", "i", "ThemeCache", "cache", "Map", "keys", "cacheCallTimes", "key", "value", "size", "internalGet", "derivativeOption", "_cache2", "_cache3", "updateCallTimes", "arguments", "undefined", "map", "for<PERSON>ach", "derivative", "_cache", "get", "_this$internalGet", "has", "set", "_this", "MAX_CACHE_SIZE", "MAX_CACHE_OFFSET", "_this$keys$reduce", "reduce", "result", "_result", "callTimes", "_this$keys$reduce2", "<PERSON><PERSON><PERSON>", "delete", "push", "index", "cacheValue", "deleteByPath", "currentCache", "derivatives", "_cache$value", "slice", "_delete", "filter", "item", "default"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/theme/ThemeCache.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// ================================== Cache ==================================\n\nexport function sameDerivativeOption(left, right) {\n  if (left.length !== right.length) {\n    return false;\n  }\n  for (var i = 0; i < left.length; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nvar ThemeCache = /*#__PURE__*/function () {\n  function ThemeCache() {\n    _classCallCheck(this, ThemeCache);\n    _defineProperty(this, \"cache\", void 0);\n    _defineProperty(this, \"keys\", void 0);\n    _defineProperty(this, \"cacheCallTimes\", void 0);\n    this.cache = new Map();\n    this.keys = [];\n    this.cacheCallTimes = 0;\n  }\n  _createClass(ThemeCache, [{\n    key: \"size\",\n    value: function size() {\n      return this.keys.length;\n    }\n  }, {\n    key: \"internalGet\",\n    value: function internalGet(derivativeOption) {\n      var _cache2, _cache3;\n      var updateCallTimes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var cache = {\n        map: this.cache\n      };\n      derivativeOption.forEach(function (derivative) {\n        if (!cache) {\n          cache = undefined;\n        } else {\n          var _cache;\n          cache = (_cache = cache) === null || _cache === void 0 || (_cache = _cache.map) === null || _cache === void 0 ? void 0 : _cache.get(derivative);\n        }\n      });\n      if ((_cache2 = cache) !== null && _cache2 !== void 0 && _cache2.value && updateCallTimes) {\n        cache.value[1] = this.cacheCallTimes++;\n      }\n      return (_cache3 = cache) === null || _cache3 === void 0 ? void 0 : _cache3.value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(derivativeOption) {\n      var _this$internalGet;\n      return (_this$internalGet = this.internalGet(derivativeOption, true)) === null || _this$internalGet === void 0 ? void 0 : _this$internalGet[0];\n    }\n  }, {\n    key: \"has\",\n    value: function has(derivativeOption) {\n      return !!this.internalGet(derivativeOption);\n    }\n  }, {\n    key: \"set\",\n    value: function set(derivativeOption, value) {\n      var _this = this;\n      // New cache\n      if (!this.has(derivativeOption)) {\n        if (this.size() + 1 > ThemeCache.MAX_CACHE_SIZE + ThemeCache.MAX_CACHE_OFFSET) {\n          var _this$keys$reduce = this.keys.reduce(function (result, key) {\n              var _result = _slicedToArray(result, 2),\n                callTimes = _result[1];\n              if (_this.internalGet(key)[1] < callTimes) {\n                return [key, _this.internalGet(key)[1]];\n              }\n              return result;\n            }, [this.keys[0], this.cacheCallTimes]),\n            _this$keys$reduce2 = _slicedToArray(_this$keys$reduce, 1),\n            targetKey = _this$keys$reduce2[0];\n          this.delete(targetKey);\n        }\n        this.keys.push(derivativeOption);\n      }\n      var cache = this.cache;\n      derivativeOption.forEach(function (derivative, index) {\n        if (index === derivativeOption.length - 1) {\n          cache.set(derivative, {\n            value: [value, _this.cacheCallTimes++]\n          });\n        } else {\n          var cacheValue = cache.get(derivative);\n          if (!cacheValue) {\n            cache.set(derivative, {\n              map: new Map()\n            });\n          } else if (!cacheValue.map) {\n            cacheValue.map = new Map();\n          }\n          cache = cache.get(derivative).map;\n        }\n      });\n    }\n  }, {\n    key: \"deleteByPath\",\n    value: function deleteByPath(currentCache, derivatives) {\n      var cache = currentCache.get(derivatives[0]);\n      if (derivatives.length === 1) {\n        var _cache$value;\n        if (!cache.map) {\n          currentCache.delete(derivatives[0]);\n        } else {\n          currentCache.set(derivatives[0], {\n            map: cache.map\n          });\n        }\n        return (_cache$value = cache.value) === null || _cache$value === void 0 ? void 0 : _cache$value[0];\n      }\n      var result = this.deleteByPath(cache.map, derivatives.slice(1));\n      if ((!cache.map || cache.map.size === 0) && !cache.value) {\n        currentCache.delete(derivatives[0]);\n      }\n      return result;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(derivativeOption) {\n      // If cache exists\n      if (this.has(derivativeOption)) {\n        this.keys = this.keys.filter(function (item) {\n          return !sameDerivativeOption(item, derivativeOption);\n        });\n        return this.deleteByPath(this.cache, derivativeOption);\n      }\n      return undefined;\n    }\n  }]);\n  return ThemeCache;\n}();\n_defineProperty(ThemeCache, \"MAX_CACHE_SIZE\", 20);\n_defineProperty(ThemeCache, \"MAX_CACHE_OFFSET\", 5);\nexport { ThemeCache as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAChD,IAAID,IAAI,CAACE,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IAChC,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACpC,IAAIH,IAAI,CAACG,CAAC,CAAC,KAAKF,KAAK,CAACE,CAAC,CAAC,EAAE;MACxB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG;IACpBR,eAAe,CAAC,IAAI,EAAEQ,UAAU,CAAC;IACjCN,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtCA,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrCA,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC/C,IAAI,CAACO,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,cAAc,GAAG,CAAC;EACzB;EACAX,YAAY,CAACO,UAAU,EAAE,CAAC;IACxBK,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASC,IAAIA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACJ,IAAI,CAACL,MAAM;IACzB;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASE,WAAWA,CAACC,gBAAgB,EAAE;MAC5C,IAAIC,OAAO,EAAEC,OAAO;MACpB,IAAIC,eAAe,GAAGC,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC/F,IAAIZ,KAAK,GAAG;QACVc,GAAG,EAAE,IAAI,CAACd;MACZ,CAAC;MACDQ,gBAAgB,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAE;QAC7C,IAAI,CAAChB,KAAK,EAAE;UACVA,KAAK,GAAGa,SAAS;QACnB,CAAC,MAAM;UACL,IAAII,MAAM;UACVjB,KAAK,GAAG,CAACiB,MAAM,GAAGjB,KAAK,MAAM,IAAI,IAAIiB,MAAM,KAAK,KAAK,CAAC,IAAI,CAACA,MAAM,GAAGA,MAAM,CAACH,GAAG,MAAM,IAAI,IAAIG,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;QACjJ;MACF,CAAC,CAAC;MACF,IAAI,CAACP,OAAO,GAAGT,KAAK,MAAM,IAAI,IAAIS,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACJ,KAAK,IAAIM,eAAe,EAAE;QACxFX,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACF,cAAc,EAAE;MACxC;MACA,OAAO,CAACO,OAAO,GAAGV,KAAK,MAAM,IAAI,IAAIU,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACL,KAAK;IAClF;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASa,GAAGA,CAACV,gBAAgB,EAAE;MACpC,IAAIW,iBAAiB;MACrB,OAAO,CAACA,iBAAiB,GAAG,IAAI,CAACZ,WAAW,CAACC,gBAAgB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIW,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,CAAC,CAAC;IAChJ;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASe,GAAGA,CAACZ,gBAAgB,EAAE;MACpC,OAAO,CAAC,CAAC,IAAI,CAACD,WAAW,CAACC,gBAAgB,CAAC;IAC7C;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASgB,GAAGA,CAACb,gBAAgB,EAAEH,KAAK,EAAE;MAC3C,IAAIiB,KAAK,GAAG,IAAI;MAChB;MACA,IAAI,CAAC,IAAI,CAACF,GAAG,CAACZ,gBAAgB,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACF,IAAI,CAAC,CAAC,GAAG,CAAC,GAAGP,UAAU,CAACwB,cAAc,GAAGxB,UAAU,CAACyB,gBAAgB,EAAE;UAC7E,IAAIC,iBAAiB,GAAG,IAAI,CAACvB,IAAI,CAACwB,MAAM,CAAC,UAAUC,MAAM,EAAEvB,GAAG,EAAE;cAC5D,IAAIwB,OAAO,GAAGtC,cAAc,CAACqC,MAAM,EAAE,CAAC,CAAC;gBACrCE,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC;cACxB,IAAIN,KAAK,CAACf,WAAW,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGyB,SAAS,EAAE;gBACzC,OAAO,CAACzB,GAAG,EAAEkB,KAAK,CAACf,WAAW,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC;cACA,OAAOuB,MAAM;YACf,CAAC,EAAE,CAAC,IAAI,CAACzB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;YACvC2B,kBAAkB,GAAGxC,cAAc,CAACmC,iBAAiB,EAAE,CAAC,CAAC;YACzDM,SAAS,GAAGD,kBAAkB,CAAC,CAAC,CAAC;UACnC,IAAI,CAACE,MAAM,CAACD,SAAS,CAAC;QACxB;QACA,IAAI,CAAC7B,IAAI,CAAC+B,IAAI,CAACzB,gBAAgB,CAAC;MAClC;MACA,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MACtBQ,gBAAgB,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAEkB,KAAK,EAAE;QACpD,IAAIA,KAAK,KAAK1B,gBAAgB,CAACX,MAAM,GAAG,CAAC,EAAE;UACzCG,KAAK,CAACqB,GAAG,CAACL,UAAU,EAAE;YACpBX,KAAK,EAAE,CAACA,KAAK,EAAEiB,KAAK,CAACnB,cAAc,EAAE;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAIgC,UAAU,GAAGnC,KAAK,CAACkB,GAAG,CAACF,UAAU,CAAC;UACtC,IAAI,CAACmB,UAAU,EAAE;YACfnC,KAAK,CAACqB,GAAG,CAACL,UAAU,EAAE;cACpBF,GAAG,EAAE,IAAIb,GAAG,CAAC;YACf,CAAC,CAAC;UACJ,CAAC,MAAM,IAAI,CAACkC,UAAU,CAACrB,GAAG,EAAE;YAC1BqB,UAAU,CAACrB,GAAG,GAAG,IAAIb,GAAG,CAAC,CAAC;UAC5B;UACAD,KAAK,GAAGA,KAAK,CAACkB,GAAG,CAACF,UAAU,CAAC,CAACF,GAAG;QACnC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAAS+B,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAE;MACtD,IAAItC,KAAK,GAAGqC,YAAY,CAACnB,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAIA,WAAW,CAACzC,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAI0C,YAAY;QAChB,IAAI,CAACvC,KAAK,CAACc,GAAG,EAAE;UACduB,YAAY,CAACL,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM;UACLD,YAAY,CAAChB,GAAG,CAACiB,WAAW,CAAC,CAAC,CAAC,EAAE;YAC/BxB,GAAG,EAAEd,KAAK,CAACc;UACb,CAAC,CAAC;QACJ;QACA,OAAO,CAACyB,YAAY,GAAGvC,KAAK,CAACK,KAAK,MAAM,IAAI,IAAIkC,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;MACpG;MACA,IAAIZ,MAAM,GAAG,IAAI,CAACS,YAAY,CAACpC,KAAK,CAACc,GAAG,EAAEwB,WAAW,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAI,CAAC,CAACxC,KAAK,CAACc,GAAG,IAAId,KAAK,CAACc,GAAG,CAACR,IAAI,KAAK,CAAC,KAAK,CAACN,KAAK,CAACK,KAAK,EAAE;QACxDgC,YAAY,CAACL,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC;MACrC;MACA,OAAOX,MAAM;IACf;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASoC,OAAOA,CAACjC,gBAAgB,EAAE;MACxC;MACA,IAAI,IAAI,CAACY,GAAG,CAACZ,gBAAgB,CAAC,EAAE;QAC9B,IAAI,CAACN,IAAI,GAAG,IAAI,CAACA,IAAI,CAACwC,MAAM,CAAC,UAAUC,IAAI,EAAE;UAC3C,OAAO,CAACjD,oBAAoB,CAACiD,IAAI,EAAEnC,gBAAgB,CAAC;QACtD,CAAC,CAAC;QACF,OAAO,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAACpC,KAAK,EAAEQ,gBAAgB,CAAC;MACxD;MACA,OAAOK,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EACH,OAAOd,UAAU;AACnB,CAAC,CAAC,CAAC;AACHN,eAAe,CAACM,UAAU,EAAE,gBAAgB,EAAE,EAAE,CAAC;AACjDN,eAAe,CAACM,UAAU,EAAE,kBAAkB,EAAE,CAAC,CAAC;AAClD,SAASA,UAAU,IAAI6C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
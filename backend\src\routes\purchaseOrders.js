const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const purchaseOrderController = require('../controllers/purchaseOrderController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createPurchaseOrderValidation = [
  body('supplier_id')
    .isInt({ min: 1 })
    .withMessage('供应商ID必须是正整数'),
  body('order_date')
    .isISO8601()
    .withMessage('订单日期格式不正确'),
  body('expected_delivery_date')
    .optional()
    .isISO8601()
    .withMessage('预期交货日期格式不正确'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('备注长度不能超过1000个字符')
    .trim(),
  body('items')
    .isArray({ min: 1 })
    .withMessage('订单项目不能为空'),
  body('items.*.product_id')
    .isInt({ min: 1 })
    .withMessage('产品ID必须是正整数'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('数量必须是正整数'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('单价必须是非负数'),
  body('items.*.tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('税率必须在0-100之间')
];

const updatePurchaseOrderValidation = [
  body('supplier_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('供应商ID必须是正整数'),
  body('order_date')
    .optional()
    .isISO8601()
    .withMessage('订单日期格式不正确'),
  body('expected_delivery_date')
    .optional()
    .isISO8601()
    .withMessage('预期交货日期格式不正确'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('备注长度不能超过1000个字符')
    .trim(),
  body('items')
    .optional()
    .isArray({ min: 1 })
    .withMessage('订单项目不能为空'),
  body('items.*.product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('产品ID必须是正整数'),
  body('items.*.quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('数量必须是正整数'),
  body('items.*.unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('单价必须是非负数'),
  body('items.*.tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('税率必须在0-100之间')
];

const receiveItemsValidation = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('收货项目不能为空'),
  body('items.*.item_id')
    .isInt({ min: 1 })
    .withMessage('项目ID必须是正整数'),
  body('items.*.received_quantity')
    .isInt({ min: 1 })
    .withMessage('收货数量必须是正整数'),
  body('received_date')
    .optional()
    .isISO8601()
    .withMessage('收货日期格式不正确'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('备注长度不能超过500个字符')
    .trim()
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取采购订单列表
router.get('/', 
  auth,
  paginationValidation,
  purchaseOrderController.getPurchaseOrders
);

// 获取采购订单详情
router.get('/:id', 
  auth,
  purchaseOrderController.getPurchaseOrderById
);

// 创建采购订单
router.post('/', 
  auth,
  authorize('admin', 'manager', 'employee'),
  createPurchaseOrderValidation,
  purchaseOrderController.createPurchaseOrder
);

// 更新采购订单
router.put('/:id', 
  auth,
  authorize('admin', 'manager', 'employee'),
  updatePurchaseOrderValidation,
  purchaseOrderController.updatePurchaseOrder
);

// 删除采购订单
router.delete('/:id', 
  auth,
  authorize('admin', 'manager'),
  purchaseOrderController.deletePurchaseOrder
);

// 更新采购订单状态
router.patch('/:id/status', 
  auth,
  authorize('admin', 'manager'),
  body('status').isIn(['pending', 'confirmed', 'shipped', 'received', 'cancelled']).withMessage('状态必须是: pending, confirmed, shipped, received, cancelled'),
  purchaseOrderController.updatePurchaseOrderStatus
);

// 收货
router.post('/:id/receive', 
  auth,
  authorize('admin', 'manager', 'employee'),
  receiveItemsValidation,
  purchaseOrderController.receiveItems
);

module.exports = router;

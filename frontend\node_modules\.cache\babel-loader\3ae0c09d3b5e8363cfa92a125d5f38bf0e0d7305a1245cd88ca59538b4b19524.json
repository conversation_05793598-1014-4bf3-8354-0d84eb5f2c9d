{"ast": null, "code": "\"use strict\";\n\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"animating\"];\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nimport React from \"react\";\nimport initialState from \"./initial-state\";\nimport { debounce } from \"throttle-debounce\";\nimport classnames from \"classnames\";\nimport { getOnDemandLazySlides, extractObject, initializedState, getHeight, canGoNext, slideHandler, changeSlide, keyHandler, swipeStart, swipeMove, swipeEnd, getPreClones, getPostClones, getTrackLeft, getTrackCSS } from \"./utils/innerSliderUtils\";\nimport { Track } from \"./track\";\nimport { Dots } from \"./dots\";\nimport { PrevArrow, NextArrow } from \"./arrows\";\nimport ResizeObserver from \"resize-observer-polyfill\";\nexport var InnerSlider = /*#__PURE__*/function (_React$Component) {\n  function InnerSlider(props) {\n    var _this;\n    _classCallCheck(this, InnerSlider);\n    _this = _callSuper(this, InnerSlider, [props]);\n    _defineProperty(_this, \"listRefHandler\", function (ref) {\n      return _this.list = ref;\n    });\n    _defineProperty(_this, \"trackRefHandler\", function (ref) {\n      return _this.track = ref;\n    });\n    _defineProperty(_this, \"adaptHeight\", function () {\n      if (_this.props.adaptiveHeight && _this.list) {\n        var elem = _this.list.querySelector(\"[data-index=\\\"\".concat(_this.state.currentSlide, \"\\\"]\"));\n        _this.list.style.height = getHeight(elem) + \"px\";\n      }\n    });\n    _defineProperty(_this, \"componentDidMount\", function () {\n      _this.props.onInit && _this.props.onInit();\n      if (_this.props.lazyLoad) {\n        var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, _this.props), _this.state));\n        if (slidesToLoad.length > 0) {\n          _this.setState(function (prevState) {\n            return {\n              lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n            };\n          });\n          if (_this.props.onLazyLoad) {\n            _this.props.onLazyLoad(slidesToLoad);\n          }\n        }\n      }\n      var spec = _objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props);\n      _this.updateState(spec, true, function () {\n        _this.adaptHeight();\n        _this.props.autoplay && _this.autoPlay(\"playing\");\n      });\n      if (_this.props.lazyLoad === \"progressive\") {\n        _this.lazyLoadTimer = setInterval(_this.progressiveLazyLoad, 1000);\n      }\n      _this.ro = new ResizeObserver(function () {\n        if (_this.state.animating) {\n          _this.onWindowResized(false); // don't set trackStyle hence don't break animation\n          _this.callbackTimers.push(setTimeout(function () {\n            return _this.onWindowResized();\n          }, _this.props.speed));\n        } else {\n          _this.onWindowResized();\n        }\n      });\n      _this.ro.observe(_this.list);\n      document.querySelectorAll && Array.prototype.forEach.call(document.querySelectorAll(\".slick-slide\"), function (slide) {\n        slide.onfocus = _this.props.pauseOnFocus ? _this.onSlideFocus : null;\n        slide.onblur = _this.props.pauseOnFocus ? _this.onSlideBlur : null;\n      });\n      if (window.addEventListener) {\n        window.addEventListener(\"resize\", _this.onWindowResized);\n      } else {\n        window.attachEvent(\"onresize\", _this.onWindowResized);\n      }\n    });\n    _defineProperty(_this, \"componentWillUnmount\", function () {\n      if (_this.animationEndCallback) {\n        clearTimeout(_this.animationEndCallback);\n      }\n      if (_this.lazyLoadTimer) {\n        clearInterval(_this.lazyLoadTimer);\n      }\n      if (_this.callbackTimers.length) {\n        _this.callbackTimers.forEach(function (timer) {\n          return clearTimeout(timer);\n        });\n        _this.callbackTimers = [];\n      }\n      if (window.addEventListener) {\n        window.removeEventListener(\"resize\", _this.onWindowResized);\n      } else {\n        window.detachEvent(\"onresize\", _this.onWindowResized);\n      }\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n      }\n      _this.ro.disconnect();\n    });\n    _defineProperty(_this, \"componentDidUpdate\", function (prevProps) {\n      _this.checkImagesLoad();\n      _this.props.onReInit && _this.props.onReInit();\n      if (_this.props.lazyLoad) {\n        var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, _this.props), _this.state));\n        if (slidesToLoad.length > 0) {\n          _this.setState(function (prevState) {\n            return {\n              lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n            };\n          });\n          if (_this.props.onLazyLoad) {\n            _this.props.onLazyLoad(slidesToLoad);\n          }\n        }\n      }\n      // if (this.props.onLazyLoad) {\n      //   this.props.onLazyLoad([leftMostSlide])\n      // }\n      _this.adaptHeight();\n      var spec = _objectSpread(_objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props), _this.state);\n      var setTrackStyle = _this.didPropsChange(prevProps);\n      setTrackStyle && _this.updateState(spec, setTrackStyle, function () {\n        if (_this.state.currentSlide >= React.Children.count(_this.props.children)) {\n          _this.changeSlide({\n            message: \"index\",\n            index: React.Children.count(_this.props.children) - _this.props.slidesToShow,\n            currentSlide: _this.state.currentSlide\n          });\n        }\n        if (prevProps.autoplay !== _this.props.autoplay || prevProps.autoplaySpeed !== _this.props.autoplaySpeed) {\n          if (!prevProps.autoplay && _this.props.autoplay) {\n            _this.autoPlay(\"playing\");\n          } else if (_this.props.autoplay) {\n            _this.autoPlay(\"update\");\n          } else {\n            _this.pause(\"paused\");\n          }\n        }\n      });\n    });\n    _defineProperty(_this, \"onWindowResized\", function (setTrackStyle) {\n      if (_this.debouncedResize) _this.debouncedResize.cancel();\n      _this.debouncedResize = debounce(50, function () {\n        return _this.resizeWindow(setTrackStyle);\n      });\n      _this.debouncedResize();\n    });\n    _defineProperty(_this, \"resizeWindow\", function () {\n      var setTrackStyle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var isTrackMounted = Boolean(_this.track && _this.track.node);\n      // prevent warning: setting state on unmounted component (server side rendering)\n      if (!isTrackMounted) return;\n      var spec = _objectSpread(_objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props), _this.state);\n      _this.updateState(spec, setTrackStyle, function () {\n        if (_this.props.autoplay) _this.autoPlay(\"update\");else _this.pause(\"paused\");\n      });\n      // animating state should be cleared while resizing, otherwise autoplay stops working\n      _this.setState({\n        animating: false\n      });\n      clearTimeout(_this.animationEndCallback);\n      delete _this.animationEndCallback;\n    });\n    _defineProperty(_this, \"updateState\", function (spec, setTrackStyle, callback) {\n      var updatedState = initializedState(spec);\n      spec = _objectSpread(_objectSpread(_objectSpread({}, spec), updatedState), {}, {\n        slideIndex: updatedState.currentSlide\n      });\n      var targetLeft = getTrackLeft(spec);\n      spec = _objectSpread(_objectSpread({}, spec), {}, {\n        left: targetLeft\n      });\n      var trackStyle = getTrackCSS(spec);\n      if (setTrackStyle || React.Children.count(_this.props.children) !== React.Children.count(spec.children)) {\n        updatedState[\"trackStyle\"] = trackStyle;\n      }\n      _this.setState(updatedState, callback);\n    });\n    _defineProperty(_this, \"ssrInit\", function () {\n      if (_this.props.variableWidth) {\n        var _trackWidth = 0,\n          _trackLeft = 0;\n        var childrenWidths = [];\n        var preClones = getPreClones(_objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n          slideCount: _this.props.children.length\n        }));\n        var postClones = getPostClones(_objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n          slideCount: _this.props.children.length\n        }));\n        _this.props.children.forEach(function (child) {\n          childrenWidths.push(child.props.style.width);\n          _trackWidth += child.props.style.width;\n        });\n        for (var i = 0; i < preClones; i++) {\n          _trackLeft += childrenWidths[childrenWidths.length - 1 - i];\n          _trackWidth += childrenWidths[childrenWidths.length - 1 - i];\n        }\n        for (var _i = 0; _i < postClones; _i++) {\n          _trackWidth += childrenWidths[_i];\n        }\n        for (var _i2 = 0; _i2 < _this.state.currentSlide; _i2++) {\n          _trackLeft += childrenWidths[_i2];\n        }\n        var _trackStyle = {\n          width: _trackWidth + \"px\",\n          left: -_trackLeft + \"px\"\n        };\n        if (_this.props.centerMode) {\n          var currentWidth = \"\".concat(childrenWidths[_this.state.currentSlide], \"px\");\n          _trackStyle.left = \"calc(\".concat(_trackStyle.left, \" + (100% - \").concat(currentWidth, \") / 2 ) \");\n        }\n        return {\n          trackStyle: _trackStyle\n        };\n      }\n      var childrenCount = React.Children.count(_this.props.children);\n      var spec = _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        slideCount: childrenCount\n      });\n      var slideCount = getPreClones(spec) + getPostClones(spec) + childrenCount;\n      var trackWidth = 100 / _this.props.slidesToShow * slideCount;\n      var slideWidth = 100 / slideCount;\n      var trackLeft = -slideWidth * (getPreClones(spec) + _this.state.currentSlide) * trackWidth / 100;\n      if (_this.props.centerMode) {\n        trackLeft += (100 - slideWidth * trackWidth / 100) / 2;\n      }\n      var trackStyle = {\n        width: trackWidth + \"%\",\n        left: trackLeft + \"%\"\n      };\n      return {\n        slideWidth: slideWidth + \"%\",\n        trackStyle: trackStyle\n      };\n    });\n    _defineProperty(_this, \"checkImagesLoad\", function () {\n      var images = _this.list && _this.list.querySelectorAll && _this.list.querySelectorAll(\".slick-slide img\") || [];\n      var imagesCount = images.length,\n        loadedCount = 0;\n      Array.prototype.forEach.call(images, function (image) {\n        var handler = function handler() {\n          return ++loadedCount && loadedCount >= imagesCount && _this.onWindowResized();\n        };\n        if (!image.onclick) {\n          image.onclick = function () {\n            return image.parentNode.focus();\n          };\n        } else {\n          var prevClickHandler = image.onclick;\n          image.onclick = function (e) {\n            prevClickHandler(e);\n            image.parentNode.focus();\n          };\n        }\n        if (!image.onload) {\n          if (_this.props.lazyLoad) {\n            image.onload = function () {\n              _this.adaptHeight();\n              _this.callbackTimers.push(setTimeout(_this.onWindowResized, _this.props.speed));\n            };\n          } else {\n            image.onload = handler;\n            image.onerror = function () {\n              handler();\n              _this.props.onLazyLoadError && _this.props.onLazyLoadError();\n            };\n          }\n        }\n      });\n    });\n    _defineProperty(_this, \"progressiveLazyLoad\", function () {\n      var slidesToLoad = [];\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      for (var index = _this.state.currentSlide; index < _this.state.slideCount + getPostClones(spec); index++) {\n        if (_this.state.lazyLoadedList.indexOf(index) < 0) {\n          slidesToLoad.push(index);\n          break;\n        }\n      }\n      for (var _index = _this.state.currentSlide - 1; _index >= -getPreClones(spec); _index--) {\n        if (_this.state.lazyLoadedList.indexOf(_index) < 0) {\n          slidesToLoad.push(_index);\n          break;\n        }\n      }\n      if (slidesToLoad.length > 0) {\n        _this.setState(function (state) {\n          return {\n            lazyLoadedList: state.lazyLoadedList.concat(slidesToLoad)\n          };\n        });\n        if (_this.props.onLazyLoad) {\n          _this.props.onLazyLoad(slidesToLoad);\n        }\n      } else {\n        if (_this.lazyLoadTimer) {\n          clearInterval(_this.lazyLoadTimer);\n          delete _this.lazyLoadTimer;\n        }\n      }\n    });\n    _defineProperty(_this, \"slideHandler\", function (index) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var _this$props = _this.props,\n        asNavFor = _this$props.asNavFor,\n        beforeChange = _this$props.beforeChange,\n        onLazyLoad = _this$props.onLazyLoad,\n        speed = _this$props.speed,\n        afterChange = _this$props.afterChange;\n      // capture currentslide before state is updated\n      var currentSlide = _this.state.currentSlide;\n      var _slideHandler = slideHandler(_objectSpread(_objectSpread(_objectSpread({\n          index: index\n        }, _this.props), _this.state), {}, {\n          trackRef: _this.track,\n          useCSS: _this.props.useCSS && !dontAnimate\n        })),\n        state = _slideHandler.state,\n        nextState = _slideHandler.nextState;\n      if (!state) return;\n      beforeChange && beforeChange(currentSlide, state.currentSlide);\n      var slidesToLoad = state.lazyLoadedList.filter(function (value) {\n        return _this.state.lazyLoadedList.indexOf(value) < 0;\n      });\n      onLazyLoad && slidesToLoad.length > 0 && onLazyLoad(slidesToLoad);\n      if (!_this.props.waitForAnimate && _this.animationEndCallback) {\n        clearTimeout(_this.animationEndCallback);\n        afterChange && afterChange(currentSlide);\n        delete _this.animationEndCallback;\n      }\n      _this.setState(state, function () {\n        // asNavForIndex check is to avoid recursive calls of slideHandler in waitForAnimate=false mode\n        if (asNavFor && _this.asNavForIndex !== index) {\n          _this.asNavForIndex = index;\n          asNavFor.innerSlider.slideHandler(index);\n        }\n        if (!nextState) return;\n        _this.animationEndCallback = setTimeout(function () {\n          var animating = nextState.animating,\n            firstBatch = _objectWithoutProperties(nextState, _excluded);\n          _this.setState(firstBatch, function () {\n            _this.callbackTimers.push(setTimeout(function () {\n              return _this.setState({\n                animating: animating\n              });\n            }, 10));\n            afterChange && afterChange(state.currentSlide);\n            delete _this.animationEndCallback;\n          });\n        }, speed);\n      });\n    });\n    _defineProperty(_this, \"changeSlide\", function (options) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      var targetSlide = changeSlide(spec, options);\n      if (targetSlide !== 0 && !targetSlide) return;\n      if (dontAnimate === true) {\n        _this.slideHandler(targetSlide, dontAnimate);\n      } else {\n        _this.slideHandler(targetSlide);\n      }\n      _this.props.autoplay && _this.autoPlay(\"update\");\n      if (_this.props.focusOnSelect) {\n        var nodes = _this.list.querySelectorAll(\".slick-current\");\n        nodes[0] && nodes[0].focus();\n      }\n    });\n    _defineProperty(_this, \"clickHandler\", function (e) {\n      if (_this.clickable === false) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n      _this.clickable = true;\n    });\n    _defineProperty(_this, \"keyHandler\", function (e) {\n      var dir = keyHandler(e, _this.props.accessibility, _this.props.rtl);\n      dir !== \"\" && _this.changeSlide({\n        message: dir\n      });\n    });\n    _defineProperty(_this, \"selectHandler\", function (options) {\n      _this.changeSlide(options);\n    });\n    _defineProperty(_this, \"disableBodyScroll\", function () {\n      var preventDefault = function preventDefault(e) {\n        e = e || window.event;\n        if (e.preventDefault) e.preventDefault();\n        e.returnValue = false;\n      };\n      window.ontouchmove = preventDefault;\n    });\n    _defineProperty(_this, \"enableBodyScroll\", function () {\n      window.ontouchmove = null;\n    });\n    _defineProperty(_this, \"swipeStart\", function (e) {\n      if (_this.props.verticalSwiping) {\n        _this.disableBodyScroll();\n      }\n      var state = swipeStart(e, _this.props.swipe, _this.props.draggable);\n      state !== \"\" && _this.setState(state);\n    });\n    _defineProperty(_this, \"swipeMove\", function (e) {\n      var state = swipeMove(e, _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        trackRef: _this.track,\n        listRef: _this.list,\n        slideIndex: _this.state.currentSlide\n      }));\n      if (!state) return;\n      if (state[\"swiping\"]) {\n        _this.clickable = false;\n      }\n      _this.setState(state);\n    });\n    _defineProperty(_this, \"swipeEnd\", function (e) {\n      var state = swipeEnd(e, _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        trackRef: _this.track,\n        listRef: _this.list,\n        slideIndex: _this.state.currentSlide\n      }));\n      if (!state) return;\n      var triggerSlideHandler = state[\"triggerSlideHandler\"];\n      delete state[\"triggerSlideHandler\"];\n      _this.setState(state);\n      if (triggerSlideHandler === undefined) return;\n      _this.slideHandler(triggerSlideHandler);\n      if (_this.props.verticalSwiping) {\n        _this.enableBodyScroll();\n      }\n    });\n    _defineProperty(_this, \"touchEnd\", function (e) {\n      _this.swipeEnd(e);\n      _this.clickable = true;\n    });\n    _defineProperty(_this, \"slickPrev\", function () {\n      // this and fellow methods are wrapped in setTimeout\n      // to make sure initialize setState has happened before\n      // any of such methods are called\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"previous\"\n        });\n      }, 0));\n    });\n    _defineProperty(_this, \"slickNext\", function () {\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"next\"\n        });\n      }, 0));\n    });\n    _defineProperty(_this, \"slickGoTo\", function (slide) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      slide = Number(slide);\n      if (isNaN(slide)) return \"\";\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"index\",\n          index: slide,\n          currentSlide: _this.state.currentSlide\n        }, dontAnimate);\n      }, 0));\n    });\n    _defineProperty(_this, \"play\", function () {\n      var nextIndex;\n      if (_this.props.rtl) {\n        nextIndex = _this.state.currentSlide - _this.props.slidesToScroll;\n      } else {\n        if (canGoNext(_objectSpread(_objectSpread({}, _this.props), _this.state))) {\n          nextIndex = _this.state.currentSlide + _this.props.slidesToScroll;\n        } else {\n          return false;\n        }\n      }\n      _this.slideHandler(nextIndex);\n    });\n    _defineProperty(_this, \"autoPlay\", function (playType) {\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n      }\n      var autoplaying = _this.state.autoplaying;\n      if (playType === \"update\") {\n        if (autoplaying === \"hovered\" || autoplaying === \"focused\" || autoplaying === \"paused\") {\n          return;\n        }\n      } else if (playType === \"leave\") {\n        if (autoplaying === \"paused\" || autoplaying === \"focused\") {\n          return;\n        }\n      } else if (playType === \"blur\") {\n        if (autoplaying === \"paused\" || autoplaying === \"hovered\") {\n          return;\n        }\n      }\n      _this.autoplayTimer = setInterval(_this.play, _this.props.autoplaySpeed + 50);\n      _this.setState({\n        autoplaying: \"playing\"\n      });\n    });\n    _defineProperty(_this, \"pause\", function (pauseType) {\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n        _this.autoplayTimer = null;\n      }\n      var autoplaying = _this.state.autoplaying;\n      if (pauseType === \"paused\") {\n        _this.setState({\n          autoplaying: \"paused\"\n        });\n      } else if (pauseType === \"focused\") {\n        if (autoplaying === \"hovered\" || autoplaying === \"playing\") {\n          _this.setState({\n            autoplaying: \"focused\"\n          });\n        }\n      } else {\n        // pauseType  is 'hovered'\n        if (autoplaying === \"playing\") {\n          _this.setState({\n            autoplaying: \"hovered\"\n          });\n        }\n      }\n    });\n    _defineProperty(_this, \"onDotsOver\", function () {\n      return _this.props.autoplay && _this.pause(\"hovered\");\n    });\n    _defineProperty(_this, \"onDotsLeave\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"hovered\" && _this.autoPlay(\"leave\");\n    });\n    _defineProperty(_this, \"onTrackOver\", function () {\n      return _this.props.autoplay && _this.pause(\"hovered\");\n    });\n    _defineProperty(_this, \"onTrackLeave\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"hovered\" && _this.autoPlay(\"leave\");\n    });\n    _defineProperty(_this, \"onSlideFocus\", function () {\n      return _this.props.autoplay && _this.pause(\"focused\");\n    });\n    _defineProperty(_this, \"onSlideBlur\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"focused\" && _this.autoPlay(\"blur\");\n    });\n    _defineProperty(_this, \"render\", function () {\n      var className = classnames(\"slick-slider\", _this.props.className, {\n        \"slick-vertical\": _this.props.vertical,\n        \"slick-initialized\": true\n      });\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      var trackProps = extractObject(spec, [\"fade\", \"cssEase\", \"speed\", \"infinite\", \"centerMode\", \"focusOnSelect\", \"currentSlide\", \"lazyLoad\", \"lazyLoadedList\", \"rtl\", \"slideWidth\", \"slideHeight\", \"listHeight\", \"vertical\", \"slidesToShow\", \"slidesToScroll\", \"slideCount\", \"trackStyle\", \"variableWidth\", \"unslick\", \"centerPadding\", \"targetSlide\", \"useCSS\"]);\n      var pauseOnHover = _this.props.pauseOnHover;\n      trackProps = _objectSpread(_objectSpread({}, trackProps), {}, {\n        onMouseEnter: pauseOnHover ? _this.onTrackOver : null,\n        onMouseLeave: pauseOnHover ? _this.onTrackLeave : null,\n        onMouseOver: pauseOnHover ? _this.onTrackOver : null,\n        focusOnSelect: _this.props.focusOnSelect && _this.clickable ? _this.selectHandler : null\n      });\n      var dots;\n      if (_this.props.dots === true && _this.state.slideCount >= _this.props.slidesToShow) {\n        var dotProps = extractObject(spec, [\"dotsClass\", \"slideCount\", \"slidesToShow\", \"currentSlide\", \"slidesToScroll\", \"clickHandler\", \"children\", \"customPaging\", \"infinite\", \"appendDots\"]);\n        var pauseOnDotsHover = _this.props.pauseOnDotsHover;\n        dotProps = _objectSpread(_objectSpread({}, dotProps), {}, {\n          clickHandler: _this.changeSlide,\n          onMouseEnter: pauseOnDotsHover ? _this.onDotsLeave : null,\n          onMouseOver: pauseOnDotsHover ? _this.onDotsOver : null,\n          onMouseLeave: pauseOnDotsHover ? _this.onDotsLeave : null\n        });\n        dots = /*#__PURE__*/React.createElement(Dots, dotProps);\n      }\n      var prevArrow, nextArrow;\n      var arrowProps = extractObject(spec, [\"infinite\", \"centerMode\", \"currentSlide\", \"slideCount\", \"slidesToShow\", \"prevArrow\", \"nextArrow\"]);\n      arrowProps.clickHandler = _this.changeSlide;\n      if (_this.props.arrows) {\n        prevArrow = /*#__PURE__*/React.createElement(PrevArrow, arrowProps);\n        nextArrow = /*#__PURE__*/React.createElement(NextArrow, arrowProps);\n      }\n      var verticalHeightStyle = null;\n      if (_this.props.vertical) {\n        verticalHeightStyle = {\n          height: _this.state.listHeight\n        };\n      }\n      var centerPaddingStyle = null;\n      if (_this.props.vertical === false) {\n        if (_this.props.centerMode === true) {\n          centerPaddingStyle = {\n            padding: \"0px \" + _this.props.centerPadding\n          };\n        }\n      } else {\n        if (_this.props.centerMode === true) {\n          centerPaddingStyle = {\n            padding: _this.props.centerPadding + \" 0px\"\n          };\n        }\n      }\n      var listStyle = _objectSpread(_objectSpread({}, verticalHeightStyle), centerPaddingStyle);\n      var touchMove = _this.props.touchMove;\n      var listProps = {\n        className: \"slick-list\",\n        style: listStyle,\n        onClick: _this.clickHandler,\n        onMouseDown: touchMove ? _this.swipeStart : null,\n        onMouseMove: _this.state.dragging && touchMove ? _this.swipeMove : null,\n        onMouseUp: touchMove ? _this.swipeEnd : null,\n        onMouseLeave: _this.state.dragging && touchMove ? _this.swipeEnd : null,\n        onTouchStart: touchMove ? _this.swipeStart : null,\n        onTouchMove: _this.state.dragging && touchMove ? _this.swipeMove : null,\n        onTouchEnd: touchMove ? _this.touchEnd : null,\n        onTouchCancel: _this.state.dragging && touchMove ? _this.swipeEnd : null,\n        onKeyDown: _this.props.accessibility ? _this.keyHandler : null\n      };\n      var innerSliderProps = {\n        className: className,\n        dir: \"ltr\",\n        style: _this.props.style\n      };\n      if (_this.props.unslick) {\n        listProps = {\n          className: \"slick-list\"\n        };\n        innerSliderProps = {\n          className: className,\n          style: _this.props.style\n        };\n      }\n      return /*#__PURE__*/React.createElement(\"div\", innerSliderProps, !_this.props.unslick ? prevArrow : \"\", /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: _this.listRefHandler\n      }, listProps), /*#__PURE__*/React.createElement(Track, _extends({\n        ref: _this.trackRefHandler\n      }, trackProps), _this.props.children)), !_this.props.unslick ? nextArrow : \"\", !_this.props.unslick ? dots : \"\");\n    });\n    _this.list = null;\n    _this.track = null;\n    _this.state = _objectSpread(_objectSpread({}, initialState), {}, {\n      currentSlide: _this.props.initialSlide,\n      targetSlide: _this.props.initialSlide ? _this.props.initialSlide : 0,\n      slideCount: React.Children.count(_this.props.children)\n    });\n    _this.callbackTimers = [];\n    _this.clickable = true;\n    _this.debouncedResize = null;\n    var ssrState = _this.ssrInit();\n    _this.state = _objectSpread(_objectSpread({}, _this.state), ssrState);\n    return _this;\n  }\n  _inherits(InnerSlider, _React$Component);\n  return _createClass(InnerSlider, [{\n    key: \"didPropsChange\",\n    value: function didPropsChange(prevProps) {\n      var setTrackStyle = false;\n      for (var _i3 = 0, _Object$keys = Object.keys(this.props); _i3 < _Object$keys.length; _i3++) {\n        var key = _Object$keys[_i3];\n        // eslint-disable-next-line no-prototype-builtins\n        if (!prevProps.hasOwnProperty(key)) {\n          setTrackStyle = true;\n          break;\n        }\n        if (_typeof(prevProps[key]) === \"object\" || typeof prevProps[key] === \"function\" || isNaN(prevProps[key])) {\n          continue;\n        }\n        if (prevProps[key] !== this.props[key]) {\n          setTrackStyle = true;\n          break;\n        }\n      }\n      return setTrackStyle || React.Children.count(this.props.children) !== React.Children.count(prevProps.children);\n    }\n  }]);\n}(React.Component);", "map": {"version": 3, "names": ["_typeof", "_extends", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_isNativeReflectConstruct", "_getPrototypeOf", "_inherits", "_defineProperty", "_excluded", "_callSuper", "t", "o", "e", "Reflect", "construct", "constructor", "apply", "React", "initialState", "debounce", "classnames", "getOnDemandLazySlides", "extractObject", "initializedState", "getHeight", "canGoNext", "<PERSON><PERSON><PERSON><PERSON>", "changeSlide", "<PERSON><PERSON><PERSON><PERSON>", "swipeStart", "swipeMove", "swipeEnd", "getPreClones", "getPostClones", "getTrackLeft", "getTrackCSS", "Track", "Dots", "PrevArrow", "NextArrow", "ResizeObserver", "InnerSlider", "_React$Component", "props", "_this", "ref", "list", "track", "adaptiveHeight", "elem", "querySelector", "concat", "state", "currentSlide", "style", "height", "onInit", "lazyLoad", "slidesToLoad", "length", "setState", "prevState", "lazyLoadedList", "onLazyLoad", "spec", "listRef", "trackRef", "updateState", "adaptHeight", "autoplay", "autoPlay", "lazyLoadTimer", "setInterval", "progressiveLazyLoad", "ro", "animating", "onWindowResized", "callbackTimers", "push", "setTimeout", "speed", "observe", "document", "querySelectorAll", "Array", "prototype", "for<PERSON>ach", "call", "slide", "onfocus", "pauseOnFocus", "onSlideFocus", "onblur", "onSlideBlur", "window", "addEventListener", "attachEvent", "animationEndCallback", "clearTimeout", "clearInterval", "timer", "removeEventListener", "detachEvent", "autoplayTimer", "disconnect", "prevProps", "checkImagesLoad", "onReInit", "setTrackStyle", "didPropsChange", "Children", "count", "children", "message", "index", "slidesToShow", "autoplaySpeed", "pause", "debouncedResize", "cancel", "resizeWindow", "arguments", "undefined", "isTrackMounted", "Boolean", "node", "callback", "updatedState", "slideIndex", "targetLeft", "left", "trackStyle", "variableWidth", "_trackWidth", "_trackLeft", "childrenWidths", "preClones", "slideCount", "postClones", "child", "width", "i", "_i", "_i2", "_trackStyle", "centerMode", "currentWidth", "childrenCount", "trackWidth", "slideWidth", "trackLeft", "images", "imagesCount", "loadedCount", "image", "handler", "onclick", "parentNode", "focus", "prevClickHandler", "onload", "onerror", "onLazyLoadError", "indexOf", "_index", "dontAnimate", "_this$props", "asNavFor", "beforeChange", "afterChange", "_<PERSON><PERSON><PERSON><PERSON>", "useCSS", "nextState", "filter", "value", "waitForAnimate", "asNavForIndex", "innerSlider", "firstBatch", "options", "targetSlide", "focusOnSelect", "nodes", "clickable", "stopPropagation", "preventDefault", "dir", "accessibility", "rtl", "event", "returnValue", "ontouchmove", "verticalSwiping", "disableBodyScroll", "swipe", "draggable", "triggerSlideHandler", "enableBodyScroll", "Number", "isNaN", "nextIndex", "slidesToScroll", "playType", "autoplaying", "play", "pauseType", "className", "vertical", "trackProps", "pauseOnHover", "onMouseEnter", "onTrackOver", "onMouseLeave", "onTrackLeave", "onMouseOver", "<PERSON><PERSON><PERSON><PERSON>", "dots", "dotProps", "pauseOnDotsHover", "clickHandler", "onDotsLeave", "onDotsOver", "createElement", "prevArrow", "nextArrow", "arrowProps", "arrows", "verticalHeightStyle", "listHeight", "centerPaddingStyle", "padding", "centerPadding", "listStyle", "touchMove", "listProps", "onClick", "onMouseDown", "onMouseMove", "dragging", "onMouseUp", "onTouchStart", "onTouchMove", "onTouchEnd", "touchEnd", "onTouchCancel", "onKeyDown", "innerSliderProps", "unslick", "listRefHandler", "trackRefHandler", "initialSlide", "ssrState", "ssrInit", "key", "_i3", "_Object$keys", "Object", "keys", "hasOwnProperty", "Component"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+react-slick@1.1.2_react@18.3.1/node_modules/@ant-design/react-slick/es/inner-slider.js"], "sourcesContent": ["\"use strict\";\n\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"animating\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nimport React from \"react\";\nimport initialState from \"./initial-state\";\nimport { debounce } from \"throttle-debounce\";\nimport classnames from \"classnames\";\nimport { getOnDemandLazySlides, extractObject, initializedState, getHeight, canGoNext, slideHandler, changeSlide, keyHandler, swipeStart, swipeMove, swipeEnd, getPreClones, getPostClones, getTrackLeft, getTrackCSS } from \"./utils/innerSliderUtils\";\nimport { Track } from \"./track\";\nimport { Dots } from \"./dots\";\nimport { PrevArrow, NextArrow } from \"./arrows\";\nimport ResizeObserver from \"resize-observer-polyfill\";\nexport var InnerSlider = /*#__PURE__*/function (_React$Component) {\n  function InnerSlider(props) {\n    var _this;\n    _classCallCheck(this, InnerSlider);\n    _this = _callSuper(this, InnerSlider, [props]);\n    _defineProperty(_this, \"listRefHandler\", function (ref) {\n      return _this.list = ref;\n    });\n    _defineProperty(_this, \"trackRefHandler\", function (ref) {\n      return _this.track = ref;\n    });\n    _defineProperty(_this, \"adaptHeight\", function () {\n      if (_this.props.adaptiveHeight && _this.list) {\n        var elem = _this.list.querySelector(\"[data-index=\\\"\".concat(_this.state.currentSlide, \"\\\"]\"));\n        _this.list.style.height = getHeight(elem) + \"px\";\n      }\n    });\n    _defineProperty(_this, \"componentDidMount\", function () {\n      _this.props.onInit && _this.props.onInit();\n      if (_this.props.lazyLoad) {\n        var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, _this.props), _this.state));\n        if (slidesToLoad.length > 0) {\n          _this.setState(function (prevState) {\n            return {\n              lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n            };\n          });\n          if (_this.props.onLazyLoad) {\n            _this.props.onLazyLoad(slidesToLoad);\n          }\n        }\n      }\n      var spec = _objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props);\n      _this.updateState(spec, true, function () {\n        _this.adaptHeight();\n        _this.props.autoplay && _this.autoPlay(\"playing\");\n      });\n      if (_this.props.lazyLoad === \"progressive\") {\n        _this.lazyLoadTimer = setInterval(_this.progressiveLazyLoad, 1000);\n      }\n      _this.ro = new ResizeObserver(function () {\n        if (_this.state.animating) {\n          _this.onWindowResized(false); // don't set trackStyle hence don't break animation\n          _this.callbackTimers.push(setTimeout(function () {\n            return _this.onWindowResized();\n          }, _this.props.speed));\n        } else {\n          _this.onWindowResized();\n        }\n      });\n      _this.ro.observe(_this.list);\n      document.querySelectorAll && Array.prototype.forEach.call(document.querySelectorAll(\".slick-slide\"), function (slide) {\n        slide.onfocus = _this.props.pauseOnFocus ? _this.onSlideFocus : null;\n        slide.onblur = _this.props.pauseOnFocus ? _this.onSlideBlur : null;\n      });\n      if (window.addEventListener) {\n        window.addEventListener(\"resize\", _this.onWindowResized);\n      } else {\n        window.attachEvent(\"onresize\", _this.onWindowResized);\n      }\n    });\n    _defineProperty(_this, \"componentWillUnmount\", function () {\n      if (_this.animationEndCallback) {\n        clearTimeout(_this.animationEndCallback);\n      }\n      if (_this.lazyLoadTimer) {\n        clearInterval(_this.lazyLoadTimer);\n      }\n      if (_this.callbackTimers.length) {\n        _this.callbackTimers.forEach(function (timer) {\n          return clearTimeout(timer);\n        });\n        _this.callbackTimers = [];\n      }\n      if (window.addEventListener) {\n        window.removeEventListener(\"resize\", _this.onWindowResized);\n      } else {\n        window.detachEvent(\"onresize\", _this.onWindowResized);\n      }\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n      }\n      _this.ro.disconnect();\n    });\n    _defineProperty(_this, \"componentDidUpdate\", function (prevProps) {\n      _this.checkImagesLoad();\n      _this.props.onReInit && _this.props.onReInit();\n      if (_this.props.lazyLoad) {\n        var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, _this.props), _this.state));\n        if (slidesToLoad.length > 0) {\n          _this.setState(function (prevState) {\n            return {\n              lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n            };\n          });\n          if (_this.props.onLazyLoad) {\n            _this.props.onLazyLoad(slidesToLoad);\n          }\n        }\n      }\n      // if (this.props.onLazyLoad) {\n      //   this.props.onLazyLoad([leftMostSlide])\n      // }\n      _this.adaptHeight();\n      var spec = _objectSpread(_objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props), _this.state);\n      var setTrackStyle = _this.didPropsChange(prevProps);\n      setTrackStyle && _this.updateState(spec, setTrackStyle, function () {\n        if (_this.state.currentSlide >= React.Children.count(_this.props.children)) {\n          _this.changeSlide({\n            message: \"index\",\n            index: React.Children.count(_this.props.children) - _this.props.slidesToShow,\n            currentSlide: _this.state.currentSlide\n          });\n        }\n        if (prevProps.autoplay !== _this.props.autoplay || prevProps.autoplaySpeed !== _this.props.autoplaySpeed) {\n          if (!prevProps.autoplay && _this.props.autoplay) {\n            _this.autoPlay(\"playing\");\n          } else if (_this.props.autoplay) {\n            _this.autoPlay(\"update\");\n          } else {\n            _this.pause(\"paused\");\n          }\n        }\n      });\n    });\n    _defineProperty(_this, \"onWindowResized\", function (setTrackStyle) {\n      if (_this.debouncedResize) _this.debouncedResize.cancel();\n      _this.debouncedResize = debounce(50, function () {\n        return _this.resizeWindow(setTrackStyle);\n      });\n      _this.debouncedResize();\n    });\n    _defineProperty(_this, \"resizeWindow\", function () {\n      var setTrackStyle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var isTrackMounted = Boolean(_this.track && _this.track.node);\n      // prevent warning: setting state on unmounted component (server side rendering)\n      if (!isTrackMounted) return;\n      var spec = _objectSpread(_objectSpread({\n        listRef: _this.list,\n        trackRef: _this.track\n      }, _this.props), _this.state);\n      _this.updateState(spec, setTrackStyle, function () {\n        if (_this.props.autoplay) _this.autoPlay(\"update\");else _this.pause(\"paused\");\n      });\n      // animating state should be cleared while resizing, otherwise autoplay stops working\n      _this.setState({\n        animating: false\n      });\n      clearTimeout(_this.animationEndCallback);\n      delete _this.animationEndCallback;\n    });\n    _defineProperty(_this, \"updateState\", function (spec, setTrackStyle, callback) {\n      var updatedState = initializedState(spec);\n      spec = _objectSpread(_objectSpread(_objectSpread({}, spec), updatedState), {}, {\n        slideIndex: updatedState.currentSlide\n      });\n      var targetLeft = getTrackLeft(spec);\n      spec = _objectSpread(_objectSpread({}, spec), {}, {\n        left: targetLeft\n      });\n      var trackStyle = getTrackCSS(spec);\n      if (setTrackStyle || React.Children.count(_this.props.children) !== React.Children.count(spec.children)) {\n        updatedState[\"trackStyle\"] = trackStyle;\n      }\n      _this.setState(updatedState, callback);\n    });\n    _defineProperty(_this, \"ssrInit\", function () {\n      if (_this.props.variableWidth) {\n        var _trackWidth = 0,\n          _trackLeft = 0;\n        var childrenWidths = [];\n        var preClones = getPreClones(_objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n          slideCount: _this.props.children.length\n        }));\n        var postClones = getPostClones(_objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n          slideCount: _this.props.children.length\n        }));\n        _this.props.children.forEach(function (child) {\n          childrenWidths.push(child.props.style.width);\n          _trackWidth += child.props.style.width;\n        });\n        for (var i = 0; i < preClones; i++) {\n          _trackLeft += childrenWidths[childrenWidths.length - 1 - i];\n          _trackWidth += childrenWidths[childrenWidths.length - 1 - i];\n        }\n        for (var _i = 0; _i < postClones; _i++) {\n          _trackWidth += childrenWidths[_i];\n        }\n        for (var _i2 = 0; _i2 < _this.state.currentSlide; _i2++) {\n          _trackLeft += childrenWidths[_i2];\n        }\n        var _trackStyle = {\n          width: _trackWidth + \"px\",\n          left: -_trackLeft + \"px\"\n        };\n        if (_this.props.centerMode) {\n          var currentWidth = \"\".concat(childrenWidths[_this.state.currentSlide], \"px\");\n          _trackStyle.left = \"calc(\".concat(_trackStyle.left, \" + (100% - \").concat(currentWidth, \") / 2 ) \");\n        }\n        return {\n          trackStyle: _trackStyle\n        };\n      }\n      var childrenCount = React.Children.count(_this.props.children);\n      var spec = _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        slideCount: childrenCount\n      });\n      var slideCount = getPreClones(spec) + getPostClones(spec) + childrenCount;\n      var trackWidth = 100 / _this.props.slidesToShow * slideCount;\n      var slideWidth = 100 / slideCount;\n      var trackLeft = -slideWidth * (getPreClones(spec) + _this.state.currentSlide) * trackWidth / 100;\n      if (_this.props.centerMode) {\n        trackLeft += (100 - slideWidth * trackWidth / 100) / 2;\n      }\n      var trackStyle = {\n        width: trackWidth + \"%\",\n        left: trackLeft + \"%\"\n      };\n      return {\n        slideWidth: slideWidth + \"%\",\n        trackStyle: trackStyle\n      };\n    });\n    _defineProperty(_this, \"checkImagesLoad\", function () {\n      var images = _this.list && _this.list.querySelectorAll && _this.list.querySelectorAll(\".slick-slide img\") || [];\n      var imagesCount = images.length,\n        loadedCount = 0;\n      Array.prototype.forEach.call(images, function (image) {\n        var handler = function handler() {\n          return ++loadedCount && loadedCount >= imagesCount && _this.onWindowResized();\n        };\n        if (!image.onclick) {\n          image.onclick = function () {\n            return image.parentNode.focus();\n          };\n        } else {\n          var prevClickHandler = image.onclick;\n          image.onclick = function (e) {\n            prevClickHandler(e);\n            image.parentNode.focus();\n          };\n        }\n        if (!image.onload) {\n          if (_this.props.lazyLoad) {\n            image.onload = function () {\n              _this.adaptHeight();\n              _this.callbackTimers.push(setTimeout(_this.onWindowResized, _this.props.speed));\n            };\n          } else {\n            image.onload = handler;\n            image.onerror = function () {\n              handler();\n              _this.props.onLazyLoadError && _this.props.onLazyLoadError();\n            };\n          }\n        }\n      });\n    });\n    _defineProperty(_this, \"progressiveLazyLoad\", function () {\n      var slidesToLoad = [];\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      for (var index = _this.state.currentSlide; index < _this.state.slideCount + getPostClones(spec); index++) {\n        if (_this.state.lazyLoadedList.indexOf(index) < 0) {\n          slidesToLoad.push(index);\n          break;\n        }\n      }\n      for (var _index = _this.state.currentSlide - 1; _index >= -getPreClones(spec); _index--) {\n        if (_this.state.lazyLoadedList.indexOf(_index) < 0) {\n          slidesToLoad.push(_index);\n          break;\n        }\n      }\n      if (slidesToLoad.length > 0) {\n        _this.setState(function (state) {\n          return {\n            lazyLoadedList: state.lazyLoadedList.concat(slidesToLoad)\n          };\n        });\n        if (_this.props.onLazyLoad) {\n          _this.props.onLazyLoad(slidesToLoad);\n        }\n      } else {\n        if (_this.lazyLoadTimer) {\n          clearInterval(_this.lazyLoadTimer);\n          delete _this.lazyLoadTimer;\n        }\n      }\n    });\n    _defineProperty(_this, \"slideHandler\", function (index) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var _this$props = _this.props,\n        asNavFor = _this$props.asNavFor,\n        beforeChange = _this$props.beforeChange,\n        onLazyLoad = _this$props.onLazyLoad,\n        speed = _this$props.speed,\n        afterChange = _this$props.afterChange;\n      // capture currentslide before state is updated\n      var currentSlide = _this.state.currentSlide;\n      var _slideHandler = slideHandler(_objectSpread(_objectSpread(_objectSpread({\n          index: index\n        }, _this.props), _this.state), {}, {\n          trackRef: _this.track,\n          useCSS: _this.props.useCSS && !dontAnimate\n        })),\n        state = _slideHandler.state,\n        nextState = _slideHandler.nextState;\n      if (!state) return;\n      beforeChange && beforeChange(currentSlide, state.currentSlide);\n      var slidesToLoad = state.lazyLoadedList.filter(function (value) {\n        return _this.state.lazyLoadedList.indexOf(value) < 0;\n      });\n      onLazyLoad && slidesToLoad.length > 0 && onLazyLoad(slidesToLoad);\n      if (!_this.props.waitForAnimate && _this.animationEndCallback) {\n        clearTimeout(_this.animationEndCallback);\n        afterChange && afterChange(currentSlide);\n        delete _this.animationEndCallback;\n      }\n      _this.setState(state, function () {\n        // asNavForIndex check is to avoid recursive calls of slideHandler in waitForAnimate=false mode\n        if (asNavFor && _this.asNavForIndex !== index) {\n          _this.asNavForIndex = index;\n          asNavFor.innerSlider.slideHandler(index);\n        }\n        if (!nextState) return;\n        _this.animationEndCallback = setTimeout(function () {\n          var animating = nextState.animating,\n            firstBatch = _objectWithoutProperties(nextState, _excluded);\n          _this.setState(firstBatch, function () {\n            _this.callbackTimers.push(setTimeout(function () {\n              return _this.setState({\n                animating: animating\n              });\n            }, 10));\n            afterChange && afterChange(state.currentSlide);\n            delete _this.animationEndCallback;\n          });\n        }, speed);\n      });\n    });\n    _defineProperty(_this, \"changeSlide\", function (options) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      var targetSlide = changeSlide(spec, options);\n      if (targetSlide !== 0 && !targetSlide) return;\n      if (dontAnimate === true) {\n        _this.slideHandler(targetSlide, dontAnimate);\n      } else {\n        _this.slideHandler(targetSlide);\n      }\n      _this.props.autoplay && _this.autoPlay(\"update\");\n      if (_this.props.focusOnSelect) {\n        var nodes = _this.list.querySelectorAll(\".slick-current\");\n        nodes[0] && nodes[0].focus();\n      }\n    });\n    _defineProperty(_this, \"clickHandler\", function (e) {\n      if (_this.clickable === false) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n      _this.clickable = true;\n    });\n    _defineProperty(_this, \"keyHandler\", function (e) {\n      var dir = keyHandler(e, _this.props.accessibility, _this.props.rtl);\n      dir !== \"\" && _this.changeSlide({\n        message: dir\n      });\n    });\n    _defineProperty(_this, \"selectHandler\", function (options) {\n      _this.changeSlide(options);\n    });\n    _defineProperty(_this, \"disableBodyScroll\", function () {\n      var preventDefault = function preventDefault(e) {\n        e = e || window.event;\n        if (e.preventDefault) e.preventDefault();\n        e.returnValue = false;\n      };\n      window.ontouchmove = preventDefault;\n    });\n    _defineProperty(_this, \"enableBodyScroll\", function () {\n      window.ontouchmove = null;\n    });\n    _defineProperty(_this, \"swipeStart\", function (e) {\n      if (_this.props.verticalSwiping) {\n        _this.disableBodyScroll();\n      }\n      var state = swipeStart(e, _this.props.swipe, _this.props.draggable);\n      state !== \"\" && _this.setState(state);\n    });\n    _defineProperty(_this, \"swipeMove\", function (e) {\n      var state = swipeMove(e, _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        trackRef: _this.track,\n        listRef: _this.list,\n        slideIndex: _this.state.currentSlide\n      }));\n      if (!state) return;\n      if (state[\"swiping\"]) {\n        _this.clickable = false;\n      }\n      _this.setState(state);\n    });\n    _defineProperty(_this, \"swipeEnd\", function (e) {\n      var state = swipeEnd(e, _objectSpread(_objectSpread(_objectSpread({}, _this.props), _this.state), {}, {\n        trackRef: _this.track,\n        listRef: _this.list,\n        slideIndex: _this.state.currentSlide\n      }));\n      if (!state) return;\n      var triggerSlideHandler = state[\"triggerSlideHandler\"];\n      delete state[\"triggerSlideHandler\"];\n      _this.setState(state);\n      if (triggerSlideHandler === undefined) return;\n      _this.slideHandler(triggerSlideHandler);\n      if (_this.props.verticalSwiping) {\n        _this.enableBodyScroll();\n      }\n    });\n    _defineProperty(_this, \"touchEnd\", function (e) {\n      _this.swipeEnd(e);\n      _this.clickable = true;\n    });\n    _defineProperty(_this, \"slickPrev\", function () {\n      // this and fellow methods are wrapped in setTimeout\n      // to make sure initialize setState has happened before\n      // any of such methods are called\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"previous\"\n        });\n      }, 0));\n    });\n    _defineProperty(_this, \"slickNext\", function () {\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"next\"\n        });\n      }, 0));\n    });\n    _defineProperty(_this, \"slickGoTo\", function (slide) {\n      var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      slide = Number(slide);\n      if (isNaN(slide)) return \"\";\n      _this.callbackTimers.push(setTimeout(function () {\n        return _this.changeSlide({\n          message: \"index\",\n          index: slide,\n          currentSlide: _this.state.currentSlide\n        }, dontAnimate);\n      }, 0));\n    });\n    _defineProperty(_this, \"play\", function () {\n      var nextIndex;\n      if (_this.props.rtl) {\n        nextIndex = _this.state.currentSlide - _this.props.slidesToScroll;\n      } else {\n        if (canGoNext(_objectSpread(_objectSpread({}, _this.props), _this.state))) {\n          nextIndex = _this.state.currentSlide + _this.props.slidesToScroll;\n        } else {\n          return false;\n        }\n      }\n      _this.slideHandler(nextIndex);\n    });\n    _defineProperty(_this, \"autoPlay\", function (playType) {\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n      }\n      var autoplaying = _this.state.autoplaying;\n      if (playType === \"update\") {\n        if (autoplaying === \"hovered\" || autoplaying === \"focused\" || autoplaying === \"paused\") {\n          return;\n        }\n      } else if (playType === \"leave\") {\n        if (autoplaying === \"paused\" || autoplaying === \"focused\") {\n          return;\n        }\n      } else if (playType === \"blur\") {\n        if (autoplaying === \"paused\" || autoplaying === \"hovered\") {\n          return;\n        }\n      }\n      _this.autoplayTimer = setInterval(_this.play, _this.props.autoplaySpeed + 50);\n      _this.setState({\n        autoplaying: \"playing\"\n      });\n    });\n    _defineProperty(_this, \"pause\", function (pauseType) {\n      if (_this.autoplayTimer) {\n        clearInterval(_this.autoplayTimer);\n        _this.autoplayTimer = null;\n      }\n      var autoplaying = _this.state.autoplaying;\n      if (pauseType === \"paused\") {\n        _this.setState({\n          autoplaying: \"paused\"\n        });\n      } else if (pauseType === \"focused\") {\n        if (autoplaying === \"hovered\" || autoplaying === \"playing\") {\n          _this.setState({\n            autoplaying: \"focused\"\n          });\n        }\n      } else {\n        // pauseType  is 'hovered'\n        if (autoplaying === \"playing\") {\n          _this.setState({\n            autoplaying: \"hovered\"\n          });\n        }\n      }\n    });\n    _defineProperty(_this, \"onDotsOver\", function () {\n      return _this.props.autoplay && _this.pause(\"hovered\");\n    });\n    _defineProperty(_this, \"onDotsLeave\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"hovered\" && _this.autoPlay(\"leave\");\n    });\n    _defineProperty(_this, \"onTrackOver\", function () {\n      return _this.props.autoplay && _this.pause(\"hovered\");\n    });\n    _defineProperty(_this, \"onTrackLeave\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"hovered\" && _this.autoPlay(\"leave\");\n    });\n    _defineProperty(_this, \"onSlideFocus\", function () {\n      return _this.props.autoplay && _this.pause(\"focused\");\n    });\n    _defineProperty(_this, \"onSlideBlur\", function () {\n      return _this.props.autoplay && _this.state.autoplaying === \"focused\" && _this.autoPlay(\"blur\");\n    });\n    _defineProperty(_this, \"render\", function () {\n      var className = classnames(\"slick-slider\", _this.props.className, {\n        \"slick-vertical\": _this.props.vertical,\n        \"slick-initialized\": true\n      });\n      var spec = _objectSpread(_objectSpread({}, _this.props), _this.state);\n      var trackProps = extractObject(spec, [\"fade\", \"cssEase\", \"speed\", \"infinite\", \"centerMode\", \"focusOnSelect\", \"currentSlide\", \"lazyLoad\", \"lazyLoadedList\", \"rtl\", \"slideWidth\", \"slideHeight\", \"listHeight\", \"vertical\", \"slidesToShow\", \"slidesToScroll\", \"slideCount\", \"trackStyle\", \"variableWidth\", \"unslick\", \"centerPadding\", \"targetSlide\", \"useCSS\"]);\n      var pauseOnHover = _this.props.pauseOnHover;\n      trackProps = _objectSpread(_objectSpread({}, trackProps), {}, {\n        onMouseEnter: pauseOnHover ? _this.onTrackOver : null,\n        onMouseLeave: pauseOnHover ? _this.onTrackLeave : null,\n        onMouseOver: pauseOnHover ? _this.onTrackOver : null,\n        focusOnSelect: _this.props.focusOnSelect && _this.clickable ? _this.selectHandler : null\n      });\n      var dots;\n      if (_this.props.dots === true && _this.state.slideCount >= _this.props.slidesToShow) {\n        var dotProps = extractObject(spec, [\"dotsClass\", \"slideCount\", \"slidesToShow\", \"currentSlide\", \"slidesToScroll\", \"clickHandler\", \"children\", \"customPaging\", \"infinite\", \"appendDots\"]);\n        var pauseOnDotsHover = _this.props.pauseOnDotsHover;\n        dotProps = _objectSpread(_objectSpread({}, dotProps), {}, {\n          clickHandler: _this.changeSlide,\n          onMouseEnter: pauseOnDotsHover ? _this.onDotsLeave : null,\n          onMouseOver: pauseOnDotsHover ? _this.onDotsOver : null,\n          onMouseLeave: pauseOnDotsHover ? _this.onDotsLeave : null\n        });\n        dots = /*#__PURE__*/React.createElement(Dots, dotProps);\n      }\n      var prevArrow, nextArrow;\n      var arrowProps = extractObject(spec, [\"infinite\", \"centerMode\", \"currentSlide\", \"slideCount\", \"slidesToShow\", \"prevArrow\", \"nextArrow\"]);\n      arrowProps.clickHandler = _this.changeSlide;\n      if (_this.props.arrows) {\n        prevArrow = /*#__PURE__*/React.createElement(PrevArrow, arrowProps);\n        nextArrow = /*#__PURE__*/React.createElement(NextArrow, arrowProps);\n      }\n      var verticalHeightStyle = null;\n      if (_this.props.vertical) {\n        verticalHeightStyle = {\n          height: _this.state.listHeight\n        };\n      }\n      var centerPaddingStyle = null;\n      if (_this.props.vertical === false) {\n        if (_this.props.centerMode === true) {\n          centerPaddingStyle = {\n            padding: \"0px \" + _this.props.centerPadding\n          };\n        }\n      } else {\n        if (_this.props.centerMode === true) {\n          centerPaddingStyle = {\n            padding: _this.props.centerPadding + \" 0px\"\n          };\n        }\n      }\n      var listStyle = _objectSpread(_objectSpread({}, verticalHeightStyle), centerPaddingStyle);\n      var touchMove = _this.props.touchMove;\n      var listProps = {\n        className: \"slick-list\",\n        style: listStyle,\n        onClick: _this.clickHandler,\n        onMouseDown: touchMove ? _this.swipeStart : null,\n        onMouseMove: _this.state.dragging && touchMove ? _this.swipeMove : null,\n        onMouseUp: touchMove ? _this.swipeEnd : null,\n        onMouseLeave: _this.state.dragging && touchMove ? _this.swipeEnd : null,\n        onTouchStart: touchMove ? _this.swipeStart : null,\n        onTouchMove: _this.state.dragging && touchMove ? _this.swipeMove : null,\n        onTouchEnd: touchMove ? _this.touchEnd : null,\n        onTouchCancel: _this.state.dragging && touchMove ? _this.swipeEnd : null,\n        onKeyDown: _this.props.accessibility ? _this.keyHandler : null\n      };\n      var innerSliderProps = {\n        className: className,\n        dir: \"ltr\",\n        style: _this.props.style\n      };\n      if (_this.props.unslick) {\n        listProps = {\n          className: \"slick-list\"\n        };\n        innerSliderProps = {\n          className: className,\n          style: _this.props.style\n        };\n      }\n      return /*#__PURE__*/React.createElement(\"div\", innerSliderProps, !_this.props.unslick ? prevArrow : \"\", /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: _this.listRefHandler\n      }, listProps), /*#__PURE__*/React.createElement(Track, _extends({\n        ref: _this.trackRefHandler\n      }, trackProps), _this.props.children)), !_this.props.unslick ? nextArrow : \"\", !_this.props.unslick ? dots : \"\");\n    });\n    _this.list = null;\n    _this.track = null;\n    _this.state = _objectSpread(_objectSpread({}, initialState), {}, {\n      currentSlide: _this.props.initialSlide,\n      targetSlide: _this.props.initialSlide ? _this.props.initialSlide : 0,\n      slideCount: React.Children.count(_this.props.children)\n    });\n    _this.callbackTimers = [];\n    _this.clickable = true;\n    _this.debouncedResize = null;\n    var ssrState = _this.ssrInit();\n    _this.state = _objectSpread(_objectSpread({}, _this.state), ssrState);\n    return _this;\n  }\n  _inherits(InnerSlider, _React$Component);\n  return _createClass(InnerSlider, [{\n    key: \"didPropsChange\",\n    value: function didPropsChange(prevProps) {\n      var setTrackStyle = false;\n      for (var _i3 = 0, _Object$keys = Object.keys(this.props); _i3 < _Object$keys.length; _i3++) {\n        var key = _Object$keys[_i3];\n        // eslint-disable-next-line no-prototype-builtins\n        if (!prevProps.hasOwnProperty(key)) {\n          setTrackStyle = true;\n          break;\n        }\n        if (_typeof(prevProps[key]) === \"object\" || typeof prevProps[key] === \"function\" || isNaN(prevProps[key])) {\n          continue;\n        }\n        if (prevProps[key] !== this.props[key]) {\n          setTrackStyle = true;\n          break;\n        }\n      }\n      return setTrackStyle || React.Children.count(this.props.children) !== React.Children.count(prevProps.children);\n    }\n  }]);\n}(React.Component);"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC7B,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,GAAGN,eAAe,CAACM,CAAC,CAAC,EAAER,0BAA0B,CAACO,CAAC,EAAEN,yBAAyB,CAAC,CAAC,GAAGS,OAAO,CAACC,SAAS,CAACH,CAAC,EAAEC,CAAC,IAAI,EAAE,EAAEP,eAAe,CAACK,CAAC,CAAC,CAACK,WAAW,CAAC,GAAGJ,CAAC,CAACK,KAAK,CAACN,CAAC,EAAEE,CAAC,CAAC,CAAC;AAAE;AAC1M,OAAOK,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,QAAQ,0BAA0B;AACvP,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,SAAS,EAAEC,SAAS,QAAQ,UAAU;AAC/C,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAO,IAAIC,WAAW,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAChE,SAASD,WAAWA,CAACE,KAAK,EAAE;IAC1B,IAAIC,KAAK;IACT3C,eAAe,CAAC,IAAI,EAAEwC,WAAW,CAAC;IAClCG,KAAK,GAAGnC,UAAU,CAAC,IAAI,EAAEgC,WAAW,EAAE,CAACE,KAAK,CAAC,CAAC;IAC9CpC,eAAe,CAACqC,KAAK,EAAE,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MACtD,OAAOD,KAAK,CAACE,IAAI,GAAGD,GAAG;IACzB,CAAC,CAAC;IACFtC,eAAe,CAACqC,KAAK,EAAE,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MACvD,OAAOD,KAAK,CAACG,KAAK,GAAGF,GAAG;IAC1B,CAAC,CAAC;IACFtC,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,YAAY;MAChD,IAAIA,KAAK,CAACD,KAAK,CAACK,cAAc,IAAIJ,KAAK,CAACE,IAAI,EAAE;QAC5C,IAAIG,IAAI,GAAGL,KAAK,CAACE,IAAI,CAACI,aAAa,CAAC,gBAAgB,CAACC,MAAM,CAACP,KAAK,CAACQ,KAAK,CAACC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC7FT,KAAK,CAACE,IAAI,CAACQ,KAAK,CAACC,MAAM,GAAG/B,SAAS,CAACyB,IAAI,CAAC,GAAG,IAAI;MAClD;IACF,CAAC,CAAC;IACF1C,eAAe,CAACqC,KAAK,EAAE,mBAAmB,EAAE,YAAY;MACtDA,KAAK,CAACD,KAAK,CAACa,MAAM,IAAIZ,KAAK,CAACD,KAAK,CAACa,MAAM,CAAC,CAAC;MAC1C,IAAIZ,KAAK,CAACD,KAAK,CAACc,QAAQ,EAAE;QACxB,IAAIC,YAAY,GAAGrC,qBAAqB,CAACrB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,CAAC;QACpG,IAAIM,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3Bf,KAAK,CAACgB,QAAQ,CAAC,UAAUC,SAAS,EAAE;YAClC,OAAO;cACLC,cAAc,EAAED,SAAS,CAACC,cAAc,CAACX,MAAM,CAACO,YAAY;YAC9D,CAAC;UACH,CAAC,CAAC;UACF,IAAId,KAAK,CAACD,KAAK,CAACoB,UAAU,EAAE;YAC1BnB,KAAK,CAACD,KAAK,CAACoB,UAAU,CAACL,YAAY,CAAC;UACtC;QACF;MACF;MACA,IAAIM,IAAI,GAAGhE,aAAa,CAAC;QACvBiE,OAAO,EAAErB,KAAK,CAACE,IAAI;QACnBoB,QAAQ,EAAEtB,KAAK,CAACG;MAClB,CAAC,EAAEH,KAAK,CAACD,KAAK,CAAC;MACfC,KAAK,CAACuB,WAAW,CAACH,IAAI,EAAE,IAAI,EAAE,YAAY;QACxCpB,KAAK,CAACwB,WAAW,CAAC,CAAC;QACnBxB,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAAC0B,QAAQ,CAAC,SAAS,CAAC;MACnD,CAAC,CAAC;MACF,IAAI1B,KAAK,CAACD,KAAK,CAACc,QAAQ,KAAK,aAAa,EAAE;QAC1Cb,KAAK,CAAC2B,aAAa,GAAGC,WAAW,CAAC5B,KAAK,CAAC6B,mBAAmB,EAAE,IAAI,CAAC;MACpE;MACA7B,KAAK,CAAC8B,EAAE,GAAG,IAAIlC,cAAc,CAAC,YAAY;QACxC,IAAII,KAAK,CAACQ,KAAK,CAACuB,SAAS,EAAE;UACzB/B,KAAK,CAACgC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;UAC9BhC,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY;YAC/C,OAAOnC,KAAK,CAACgC,eAAe,CAAC,CAAC;UAChC,CAAC,EAAEhC,KAAK,CAACD,KAAK,CAACqC,KAAK,CAAC,CAAC;QACxB,CAAC,MAAM;UACLpC,KAAK,CAACgC,eAAe,CAAC,CAAC;QACzB;MACF,CAAC,CAAC;MACFhC,KAAK,CAAC8B,EAAE,CAACO,OAAO,CAACrC,KAAK,CAACE,IAAI,CAAC;MAC5BoC,QAAQ,CAACC,gBAAgB,IAAIC,KAAK,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACL,QAAQ,CAACC,gBAAgB,CAAC,cAAc,CAAC,EAAE,UAAUK,KAAK,EAAE;QACpHA,KAAK,CAACC,OAAO,GAAG7C,KAAK,CAACD,KAAK,CAAC+C,YAAY,GAAG9C,KAAK,CAAC+C,YAAY,GAAG,IAAI;QACpEH,KAAK,CAACI,MAAM,GAAGhD,KAAK,CAACD,KAAK,CAAC+C,YAAY,GAAG9C,KAAK,CAACiD,WAAW,GAAG,IAAI;MACpE,CAAC,CAAC;MACF,IAAIC,MAAM,CAACC,gBAAgB,EAAE;QAC3BD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEnD,KAAK,CAACgC,eAAe,CAAC;MAC1D,CAAC,MAAM;QACLkB,MAAM,CAACE,WAAW,CAAC,UAAU,EAAEpD,KAAK,CAACgC,eAAe,CAAC;MACvD;IACF,CAAC,CAAC;IACFrE,eAAe,CAACqC,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAIA,KAAK,CAACqD,oBAAoB,EAAE;QAC9BC,YAAY,CAACtD,KAAK,CAACqD,oBAAoB,CAAC;MAC1C;MACA,IAAIrD,KAAK,CAAC2B,aAAa,EAAE;QACvB4B,aAAa,CAACvD,KAAK,CAAC2B,aAAa,CAAC;MACpC;MACA,IAAI3B,KAAK,CAACiC,cAAc,CAAClB,MAAM,EAAE;QAC/Bf,KAAK,CAACiC,cAAc,CAACS,OAAO,CAAC,UAAUc,KAAK,EAAE;UAC5C,OAAOF,YAAY,CAACE,KAAK,CAAC;QAC5B,CAAC,CAAC;QACFxD,KAAK,CAACiC,cAAc,GAAG,EAAE;MAC3B;MACA,IAAIiB,MAAM,CAACC,gBAAgB,EAAE;QAC3BD,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAEzD,KAAK,CAACgC,eAAe,CAAC;MAC7D,CAAC,MAAM;QACLkB,MAAM,CAACQ,WAAW,CAAC,UAAU,EAAE1D,KAAK,CAACgC,eAAe,CAAC;MACvD;MACA,IAAIhC,KAAK,CAAC2D,aAAa,EAAE;QACvBJ,aAAa,CAACvD,KAAK,CAAC2D,aAAa,CAAC;MACpC;MACA3D,KAAK,CAAC8B,EAAE,CAAC8B,UAAU,CAAC,CAAC;IACvB,CAAC,CAAC;IACFjG,eAAe,CAACqC,KAAK,EAAE,oBAAoB,EAAE,UAAU6D,SAAS,EAAE;MAChE7D,KAAK,CAAC8D,eAAe,CAAC,CAAC;MACvB9D,KAAK,CAACD,KAAK,CAACgE,QAAQ,IAAI/D,KAAK,CAACD,KAAK,CAACgE,QAAQ,CAAC,CAAC;MAC9C,IAAI/D,KAAK,CAACD,KAAK,CAACc,QAAQ,EAAE;QACxB,IAAIC,YAAY,GAAGrC,qBAAqB,CAACrB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,CAAC;QACpG,IAAIM,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3Bf,KAAK,CAACgB,QAAQ,CAAC,UAAUC,SAAS,EAAE;YAClC,OAAO;cACLC,cAAc,EAAED,SAAS,CAACC,cAAc,CAACX,MAAM,CAACO,YAAY;YAC9D,CAAC;UACH,CAAC,CAAC;UACF,IAAId,KAAK,CAACD,KAAK,CAACoB,UAAU,EAAE;YAC1BnB,KAAK,CAACD,KAAK,CAACoB,UAAU,CAACL,YAAY,CAAC;UACtC;QACF;MACF;MACA;MACA;MACA;MACAd,KAAK,CAACwB,WAAW,CAAC,CAAC;MACnB,IAAIJ,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC;QACrCiE,OAAO,EAAErB,KAAK,CAACE,IAAI;QACnBoB,QAAQ,EAAEtB,KAAK,CAACG;MAClB,CAAC,EAAEH,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC;MAC7B,IAAIwD,aAAa,GAAGhE,KAAK,CAACiE,cAAc,CAACJ,SAAS,CAAC;MACnDG,aAAa,IAAIhE,KAAK,CAACuB,WAAW,CAACH,IAAI,EAAE4C,aAAa,EAAE,YAAY;QAClE,IAAIhE,KAAK,CAACQ,KAAK,CAACC,YAAY,IAAIpC,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACnE,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC,EAAE;UAC1EpE,KAAK,CAACjB,WAAW,CAAC;YAChBsF,OAAO,EAAE,OAAO;YAChBC,KAAK,EAAEjG,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACnE,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC,GAAGpE,KAAK,CAACD,KAAK,CAACwE,YAAY;YAC5E9D,YAAY,EAAET,KAAK,CAACQ,KAAK,CAACC;UAC5B,CAAC,CAAC;QACJ;QACA,IAAIoD,SAAS,CAACpC,QAAQ,KAAKzB,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIoC,SAAS,CAACW,aAAa,KAAKxE,KAAK,CAACD,KAAK,CAACyE,aAAa,EAAE;UACxG,IAAI,CAACX,SAAS,CAACpC,QAAQ,IAAIzB,KAAK,CAACD,KAAK,CAAC0B,QAAQ,EAAE;YAC/CzB,KAAK,CAAC0B,QAAQ,CAAC,SAAS,CAAC;UAC3B,CAAC,MAAM,IAAI1B,KAAK,CAACD,KAAK,CAAC0B,QAAQ,EAAE;YAC/BzB,KAAK,CAAC0B,QAAQ,CAAC,QAAQ,CAAC;UAC1B,CAAC,MAAM;YACL1B,KAAK,CAACyE,KAAK,CAAC,QAAQ,CAAC;UACvB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF9G,eAAe,CAACqC,KAAK,EAAE,iBAAiB,EAAE,UAAUgE,aAAa,EAAE;MACjE,IAAIhE,KAAK,CAAC0E,eAAe,EAAE1E,KAAK,CAAC0E,eAAe,CAACC,MAAM,CAAC,CAAC;MACzD3E,KAAK,CAAC0E,eAAe,GAAGnG,QAAQ,CAAC,EAAE,EAAE,YAAY;QAC/C,OAAOyB,KAAK,CAAC4E,YAAY,CAACZ,aAAa,CAAC;MAC1C,CAAC,CAAC;MACFhE,KAAK,CAAC0E,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC;IACF/G,eAAe,CAACqC,KAAK,EAAE,cAAc,EAAE,YAAY;MACjD,IAAIgE,aAAa,GAAGa,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAC5F,IAAIE,cAAc,GAAGC,OAAO,CAAChF,KAAK,CAACG,KAAK,IAAIH,KAAK,CAACG,KAAK,CAAC8E,IAAI,CAAC;MAC7D;MACA,IAAI,CAACF,cAAc,EAAE;MACrB,IAAI3D,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC;QACrCiE,OAAO,EAAErB,KAAK,CAACE,IAAI;QACnBoB,QAAQ,EAAEtB,KAAK,CAACG;MAClB,CAAC,EAAEH,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC;MAC7BR,KAAK,CAACuB,WAAW,CAACH,IAAI,EAAE4C,aAAa,EAAE,YAAY;QACjD,IAAIhE,KAAK,CAACD,KAAK,CAAC0B,QAAQ,EAAEzB,KAAK,CAAC0B,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK1B,KAAK,CAACyE,KAAK,CAAC,QAAQ,CAAC;MAC/E,CAAC,CAAC;MACF;MACAzE,KAAK,CAACgB,QAAQ,CAAC;QACbe,SAAS,EAAE;MACb,CAAC,CAAC;MACFuB,YAAY,CAACtD,KAAK,CAACqD,oBAAoB,CAAC;MACxC,OAAOrD,KAAK,CAACqD,oBAAoB;IACnC,CAAC,CAAC;IACF1F,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,UAAUoB,IAAI,EAAE4C,aAAa,EAAEkB,QAAQ,EAAE;MAC7E,IAAIC,YAAY,GAAGxG,gBAAgB,CAACyC,IAAI,CAAC;MACzCA,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgE,IAAI,CAAC,EAAE+D,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7EC,UAAU,EAAED,YAAY,CAAC1E;MAC3B,CAAC,CAAC;MACF,IAAI4E,UAAU,GAAG/F,YAAY,CAAC8B,IAAI,CAAC;MACnCA,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDkE,IAAI,EAAED;MACR,CAAC,CAAC;MACF,IAAIE,UAAU,GAAGhG,WAAW,CAAC6B,IAAI,CAAC;MAClC,IAAI4C,aAAa,IAAI3F,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACnE,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC,KAAK/F,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAAC/C,IAAI,CAACgD,QAAQ,CAAC,EAAE;QACvGe,YAAY,CAAC,YAAY,CAAC,GAAGI,UAAU;MACzC;MACAvF,KAAK,CAACgB,QAAQ,CAACmE,YAAY,EAAED,QAAQ,CAAC;IACxC,CAAC,CAAC;IACFvH,eAAe,CAACqC,KAAK,EAAE,SAAS,EAAE,YAAY;MAC5C,IAAIA,KAAK,CAACD,KAAK,CAACyF,aAAa,EAAE;QAC7B,IAAIC,WAAW,GAAG,CAAC;UACjBC,UAAU,GAAG,CAAC;QAChB,IAAIC,cAAc,GAAG,EAAE;QACvB,IAAIC,SAAS,GAAGxG,YAAY,CAAChC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACzGqF,UAAU,EAAE7F,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAACrD;QACnC,CAAC,CAAC,CAAC;QACH,IAAI+E,UAAU,GAAGzG,aAAa,CAACjC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3GqF,UAAU,EAAE7F,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAACrD;QACnC,CAAC,CAAC,CAAC;QACHf,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC1B,OAAO,CAAC,UAAUqD,KAAK,EAAE;UAC5CJ,cAAc,CAACzD,IAAI,CAAC6D,KAAK,CAAChG,KAAK,CAACW,KAAK,CAACsF,KAAK,CAAC;UAC5CP,WAAW,IAAIM,KAAK,CAAChG,KAAK,CAACW,KAAK,CAACsF,KAAK;QACxC,CAAC,CAAC;QACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;UAClCP,UAAU,IAAIC,cAAc,CAACA,cAAc,CAAC5E,MAAM,GAAG,CAAC,GAAGkF,CAAC,CAAC;UAC3DR,WAAW,IAAIE,cAAc,CAACA,cAAc,CAAC5E,MAAM,GAAG,CAAC,GAAGkF,CAAC,CAAC;QAC9D;QACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGJ,UAAU,EAAEI,EAAE,EAAE,EAAE;UACtCT,WAAW,IAAIE,cAAc,CAACO,EAAE,CAAC;QACnC;QACA,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGnG,KAAK,CAACQ,KAAK,CAACC,YAAY,EAAE0F,GAAG,EAAE,EAAE;UACvDT,UAAU,IAAIC,cAAc,CAACQ,GAAG,CAAC;QACnC;QACA,IAAIC,WAAW,GAAG;UAChBJ,KAAK,EAAEP,WAAW,GAAG,IAAI;UACzBH,IAAI,EAAE,CAACI,UAAU,GAAG;QACtB,CAAC;QACD,IAAI1F,KAAK,CAACD,KAAK,CAACsG,UAAU,EAAE;UAC1B,IAAIC,YAAY,GAAG,EAAE,CAAC/F,MAAM,CAACoF,cAAc,CAAC3F,KAAK,CAACQ,KAAK,CAACC,YAAY,CAAC,EAAE,IAAI,CAAC;UAC5E2F,WAAW,CAACd,IAAI,GAAG,OAAO,CAAC/E,MAAM,CAAC6F,WAAW,CAACd,IAAI,EAAE,aAAa,CAAC,CAAC/E,MAAM,CAAC+F,YAAY,EAAE,UAAU,CAAC;QACrG;QACA,OAAO;UACLf,UAAU,EAAEa;QACd,CAAC;MACH;MACA,IAAIG,aAAa,GAAGlI,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACnE,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC;MAC9D,IAAIhD,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACvFqF,UAAU,EAAEU;MACd,CAAC,CAAC;MACF,IAAIV,UAAU,GAAGzG,YAAY,CAACgC,IAAI,CAAC,GAAG/B,aAAa,CAAC+B,IAAI,CAAC,GAAGmF,aAAa;MACzE,IAAIC,UAAU,GAAG,GAAG,GAAGxG,KAAK,CAACD,KAAK,CAACwE,YAAY,GAAGsB,UAAU;MAC5D,IAAIY,UAAU,GAAG,GAAG,GAAGZ,UAAU;MACjC,IAAIa,SAAS,GAAG,CAACD,UAAU,IAAIrH,YAAY,CAACgC,IAAI,CAAC,GAAGpB,KAAK,CAACQ,KAAK,CAACC,YAAY,CAAC,GAAG+F,UAAU,GAAG,GAAG;MAChG,IAAIxG,KAAK,CAACD,KAAK,CAACsG,UAAU,EAAE;QAC1BK,SAAS,IAAI,CAAC,GAAG,GAAGD,UAAU,GAAGD,UAAU,GAAG,GAAG,IAAI,CAAC;MACxD;MACA,IAAIjB,UAAU,GAAG;QACfS,KAAK,EAAEQ,UAAU,GAAG,GAAG;QACvBlB,IAAI,EAAEoB,SAAS,GAAG;MACpB,CAAC;MACD,OAAO;QACLD,UAAU,EAAEA,UAAU,GAAG,GAAG;QAC5BlB,UAAU,EAAEA;MACd,CAAC;IACH,CAAC,CAAC;IACF5H,eAAe,CAACqC,KAAK,EAAE,iBAAiB,EAAE,YAAY;MACpD,IAAI2G,MAAM,GAAG3G,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACE,IAAI,CAACqC,gBAAgB,IAAIvC,KAAK,CAACE,IAAI,CAACqC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,EAAE;MAC/G,IAAIqE,WAAW,GAAGD,MAAM,CAAC5F,MAAM;QAC7B8F,WAAW,GAAG,CAAC;MACjBrE,KAAK,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACgE,MAAM,EAAE,UAAUG,KAAK,EAAE;QACpD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;UAC/B,OAAO,EAAEF,WAAW,IAAIA,WAAW,IAAID,WAAW,IAAI5G,KAAK,CAACgC,eAAe,CAAC,CAAC;QAC/E,CAAC;QACD,IAAI,CAAC8E,KAAK,CAACE,OAAO,EAAE;UAClBF,KAAK,CAACE,OAAO,GAAG,YAAY;YAC1B,OAAOF,KAAK,CAACG,UAAU,CAACC,KAAK,CAAC,CAAC;UACjC,CAAC;QACH,CAAC,MAAM;UACL,IAAIC,gBAAgB,GAAGL,KAAK,CAACE,OAAO;UACpCF,KAAK,CAACE,OAAO,GAAG,UAAUhJ,CAAC,EAAE;YAC3BmJ,gBAAgB,CAACnJ,CAAC,CAAC;YACnB8I,KAAK,CAACG,UAAU,CAACC,KAAK,CAAC,CAAC;UAC1B,CAAC;QACH;QACA,IAAI,CAACJ,KAAK,CAACM,MAAM,EAAE;UACjB,IAAIpH,KAAK,CAACD,KAAK,CAACc,QAAQ,EAAE;YACxBiG,KAAK,CAACM,MAAM,GAAG,YAAY;cACzBpH,KAAK,CAACwB,WAAW,CAAC,CAAC;cACnBxB,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAACnC,KAAK,CAACgC,eAAe,EAAEhC,KAAK,CAACD,KAAK,CAACqC,KAAK,CAAC,CAAC;YACjF,CAAC;UACH,CAAC,MAAM;YACL0E,KAAK,CAACM,MAAM,GAAGL,OAAO;YACtBD,KAAK,CAACO,OAAO,GAAG,YAAY;cAC1BN,OAAO,CAAC,CAAC;cACT/G,KAAK,CAACD,KAAK,CAACuH,eAAe,IAAItH,KAAK,CAACD,KAAK,CAACuH,eAAe,CAAC,CAAC;YAC9D,CAAC;UACH;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF3J,eAAe,CAACqC,KAAK,EAAE,qBAAqB,EAAE,YAAY;MACxD,IAAIc,YAAY,GAAG,EAAE;MACrB,IAAIM,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC;MACrE,KAAK,IAAI8D,KAAK,GAAGtE,KAAK,CAACQ,KAAK,CAACC,YAAY,EAAE6D,KAAK,GAAGtE,KAAK,CAACQ,KAAK,CAACqF,UAAU,GAAGxG,aAAa,CAAC+B,IAAI,CAAC,EAAEkD,KAAK,EAAE,EAAE;QACxG,IAAItE,KAAK,CAACQ,KAAK,CAACU,cAAc,CAACqG,OAAO,CAACjD,KAAK,CAAC,GAAG,CAAC,EAAE;UACjDxD,YAAY,CAACoB,IAAI,CAACoC,KAAK,CAAC;UACxB;QACF;MACF;MACA,KAAK,IAAIkD,MAAM,GAAGxH,KAAK,CAACQ,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE+G,MAAM,IAAI,CAACpI,YAAY,CAACgC,IAAI,CAAC,EAAEoG,MAAM,EAAE,EAAE;QACvF,IAAIxH,KAAK,CAACQ,KAAK,CAACU,cAAc,CAACqG,OAAO,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;UAClD1G,YAAY,CAACoB,IAAI,CAACsF,MAAM,CAAC;UACzB;QACF;MACF;MACA,IAAI1G,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3Bf,KAAK,CAACgB,QAAQ,CAAC,UAAUR,KAAK,EAAE;UAC9B,OAAO;YACLU,cAAc,EAAEV,KAAK,CAACU,cAAc,CAACX,MAAM,CAACO,YAAY;UAC1D,CAAC;QACH,CAAC,CAAC;QACF,IAAId,KAAK,CAACD,KAAK,CAACoB,UAAU,EAAE;UAC1BnB,KAAK,CAACD,KAAK,CAACoB,UAAU,CAACL,YAAY,CAAC;QACtC;MACF,CAAC,MAAM;QACL,IAAId,KAAK,CAAC2B,aAAa,EAAE;UACvB4B,aAAa,CAACvD,KAAK,CAAC2B,aAAa,CAAC;UAClC,OAAO3B,KAAK,CAAC2B,aAAa;QAC5B;MACF;IACF,CAAC,CAAC;IACFhE,eAAe,CAACqC,KAAK,EAAE,cAAc,EAAE,UAAUsE,KAAK,EAAE;MACtD,IAAImD,WAAW,GAAG5C,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3F,IAAI6C,WAAW,GAAG1H,KAAK,CAACD,KAAK;QAC3B4H,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,YAAY,GAAGF,WAAW,CAACE,YAAY;QACvCzG,UAAU,GAAGuG,WAAW,CAACvG,UAAU;QACnCiB,KAAK,GAAGsF,WAAW,CAACtF,KAAK;QACzByF,WAAW,GAAGH,WAAW,CAACG,WAAW;MACvC;MACA,IAAIpH,YAAY,GAAGT,KAAK,CAACQ,KAAK,CAACC,YAAY;MAC3C,IAAIqH,aAAa,GAAGhJ,YAAY,CAAC1B,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACvEkH,KAAK,EAAEA;QACT,CAAC,EAAEtE,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjCc,QAAQ,EAAEtB,KAAK,CAACG,KAAK;UACrB4H,MAAM,EAAE/H,KAAK,CAACD,KAAK,CAACgI,MAAM,IAAI,CAACN;QACjC,CAAC,CAAC,CAAC;QACHjH,KAAK,GAAGsH,aAAa,CAACtH,KAAK;QAC3BwH,SAAS,GAAGF,aAAa,CAACE,SAAS;MACrC,IAAI,CAACxH,KAAK,EAAE;MACZoH,YAAY,IAAIA,YAAY,CAACnH,YAAY,EAAED,KAAK,CAACC,YAAY,CAAC;MAC9D,IAAIK,YAAY,GAAGN,KAAK,CAACU,cAAc,CAAC+G,MAAM,CAAC,UAAUC,KAAK,EAAE;QAC9D,OAAOlI,KAAK,CAACQ,KAAK,CAACU,cAAc,CAACqG,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC;MACtD,CAAC,CAAC;MACF/G,UAAU,IAAIL,YAAY,CAACC,MAAM,GAAG,CAAC,IAAII,UAAU,CAACL,YAAY,CAAC;MACjE,IAAI,CAACd,KAAK,CAACD,KAAK,CAACoI,cAAc,IAAInI,KAAK,CAACqD,oBAAoB,EAAE;QAC7DC,YAAY,CAACtD,KAAK,CAACqD,oBAAoB,CAAC;QACxCwE,WAAW,IAAIA,WAAW,CAACpH,YAAY,CAAC;QACxC,OAAOT,KAAK,CAACqD,oBAAoB;MACnC;MACArD,KAAK,CAACgB,QAAQ,CAACR,KAAK,EAAE,YAAY;QAChC;QACA,IAAImH,QAAQ,IAAI3H,KAAK,CAACoI,aAAa,KAAK9D,KAAK,EAAE;UAC7CtE,KAAK,CAACoI,aAAa,GAAG9D,KAAK;UAC3BqD,QAAQ,CAACU,WAAW,CAACvJ,YAAY,CAACwF,KAAK,CAAC;QAC1C;QACA,IAAI,CAAC0D,SAAS,EAAE;QAChBhI,KAAK,CAACqD,oBAAoB,GAAGlB,UAAU,CAAC,YAAY;UAClD,IAAIJ,SAAS,GAAGiG,SAAS,CAACjG,SAAS;YACjCuG,UAAU,GAAGnL,wBAAwB,CAAC6K,SAAS,EAAEpK,SAAS,CAAC;UAC7DoC,KAAK,CAACgB,QAAQ,CAACsH,UAAU,EAAE,YAAY;YACrCtI,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY;cAC/C,OAAOnC,KAAK,CAACgB,QAAQ,CAAC;gBACpBe,SAAS,EAAEA;cACb,CAAC,CAAC;YACJ,CAAC,EAAE,EAAE,CAAC,CAAC;YACP8F,WAAW,IAAIA,WAAW,CAACrH,KAAK,CAACC,YAAY,CAAC;YAC9C,OAAOT,KAAK,CAACqD,oBAAoB;UACnC,CAAC,CAAC;QACJ,CAAC,EAAEjB,KAAK,CAAC;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;IACFzE,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,UAAUuI,OAAO,EAAE;MACvD,IAAId,WAAW,GAAG5C,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3F,IAAIzD,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC;MACrE,IAAIgI,WAAW,GAAGzJ,WAAW,CAACqC,IAAI,EAAEmH,OAAO,CAAC;MAC5C,IAAIC,WAAW,KAAK,CAAC,IAAI,CAACA,WAAW,EAAE;MACvC,IAAIf,WAAW,KAAK,IAAI,EAAE;QACxBzH,KAAK,CAAClB,YAAY,CAAC0J,WAAW,EAAEf,WAAW,CAAC;MAC9C,CAAC,MAAM;QACLzH,KAAK,CAAClB,YAAY,CAAC0J,WAAW,CAAC;MACjC;MACAxI,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAAC0B,QAAQ,CAAC,QAAQ,CAAC;MAChD,IAAI1B,KAAK,CAACD,KAAK,CAAC0I,aAAa,EAAE;QAC7B,IAAIC,KAAK,GAAG1I,KAAK,CAACE,IAAI,CAACqC,gBAAgB,CAAC,gBAAgB,CAAC;QACzDmG,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACxB,KAAK,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;IACFvJ,eAAe,CAACqC,KAAK,EAAE,cAAc,EAAE,UAAUhC,CAAC,EAAE;MAClD,IAAIgC,KAAK,CAAC2I,SAAS,KAAK,KAAK,EAAE;QAC7B3K,CAAC,CAAC4K,eAAe,CAAC,CAAC;QACnB5K,CAAC,CAAC6K,cAAc,CAAC,CAAC;MACpB;MACA7I,KAAK,CAAC2I,SAAS,GAAG,IAAI;IACxB,CAAC,CAAC;IACFhL,eAAe,CAACqC,KAAK,EAAE,YAAY,EAAE,UAAUhC,CAAC,EAAE;MAChD,IAAI8K,GAAG,GAAG9J,UAAU,CAAChB,CAAC,EAAEgC,KAAK,CAACD,KAAK,CAACgJ,aAAa,EAAE/I,KAAK,CAACD,KAAK,CAACiJ,GAAG,CAAC;MACnEF,GAAG,KAAK,EAAE,IAAI9I,KAAK,CAACjB,WAAW,CAAC;QAC9BsF,OAAO,EAAEyE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnL,eAAe,CAACqC,KAAK,EAAE,eAAe,EAAE,UAAUuI,OAAO,EAAE;MACzDvI,KAAK,CAACjB,WAAW,CAACwJ,OAAO,CAAC;IAC5B,CAAC,CAAC;IACF5K,eAAe,CAACqC,KAAK,EAAE,mBAAmB,EAAE,YAAY;MACtD,IAAI6I,cAAc,GAAG,SAASA,cAAcA,CAAC7K,CAAC,EAAE;QAC9CA,CAAC,GAAGA,CAAC,IAAIkF,MAAM,CAAC+F,KAAK;QACrB,IAAIjL,CAAC,CAAC6K,cAAc,EAAE7K,CAAC,CAAC6K,cAAc,CAAC,CAAC;QACxC7K,CAAC,CAACkL,WAAW,GAAG,KAAK;MACvB,CAAC;MACDhG,MAAM,CAACiG,WAAW,GAAGN,cAAc;IACrC,CAAC,CAAC;IACFlL,eAAe,CAACqC,KAAK,EAAE,kBAAkB,EAAE,YAAY;MACrDkD,MAAM,CAACiG,WAAW,GAAG,IAAI;IAC3B,CAAC,CAAC;IACFxL,eAAe,CAACqC,KAAK,EAAE,YAAY,EAAE,UAAUhC,CAAC,EAAE;MAChD,IAAIgC,KAAK,CAACD,KAAK,CAACqJ,eAAe,EAAE;QAC/BpJ,KAAK,CAACqJ,iBAAiB,CAAC,CAAC;MAC3B;MACA,IAAI7I,KAAK,GAAGvB,UAAU,CAACjB,CAAC,EAAEgC,KAAK,CAACD,KAAK,CAACuJ,KAAK,EAAEtJ,KAAK,CAACD,KAAK,CAACwJ,SAAS,CAAC;MACnE/I,KAAK,KAAK,EAAE,IAAIR,KAAK,CAACgB,QAAQ,CAACR,KAAK,CAAC;IACvC,CAAC,CAAC;IACF7C,eAAe,CAACqC,KAAK,EAAE,WAAW,EAAE,UAAUhC,CAAC,EAAE;MAC/C,IAAIwC,KAAK,GAAGtB,SAAS,CAAClB,CAAC,EAAEZ,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACrGc,QAAQ,EAAEtB,KAAK,CAACG,KAAK;QACrBkB,OAAO,EAAErB,KAAK,CAACE,IAAI;QACnBkF,UAAU,EAAEpF,KAAK,CAACQ,KAAK,CAACC;MAC1B,CAAC,CAAC,CAAC;MACH,IAAI,CAACD,KAAK,EAAE;MACZ,IAAIA,KAAK,CAAC,SAAS,CAAC,EAAE;QACpBR,KAAK,CAAC2I,SAAS,GAAG,KAAK;MACzB;MACA3I,KAAK,CAACgB,QAAQ,CAACR,KAAK,CAAC;IACvB,CAAC,CAAC;IACF7C,eAAe,CAACqC,KAAK,EAAE,UAAU,EAAE,UAAUhC,CAAC,EAAE;MAC9C,IAAIwC,KAAK,GAAGrB,QAAQ,CAACnB,CAAC,EAAEZ,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACpGc,QAAQ,EAAEtB,KAAK,CAACG,KAAK;QACrBkB,OAAO,EAAErB,KAAK,CAACE,IAAI;QACnBkF,UAAU,EAAEpF,KAAK,CAACQ,KAAK,CAACC;MAC1B,CAAC,CAAC,CAAC;MACH,IAAI,CAACD,KAAK,EAAE;MACZ,IAAIgJ,mBAAmB,GAAGhJ,KAAK,CAAC,qBAAqB,CAAC;MACtD,OAAOA,KAAK,CAAC,qBAAqB,CAAC;MACnCR,KAAK,CAACgB,QAAQ,CAACR,KAAK,CAAC;MACrB,IAAIgJ,mBAAmB,KAAK1E,SAAS,EAAE;MACvC9E,KAAK,CAAClB,YAAY,CAAC0K,mBAAmB,CAAC;MACvC,IAAIxJ,KAAK,CAACD,KAAK,CAACqJ,eAAe,EAAE;QAC/BpJ,KAAK,CAACyJ,gBAAgB,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IACF9L,eAAe,CAACqC,KAAK,EAAE,UAAU,EAAE,UAAUhC,CAAC,EAAE;MAC9CgC,KAAK,CAACb,QAAQ,CAACnB,CAAC,CAAC;MACjBgC,KAAK,CAAC2I,SAAS,GAAG,IAAI;IACxB,CAAC,CAAC;IACFhL,eAAe,CAACqC,KAAK,EAAE,WAAW,EAAE,YAAY;MAC9C;MACA;MACA;MACAA,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY;QAC/C,OAAOnC,KAAK,CAACjB,WAAW,CAAC;UACvBsF,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;IACF1G,eAAe,CAACqC,KAAK,EAAE,WAAW,EAAE,YAAY;MAC9CA,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY;QAC/C,OAAOnC,KAAK,CAACjB,WAAW,CAAC;UACvBsF,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;IACF1G,eAAe,CAACqC,KAAK,EAAE,WAAW,EAAE,UAAU4C,KAAK,EAAE;MACnD,IAAI6E,WAAW,GAAG5C,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3FjC,KAAK,GAAG8G,MAAM,CAAC9G,KAAK,CAAC;MACrB,IAAI+G,KAAK,CAAC/G,KAAK,CAAC,EAAE,OAAO,EAAE;MAC3B5C,KAAK,CAACiC,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY;QAC/C,OAAOnC,KAAK,CAACjB,WAAW,CAAC;UACvBsF,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE1B,KAAK;UACZnC,YAAY,EAAET,KAAK,CAACQ,KAAK,CAACC;QAC5B,CAAC,EAAEgH,WAAW,CAAC;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;IACF9J,eAAe,CAACqC,KAAK,EAAE,MAAM,EAAE,YAAY;MACzC,IAAI4J,SAAS;MACb,IAAI5J,KAAK,CAACD,KAAK,CAACiJ,GAAG,EAAE;QACnBY,SAAS,GAAG5J,KAAK,CAACQ,KAAK,CAACC,YAAY,GAAGT,KAAK,CAACD,KAAK,CAAC8J,cAAc;MACnE,CAAC,MAAM;QACL,IAAIhL,SAAS,CAACzB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC,CAAC,EAAE;UACzEoJ,SAAS,GAAG5J,KAAK,CAACQ,KAAK,CAACC,YAAY,GAAGT,KAAK,CAACD,KAAK,CAAC8J,cAAc;QACnE,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF;MACA7J,KAAK,CAAClB,YAAY,CAAC8K,SAAS,CAAC;IAC/B,CAAC,CAAC;IACFjM,eAAe,CAACqC,KAAK,EAAE,UAAU,EAAE,UAAU8J,QAAQ,EAAE;MACrD,IAAI9J,KAAK,CAAC2D,aAAa,EAAE;QACvBJ,aAAa,CAACvD,KAAK,CAAC2D,aAAa,CAAC;MACpC;MACA,IAAIoG,WAAW,GAAG/J,KAAK,CAACQ,KAAK,CAACuJ,WAAW;MACzC,IAAID,QAAQ,KAAK,QAAQ,EAAE;QACzB,IAAIC,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,QAAQ,EAAE;UACtF;QACF;MACF,CAAC,MAAM,IAAID,QAAQ,KAAK,OAAO,EAAE;QAC/B,IAAIC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,SAAS,EAAE;UACzD;QACF;MACF,CAAC,MAAM,IAAID,QAAQ,KAAK,MAAM,EAAE;QAC9B,IAAIC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,SAAS,EAAE;UACzD;QACF;MACF;MACA/J,KAAK,CAAC2D,aAAa,GAAG/B,WAAW,CAAC5B,KAAK,CAACgK,IAAI,EAAEhK,KAAK,CAACD,KAAK,CAACyE,aAAa,GAAG,EAAE,CAAC;MAC7ExE,KAAK,CAACgB,QAAQ,CAAC;QACb+I,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IACFpM,eAAe,CAACqC,KAAK,EAAE,OAAO,EAAE,UAAUiK,SAAS,EAAE;MACnD,IAAIjK,KAAK,CAAC2D,aAAa,EAAE;QACvBJ,aAAa,CAACvD,KAAK,CAAC2D,aAAa,CAAC;QAClC3D,KAAK,CAAC2D,aAAa,GAAG,IAAI;MAC5B;MACA,IAAIoG,WAAW,GAAG/J,KAAK,CAACQ,KAAK,CAACuJ,WAAW;MACzC,IAAIE,SAAS,KAAK,QAAQ,EAAE;QAC1BjK,KAAK,CAACgB,QAAQ,CAAC;UACb+I,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIE,SAAS,KAAK,SAAS,EAAE;QAClC,IAAIF,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,SAAS,EAAE;UAC1D/J,KAAK,CAACgB,QAAQ,CAAC;YACb+I,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA,IAAIA,WAAW,KAAK,SAAS,EAAE;UAC7B/J,KAAK,CAACgB,QAAQ,CAAC;YACb+I,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IACFpM,eAAe,CAACqC,KAAK,EAAE,YAAY,EAAE,YAAY;MAC/C,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACyE,KAAK,CAAC,SAAS,CAAC;IACvD,CAAC,CAAC;IACF9G,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,YAAY;MAChD,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACQ,KAAK,CAACuJ,WAAW,KAAK,SAAS,IAAI/J,KAAK,CAAC0B,QAAQ,CAAC,OAAO,CAAC;IACjG,CAAC,CAAC;IACF/D,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,YAAY;MAChD,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACyE,KAAK,CAAC,SAAS,CAAC;IACvD,CAAC,CAAC;IACF9G,eAAe,CAACqC,KAAK,EAAE,cAAc,EAAE,YAAY;MACjD,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACQ,KAAK,CAACuJ,WAAW,KAAK,SAAS,IAAI/J,KAAK,CAAC0B,QAAQ,CAAC,OAAO,CAAC;IACjG,CAAC,CAAC;IACF/D,eAAe,CAACqC,KAAK,EAAE,cAAc,EAAE,YAAY;MACjD,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACyE,KAAK,CAAC,SAAS,CAAC;IACvD,CAAC,CAAC;IACF9G,eAAe,CAACqC,KAAK,EAAE,aAAa,EAAE,YAAY;MAChD,OAAOA,KAAK,CAACD,KAAK,CAAC0B,QAAQ,IAAIzB,KAAK,CAACQ,KAAK,CAACuJ,WAAW,KAAK,SAAS,IAAI/J,KAAK,CAAC0B,QAAQ,CAAC,MAAM,CAAC;IAChG,CAAC,CAAC;IACF/D,eAAe,CAACqC,KAAK,EAAE,QAAQ,EAAE,YAAY;MAC3C,IAAIkK,SAAS,GAAG1L,UAAU,CAAC,cAAc,EAAEwB,KAAK,CAACD,KAAK,CAACmK,SAAS,EAAE;QAChE,gBAAgB,EAAElK,KAAK,CAACD,KAAK,CAACoK,QAAQ;QACtC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI/I,IAAI,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACD,KAAK,CAAC,EAAEC,KAAK,CAACQ,KAAK,CAAC;MACrE,IAAI4J,UAAU,GAAG1L,aAAa,CAAC0C,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;MAC7V,IAAIiJ,YAAY,GAAGrK,KAAK,CAACD,KAAK,CAACsK,YAAY;MAC3CD,UAAU,GAAGhN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgN,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5DE,YAAY,EAAED,YAAY,GAAGrK,KAAK,CAACuK,WAAW,GAAG,IAAI;QACrDC,YAAY,EAAEH,YAAY,GAAGrK,KAAK,CAACyK,YAAY,GAAG,IAAI;QACtDC,WAAW,EAAEL,YAAY,GAAGrK,KAAK,CAACuK,WAAW,GAAG,IAAI;QACpD9B,aAAa,EAAEzI,KAAK,CAACD,KAAK,CAAC0I,aAAa,IAAIzI,KAAK,CAAC2I,SAAS,GAAG3I,KAAK,CAAC2K,aAAa,GAAG;MACtF,CAAC,CAAC;MACF,IAAIC,IAAI;MACR,IAAI5K,KAAK,CAACD,KAAK,CAAC6K,IAAI,KAAK,IAAI,IAAI5K,KAAK,CAACQ,KAAK,CAACqF,UAAU,IAAI7F,KAAK,CAACD,KAAK,CAACwE,YAAY,EAAE;QACnF,IAAIsG,QAAQ,GAAGnM,aAAa,CAAC0C,IAAI,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QACvL,IAAI0J,gBAAgB,GAAG9K,KAAK,CAACD,KAAK,CAAC+K,gBAAgB;QACnDD,QAAQ,GAAGzN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyN,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;UACxDE,YAAY,EAAE/K,KAAK,CAACjB,WAAW;UAC/BuL,YAAY,EAAEQ,gBAAgB,GAAG9K,KAAK,CAACgL,WAAW,GAAG,IAAI;UACzDN,WAAW,EAAEI,gBAAgB,GAAG9K,KAAK,CAACiL,UAAU,GAAG,IAAI;UACvDT,YAAY,EAAEM,gBAAgB,GAAG9K,KAAK,CAACgL,WAAW,GAAG;QACvD,CAAC,CAAC;QACFJ,IAAI,GAAG,aAAavM,KAAK,CAAC6M,aAAa,CAACzL,IAAI,EAAEoL,QAAQ,CAAC;MACzD;MACA,IAAIM,SAAS,EAAEC,SAAS;MACxB,IAAIC,UAAU,GAAG3M,aAAa,CAAC0C,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;MACxIiK,UAAU,CAACN,YAAY,GAAG/K,KAAK,CAACjB,WAAW;MAC3C,IAAIiB,KAAK,CAACD,KAAK,CAACuL,MAAM,EAAE;QACtBH,SAAS,GAAG,aAAa9M,KAAK,CAAC6M,aAAa,CAACxL,SAAS,EAAE2L,UAAU,CAAC;QACnED,SAAS,GAAG,aAAa/M,KAAK,CAAC6M,aAAa,CAACvL,SAAS,EAAE0L,UAAU,CAAC;MACrE;MACA,IAAIE,mBAAmB,GAAG,IAAI;MAC9B,IAAIvL,KAAK,CAACD,KAAK,CAACoK,QAAQ,EAAE;QACxBoB,mBAAmB,GAAG;UACpB5K,MAAM,EAAEX,KAAK,CAACQ,KAAK,CAACgL;QACtB,CAAC;MACH;MACA,IAAIC,kBAAkB,GAAG,IAAI;MAC7B,IAAIzL,KAAK,CAACD,KAAK,CAACoK,QAAQ,KAAK,KAAK,EAAE;QAClC,IAAInK,KAAK,CAACD,KAAK,CAACsG,UAAU,KAAK,IAAI,EAAE;UACnCoF,kBAAkB,GAAG;YACnBC,OAAO,EAAE,MAAM,GAAG1L,KAAK,CAACD,KAAK,CAAC4L;UAChC,CAAC;QACH;MACF,CAAC,MAAM;QACL,IAAI3L,KAAK,CAACD,KAAK,CAACsG,UAAU,KAAK,IAAI,EAAE;UACnCoF,kBAAkB,GAAG;YACnBC,OAAO,EAAE1L,KAAK,CAACD,KAAK,CAAC4L,aAAa,GAAG;UACvC,CAAC;QACH;MACF;MACA,IAAIC,SAAS,GAAGxO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmO,mBAAmB,CAAC,EAAEE,kBAAkB,CAAC;MACzF,IAAII,SAAS,GAAG7L,KAAK,CAACD,KAAK,CAAC8L,SAAS;MACrC,IAAIC,SAAS,GAAG;QACd5B,SAAS,EAAE,YAAY;QACvBxJ,KAAK,EAAEkL,SAAS;QAChBG,OAAO,EAAE/L,KAAK,CAAC+K,YAAY;QAC3BiB,WAAW,EAAEH,SAAS,GAAG7L,KAAK,CAACf,UAAU,GAAG,IAAI;QAChDgN,WAAW,EAAEjM,KAAK,CAACQ,KAAK,CAAC0L,QAAQ,IAAIL,SAAS,GAAG7L,KAAK,CAACd,SAAS,GAAG,IAAI;QACvEiN,SAAS,EAAEN,SAAS,GAAG7L,KAAK,CAACb,QAAQ,GAAG,IAAI;QAC5CqL,YAAY,EAAExK,KAAK,CAACQ,KAAK,CAAC0L,QAAQ,IAAIL,SAAS,GAAG7L,KAAK,CAACb,QAAQ,GAAG,IAAI;QACvEiN,YAAY,EAAEP,SAAS,GAAG7L,KAAK,CAACf,UAAU,GAAG,IAAI;QACjDoN,WAAW,EAAErM,KAAK,CAACQ,KAAK,CAAC0L,QAAQ,IAAIL,SAAS,GAAG7L,KAAK,CAACd,SAAS,GAAG,IAAI;QACvEoN,UAAU,EAAET,SAAS,GAAG7L,KAAK,CAACuM,QAAQ,GAAG,IAAI;QAC7CC,aAAa,EAAExM,KAAK,CAACQ,KAAK,CAAC0L,QAAQ,IAAIL,SAAS,GAAG7L,KAAK,CAACb,QAAQ,GAAG,IAAI;QACxEsN,SAAS,EAAEzM,KAAK,CAACD,KAAK,CAACgJ,aAAa,GAAG/I,KAAK,CAAChB,UAAU,GAAG;MAC5D,CAAC;MACD,IAAI0N,gBAAgB,GAAG;QACrBxC,SAAS,EAAEA,SAAS;QACpBpB,GAAG,EAAE,KAAK;QACVpI,KAAK,EAAEV,KAAK,CAACD,KAAK,CAACW;MACrB,CAAC;MACD,IAAIV,KAAK,CAACD,KAAK,CAAC4M,OAAO,EAAE;QACvBb,SAAS,GAAG;UACV5B,SAAS,EAAE;QACb,CAAC;QACDwC,gBAAgB,GAAG;UACjBxC,SAAS,EAAEA,SAAS;UACpBxJ,KAAK,EAAEV,KAAK,CAACD,KAAK,CAACW;QACrB,CAAC;MACH;MACA,OAAO,aAAarC,KAAK,CAAC6M,aAAa,CAAC,KAAK,EAAEwB,gBAAgB,EAAE,CAAC1M,KAAK,CAACD,KAAK,CAAC4M,OAAO,GAAGxB,SAAS,GAAG,EAAE,EAAE,aAAa9M,KAAK,CAAC6M,aAAa,CAAC,KAAK,EAAEhO,QAAQ,CAAC;QACvJ+C,GAAG,EAAED,KAAK,CAAC4M;MACb,CAAC,EAAEd,SAAS,CAAC,EAAE,aAAazN,KAAK,CAAC6M,aAAa,CAAC1L,KAAK,EAAEtC,QAAQ,CAAC;QAC9D+C,GAAG,EAAED,KAAK,CAAC6M;MACb,CAAC,EAAEzC,UAAU,CAAC,EAAEpK,KAAK,CAACD,KAAK,CAACqE,QAAQ,CAAC,CAAC,EAAE,CAACpE,KAAK,CAACD,KAAK,CAAC4M,OAAO,GAAGvB,SAAS,GAAG,EAAE,EAAE,CAACpL,KAAK,CAACD,KAAK,CAAC4M,OAAO,GAAG/B,IAAI,GAAG,EAAE,CAAC;IAClH,CAAC,CAAC;IACF5K,KAAK,CAACE,IAAI,GAAG,IAAI;IACjBF,KAAK,CAACG,KAAK,GAAG,IAAI;IAClBH,KAAK,CAACQ,KAAK,GAAGpD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/DmC,YAAY,EAAET,KAAK,CAACD,KAAK,CAAC+M,YAAY;MACtCtE,WAAW,EAAExI,KAAK,CAACD,KAAK,CAAC+M,YAAY,GAAG9M,KAAK,CAACD,KAAK,CAAC+M,YAAY,GAAG,CAAC;MACpEjH,UAAU,EAAExH,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACnE,KAAK,CAACD,KAAK,CAACqE,QAAQ;IACvD,CAAC,CAAC;IACFpE,KAAK,CAACiC,cAAc,GAAG,EAAE;IACzBjC,KAAK,CAAC2I,SAAS,GAAG,IAAI;IACtB3I,KAAK,CAAC0E,eAAe,GAAG,IAAI;IAC5B,IAAIqI,QAAQ,GAAG/M,KAAK,CAACgN,OAAO,CAAC,CAAC;IAC9BhN,KAAK,CAACQ,KAAK,GAAGpD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAACQ,KAAK,CAAC,EAAEuM,QAAQ,CAAC;IACrE,OAAO/M,KAAK;EACd;EACAtC,SAAS,CAACmC,WAAW,EAAEC,gBAAgB,CAAC;EACxC,OAAOxC,YAAY,CAACuC,WAAW,EAAE,CAAC;IAChCoN,GAAG,EAAE,gBAAgB;IACrB/E,KAAK,EAAE,SAASjE,cAAcA,CAACJ,SAAS,EAAE;MACxC,IAAIG,aAAa,GAAG,KAAK;MACzB,KAAK,IAAIkJ,GAAG,GAAG,CAAC,EAAEC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtN,KAAK,CAAC,EAAEmN,GAAG,GAAGC,YAAY,CAACpM,MAAM,EAAEmM,GAAG,EAAE,EAAE;QAC1F,IAAID,GAAG,GAAGE,YAAY,CAACD,GAAG,CAAC;QAC3B;QACA,IAAI,CAACrJ,SAAS,CAACyJ,cAAc,CAACL,GAAG,CAAC,EAAE;UAClCjJ,aAAa,GAAG,IAAI;UACpB;QACF;QACA,IAAI/G,OAAO,CAAC4G,SAAS,CAACoJ,GAAG,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOpJ,SAAS,CAACoJ,GAAG,CAAC,KAAK,UAAU,IAAItD,KAAK,CAAC9F,SAAS,CAACoJ,GAAG,CAAC,CAAC,EAAE;UACzG;QACF;QACA,IAAIpJ,SAAS,CAACoJ,GAAG,CAAC,KAAK,IAAI,CAAClN,KAAK,CAACkN,GAAG,CAAC,EAAE;UACtCjJ,aAAa,GAAG,IAAI;UACpB;QACF;MACF;MACA,OAAOA,aAAa,IAAI3F,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAAC,IAAI,CAACpE,KAAK,CAACqE,QAAQ,CAAC,KAAK/F,KAAK,CAAC6F,QAAQ,CAACC,KAAK,CAACN,SAAS,CAACO,QAAQ,CAAC;IAChH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC/F,KAAK,CAACkP,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\nimport DefaultPanel from \"./DefaultPanel\";\nvar TourStep = function TourStep(props) {\n  var current = props.current,\n    renderPanel = props.renderPanel;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, typeof renderPanel === 'function' ? renderPanel(props, current) : /*#__PURE__*/React.createElement(DefaultPanel, props));\n};\nexport default TourStep;", "map": {"version": 3, "names": ["React", "DefaultPanel", "TourStep", "props", "current", "renderPanel", "createElement", "Fragment"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+tour@1.15.1_r_14d992225ad622c42cfc1bfce16fe219/node_modules/@rc-component/tour/es/TourStep/index.js"], "sourcesContent": ["import * as React from 'react';\nimport DefaultPanel from \"./DefaultPanel\";\nvar TourStep = function TourStep(props) {\n  var current = props.current,\n    renderPanel = props.renderPanel;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, typeof renderPanel === 'function' ? renderPanel(props, current) : /*#__PURE__*/React.createElement(DefaultPanel, props));\n};\nexport default TourStep;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACzBC,WAAW,GAAGF,KAAK,CAACE,WAAW;EACjC,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACN,KAAK,CAACO,QAAQ,EAAE,IAAI,EAAE,OAAOF,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC,GAAG,aAAaJ,KAAK,CAACM,aAAa,CAACL,YAAY,EAAEE,KAAK,CAAC,CAAC;AACxL,CAAC;AACD,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
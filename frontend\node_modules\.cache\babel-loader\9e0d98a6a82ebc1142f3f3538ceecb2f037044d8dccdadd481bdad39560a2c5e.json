{"ast": null, "code": "import * as React from 'react';\nimport SliderContext from \"../context\";\nimport Dot from \"./Dot\";\nvar Steps = function Steps(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    dots = props.dots,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    step = _React$useContext.step;\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set();\n\n    // Add marks\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    });\n\n    // Fill dots\n    if (dots && step !== null) {\n      var current = min;\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n};\nexport default Steps;", "map": {"version": 3, "names": ["React", "SliderContext", "Dot", "Steps", "props", "prefixCls", "marks", "dots", "style", "activeStyle", "_React$useContext", "useContext", "min", "max", "step", "stepDots", "useMemo", "dotSet", "Set", "for<PERSON>ach", "mark", "add", "value", "current", "Array", "from", "createElement", "className", "concat", "map", "dotValue", "key"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-slider@11.1.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-slider/es/Steps/index.js"], "sourcesContent": ["import * as React from 'react';\nimport SliderContext from \"../context\";\nimport Dot from \"./Dot\";\nvar Steps = function Steps(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    dots = props.dots,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    step = _React$useContext.step;\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set();\n\n    // Add marks\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    });\n\n    // Fill dots\n    if (dots && step !== null) {\n      var current = min;\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n};\nexport default Steps;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,OAAOC,GAAG,MAAM,OAAO;AACvB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,WAAW,GAAGL,KAAK,CAACK,WAAW;EACjC,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,aAAa,CAAC;IACrDW,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;EAC/B,IAAIC,QAAQ,GAAGf,KAAK,CAACgB,OAAO,CAAC,YAAY;IACvC,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAEtB;IACAZ,KAAK,CAACa,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC5BH,MAAM,CAACI,GAAG,CAACD,IAAI,CAACE,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAIf,IAAI,IAAIO,IAAI,KAAK,IAAI,EAAE;MACzB,IAAIS,OAAO,GAAGX,GAAG;MACjB,OAAOW,OAAO,IAAIV,GAAG,EAAE;QACrBI,MAAM,CAACI,GAAG,CAACE,OAAO,CAAC;QACnBA,OAAO,IAAIT,IAAI;MACjB;IACF;IACA,OAAOU,KAAK,CAACC,IAAI,CAACR,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACL,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEP,IAAI,EAAED,KAAK,CAAC,CAAC;EACjC,OAAO,aAAaN,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,OAAO;EACzC,CAAC,EAAEU,QAAQ,CAACc,GAAG,CAAC,UAAUC,QAAQ,EAAE;IAClC,OAAO,aAAa9B,KAAK,CAAC0B,aAAa,CAACxB,GAAG,EAAE;MAC3CG,SAAS,EAAEA,SAAS;MACpB0B,GAAG,EAAED,QAAQ;MACbR,KAAK,EAAEQ,QAAQ;MACftB,KAAK,EAAEA,KAAK;MACZC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
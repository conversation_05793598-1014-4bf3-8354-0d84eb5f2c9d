{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport List from 'rc-virtual-list';\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport SelectContext from \"./SelectContext\";\nimport TransBtn from \"./TransBtn\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\nimport { isValidCount } from \"./utils/valueUtil\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    maxCount = _React$useContext.maxCount,\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    direction = _React$useContext.direction,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    optionRender = _React$useContext.optionRender;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var overMaxCount = React.useMemo(function () {\n    return multiple && isValidCount(maxCount) && (rawValues === null || rawValues === void 0 ? void 0 : rawValues.size) >= maxCount;\n  }, [multiple, maxCount, rawValues === null || rawValues === void 0 ? void 0 : rawValues.size]);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    var _listRef$current;\n    (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(typeof args === 'number' ? {\n      index: args\n    } : args);\n  };\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return false;\n    }\n    return rawValues.has(value);\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _ref = memoFlattenOptions[current] || {},\n        group = _ref.group,\n        data = _ref.data;\n      if (!group && !(data !== null && data !== void 0 && data.disabled) && (isSelected(data.value) || !overMaxCount)) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/48036\n  var isAriaSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return String(value).toLowerCase() === searchValue.toLowerCase();\n    }\n    return rawValues.has(value);\n  }, [mode, searchValue, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        // Scroll to the option closest to the searchValue if searching.\n        var index = memoFlattenOptions.findIndex(function (_ref2) {\n          var data = _ref2.data;\n          return searchValue ? String(data.value).startsWith(searchValue) : data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 || _listRef$current2.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select (Tab / Enter)\n          case KeyCode.TAB:\n          case KeyCode.ENTER:\n            {\n              var _item$data;\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !(item !== null && item !== void 0 && (_item$data = item.data) !== null && _item$data !== void 0 && _item$data.disabled) && !overMaxCount) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  function getItemAriaProps(item, index) {\n    var group = item.group;\n    return {\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index)\n    };\n  }\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) {\n      return null;\n    }\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index\n    }, getItemAriaProps(item, index), {\n      \"aria-selected\": isAriaSelected(value)\n    }), value) : null;\n  };\n  var a11yProps = {\n    role: 'listbox',\n    id: \"\".concat(id, \"_list\")\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, virtual && /*#__PURE__*/React.createElement(\"div\", _extends({}, a11yProps, {\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }), renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual,\n    direction: direction,\n    innerProps: virtual ? null : a11yProps\n  }, function (item, itemIndex) {\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\"), data.className),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var mergedDisabled = disabled || !selected && overMaxCount;\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !mergedDisabled), \"\".concat(optionPrefixCls, \"-disabled\"), mergedDisabled), \"\".concat(optionPrefixCls, \"-selected\"), selected));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), !virtual ? getItemAriaProps(item, itemIndex) : {}, {\n      \"aria-selected\": isAriaSelected(value),\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || mergedDisabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!mergedDisabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, typeof optionRender === 'function' ? optionRender(item, {\n      index: itemIndex\n    }) : content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        value: value,\n        disabled: mergedDisabled,\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;", "map": {"version": 3, "names": ["_defineProperty", "_objectWithoutProperties", "_extends", "_slicedToArray", "_toConsumableArray", "_excluded", "classNames", "KeyCode", "useMemo", "omit", "pickAttrs", "List", "React", "useEffect", "SelectContext", "TransBtn", "useBaseProps", "isPlatformMac", "isValidCount", "isTitleType", "content", "OptionList", "_", "ref", "_useBaseProps", "prefixCls", "id", "open", "multiple", "mode", "searchValue", "toggle<PERSON><PERSON>", "notFoundContent", "onPopupScroll", "_React$useContext", "useContext", "maxCount", "flattenOptions", "onActiveValue", "defaultActiveFirstOption", "onSelect", "menuItemSelectedIcon", "rawValues", "fieldNames", "virtual", "direction", "listHeight", "listItemHeight", "optionRender", "itemPrefixCls", "concat", "memoFlattenOptions", "prev", "next", "listRef", "useRef", "overMaxCount", "size", "onListMouseDown", "event", "preventDefault", "scrollIntoView", "args", "_listRef$current", "current", "scrollTo", "index", "isSelected", "useCallback", "value", "has", "toString", "getEnabledActiveIndex", "offset", "arguments", "length", "undefined", "len", "i", "_ref", "group", "data", "disabled", "_React$useState", "useState", "_React$useState2", "activeIndex", "setActiveIndex", "setActive", "fromKeyboard", "info", "source", "flattenItem", "isAriaSelected", "String", "toLowerCase", "timeoutId", "setTimeout", "Array", "from", "findIndex", "_ref2", "startsWith", "_listRef$current2", "clearTimeout", "onSelectValue", "selected", "useImperativeHandle", "onKeyDown", "which", "ctrl<PERSON>ey", "N", "P", "UP", "DOWN", "nextActiveIndex", "TAB", "ENTER", "_item$data", "item", "ESC", "stopPropagation", "onKeyUp", "createElement", "role", "className", "onMouseDown", "omitFieldNameList", "Object", "keys", "map", "key", "get<PERSON><PERSON><PERSON>", "label", "getItemAriaProps", "renderItem", "itemData", "attrs", "mergedLabel", "a11yProps", "Fragment", "style", "height", "width", "overflow", "itemKey", "itemHeight", "fullHeight", "onScroll", "innerProps", "itemIndex", "groupOption", "_data$title", "groupTitle", "title", "children", "otherProps", "passedProps", "mergedDisabled", "optionPrefixCls", "optionClassName", "iconVisible", "optionTitle", "onMouseMove", "onClick", "isValidElement", "customizeIcon", "customizeIconProps", "RefOptionList", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/OptionList.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport List from 'rc-virtual-list';\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport SelectContext from \"./SelectContext\";\nimport TransBtn from \"./TransBtn\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\nimport { isValidCount } from \"./utils/valueUtil\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    maxCount = _React$useContext.maxCount,\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    direction = _React$useContext.direction,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    optionRender = _React$useContext.optionRender;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var overMaxCount = React.useMemo(function () {\n    return multiple && isValidCount(maxCount) && (rawValues === null || rawValues === void 0 ? void 0 : rawValues.size) >= maxCount;\n  }, [multiple, maxCount, rawValues === null || rawValues === void 0 ? void 0 : rawValues.size]);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    var _listRef$current;\n    (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(typeof args === 'number' ? {\n      index: args\n    } : args);\n  };\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return false;\n    }\n    return rawValues.has(value);\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _ref = memoFlattenOptions[current] || {},\n        group = _ref.group,\n        data = _ref.data;\n      if (!group && !(data !== null && data !== void 0 && data.disabled) && (isSelected(data.value) || !overMaxCount)) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/48036\n  var isAriaSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return String(value).toLowerCase() === searchValue.toLowerCase();\n    }\n    return rawValues.has(value);\n  }, [mode, searchValue, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        // Scroll to the option closest to the searchValue if searching.\n        var index = memoFlattenOptions.findIndex(function (_ref2) {\n          var data = _ref2.data;\n          return searchValue ? String(data.value).startsWith(searchValue) : data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 || _listRef$current2.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select (Tab / Enter)\n          case KeyCode.TAB:\n          case KeyCode.ENTER:\n            {\n              var _item$data;\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !(item !== null && item !== void 0 && (_item$data = item.data) !== null && _item$data !== void 0 && _item$data.disabled) && !overMaxCount) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  function getItemAriaProps(item, index) {\n    var group = item.group;\n    return {\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index)\n    };\n  }\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) {\n      return null;\n    }\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index\n    }, getItemAriaProps(item, index), {\n      \"aria-selected\": isAriaSelected(value)\n    }), value) : null;\n  };\n  var a11yProps = {\n    role: 'listbox',\n    id: \"\".concat(id, \"_list\")\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, virtual && /*#__PURE__*/React.createElement(\"div\", _extends({}, a11yProps, {\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }), renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual,\n    direction: direction,\n    innerProps: virtual ? null : a11yProps\n  }, function (item, itemIndex) {\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\"), data.className),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var mergedDisabled = disabled || !selected && overMaxCount;\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !mergedDisabled), \"\".concat(optionPrefixCls, \"-disabled\"), mergedDisabled), \"\".concat(optionPrefixCls, \"-selected\"), selected));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), !virtual ? getItemAriaProps(item, itemIndex) : {}, {\n      \"aria-selected\": isAriaSelected(value),\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || mergedDisabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!mergedDisabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, typeof optionRender === 'function' ? optionRender(item, {\n      index: itemIndex\n    }) : content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        value: value,\n        disabled: mergedDisabled,\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;;AAEA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,OAAO,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ;AACnE;;AAEA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC3C,IAAIC,aAAa,GAAGR,YAAY,CAAC,CAAC;IAChCS,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,EAAE,GAAGF,aAAa,CAACE,EAAE;IACrBC,IAAI,GAAGH,aAAa,CAACG,IAAI;IACzBC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;IACjCC,IAAI,GAAGL,aAAa,CAACK,IAAI;IACzBC,WAAW,GAAGN,aAAa,CAACM,WAAW;IACvCC,UAAU,GAAGP,aAAa,CAACO,UAAU;IACrCC,eAAe,GAAGR,aAAa,CAACQ,eAAe;IAC/CC,aAAa,GAAGT,aAAa,CAACS,aAAa;EAC7C,IAAIC,iBAAiB,GAAGtB,KAAK,CAACuB,UAAU,CAACrB,aAAa,CAAC;IACrDsB,QAAQ,GAAGF,iBAAiB,CAACE,QAAQ;IACrCC,cAAc,GAAGH,iBAAiB,CAACG,cAAc;IACjDC,aAAa,GAAGJ,iBAAiB,CAACI,aAAa;IAC/CC,wBAAwB,GAAGL,iBAAiB,CAACK,wBAAwB;IACrEC,QAAQ,GAAGN,iBAAiB,CAACM,QAAQ;IACrCC,oBAAoB,GAAGP,iBAAiB,CAACO,oBAAoB;IAC7DC,SAAS,GAAGR,iBAAiB,CAACQ,SAAS;IACvCC,UAAU,GAAGT,iBAAiB,CAACS,UAAU;IACzCC,OAAO,GAAGV,iBAAiB,CAACU,OAAO;IACnCC,SAAS,GAAGX,iBAAiB,CAACW,SAAS;IACvCC,UAAU,GAAGZ,iBAAiB,CAACY,UAAU;IACzCC,cAAc,GAAGb,iBAAiB,CAACa,cAAc;IACjDC,YAAY,GAAGd,iBAAiB,CAACc,YAAY;EAC/C,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAI0B,kBAAkB,GAAG3C,OAAO,CAAC,YAAY;IAC3C,OAAO6B,cAAc;EACvB,CAAC,EAAE,CAACV,IAAI,EAAEU,cAAc,CAAC,EAAE,UAAUe,IAAI,EAAEC,IAAI,EAAE;IAC/C,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,OAAO,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,YAAY,GAAG5C,KAAK,CAACJ,OAAO,CAAC,YAAY;IAC3C,OAAOoB,QAAQ,IAAIV,YAAY,CAACkB,QAAQ,CAAC,IAAI,CAACM,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACe,IAAI,KAAKrB,QAAQ;EACjI,CAAC,EAAE,CAACR,QAAQ,EAAEQ,QAAQ,EAAEM,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACe,IAAI,CAAC,CAAC;EAC9F,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpDA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;IACjD,IAAIC,gBAAgB;IACpB,CAACA,gBAAgB,GAAGT,OAAO,CAACU,OAAO,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACE,QAAQ,CAAC,OAAOH,IAAI,KAAK,QAAQ,GAAG;MACnII,KAAK,EAAEJ;IACT,CAAC,GAAGA,IAAI,CAAC;EACX,CAAC;;EAED;EACA,IAAIK,UAAU,GAAGvD,KAAK,CAACwD,WAAW,CAAC,UAAUC,KAAK,EAAE;IAClD,IAAIxC,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO,KAAK;IACd;IACA,OAAOa,SAAS,CAAC4B,GAAG,CAACD,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACxC,IAAI,EAAEzB,kBAAkB,CAACsC,SAAS,CAAC,CAAC6B,QAAQ,CAAC,CAAC,EAAE7B,SAAS,CAACe,IAAI,CAAC,CAAC;;EAEpE;EACA,IAAIe,qBAAqB,GAAG,SAASA,qBAAqBA,CAACN,KAAK,EAAE;IAChE,IAAIO,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIG,GAAG,GAAG1B,kBAAkB,CAACwB,MAAM;IACnC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAId,OAAO,GAAG,CAACE,KAAK,GAAGY,CAAC,GAAGL,MAAM,GAAGI,GAAG,IAAIA,GAAG;MAC9C,IAAIE,IAAI,GAAG5B,kBAAkB,CAACa,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1CgB,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAClB,IAAI,CAACD,KAAK,IAAI,EAAEC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,CAACC,QAAQ,CAAC,KAAKf,UAAU,CAACc,IAAI,CAACZ,KAAK,CAAC,IAAI,CAACb,YAAY,CAAC,EAAE;QAC/G,OAAOQ,OAAO;MAChB;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,IAAImB,eAAe,GAAGvE,KAAK,CAACwE,QAAQ,CAAC,YAAY;MAC7C,OAAOZ,qBAAqB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC;IACFa,gBAAgB,GAAGlF,cAAc,CAACgF,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACtB,KAAK,EAAE;IACxC,IAAIuB,YAAY,GAAGf,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5Fa,cAAc,CAACrB,KAAK,CAAC;IACrB,IAAIwB,IAAI,GAAG;MACTC,MAAM,EAAEF,YAAY,GAAG,UAAU,GAAG;IACtC,CAAC;;IAED;IACA,IAAIG,WAAW,GAAGzC,kBAAkB,CAACe,KAAK,CAAC;IAC3C,IAAI,CAAC0B,WAAW,EAAE;MAChBtD,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEoD,IAAI,CAAC;MAC7B;IACF;IACApD,aAAa,CAACsD,WAAW,CAACvB,KAAK,EAAEH,KAAK,EAAEwB,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA7E,SAAS,CAAC,YAAY;IACpB2E,SAAS,CAACjD,wBAAwB,KAAK,KAAK,GAAGiC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACrB,kBAAkB,CAACwB,MAAM,EAAE7C,WAAW,CAAC,CAAC;;EAE5C;EACA,IAAI+D,cAAc,GAAGjF,KAAK,CAACwD,WAAW,CAAC,UAAUC,KAAK,EAAE;IACtD,IAAIxC,IAAI,KAAK,UAAU,EAAE;MACvB,OAAOiE,MAAM,CAACzB,KAAK,CAAC,CAAC0B,WAAW,CAAC,CAAC,KAAKjE,WAAW,CAACiE,WAAW,CAAC,CAAC;IAClE;IACA,OAAOrD,SAAS,CAAC4B,GAAG,CAACD,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACxC,IAAI,EAAEC,WAAW,EAAE1B,kBAAkB,CAACsC,SAAS,CAAC,CAAC6B,QAAQ,CAAC,CAAC,EAAE7B,SAAS,CAACe,IAAI,CAAC,CAAC;;EAEjF;EACA5C,SAAS,CAAC,YAAY;IACpB;AACJ;AACA;AACA;AACA;IACI,IAAImF,SAAS,GAAGC,UAAU,CAAC,YAAY;MACrC,IAAI,CAACrE,QAAQ,IAAID,IAAI,IAAIe,SAAS,CAACe,IAAI,KAAK,CAAC,EAAE;QAC7C,IAAIY,KAAK,GAAG6B,KAAK,CAACC,IAAI,CAACzD,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC;QACA,IAAIwB,KAAK,GAAGf,kBAAkB,CAACiD,SAAS,CAAC,UAAUC,KAAK,EAAE;UACxD,IAAIpB,IAAI,GAAGoB,KAAK,CAACpB,IAAI;UACrB,OAAOnD,WAAW,GAAGgE,MAAM,CAACb,IAAI,CAACZ,KAAK,CAAC,CAACiC,UAAU,CAACxE,WAAW,CAAC,GAAGmD,IAAI,CAACZ,KAAK,KAAKA,KAAK;QACxF,CAAC,CAAC;QACF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBsB,SAAS,CAACtB,KAAK,CAAC;UAChBL,cAAc,CAACK,KAAK,CAAC;QACvB;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIvC,IAAI,EAAE;MACR,IAAI4E,iBAAiB;MACrB,CAACA,iBAAiB,GAAGjD,OAAO,CAACU,OAAO,MAAM,IAAI,IAAIuC,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACtC,QAAQ,CAACW,SAAS,CAAC;IACzH;IACA,OAAO,YAAY;MACjB,OAAO4B,YAAY,CAACR,SAAS,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACrE,IAAI,EAAEG,WAAW,CAAC,CAAC;;EAEvB;EACA,IAAI2E,aAAa,GAAG,SAASA,aAAaA,CAACpC,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAKO,SAAS,EAAE;MACvBpC,QAAQ,CAAC6B,KAAK,EAAE;QACdqC,QAAQ,EAAE,CAAChE,SAAS,CAAC4B,GAAG,CAACD,KAAK;MAChC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACzC,QAAQ,EAAE;MACbG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAnB,KAAK,CAAC+F,mBAAmB,CAACpF,GAAG,EAAE,YAAY;IACzC,OAAO;MACLqF,SAAS,EAAE,SAASA,SAASA,CAACjD,KAAK,EAAE;QACnC,IAAIkD,KAAK,GAAGlD,KAAK,CAACkD,KAAK;UACrBC,OAAO,GAAGnD,KAAK,CAACmD,OAAO;QACzB,QAAQD,KAAK;UACX;UACA,KAAKtG,OAAO,CAACwG,CAAC;UACd,KAAKxG,OAAO,CAACyG,CAAC;UACd,KAAKzG,OAAO,CAAC0G,EAAE;UACf,KAAK1G,OAAO,CAAC2G,IAAI;YACf;cACE,IAAIzC,MAAM,GAAG,CAAC;cACd,IAAIoC,KAAK,KAAKtG,OAAO,CAAC0G,EAAE,EAAE;gBACxBxC,MAAM,GAAG,CAAC,CAAC;cACb,CAAC,MAAM,IAAIoC,KAAK,KAAKtG,OAAO,CAAC2G,IAAI,EAAE;gBACjCzC,MAAM,GAAG,CAAC;cACZ,CAAC,MAAM,IAAIxD,aAAa,CAAC,CAAC,IAAI6F,OAAO,EAAE;gBACrC,IAAID,KAAK,KAAKtG,OAAO,CAACwG,CAAC,EAAE;kBACvBtC,MAAM,GAAG,CAAC;gBACZ,CAAC,MAAM,IAAIoC,KAAK,KAAKtG,OAAO,CAACyG,CAAC,EAAE;kBAC9BvC,MAAM,GAAG,CAAC,CAAC;gBACb;cACF;cACA,IAAIA,MAAM,KAAK,CAAC,EAAE;gBAChB,IAAI0C,eAAe,GAAG3C,qBAAqB,CAACc,WAAW,GAAGb,MAAM,EAAEA,MAAM,CAAC;gBACzEZ,cAAc,CAACsD,eAAe,CAAC;gBAC/B3B,SAAS,CAAC2B,eAAe,EAAE,IAAI,CAAC;cAClC;cACA;YACF;;UAEF;UACA,KAAK5G,OAAO,CAAC6G,GAAG;UAChB,KAAK7G,OAAO,CAAC8G,KAAK;YAChB;cACE,IAAIC,UAAU;cACd;cACA,IAAIC,IAAI,GAAGpE,kBAAkB,CAACmC,WAAW,CAAC;cAC1C,IAAIiC,IAAI,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAI,CAACD,UAAU,GAAGC,IAAI,CAACtC,IAAI,MAAM,IAAI,IAAIqC,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACpC,QAAQ,CAAC,IAAI,CAAC1B,YAAY,EAAE;gBACrJiD,aAAa,CAACc,IAAI,CAAClD,KAAK,CAAC;cAC3B,CAAC,MAAM;gBACLoC,aAAa,CAAC7B,SAAS,CAAC;cAC1B;cACA,IAAIjD,IAAI,EAAE;gBACRgC,KAAK,CAACC,cAAc,CAAC,CAAC;cACxB;cACA;YACF;;UAEF;UACA,KAAKrD,OAAO,CAACiH,GAAG;YACd;cACEzF,UAAU,CAAC,KAAK,CAAC;cACjB,IAAIJ,IAAI,EAAE;gBACRgC,KAAK,CAAC8D,eAAe,CAAC,CAAC;cACzB;YACF;QACJ;MACF,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;MAC9BzD,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;QACjCL,cAAc,CAACK,KAAK,CAAC;MACvB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIf,kBAAkB,CAACwB,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO,aAAa/D,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAE;MAC7CC,IAAI,EAAE,SAAS;MACflG,EAAE,EAAE,EAAE,CAACwB,MAAM,CAACxB,EAAE,EAAE,OAAO,CAAC;MAC1BmG,SAAS,EAAE,EAAE,CAAC3E,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC;MAC7C6E,WAAW,EAAEpE;IACf,CAAC,EAAE1B,eAAe,CAAC;EACrB;EACA,IAAI+F,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACtF,UAAU,CAAC,CAACuF,GAAG,CAAC,UAAUC,GAAG,EAAE;IACjE,OAAOxF,UAAU,CAACwF,GAAG,CAAC;EACxB,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACb,IAAI,EAAE;IACrC,OAAOA,IAAI,CAACc,KAAK;EACnB,CAAC;EACD,SAASC,gBAAgBA,CAACf,IAAI,EAAErD,KAAK,EAAE;IACrC,IAAIc,KAAK,GAAGuC,IAAI,CAACvC,KAAK;IACtB,OAAO;MACL4C,IAAI,EAAE5C,KAAK,GAAG,cAAc,GAAG,QAAQ;MACvCtD,EAAE,EAAE,EAAE,CAACwB,MAAM,CAACxB,EAAE,EAAE,QAAQ,CAAC,CAACwB,MAAM,CAACgB,KAAK;IAC1C,CAAC;EACH;EACA,IAAIqE,UAAU,GAAG,SAASA,UAAUA,CAACrE,KAAK,EAAE;IAC1C,IAAIqD,IAAI,GAAGpE,kBAAkB,CAACe,KAAK,CAAC;IACpC,IAAI,CAACqD,IAAI,EAAE;MACT,OAAO,IAAI;IACb;IACA,IAAIiB,QAAQ,GAAGjB,IAAI,CAACtC,IAAI,IAAI,CAAC,CAAC;IAC9B,IAAIZ,KAAK,GAAGmE,QAAQ,CAACnE,KAAK;IAC1B,IAAIW,KAAK,GAAGuC,IAAI,CAACvC,KAAK;IACtB,IAAIyD,KAAK,GAAG/H,SAAS,CAAC8H,QAAQ,EAAE,IAAI,CAAC;IACrC,IAAIE,WAAW,GAAGN,QAAQ,CAACb,IAAI,CAAC;IAChC,OAAOA,IAAI,GAAG,aAAa3G,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAEzH,QAAQ,CAAC;MAC7D,YAAY,EAAE,OAAOwI,WAAW,KAAK,QAAQ,IAAI,CAAC1D,KAAK,GAAG0D,WAAW,GAAG;IAC1E,CAAC,EAAED,KAAK,EAAE;MACRN,GAAG,EAAEjE;IACP,CAAC,EAAEoE,gBAAgB,CAACf,IAAI,EAAErD,KAAK,CAAC,EAAE;MAChC,eAAe,EAAE2B,cAAc,CAACxB,KAAK;IACvC,CAAC,CAAC,EAAEA,KAAK,CAAC,GAAG,IAAI;EACnB,CAAC;EACD,IAAIsE,SAAS,GAAG;IACdf,IAAI,EAAE,SAAS;IACflG,EAAE,EAAE,EAAE,CAACwB,MAAM,CAACxB,EAAE,EAAE,OAAO;EAC3B,CAAC;EACD,OAAO,aAAad,KAAK,CAAC+G,aAAa,CAAC/G,KAAK,CAACgI,QAAQ,EAAE,IAAI,EAAEhG,OAAO,IAAI,aAAahC,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAEzH,QAAQ,CAAC,CAAC,CAAC,EAAEyI,SAAS,EAAE;IACvIE,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,EAAET,UAAU,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAEiD,UAAU,CAACjD,WAAW,CAAC,EAAEiD,UAAU,CAACjD,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa1E,KAAK,CAAC+G,aAAa,CAAChH,IAAI,EAAE;IAC7HsI,OAAO,EAAE,KAAK;IACd1H,GAAG,EAAE+B,OAAO;IACZ2B,IAAI,EAAE9B,kBAAkB;IACxB2F,MAAM,EAAEhG,UAAU;IAClBoG,UAAU,EAAEnG,cAAc;IAC1BoG,UAAU,EAAE,KAAK;IACjBrB,WAAW,EAAEpE,eAAe;IAC5B0F,QAAQ,EAAEnH,aAAa;IACvBW,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBwG,UAAU,EAAEzG,OAAO,GAAG,IAAI,GAAG+F;EAC/B,CAAC,EAAE,UAAUpB,IAAI,EAAE+B,SAAS,EAAE;IAC5B,IAAItE,KAAK,GAAGuC,IAAI,CAACvC,KAAK;MACpBuE,WAAW,GAAGhC,IAAI,CAACgC,WAAW;MAC9BtE,IAAI,GAAGsC,IAAI,CAACtC,IAAI;MAChBoD,KAAK,GAAGd,IAAI,CAACc,KAAK;MAClBhE,KAAK,GAAGkD,IAAI,CAAClD,KAAK;IACpB,IAAI8D,GAAG,GAAGlD,IAAI,CAACkD,GAAG;;IAElB;IACA,IAAInD,KAAK,EAAE;MACT,IAAIwE,WAAW;MACf,IAAIC,UAAU,GAAG,CAACD,WAAW,GAAGvE,IAAI,CAACyE,KAAK,MAAM,IAAI,IAAIF,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGrI,WAAW,CAACkH,KAAK,CAAC,GAAGA,KAAK,CAAC9D,QAAQ,CAAC,CAAC,GAAGK,SAAS;MAChJ,OAAO,aAAahE,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAE;QAC7CE,SAAS,EAAEvH,UAAU,CAAC2C,aAAa,EAAE,EAAE,CAACC,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC,EAAEgC,IAAI,CAAC4C,SAAS,CAAC;QACxF6B,KAAK,EAAED;MACT,CAAC,EAAEpB,KAAK,KAAKzD,SAAS,GAAGyD,KAAK,GAAGF,GAAG,CAAC;IACvC;IACA,IAAIjD,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC1BwE,KAAK,GAAGzE,IAAI,CAACyE,KAAK;MAClBC,QAAQ,GAAG1E,IAAI,CAAC0E,QAAQ;MACxBd,KAAK,GAAG5D,IAAI,CAAC4D,KAAK;MAClBhB,SAAS,GAAG5C,IAAI,CAAC4C,SAAS;MAC1B+B,UAAU,GAAG3J,wBAAwB,CAACgF,IAAI,EAAE5E,SAAS,CAAC;IACxD,IAAIwJ,WAAW,GAAGpJ,IAAI,CAACmJ,UAAU,EAAE7B,iBAAiB,CAAC;;IAErD;IACA,IAAIrB,QAAQ,GAAGvC,UAAU,CAACE,KAAK,CAAC;IAChC,IAAIyF,cAAc,GAAG5E,QAAQ,IAAI,CAACwB,QAAQ,IAAIlD,YAAY;IAC1D,IAAIuG,eAAe,GAAG,EAAE,CAAC7G,MAAM,CAACD,aAAa,EAAE,SAAS,CAAC;IACzD,IAAI+G,eAAe,GAAG1J,UAAU,CAAC2C,aAAa,EAAE8G,eAAe,EAAElC,SAAS,EAAE7H,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkD,MAAM,CAAC6G,eAAe,EAAE,UAAU,CAAC,EAAER,WAAW,CAAC,EAAE,EAAE,CAACrG,MAAM,CAAC6G,eAAe,EAAE,SAAS,CAAC,EAAEzE,WAAW,KAAKgE,SAAS,IAAI,CAACQ,cAAc,CAAC,EAAE,EAAE,CAAC5G,MAAM,CAAC6G,eAAe,EAAE,WAAW,CAAC,EAAED,cAAc,CAAC,EAAE,EAAE,CAAC5G,MAAM,CAAC6G,eAAe,EAAE,WAAW,CAAC,EAAErD,QAAQ,CAAC,CAAC;IACzY,IAAIgC,WAAW,GAAGN,QAAQ,CAACb,IAAI,CAAC;IAChC,IAAI0C,WAAW,GAAG,CAACxH,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,UAAU,IAAIiE,QAAQ;;IAEjG;IACA,IAAItF,OAAO,GAAG,OAAOsH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,IAAIrE,KAAK;IAClF;IACA,IAAI6F,WAAW,GAAG/I,WAAW,CAACC,OAAO,CAAC,GAAGA,OAAO,CAACmD,QAAQ,CAAC,CAAC,GAAGK,SAAS;IACvE,IAAI8E,KAAK,KAAK9E,SAAS,EAAE;MACvBsF,WAAW,GAAGR,KAAK;IACrB;IACA,OAAO,aAAa9I,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAEzH,QAAQ,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAACmJ,WAAW,CAAC,EAAE,CAACjH,OAAO,GAAG0F,gBAAgB,CAACf,IAAI,EAAE+B,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;MACrI,eAAe,EAAEzD,cAAc,CAACxB,KAAK,CAAC;MACtCwD,SAAS,EAAEmC,eAAe;MAC1BN,KAAK,EAAEQ,WAAW;MAClBC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,IAAI7E,WAAW,KAAKgE,SAAS,IAAIQ,cAAc,EAAE;UAC/C;QACF;QACAtE,SAAS,CAAC8D,SAAS,CAAC;MACtB,CAAC;MACDc,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAI,CAACN,cAAc,EAAE;UACnBrD,aAAa,CAACpC,KAAK,CAAC;QACtB;MACF,CAAC;MACDwE,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE,aAAajI,KAAK,CAAC+G,aAAa,CAAC,KAAK,EAAE;MAC1CE,SAAS,EAAE,EAAE,CAAC3E,MAAM,CAAC6G,eAAe,EAAE,UAAU;IAClD,CAAC,EAAE,OAAO/G,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACuE,IAAI,EAAE;MACzDrD,KAAK,EAAEoF;IACT,CAAC,CAAC,GAAGlI,OAAO,CAAC,EAAE,aAAaR,KAAK,CAACyJ,cAAc,CAAC5H,oBAAoB,CAAC,IAAIiE,QAAQ,EAAEuD,WAAW,IAAI,aAAarJ,KAAK,CAAC+G,aAAa,CAAC5G,QAAQ,EAAE;MAC5I8G,SAAS,EAAE,EAAE,CAAC3E,MAAM,CAACD,aAAa,EAAE,eAAe,CAAC;MACpDqH,aAAa,EAAE7H,oBAAoB;MACnC8H,kBAAkB,EAAE;QAClBlG,KAAK,EAAEA,KAAK;QACZa,QAAQ,EAAE4E,cAAc;QACxB3F,UAAU,EAAEuC;MACd;IACF,CAAC,EAAEA,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAI8D,aAAa,GAAG,aAAa5J,KAAK,CAAC6J,UAAU,CAACpJ,UAAU,CAAC;AAC7D,IAAIqJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,aAAa,CAACK,WAAW,GAAG,YAAY;AAC1C;AACA,eAAeL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
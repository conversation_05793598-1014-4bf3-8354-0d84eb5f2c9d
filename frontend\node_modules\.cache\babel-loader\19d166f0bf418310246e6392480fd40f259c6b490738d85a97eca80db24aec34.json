{"ast": null, "code": "import * as React from 'react';\nimport Mark from \"./Mark\";\nvar Marks = function Marks(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    onClick = props.onClick;\n  var markPrefixCls = \"\".concat(prefixCls, \"-mark\");\n\n  // Not render mark if empty\n  if (!marks.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: markPrefixCls\n  }, marks.map(function (_ref) {\n    var value = _ref.value,\n      style = _ref.style,\n      label = _ref.label;\n    return /*#__PURE__*/React.createElement(Mark, {\n      key: value,\n      prefixCls: markPrefixCls,\n      style: style,\n      value: value,\n      onClick: onClick\n    }, label);\n  }));\n};\nexport default Marks;", "map": {"version": 3, "names": ["React", "<PERSON>", "Marks", "props", "prefixCls", "marks", "onClick", "markPrefixCls", "concat", "length", "createElement", "className", "map", "_ref", "value", "style", "label", "key"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-slider@11.1.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-slider/es/Marks/index.js"], "sourcesContent": ["import * as React from 'react';\nimport Mark from \"./Mark\";\nvar Marks = function Marks(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    onClick = props.onClick;\n  var markPrefixCls = \"\".concat(prefixCls, \"-mark\");\n\n  // Not render mark if empty\n  if (!marks.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: markPrefixCls\n  }, marks.map(function (_ref) {\n    var value = _ref.value,\n      style = _ref.style,\n      label = _ref.label;\n    return /*#__PURE__*/React.createElement(Mark, {\n      key: value,\n      prefixCls: markPrefixCls,\n      style: style,\n      value: value,\n      onClick: onClick\n    }, label);\n  }));\n};\nexport default Marks;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,OAAO,GAAGH,KAAK,CAACG,OAAO;EACzB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC;;EAEjD;EACA,IAAI,CAACC,KAAK,CAACI,MAAM,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAaT,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEJ;EACb,CAAC,EAAEF,KAAK,CAACO,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC3B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,KAAK,GAAGF,IAAI,CAACE,KAAK;MAClBC,KAAK,GAAGH,IAAI,CAACG,KAAK;IACpB,OAAO,aAAahB,KAAK,CAACU,aAAa,CAACT,IAAI,EAAE;MAC5CgB,GAAG,EAAEH,KAAK;MACVV,SAAS,EAAEG,aAAa;MACxBQ,KAAK,EAAEA,KAAK;MACZD,KAAK,EAAEA,KAAK;MACZR,OAAO,EAAEA;IACX,CAAC,EAAEU,KAAK,CAAC;EACX,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAed,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
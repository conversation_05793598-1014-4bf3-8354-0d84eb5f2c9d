[{"D:\\code\\erp1\\frontend\\src\\index.js": "1", "D:\\code\\erp1\\frontend\\src\\App.js": "2", "D:\\code\\erp1\\frontend\\src\\stores\\authStore.js": "3", "D:\\code\\erp1\\frontend\\src\\pages\\PurchaseOrders.js": "4", "D:\\code\\erp1\\frontend\\src\\components\\Layout.js": "5", "D:\\code\\erp1\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\code\\erp1\\frontend\\src\\pages\\Customers.js": "7", "D:\\code\\erp1\\frontend\\src\\pages\\Login.js": "8", "D:\\code\\erp1\\frontend\\src\\pages\\Orders.js": "9", "D:\\code\\erp1\\frontend\\src\\pages\\Products.js": "10", "D:\\code\\erp1\\frontend\\src\\pages\\Dashboard.js": "11", "D:\\code\\erp1\\frontend\\src\\pages\\Settings.js": "12", "D:\\code\\erp1\\frontend\\src\\pages\\ProfitAnalysis.js": "13", "D:\\code\\erp1\\frontend\\src\\pages\\Suppliers.js": "14", "D:\\code\\erp1\\frontend\\src\\services\\api.js": "15"}, {"size": 254, "mtime": 1750871911291, "results": "16", "hashOfConfig": "17"}, {"size": 5400, "mtime": 1750869076756, "results": "18", "hashOfConfig": "17"}, {"size": 5883, "mtime": 1750869100542, "results": "19", "hashOfConfig": "17"}, {"size": 326, "mtime": 1750871961589, "results": "20", "hashOfConfig": "17"}, {"size": 6003, "mtime": 1750869153089, "results": "21", "hashOfConfig": "17"}, {"size": 1082, "mtime": 1750869163782, "results": "22", "hashOfConfig": "17"}, {"size": 316, "mtime": 1750871973579, "results": "23", "hashOfConfig": "17"}, {"size": 7461, "mtime": 1750869351113, "results": "24", "hashOfConfig": "17"}, {"size": 310, "mtime": 1750871955561, "results": "25", "hashOfConfig": "17"}, {"size": 314, "mtime": 1750871967739, "results": "26", "hashOfConfig": "17"}, {"size": 1901, "mtime": 1750871948475, "results": "27", "hashOfConfig": "17"}, {"size": 314, "mtime": 1750871994099, "results": "28", "hashOfConfig": "17"}, {"size": 326, "mtime": 1750871987247, "results": "29", "hashOfConfig": "17"}, {"size": 322, "mtime": 1750871981123, "results": "30", "hashOfConfig": "17"}, {"size": 5343, "mtime": 1750869124940, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11qbqjq", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\code\\erp1\\frontend\\src\\index.js", [], [], "D:\\code\\erp1\\frontend\\src\\App.js", [], [], "D:\\code\\erp1\\frontend\\src\\stores\\authStore.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\PurchaseOrders.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\Layout.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Customers.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Login.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Orders.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Products.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Dashboard.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Settings.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\ProfitAnalysis.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Suppliers.js", [], [], "D:\\code\\erp1\\frontend\\src\\services\\api.js", [], []]
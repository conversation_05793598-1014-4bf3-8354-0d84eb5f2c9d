[{"D:\\code\\erp1\\frontend\\src\\index.js": "1", "D:\\code\\erp1\\frontend\\src\\App.js": "2", "D:\\code\\erp1\\frontend\\src\\stores\\authStore.js": "3", "D:\\code\\erp1\\frontend\\src\\pages\\PurchaseOrders.js": "4", "D:\\code\\erp1\\frontend\\src\\components\\Layout.js": "5", "D:\\code\\erp1\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\code\\erp1\\frontend\\src\\pages\\Customers.js": "7", "D:\\code\\erp1\\frontend\\src\\pages\\Login.js": "8", "D:\\code\\erp1\\frontend\\src\\pages\\Orders.js": "9", "D:\\code\\erp1\\frontend\\src\\pages\\Products.js": "10", "D:\\code\\erp1\\frontend\\src\\pages\\Dashboard.js": "11", "D:\\code\\erp1\\frontend\\src\\pages\\Settings.js": "12", "D:\\code\\erp1\\frontend\\src\\pages\\ProfitAnalysis.js": "13", "D:\\code\\erp1\\frontend\\src\\pages\\Suppliers.js": "14", "D:\\code\\erp1\\frontend\\src\\services\\api.js": "15", "D:\\code\\erp1\\frontend\\src\\components\\OrderForm.js": "16", "D:\\code\\erp1\\frontend\\src\\components\\OrderDetail.js": "17"}, {"size": 254, "mtime": 1750871911291, "results": "18", "hashOfConfig": "19"}, {"size": 5410, "mtime": 1750872801769, "results": "20", "hashOfConfig": "19"}, {"size": 5883, "mtime": 1750869100542, "results": "21", "hashOfConfig": "19"}, {"size": 326, "mtime": 1750871961589, "results": "22", "hashOfConfig": "19"}, {"size": 6003, "mtime": 1750869153089, "results": "23", "hashOfConfig": "19"}, {"size": 1082, "mtime": 1750869163782, "results": "24", "hashOfConfig": "19"}, {"size": 316, "mtime": 1750871973579, "results": "25", "hashOfConfig": "19"}, {"size": 7461, "mtime": 1750869351113, "results": "26", "hashOfConfig": "19"}, {"size": 15241, "mtime": 1750872900276, "results": "27", "hashOfConfig": "19"}, {"size": 314, "mtime": 1750871967739, "results": "28", "hashOfConfig": "19"}, {"size": 1901, "mtime": 1750871948475, "results": "29", "hashOfConfig": "19"}, {"size": 314, "mtime": 1750871994099, "results": "30", "hashOfConfig": "19"}, {"size": 326, "mtime": 1750871987247, "results": "31", "hashOfConfig": "19"}, {"size": 322, "mtime": 1750871981123, "results": "32", "hashOfConfig": "19"}, {"size": 5343, "mtime": 1750869124940, "results": "33", "hashOfConfig": "19"}, {"size": 22266, "mtime": 1750873643639, "results": "34", "hashOfConfig": "19"}, {"size": 14226, "mtime": 1750872943741, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11qbqjq", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\code\\erp1\\frontend\\src\\index.js", [], [], "D:\\code\\erp1\\frontend\\src\\App.js", [], [], "D:\\code\\erp1\\frontend\\src\\stores\\authStore.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\PurchaseOrders.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\Layout.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Customers.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Login.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Orders.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Products.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Dashboard.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Settings.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\ProfitAnalysis.js", [], [], "D:\\code\\erp1\\frontend\\src\\pages\\Suppliers.js", [], [], "D:\\code\\erp1\\frontend\\src\\services\\api.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\OrderForm.js", [], [], "D:\\code\\erp1\\frontend\\src\\components\\OrderDetail.js", [], []]
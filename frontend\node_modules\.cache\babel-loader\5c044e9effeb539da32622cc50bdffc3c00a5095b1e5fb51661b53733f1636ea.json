{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useMemo, useState } from 'react';\nimport { isInViewPort } from \"../util\";\nfunction isValidNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val);\n}\nexport default function useTarget(target, open, gap, scrollIntoViewOptions) {\n  // ========================= Target =========================\n  // We trade `undefined` as not get target by function yet.\n  // `null` as empty target.\n  var _useState = useState(undefined),\n    _useState2 = _slicedToArray(_useState, 2),\n    targetElement = _useState2[0],\n    setTargetElement = _useState2[1];\n  useLayoutEffect(function () {\n    var nextElement = typeof target === 'function' ? target() : target;\n    setTargetElement(nextElement || null);\n  });\n\n  // ========================= Align ==========================\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    posInfo = _useState4[0],\n    setPosInfo = _useState4[1];\n  var updatePos = useEvent(function () {\n    if (targetElement) {\n      // Exist target element. We should scroll and get target position\n      if (!isInViewPort(targetElement) && open) {\n        targetElement.scrollIntoView(scrollIntoViewOptions);\n      }\n      var _targetElement$getBou = targetElement.getBoundingClientRect(),\n        left = _targetElement$getBou.left,\n        top = _targetElement$getBou.top,\n        width = _targetElement$getBou.width,\n        height = _targetElement$getBou.height;\n      var nextPosInfo = {\n        left: left,\n        top: top,\n        width: width,\n        height: height,\n        radius: 0\n      };\n      setPosInfo(function (origin) {\n        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {\n          return nextPosInfo;\n        }\n        return origin;\n      });\n    } else {\n      // Not exist target which means we just show in center\n      setPosInfo(null);\n    }\n  });\n  var getGapOffset = function getGapOffset(index) {\n    var _ref;\n    return (_ref = Array.isArray(gap === null || gap === void 0 ? void 0 : gap.offset) ? gap === null || gap === void 0 ? void 0 : gap.offset[index] : gap === null || gap === void 0 ? void 0 : gap.offset) !== null && _ref !== void 0 ? _ref : 6;\n  };\n  useLayoutEffect(function () {\n    updatePos();\n    // update when window resize\n    window.addEventListener('resize', updatePos);\n    return function () {\n      window.removeEventListener('resize', updatePos);\n    };\n  }, [targetElement, open, updatePos]);\n\n  // ======================== PosInfo =========================\n  var mergedPosInfo = useMemo(function () {\n    if (!posInfo) {\n      return posInfo;\n    }\n    var gapOffsetX = getGapOffset(0);\n    var gapOffsetY = getGapOffset(1);\n    var gapRadius = isValidNumber(gap === null || gap === void 0 ? void 0 : gap.radius) ? gap === null || gap === void 0 ? void 0 : gap.radius : 2;\n    return {\n      left: posInfo.left - gapOffsetX,\n      top: posInfo.top - gapOffsetY,\n      width: posInfo.width + gapOffsetX * 2,\n      height: posInfo.height + gapOffsetY * 2,\n      radius: gapRadius\n    };\n  }, [posInfo, gap]);\n  return [mergedPosInfo, targetElement];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEvent", "useLayoutEffect", "useMemo", "useState", "isInViewPort", "isValidNumber", "val", "Number", "isNaN", "useTarget", "target", "open", "gap", "scrollIntoViewOptions", "_useState", "undefined", "_useState2", "targetElement", "setTargetElement", "nextElement", "_useState3", "_useState4", "posInfo", "setPosInfo", "updatePos", "scrollIntoView", "_targetElement$getBou", "getBoundingClientRect", "left", "top", "width", "height", "nextPosInfo", "radius", "origin", "JSON", "stringify", "getGapOffset", "index", "_ref", "Array", "isArray", "offset", "window", "addEventListener", "removeEventListener", "mergedPosInfo", "gapOffsetX", "gapOffsetY", "gapRadius"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+tour@1.15.1_r_14d992225ad622c42cfc1bfce16fe219/node_modules/@rc-component/tour/es/hooks/useTarget.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useMemo, useState } from 'react';\nimport { isInViewPort } from \"../util\";\nfunction isValidNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val);\n}\nexport default function useTarget(target, open, gap, scrollIntoViewOptions) {\n  // ========================= Target =========================\n  // We trade `undefined` as not get target by function yet.\n  // `null` as empty target.\n  var _useState = useState(undefined),\n    _useState2 = _slicedToArray(_useState, 2),\n    targetElement = _useState2[0],\n    setTargetElement = _useState2[1];\n  useLayoutEffect(function () {\n    var nextElement = typeof target === 'function' ? target() : target;\n    setTargetElement(nextElement || null);\n  });\n\n  // ========================= Align ==========================\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    posInfo = _useState4[0],\n    setPosInfo = _useState4[1];\n  var updatePos = useEvent(function () {\n    if (targetElement) {\n      // Exist target element. We should scroll and get target position\n      if (!isInViewPort(targetElement) && open) {\n        targetElement.scrollIntoView(scrollIntoViewOptions);\n      }\n      var _targetElement$getBou = targetElement.getBoundingClientRect(),\n        left = _targetElement$getBou.left,\n        top = _targetElement$getBou.top,\n        width = _targetElement$getBou.width,\n        height = _targetElement$getBou.height;\n      var nextPosInfo = {\n        left: left,\n        top: top,\n        width: width,\n        height: height,\n        radius: 0\n      };\n      setPosInfo(function (origin) {\n        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {\n          return nextPosInfo;\n        }\n        return origin;\n      });\n    } else {\n      // Not exist target which means we just show in center\n      setPosInfo(null);\n    }\n  });\n  var getGapOffset = function getGapOffset(index) {\n    var _ref;\n    return (_ref = Array.isArray(gap === null || gap === void 0 ? void 0 : gap.offset) ? gap === null || gap === void 0 ? void 0 : gap.offset[index] : gap === null || gap === void 0 ? void 0 : gap.offset) !== null && _ref !== void 0 ? _ref : 6;\n  };\n  useLayoutEffect(function () {\n    updatePos();\n    // update when window resize\n    window.addEventListener('resize', updatePos);\n    return function () {\n      window.removeEventListener('resize', updatePos);\n    };\n  }, [targetElement, open, updatePos]);\n\n  // ======================== PosInfo =========================\n  var mergedPosInfo = useMemo(function () {\n    if (!posInfo) {\n      return posInfo;\n    }\n    var gapOffsetX = getGapOffset(0);\n    var gapOffsetY = getGapOffset(1);\n    var gapRadius = isValidNumber(gap === null || gap === void 0 ? void 0 : gap.radius) ? gap === null || gap === void 0 ? void 0 : gap.radius : 2;\n    return {\n      left: posInfo.left - gapOffsetX,\n      top: posInfo.top - gapOffsetY,\n      width: posInfo.width + gapOffsetX * 2,\n      height: posInfo.height + gapOffsetY * 2,\n      radius: gapRadius\n    };\n  }, [posInfo, gap]);\n  return [mergedPosInfo, targetElement];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACzC,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,GAAG,CAAC;AACtD;AACA,eAAe,SAASG,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,qBAAqB,EAAE;EAC1E;EACA;EACA;EACA,IAAIC,SAAS,GAAGX,QAAQ,CAACY,SAAS,CAAC;IACjCC,UAAU,GAAGjB,cAAc,CAACe,SAAS,EAAE,CAAC,CAAC;IACzCG,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClCf,eAAe,CAAC,YAAY;IAC1B,IAAIkB,WAAW,GAAG,OAAOT,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM;IAClEQ,gBAAgB,CAACC,WAAW,IAAI,IAAI,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,UAAU,GAAGjB,QAAQ,CAAC,IAAI,CAAC;IAC7BkB,UAAU,GAAGtB,cAAc,CAACqB,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC5B,IAAIG,SAAS,GAAGxB,QAAQ,CAAC,YAAY;IACnC,IAAIiB,aAAa,EAAE;MACjB;MACA,IAAI,CAACb,YAAY,CAACa,aAAa,CAAC,IAAIN,IAAI,EAAE;QACxCM,aAAa,CAACQ,cAAc,CAACZ,qBAAqB,CAAC;MACrD;MACA,IAAIa,qBAAqB,GAAGT,aAAa,CAACU,qBAAqB,CAAC,CAAC;QAC/DC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;QACjCC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;QAC/BC,KAAK,GAAGJ,qBAAqB,CAACI,KAAK;QACnCC,MAAM,GAAGL,qBAAqB,CAACK,MAAM;MACvC,IAAIC,WAAW,GAAG;QAChBJ,IAAI,EAAEA,IAAI;QACVC,GAAG,EAAEA,GAAG;QACRC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA,MAAM;QACdE,MAAM,EAAE;MACV,CAAC;MACDV,UAAU,CAAC,UAAUW,MAAM,EAAE;QAC3B,IAAIC,IAAI,CAACC,SAAS,CAACF,MAAM,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC,EAAE;UAC1D,OAAOA,WAAW;QACpB;QACA,OAAOE,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAX,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EACF,IAAIc,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9C,IAAIC,IAAI;IACR,OAAO,CAACA,IAAI,GAAGC,KAAK,CAACC,OAAO,CAAC7B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC8B,MAAM,CAAC,GAAG9B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC8B,MAAM,CAACJ,KAAK,CAAC,GAAG1B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC8B,MAAM,MAAM,IAAI,IAAIH,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC;EACjP,CAAC;EACDtC,eAAe,CAAC,YAAY;IAC1BuB,SAAS,CAAC,CAAC;IACX;IACAmB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEpB,SAAS,CAAC;IAC5C,OAAO,YAAY;MACjBmB,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAErB,SAAS,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACP,aAAa,EAAEN,IAAI,EAAEa,SAAS,CAAC,CAAC;;EAEpC;EACA,IAAIsB,aAAa,GAAG5C,OAAO,CAAC,YAAY;IACtC,IAAI,CAACoB,OAAO,EAAE;MACZ,OAAOA,OAAO;IAChB;IACA,IAAIyB,UAAU,GAAGV,YAAY,CAAC,CAAC,CAAC;IAChC,IAAIW,UAAU,GAAGX,YAAY,CAAC,CAAC,CAAC;IAChC,IAAIY,SAAS,GAAG5C,aAAa,CAACO,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,MAAM,CAAC,GAAGrB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,MAAM,GAAG,CAAC;IAC9I,OAAO;MACLL,IAAI,EAAEN,OAAO,CAACM,IAAI,GAAGmB,UAAU;MAC/BlB,GAAG,EAAEP,OAAO,CAACO,GAAG,GAAGmB,UAAU;MAC7BlB,KAAK,EAAER,OAAO,CAACQ,KAAK,GAAGiB,UAAU,GAAG,CAAC;MACrChB,MAAM,EAAET,OAAO,CAACS,MAAM,GAAGiB,UAAU,GAAG,CAAC;MACvCf,MAAM,EAAEgB;IACV,CAAC;EACH,CAAC,EAAE,CAAC3B,OAAO,EAAEV,GAAG,CAAC,CAAC;EAClB,OAAO,CAACkC,aAAa,EAAE7B,aAAa,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
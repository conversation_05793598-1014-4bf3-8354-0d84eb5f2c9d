{"ast": null, "code": "export function toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\n/**\n * Check for element is native ellipsis\n * ref:\n * - https://github.com/ant-design/ant-design/issues/50143\n * - https://github.com/ant-design/ant-design/issues/50414\n */\nexport function isEleEllipsis(ele) {\n  // Create a new div to get the size\n  const childDiv = document.createElement('em');\n  ele.appendChild(childDiv);\n  // For test case\n  if (process.env.NODE_ENV !== 'production') {\n    childDiv.className = 'ant-typography-css-ellipsis-content-measure';\n  }\n  const rect = ele.getBoundingClientRect();\n  const childRect = childDiv.getBoundingClientRect();\n  // Reset\n  ele.removeChild(childDiv);\n  // Range checker\n  return (\n    // Horizontal out of range\n    rect.left > childRect.left || childRect.right > rect.right ||\n    // Vertical out of range\n    rect.top > childRect.top || childRect.bottom > rect.bottom\n  );\n}\nexport const isValidText = val => ['string', 'number'].includes(typeof val);", "map": {"version": 3, "names": ["toList", "val", "Array", "isArray", "getNode", "dom", "defaultNode", "needDom", "undefined", "isEleEllipsis", "ele", "childDiv", "document", "createElement", "append<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "className", "rect", "getBoundingClientRect", "childRect", "<PERSON><PERSON><PERSON><PERSON>", "left", "right", "top", "bottom", "isValidText", "includes"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/Base/util.js"], "sourcesContent": ["export function toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\n/**\n * Check for element is native ellipsis\n * ref:\n * - https://github.com/ant-design/ant-design/issues/50143\n * - https://github.com/ant-design/ant-design/issues/50414\n */\nexport function isEleEllipsis(ele) {\n  // Create a new div to get the size\n  const childDiv = document.createElement('em');\n  ele.appendChild(childDiv);\n  // For test case\n  if (process.env.NODE_ENV !== 'production') {\n    childDiv.className = 'ant-typography-css-ellipsis-content-measure';\n  }\n  const rect = ele.getBoundingClientRect();\n  const childRect = childDiv.getBoundingClientRect();\n  // Reset\n  ele.removeChild(childDiv);\n  // Range checker\n  return (\n    // Horizontal out of range\n    rect.left > childRect.left || childRect.right > rect.right ||\n    // Vertical out of range\n    rect.top > childRect.top || childRect.bottom > rect.bottom\n  );\n}\nexport const isValidText = val => ['string', 'number'].includes(typeof val);"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,GAAG,EAAE;EAC1B,IAAIA,GAAG,KAAK,KAAK,EAAE;IACjB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;EACvB;EACA,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AACA,OAAO,SAASG,OAAOA,CAACC,GAAG,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjD,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;IACrC,OAAOF,WAAW;EACpB;EACA,OAAOD,GAAG,IAAIE,OAAO,IAAID,WAAW;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,aAAaA,CAACC,GAAG,EAAE;EACjC;EACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;EAC7CH,GAAG,CAACI,WAAW,CAACH,QAAQ,CAAC;EACzB;EACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCN,QAAQ,CAACO,SAAS,GAAG,6CAA6C;EACpE;EACA,MAAMC,IAAI,GAAGT,GAAG,CAACU,qBAAqB,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGV,QAAQ,CAACS,qBAAqB,CAAC,CAAC;EAClD;EACAV,GAAG,CAACY,WAAW,CAACX,QAAQ,CAAC;EACzB;EACA;IACE;IACAQ,IAAI,CAACI,IAAI,GAAGF,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACG,KAAK,GAAGL,IAAI,CAACK,KAAK;IAC1D;IACAL,IAAI,CAACM,GAAG,GAAGJ,SAAS,CAACI,GAAG,IAAIJ,SAAS,CAACK,MAAM,GAAGP,IAAI,CAACO;EAAM;AAE9D;AACA,OAAO,MAAMC,WAAW,GAAG1B,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC2B,QAAQ,CAAC,OAAO3B,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var locale = {\n  // Options\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};\nexport default locale;", "map": {"version": 3, "names": ["locale", "items_per_page", "jump_to", "jump_to_confirm", "page", "prev_page", "next_page", "prev_5", "next_5", "prev_3", "next_3", "page_size"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-pagination@5.1.0_react-d_459b2e8d0467f517bd67196250306427/node_modules/rc-pagination/es/locale/zh_CN.js"], "sourcesContent": ["var locale = {\n  // Options\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};\nexport default locale;"], "mappings": "AAAA,IAAIA,MAAM,GAAG;EACX;EACAC,cAAc,EAAE,KAAK;EACrBC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,IAAI;EACrBC,IAAI,EAAE,GAAG;EACT;EACAC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC;AACD,eAAeX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import ColorPicker from \"./ColorPicker\";\nexport { Color } from \"./color\";\nexport { default as ColorBlock } from \"./components/ColorBlock\";\nexport * from \"./interface\";\nexport default ColorPicker;", "map": {"version": 3, "names": ["ColorPicker", "Color", "default", "ColorBlock"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+color-picker@_34570774f6314db73ae9bf418879662f/node_modules/@rc-component/color-picker/es/index.js"], "sourcesContent": ["import ColorPicker from \"./ColorPicker\";\nexport { Color } from \"./color\";\nexport { default as ColorBlock } from \"./components/ColorBlock\";\nexport * from \"./interface\";\nexport default ColorPicker;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,OAAO,IAAIC,UAAU,QAAQ,yBAAyB;AAC/D,cAAc,aAAa;AAC3B,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
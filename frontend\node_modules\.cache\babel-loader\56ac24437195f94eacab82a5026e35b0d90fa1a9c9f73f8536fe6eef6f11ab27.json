{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport Portal from '@rc-component/portal';\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport Dialog from \"./Dialog\";\n// fix issue #10656\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose,\n    panelRef = props.panelRef;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  var refContext = React.useMemo(function () {\n    return {\n      panel: panelRef\n    };\n  }, [panelRef]);\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]);\n\n  // Destroy on close will remove wrapped div\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(RefContext.Provider, {\n    value: refContext\n  }, /*#__PURE__*/React.createElement(Portal, {\n    open: visible || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: visible || animatedVisible\n  }, /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n    destroyOnClose: destroyOnClose,\n    afterClose: function afterClose() {\n      _afterClose === null || _afterClose === void 0 || _afterClose();\n      setAnimatedVisible(false);\n    }\n  }))));\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "Portal", "React", "RefContext", "Dialog", "DialogWrap", "props", "visible", "getContainer", "forceRender", "_props$destroyOnClose", "destroyOnClose", "_afterClose", "afterClose", "panelRef", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "refContext", "useMemo", "panel", "useEffect", "createElement", "Provider", "value", "open", "autoDestroy", "autoLock", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-dialog@9.6.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-dialog/es/DialogWrap.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport Portal from '@rc-component/portal';\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport Dialog from \"./Dialog\";\n// fix issue #10656\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose,\n    panelRef = props.panelRef;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  var refContext = React.useMemo(function () {\n    return {\n      panel: panelRef\n    };\n  }, [panelRef]);\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]);\n\n  // Destroy on close will remove wrapped div\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(RefContext.Provider, {\n    value: refContext\n  }, /*#__PURE__*/React.createElement(Portal, {\n    open: visible || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: visible || animatedVisible\n  }, /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n    destroyOnClose: destroyOnClose,\n    afterClose: function afterClose() {\n      _afterClose === null || _afterClose === void 0 || _afterClose();\n      setAnimatedVisible(false);\n    }\n  }))));\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,WAAW;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACzBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,qBAAqB,GAAGJ,KAAK,CAACK,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACjFE,WAAW,GAAGN,KAAK,CAACO,UAAU;IAC9BC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EAC3B,IAAIC,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAACT,OAAO,CAAC;IAC3CU,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,UAAU,GAAGlB,KAAK,CAACmB,OAAO,CAAC,YAAY;IACzC,OAAO;MACLC,KAAK,EAAER;IACT,CAAC;EACH,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdZ,KAAK,CAACqB,SAAS,CAAC,YAAY;IAC1B,IAAIhB,OAAO,EAAE;MACXY,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACZ,OAAO,CAAC,CAAC;;EAEb;EACA,IAAI,CAACE,WAAW,IAAIE,cAAc,IAAI,CAACO,eAAe,EAAE;IACtD,OAAO,IAAI;EACb;EACA,OAAO,aAAahB,KAAK,CAACsB,aAAa,CAACrB,UAAU,CAACsB,QAAQ,EAAE;IAC3DC,KAAK,EAAEN;EACT,CAAC,EAAE,aAAalB,KAAK,CAACsB,aAAa,CAACvB,MAAM,EAAE;IAC1C0B,IAAI,EAAEpB,OAAO,IAAIE,WAAW,IAAIS,eAAe;IAC/CU,WAAW,EAAE,KAAK;IAClBpB,YAAY,EAAEA,YAAY;IAC1BqB,QAAQ,EAAEtB,OAAO,IAAIW;EACvB,CAAC,EAAE,aAAahB,KAAK,CAACsB,aAAa,CAACpB,MAAM,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IAC9DK,cAAc,EAAEA,cAAc;IAC9BE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC,CAAC;MAC/DO,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACDd,UAAU,CAACyB,WAAW,GAAG,QAAQ;AACjC,eAAezB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
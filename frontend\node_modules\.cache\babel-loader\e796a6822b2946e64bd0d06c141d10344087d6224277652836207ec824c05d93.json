{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}", "map": {"version": 3, "names": ["React", "useRefFunc", "callback", "funcRef", "useRef", "current", "cacheFn", "useCallback", "apply", "arguments"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/hooks/useRefFunc.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC3C,IAAIC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC;EAC5BD,OAAO,CAACE,OAAO,GAAGH,QAAQ;EAC1B,IAAII,OAAO,GAAGN,KAAK,CAACO,WAAW,CAAC,YAAY;IAC1C,OAAOJ,OAAO,CAACE,OAAO,CAACG,KAAK,CAACL,OAAO,EAAEM,SAAS,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
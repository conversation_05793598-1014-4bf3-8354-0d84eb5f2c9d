{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\n\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils.toObjectSet(['age', 'authorization', 'content-length', 'content-type', 'etag', 'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since', 'last-modified', 'location', 'max-forwards', 'proxy-authorization', 'referer', 'retry-after', 'user-agent']);\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\nexport default rawHeaders => {\n  const parsed = {};\n  let key;\n  let val;\n  let i;\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n    if (!key || parsed[key] && ignoreDuplicateOf[key]) {\n      return;\n    }\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n  return parsed;\n};", "map": {"version": 3, "names": ["utils", "ignoreDuplicateOf", "toObjectSet", "rawHeaders", "parsed", "key", "val", "i", "split", "for<PERSON>ach", "parser", "line", "indexOf", "substring", "trim", "toLowerCase", "push"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/parseHeaders.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils.toObjectSet([\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n]);\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\nexport default rawHeaders => {\n  const parsed = {};\n  let key;\n  let val;\n  let i;\n\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n\n    if (!key || (parsed[key] && ignoreDuplicateOf[key])) {\n      return;\n    }\n\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n\n  return parsed;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,eAAe;;AAEjC;AACA;AACA,MAAMC,iBAAiB,GAAGD,KAAK,CAACE,WAAW,CAAC,CAC1C,KAAK,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,MAAM,EAChE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,qBAAqB,EACrE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,qBAAqB,EAClE,SAAS,EAAE,aAAa,EAAE,YAAY,CACvC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,UAAU,IAAI;EAC3B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,IAAIC,CAAC;EAELJ,UAAU,IAAIA,UAAU,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACjEJ,CAAC,GAAGI,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC;IACrBP,GAAG,GAAGM,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEN,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/CT,GAAG,GAAGK,IAAI,CAACE,SAAS,CAACN,CAAC,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;IAElC,IAAI,CAACT,GAAG,IAAKD,MAAM,CAACC,GAAG,CAAC,IAAIJ,iBAAiB,CAACI,GAAG,CAAE,EAAE;MACnD;IACF;IAEA,IAAIA,GAAG,KAAK,YAAY,EAAE;MACxB,IAAID,MAAM,CAACC,GAAG,CAAC,EAAE;QACfD,MAAM,CAACC,GAAG,CAAC,CAACW,IAAI,CAACV,GAAG,CAAC;MACvB,CAAC,MAAM;QACLF,MAAM,CAACC,GAAG,CAAC,GAAG,CAACC,GAAG,CAAC;MACrB;IACF,CAAC,MAAM;MACLF,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,GAAGC,GAAG,GAAGA,GAAG;IAC5D;EACF,CAAC,CAAC;EAEF,OAAOF,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
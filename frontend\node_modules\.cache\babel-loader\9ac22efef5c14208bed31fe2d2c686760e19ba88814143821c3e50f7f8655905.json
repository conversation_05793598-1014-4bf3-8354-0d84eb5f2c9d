{"ast": null, "code": "import * as React from 'react';\nimport useSelectIcons from '../select/useIcons';\nexport function getPlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.yearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.quarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.monthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.weekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.placeholder;\n  }\n  return locale.lang.placeholder;\n}\nexport function getRangePlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.rangeYearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.rangeQuarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.rangeMonthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.rangeWeekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.rangePlaceholder;\n  }\n  return locale.lang.rangePlaceholder;\n}\nexport function useIcons(props, prefixCls) {\n  const {\n    allowClear = true\n  } = props;\n  const {\n    clearIcon,\n    removeIcon\n  } = useSelectIcons(Object.assign(Object.assign({}, props), {\n    prefixCls,\n    componentName: 'DatePicker'\n  }));\n  const mergedAllowClear = React.useMemo(() => {\n    if (allowClear === false) {\n      return false;\n    }\n    const allowClearConfig = allowClear === true ? {} : allowClear;\n    return Object.assign({\n      clearIcon: clearIcon\n    }, allowClearConfig);\n  }, [allowClear, clearIcon]);\n  return [mergedAllowClear, removeIcon];\n}", "map": {"version": 3, "names": ["React", "useSelectIcons", "getPlaceholder", "locale", "picker", "customizePlaceholder", "undefined", "lang", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "timePickerLocale", "placeholder", "getRangePlaceholder", "rangeYearPlaceholder", "rangeQuarterPlaceholder", "rangeMonthPlaceholder", "rangeWeekPlaceholder", "rangePlaceholder", "useIcons", "props", "prefixCls", "allowClear", "clearIcon", "removeIcon", "Object", "assign", "componentName", "mergedAllowClear", "useMemo", "allowClearConfig"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/date-picker/util.js"], "sourcesContent": ["import * as React from 'react';\nimport useSelectIcons from '../select/useIcons';\nexport function getPlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.yearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.quarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.monthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.weekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.placeholder;\n  }\n  return locale.lang.placeholder;\n}\nexport function getRangePlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.rangeYearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.rangeQuarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.rangeMonthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.rangeWeekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.rangePlaceholder;\n  }\n  return locale.lang.rangePlaceholder;\n}\nexport function useIcons(props, prefixCls) {\n  const {\n    allowClear = true\n  } = props;\n  const {\n    clearIcon,\n    removeIcon\n  } = useSelectIcons(Object.assign(Object.assign({}, props), {\n    prefixCls,\n    componentName: 'DatePicker'\n  }));\n  const mergedAllowClear = React.useMemo(() => {\n    if (allowClear === false) {\n      return false;\n    }\n    const allowClearConfig = allowClear === true ? {} : allowClear;\n    return Object.assign({\n      clearIcon: clearIcon\n    }, allowClearConfig);\n  }, [allowClear, clearIcon]);\n  return [mergedAllowClear, removeIcon];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,oBAAoB,EAAE;EACnE,IAAIA,oBAAoB,KAAKC,SAAS,EAAE;IACtC,OAAOD,oBAAoB;EAC7B;EACA,IAAID,MAAM,KAAK,MAAM,IAAID,MAAM,CAACI,IAAI,CAACC,eAAe,EAAE;IACpD,OAAOL,MAAM,CAACI,IAAI,CAACC,eAAe;EACpC;EACA,IAAIJ,MAAM,KAAK,SAAS,IAAID,MAAM,CAACI,IAAI,CAACE,kBAAkB,EAAE;IAC1D,OAAON,MAAM,CAACI,IAAI,CAACE,kBAAkB;EACvC;EACA,IAAIL,MAAM,KAAK,OAAO,IAAID,MAAM,CAACI,IAAI,CAACG,gBAAgB,EAAE;IACtD,OAAOP,MAAM,CAACI,IAAI,CAACG,gBAAgB;EACrC;EACA,IAAIN,MAAM,KAAK,MAAM,IAAID,MAAM,CAACI,IAAI,CAACI,eAAe,EAAE;IACpD,OAAOR,MAAM,CAACI,IAAI,CAACI,eAAe;EACpC;EACA,IAAIP,MAAM,KAAK,MAAM,IAAID,MAAM,CAACS,gBAAgB,CAACC,WAAW,EAAE;IAC5D,OAAOV,MAAM,CAACS,gBAAgB,CAACC,WAAW;EAC5C;EACA,OAAOV,MAAM,CAACI,IAAI,CAACM,WAAW;AAChC;AACA,OAAO,SAASC,mBAAmBA,CAACX,MAAM,EAAEC,MAAM,EAAEC,oBAAoB,EAAE;EACxE,IAAIA,oBAAoB,KAAKC,SAAS,EAAE;IACtC,OAAOD,oBAAoB;EAC7B;EACA,IAAID,MAAM,KAAK,MAAM,IAAID,MAAM,CAACI,IAAI,CAACC,eAAe,EAAE;IACpD,OAAOL,MAAM,CAACI,IAAI,CAACQ,oBAAoB;EACzC;EACA,IAAIX,MAAM,KAAK,SAAS,IAAID,MAAM,CAACI,IAAI,CAACE,kBAAkB,EAAE;IAC1D,OAAON,MAAM,CAACI,IAAI,CAACS,uBAAuB;EAC5C;EACA,IAAIZ,MAAM,KAAK,OAAO,IAAID,MAAM,CAACI,IAAI,CAACG,gBAAgB,EAAE;IACtD,OAAOP,MAAM,CAACI,IAAI,CAACU,qBAAqB;EAC1C;EACA,IAAIb,MAAM,KAAK,MAAM,IAAID,MAAM,CAACI,IAAI,CAACI,eAAe,EAAE;IACpD,OAAOR,MAAM,CAACI,IAAI,CAACW,oBAAoB;EACzC;EACA,IAAId,MAAM,KAAK,MAAM,IAAID,MAAM,CAACS,gBAAgB,CAACC,WAAW,EAAE;IAC5D,OAAOV,MAAM,CAACS,gBAAgB,CAACO,gBAAgB;EACjD;EACA,OAAOhB,MAAM,CAACI,IAAI,CAACY,gBAAgB;AACrC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACzC,MAAM;IACJC,UAAU,GAAG;EACf,CAAC,GAAGF,KAAK;EACT,MAAM;IACJG,SAAS;IACTC;EACF,CAAC,GAAGxB,cAAc,CAACyB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAE;IACzDC,SAAS;IACTM,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC;EACH,MAAMC,gBAAgB,GAAG7B,KAAK,CAAC8B,OAAO,CAAC,MAAM;IAC3C,IAAIP,UAAU,KAAK,KAAK,EAAE;MACxB,OAAO,KAAK;IACd;IACA,MAAMQ,gBAAgB,GAAGR,UAAU,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,UAAU;IAC9D,OAAOG,MAAM,CAACC,MAAM,CAAC;MACnBH,SAAS,EAAEA;IACb,CAAC,EAAEO,gBAAgB,CAAC;EACtB,CAAC,EAAE,CAACR,UAAU,EAAEC,SAAS,CAAC,CAAC;EAC3B,OAAO,CAACK,gBAAgB,EAAEJ,UAAU,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
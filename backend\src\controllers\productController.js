const { Product } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// 生成产品编号
const generateProductCode = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  
  // 查找今天创建的最后一个产品编号
  const lastProduct = await Product.findOne({
    where: {
      product_code: {
        [Op.like]: `PRD${dateStr}%`
      }
    },
    order: [['product_code', 'DESC']]
  });
  
  let sequence = 1;
  if (lastProduct) {
    const lastSequence = parseInt(lastProduct.product_code.slice(-3));
    sequence = lastSequence + 1;
  }
  
  return `PRD${dateStr}${sequence.toString().padStart(3, '0')}`;
};

// 获取产品列表
exports.getProducts = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      search,
      category,
      status = 'active',
      low_stock = false
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const where = {};

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { product_code: { [Op.like]: `%${search}%` } },
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 分类筛选
    if (category) {
      where.category = category;
    }

    // 状态筛选
    if (status === 'active') {
      where.status = 'active';
    } else if (status === 'inactive') {
      where.status = 'inactive';
    }

    // 低库存筛选
    if (low_stock === 'true') {
      where[Op.and] = [
        { current_stock: { [Op.lte]: { [Op.col]: 'min_stock_level' } } },
        { min_stock_level: { [Op.gt]: 0 } }
      ];
    }

    const { count, rows: products } = await Product.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: count,
          total_pages: Math.ceil(count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取产品列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取产品列表失败',
      error: error.message
    });
  }
};

// 获取产品详情
exports.getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    res.json({
      success: true,
      data: { product }
    });
  } catch (error) {
    console.error('获取产品详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取产品详情失败',
      error: error.message
    });
  }
};

// 创建产品
exports.createProduct = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const productData = req.body;

    // 生成产品编号
    productData.product_code = await generateProductCode();

    // 创建产品
    const product = await Product.create(productData);

    res.status(201).json({
      success: true,
      message: '产品创建成功',
      data: { product }
    });
  } catch (error) {
    console.error('创建产品失败:', error);
    res.status(500).json({
      success: false,
      message: '创建产品失败',
      error: error.message
    });
  }
};

// 更新产品
exports.updateProduct = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    // 更新产品
    await product.update(updateData);

    res.json({
      success: true,
      message: '产品更新成功',
      data: { product }
    });
  } catch (error) {
    console.error('更新产品失败:', error);
    res.status(500).json({
      success: false,
      message: '更新产品失败',
      error: error.message
    });
  }
};

// 删除产品
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    await product.destroy();

    res.json({
      success: true,
      message: '产品删除成功'
    });
  } catch (error) {
    console.error('删除产品失败:', error);
    res.status(500).json({
      success: false,
      message: '删除产品失败',
      error: error.message
    });
  }
};

// 更新产品状态
exports.updateProductStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    await product.update({ status });

    res.json({
      success: true,
      message: '产品状态更新成功',
      data: { product }
    });
  } catch (error) {
    console.error('更新产品状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新产品状态失败',
      error: error.message
    });
  }
};

// 更新库存
exports.updateStock = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { quantity, type, reason } = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    // 计算新库存
    let newStock = product.current_stock;
    if (type === 'in') {
      newStock += quantity;
    } else if (type === 'out') {
      newStock -= quantity;
      if (newStock < 0) {
        return res.status(400).json({
          success: false,
          message: '库存不足'
        });
      }
    }

    // 更新库存
    await product.update({ current_stock: newStock });

    res.json({
      success: true,
      message: '库存更新成功',
      data: { 
        product,
        stock_change: {
          type,
          quantity,
          reason,
          previous_stock: product.current_stock,
          new_stock: newStock
        }
      }
    });
  } catch (error) {
    console.error('更新库存失败:', error);
    res.status(500).json({
      success: false,
      message: '更新库存失败',
      error: error.message
    });
  }
};

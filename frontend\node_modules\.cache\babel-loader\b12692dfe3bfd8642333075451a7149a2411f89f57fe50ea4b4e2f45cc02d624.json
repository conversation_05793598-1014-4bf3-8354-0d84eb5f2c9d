{"ast": null, "code": "// This icon file is generated automatically.\nvar WechatWorkOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M909.78 729.59a135.87 135.87 0 00-47.04 19.04 114.24 114.24 0 01-51.4 31.08 76.29 76.29 0 0124.45-45.42 169.3 169.3 0 0023.4-55.02 50.41 50.41 0 1150.6 50.32zm-92.21-120.76a168.83 168.83 0 00-54.81-23.68 50.41 50.41 0 01-50.4-50.42 50.41 50.41 0 11100.8 0 137.5 137.5 0 0018.82 47.2 114.8 114.8 0 0130.76 51.66 76.08 76.08 0 01-45.02-24.76h-.19zm-83.04-177.71c-15.19-127.33-146.98-227.1-306.44-227.1-169.87 0-308.09 113.1-308.09 252.2A235.81 235.81 0 00230.06 647.6a311.28 311.28 0 0033.6 21.59L250 723.76c4.93 2.31 9.7 4.78 14.75 6.9l69-34.5c10.07 2.61 20.68 4.3 31.2 6.08 6.73 1.2 13.45 2.43 20.35 3.25a354.83 354.83 0 00128.81-7.4 248.88 248.88 0 0010.15 55.06 425.64 425.64 0 01-96.17 11.24 417.98 417.98 0 01-86.4-9.52L216.52 817.4a27.62 27.62 0 01-29.98-3.14 28.02 28.02 0 01-9.67-28.61l22.4-90.24A292.26 292.26 0 0164 456.21C64 285.98 227 148 428.09 148c190.93 0 347.29 124.53 362.52 282.82a244.97 244.97 0 00-26.47-2.62c-9.9.38-19.79 1.31-29.6 2.88zm-116.3 198.81a135.76 135.76 0 0047.05-19.04 114.24 114.24 0 0151.45-31 76.47 76.47 0 01-24.5 45.34 169.48 169.48 0 00-23.4 55.05 50.41 50.41 0 01-100.8.23 50.41 50.41 0 0150.2-50.58m90.8 121.32a168.6 168.6 0 0054.66 23.9 50.44 50.44 0 0135.64 86.08 50.38 50.38 0 01-86.04-35.66 136.74 136.74 0 00-18.67-47.28 114.71 114.71 0 01-30.54-51.8 76 76 0 0144.95 25.06z\"\n      }\n    }]\n  },\n  \"name\": \"wechat-work\",\n  \"theme\": \"outlined\"\n};\nexport default WechatWorkOutlined;", "map": {"version": 3, "names": ["WechatWorkOutlined"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/WechatWorkOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar WechatWorkOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M909.78 729.59a135.87 135.87 0 00-47.04 19.04 114.24 114.24 0 01-51.4 31.08 76.29 76.29 0 0124.45-45.42 169.3 169.3 0 0023.4-55.02 50.41 50.41 0 1150.6 50.32zm-92.21-120.76a168.83 168.83 0 00-54.81-23.68 50.41 50.41 0 01-50.4-50.42 50.41 50.41 0 11100.8 0 137.5 137.5 0 0018.82 47.2 114.8 114.8 0 0130.76 51.66 76.08 76.08 0 01-45.02-24.76h-.19zm-83.04-177.71c-15.19-127.33-146.98-227.1-306.44-227.1-169.87 0-308.09 113.1-308.09 252.2A235.81 235.81 0 00230.06 647.6a311.28 311.28 0 0033.6 21.59L250 723.76c4.93 2.31 9.7 4.78 14.75 6.9l69-34.5c10.07 2.61 20.68 4.3 31.2 6.08 6.73 1.2 13.45 2.43 20.35 3.25a354.83 354.83 0 00128.81-7.4 248.88 248.88 0 0010.15 55.06 425.64 425.64 0 01-96.17 11.24 417.98 417.98 0 01-86.4-9.52L216.52 817.4a27.62 27.62 0 01-29.98-3.14 28.02 28.02 0 01-9.67-28.61l22.4-90.24A292.26 292.26 0 0164 456.21C64 285.98 227 148 428.09 148c190.93 0 347.29 124.53 362.52 282.82a244.97 244.97 0 00-26.47-2.62c-9.9.38-19.79 1.31-29.6 2.88zm-116.3 198.81a135.76 135.76 0 0047.05-19.04 114.24 114.24 0 0151.45-31 76.47 76.47 0 01-24.5 45.34 169.48 169.48 0 00-23.4 55.05 50.41 50.41 0 01-100.8.23 50.41 50.41 0 0150.2-50.58m90.8 121.32a168.6 168.6 0 0054.66 23.9 50.44 50.44 0 0135.64 86.08 50.38 50.38 0 01-86.04-35.66 136.74 136.74 0 00-18.67-47.28 114.71 114.71 0 01-30.54-51.8 76 76 0 0144.95 25.06z\" } }] }, \"name\": \"wechat-work\", \"theme\": \"outlined\" };\nexport default WechatWorkOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,WAAW,EAAE,SAAS;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA0yC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,aAAa;EAAE,OAAO,EAAE;AAAW,CAAC;AACthD,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle, safeColumnTitle } from '../util';\nconst ASCEND = 'ascend';\nconst DESCEND = 'descend';\nconst getMultiplePriority = column => {\n  if (typeof column.sorter === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n};\nconst getSortFunction = sorter => {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && typeof sorter === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n};\nconst nextSortDirection = (sortDirections, current) => {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n};\nconst collectSortStates = (columns, init, pos) => {\n  let sortStates = [];\n  const pushState = (column, columnPos) => {\n    sortStates.push({\n      column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  };\n  (columns || []).forEach((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n};\nconst injectSorter = (prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) => {\n  const finalColumns = (columns || []).map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    let newColumn = column;\n    if (newColumn.sorter) {\n      const sortDirections = newColumn.sortDirections || defaultSortDirections;\n      const showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const sorterState = sorterStates.find(({\n        key\n      }) => key === columnKey);\n      const sortOrder = sorterState ? sorterState.sortOrder : null;\n      const nextSortOrder = nextSortDirection(sortDirections, sortOrder);\n      let sorter;\n      if (column.sortIcon) {\n        sorter = column.sortIcon({\n          sortOrder\n        });\n      } else {\n        const upNode = sortDirections.includes(ASCEND) && (/*#__PURE__*/React.createElement(CaretUpOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-up`, {\n            active: sortOrder === ASCEND\n          })\n        }));\n        const downNode = sortDirections.includes(DESCEND) && (/*#__PURE__*/React.createElement(CaretDownOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-down`, {\n            active: sortOrder === DESCEND\n          })\n        }));\n        sorter = /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(`${prefixCls}-column-sorter`, {\n            [`${prefixCls}-column-sorter-full`]: !!(upNode && downNode)\n          })\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-column-sorter-inner`,\n          \"aria-hidden\": \"true\"\n        }, upNode, downNode));\n      }\n      const {\n        cancelSort,\n        triggerAsc,\n        triggerDesc\n      } = tableLocale || {};\n      let sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      const tooltipProps = typeof showSorterTooltip === 'object' ? Object.assign({\n        title: sortTip\n      }, showSorterTooltip) : {\n        title: sortTip\n      };\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        className: classNames(newColumn.className, {\n          [`${prefixCls}-column-sort`]: sortOrder\n        }),\n        title: renderProps => {\n          const columnSortersClass = `${prefixCls}-column-sorters`;\n          const renderColumnTitleWrapper = /*#__PURE__*/React.createElement(\"span\", {\n            className: `${prefixCls}-column-title`\n          }, renderColumnTitle(column.title, renderProps));\n          const renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: columnSortersClass\n          }, renderColumnTitleWrapper, sorter);\n          if (showSorterTooltip) {\n            if (typeof showSorterTooltip !== 'boolean' && (showSorterTooltip === null || showSorterTooltip === void 0 ? void 0 : showSorterTooltip.target) === 'sorter-icon') {\n              return /*#__PURE__*/React.createElement(\"div\", {\n                className: `${columnSortersClass} ${prefixCls}-column-sorters-tooltip-target-sorter`\n              }, renderColumnTitleWrapper, /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), sorter));\n            }\n            return /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), renderSortTitle);\n          }\n          return renderSortTitle;\n        },\n        onHeaderCell: col => {\n          var _a;\n          const cell = ((_a = column.onHeaderCell) === null || _a === void 0 ? void 0 : _a.call(column, col)) || {};\n          const originOnClick = cell.onClick;\n          const originOKeyDown = cell.onKeyDown;\n          cell.onClick = event => {\n            triggerSorter({\n              column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = event => {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          const renderTitle = safeColumnTitle(column.title, {});\n          const displayTitle = renderTitle === null || renderTitle === void 0 ? void 0 : renderTitle.toString();\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sortOrder) {\n            cell['aria-sort'] = sortOrder === 'ascend' ? 'ascending' : 'descending';\n          }\n          cell['aria-label'] = displayTitle || '';\n          cell.className = classNames(cell.className, `${prefixCls}-column-has-sorters`);\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = (renderTitle !== null && renderTitle !== void 0 ? renderTitle : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n  return finalColumns;\n};\nconst stateToInfo = sorterState => {\n  const {\n    column,\n    sortOrder\n  } = sorterState;\n  return {\n    column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n};\nconst generateSorterInfo = sorterStates => {\n  const activeSorters = sorterStates.filter(({\n    sortOrder\n  }) => sortOrder).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (activeSorters.length === 0 && sorterStates.length) {\n    const lastIndex = sorterStates.length - 1;\n    return Object.assign(Object.assign({}, stateToInfo(sorterStates[lastIndex])), {\n      column: undefined,\n      order: undefined,\n      field: undefined,\n      columnKey: undefined\n    });\n  }\n  if (activeSorters.length <= 1) {\n    return activeSorters[0] || {};\n  }\n  return activeSorters;\n};\nexport const getSortData = (data, sortStates, childrenColumnName) => {\n  const innerSorterStates = sortStates.slice().sort((a, b) => b.multiplePriority - a.multiplePriority);\n  const cloneData = data.slice();\n  const runningSorters = innerSorterStates.filter(({\n    column: {\n      sorter\n    },\n    sortOrder\n  }) => getSortFunction(sorter) && sortOrder);\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort((record1, record2) => {\n    for (let i = 0; i < runningSorters.length; i += 1) {\n      const sorterState = runningSorters[i];\n      const {\n        column: {\n          sorter\n        },\n        sortOrder\n      } = sorterState;\n      const compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        const compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(record => {\n    const subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return Object.assign(Object.assign({}, record), {\n        [childrenColumnName]: getSortData(subRecords, sortStates, childrenColumnName)\n      });\n    }\n    return record;\n  });\n};\nconst useFilterSorter = props => {\n  const {\n    prefixCls,\n    mergedColumns,\n    sortDirections,\n    tableLocale,\n    showSorterTooltip,\n    onSorterChange\n  } = props;\n  const [sortStates, setSortStates] = React.useState(() => collectSortStates(mergedColumns, true));\n  const getColumnKeys = (columns, pos) => {\n    const newKeys = [];\n    columns.forEach((item, index) => {\n      const columnPos = getColumnPos(index, pos);\n      newKeys.push(getColumnKey(item, columnPos));\n      if (Array.isArray(item.children)) {\n        const childKeys = getColumnKeys(item.children, columnPos);\n        newKeys.push.apply(newKeys, _toConsumableArray(childKeys));\n      }\n    });\n    return newKeys;\n  };\n  const mergedSorterStates = React.useMemo(() => {\n    let validate = true;\n    const collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      const mergedColumnsKeys = getColumnKeys(mergedColumns);\n      return sortStates.filter(({\n        key\n      }) => mergedColumnsKeys.includes(key));\n    }\n    const validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(Object.assign(Object.assign({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    let multipleMode = null;\n    collectedStates.forEach(state => {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  const columnTitleSorterProps = React.useMemo(() => {\n    var _a, _b;\n    const sortColumns = mergedSorterStates.map(({\n      column,\n      sortOrder\n    }) => ({\n      column,\n      order: sortOrder\n    }));\n    return {\n      sortColumns,\n      // Legacy\n      sortColumn: (_a = sortColumns[0]) === null || _a === void 0 ? void 0 : _a.column,\n      sortOrder: (_b = sortColumns[0]) === null || _b === void 0 ? void 0 : _b.order\n    };\n  }, [mergedSorterStates]);\n  const triggerSorter = sortState => {\n    let newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(({\n        key\n      }) => key !== sortState.key)), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  };\n  const transformColumns = innerColumns => injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  const getSorters = () => generateSorterInfo(mergedSorterStates);\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n};\nexport default useFilterSorter;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "CaretDownOutlined", "CaretUpOutlined", "classNames", "KeyCode", "<PERSON><PERSON><PERSON>", "getColumnKey", "getColumnPos", "renderColumnTitle", "safeColumnTitle", "ASCEND", "DESCEND", "getMultiplePriority", "column", "sorter", "multiple", "getSortFunction", "compare", "nextSortDirection", "sortDirections", "current", "indexOf", "collectSortStates", "columns", "init", "pos", "sortStates", "pushState", "columnPos", "push", "key", "multiplePriority", "sortOrder", "for<PERSON>ach", "index", "children", "concat", "defaultSortOrder", "injectSorter", "prefixCls", "sorterStates", "triggerSorter", "defaultSortDirections", "tableLocale", "tableShowSorterTooltip", "finalColumns", "map", "newColumn", "showSorterTooltip", "undefined", "column<PERSON>ey", "sorterState", "find", "nextSortOrder", "sortIcon", "upNode", "includes", "createElement", "className", "active", "downNode", "cancelSort", "triggerAsc", "triggerDesc", "sortTip", "tooltipProps", "Object", "assign", "title", "renderProps", "columnSortersClass", "renderColumnTitleWrapper", "renderSortTitle", "target", "onHeaderCell", "col", "_a", "cell", "call", "originOnClick", "onClick", "originOKeyDown", "onKeyDown", "event", "keyCode", "ENTER", "renderTitle", "displayTitle", "toString", "tabIndex", "ellipsis", "stateToInfo", "order", "field", "dataIndex", "generateSorterInfo", "activeSorters", "filter", "length", "lastIndex", "getSortData", "data", "childrenColumnName", "innerSorterStates", "slice", "sort", "a", "b", "cloneData", "running<PERSON><PERSON><PERSON>", "record1", "record2", "i", "compareFn", "compareResult", "record", "subRecords", "use<PERSON>ilter<PERSON><PERSON>er", "props", "mergedColumns", "onSorterChange", "setSortStates", "useState", "getColumnKeys", "newKeys", "item", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "apply", "mergedSorterStates", "useMemo", "validate", "collectedStates", "mergedColumnsKeys", "validateStates", "patchStates", "state", "multipleMode", "columnTitleSorterProps", "_b", "sortColumns", "sortColumn", "sortState", "newSorterStates", "transformColumns", "innerColumns", "getSorters"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/hooks/useSorter.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle, safeColumnTitle } from '../util';\nconst ASCEND = 'ascend';\nconst DESCEND = 'descend';\nconst getMultiplePriority = column => {\n  if (typeof column.sorter === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n};\nconst getSortFunction = sorter => {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && typeof sorter === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n};\nconst nextSortDirection = (sortDirections, current) => {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n};\nconst collectSortStates = (columns, init, pos) => {\n  let sortStates = [];\n  const pushState = (column, columnPos) => {\n    sortStates.push({\n      column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  };\n  (columns || []).forEach((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n};\nconst injectSorter = (prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) => {\n  const finalColumns = (columns || []).map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    let newColumn = column;\n    if (newColumn.sorter) {\n      const sortDirections = newColumn.sortDirections || defaultSortDirections;\n      const showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const sorterState = sorterStates.find(({\n        key\n      }) => key === columnKey);\n      const sortOrder = sorterState ? sorterState.sortOrder : null;\n      const nextSortOrder = nextSortDirection(sortDirections, sortOrder);\n      let sorter;\n      if (column.sortIcon) {\n        sorter = column.sortIcon({\n          sortOrder\n        });\n      } else {\n        const upNode = sortDirections.includes(ASCEND) && (/*#__PURE__*/React.createElement(CaretUpOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-up`, {\n            active: sortOrder === ASCEND\n          })\n        }));\n        const downNode = sortDirections.includes(DESCEND) && (/*#__PURE__*/React.createElement(CaretDownOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-down`, {\n            active: sortOrder === DESCEND\n          })\n        }));\n        sorter = /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(`${prefixCls}-column-sorter`, {\n            [`${prefixCls}-column-sorter-full`]: !!(upNode && downNode)\n          })\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-column-sorter-inner`,\n          \"aria-hidden\": \"true\"\n        }, upNode, downNode));\n      }\n      const {\n        cancelSort,\n        triggerAsc,\n        triggerDesc\n      } = tableLocale || {};\n      let sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      const tooltipProps = typeof showSorterTooltip === 'object' ? Object.assign({\n        title: sortTip\n      }, showSorterTooltip) : {\n        title: sortTip\n      };\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        className: classNames(newColumn.className, {\n          [`${prefixCls}-column-sort`]: sortOrder\n        }),\n        title: renderProps => {\n          const columnSortersClass = `${prefixCls}-column-sorters`;\n          const renderColumnTitleWrapper = /*#__PURE__*/React.createElement(\"span\", {\n            className: `${prefixCls}-column-title`\n          }, renderColumnTitle(column.title, renderProps));\n          const renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: columnSortersClass\n          }, renderColumnTitleWrapper, sorter);\n          if (showSorterTooltip) {\n            if (typeof showSorterTooltip !== 'boolean' && (showSorterTooltip === null || showSorterTooltip === void 0 ? void 0 : showSorterTooltip.target) === 'sorter-icon') {\n              return /*#__PURE__*/React.createElement(\"div\", {\n                className: `${columnSortersClass} ${prefixCls}-column-sorters-tooltip-target-sorter`\n              }, renderColumnTitleWrapper, /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), sorter));\n            }\n            return /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), renderSortTitle);\n          }\n          return renderSortTitle;\n        },\n        onHeaderCell: col => {\n          var _a;\n          const cell = ((_a = column.onHeaderCell) === null || _a === void 0 ? void 0 : _a.call(column, col)) || {};\n          const originOnClick = cell.onClick;\n          const originOKeyDown = cell.onKeyDown;\n          cell.onClick = event => {\n            triggerSorter({\n              column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = event => {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          const renderTitle = safeColumnTitle(column.title, {});\n          const displayTitle = renderTitle === null || renderTitle === void 0 ? void 0 : renderTitle.toString();\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sortOrder) {\n            cell['aria-sort'] = sortOrder === 'ascend' ? 'ascending' : 'descending';\n          }\n          cell['aria-label'] = displayTitle || '';\n          cell.className = classNames(cell.className, `${prefixCls}-column-has-sorters`);\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = (renderTitle !== null && renderTitle !== void 0 ? renderTitle : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n  return finalColumns;\n};\nconst stateToInfo = sorterState => {\n  const {\n    column,\n    sortOrder\n  } = sorterState;\n  return {\n    column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n};\nconst generateSorterInfo = sorterStates => {\n  const activeSorters = sorterStates.filter(({\n    sortOrder\n  }) => sortOrder).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (activeSorters.length === 0 && sorterStates.length) {\n    const lastIndex = sorterStates.length - 1;\n    return Object.assign(Object.assign({}, stateToInfo(sorterStates[lastIndex])), {\n      column: undefined,\n      order: undefined,\n      field: undefined,\n      columnKey: undefined\n    });\n  }\n  if (activeSorters.length <= 1) {\n    return activeSorters[0] || {};\n  }\n  return activeSorters;\n};\nexport const getSortData = (data, sortStates, childrenColumnName) => {\n  const innerSorterStates = sortStates.slice().sort((a, b) => b.multiplePriority - a.multiplePriority);\n  const cloneData = data.slice();\n  const runningSorters = innerSorterStates.filter(({\n    column: {\n      sorter\n    },\n    sortOrder\n  }) => getSortFunction(sorter) && sortOrder);\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort((record1, record2) => {\n    for (let i = 0; i < runningSorters.length; i += 1) {\n      const sorterState = runningSorters[i];\n      const {\n        column: {\n          sorter\n        },\n        sortOrder\n      } = sorterState;\n      const compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        const compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(record => {\n    const subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return Object.assign(Object.assign({}, record), {\n        [childrenColumnName]: getSortData(subRecords, sortStates, childrenColumnName)\n      });\n    }\n    return record;\n  });\n};\nconst useFilterSorter = props => {\n  const {\n    prefixCls,\n    mergedColumns,\n    sortDirections,\n    tableLocale,\n    showSorterTooltip,\n    onSorterChange\n  } = props;\n  const [sortStates, setSortStates] = React.useState(() => collectSortStates(mergedColumns, true));\n  const getColumnKeys = (columns, pos) => {\n    const newKeys = [];\n    columns.forEach((item, index) => {\n      const columnPos = getColumnPos(index, pos);\n      newKeys.push(getColumnKey(item, columnPos));\n      if (Array.isArray(item.children)) {\n        const childKeys = getColumnKeys(item.children, columnPos);\n        newKeys.push.apply(newKeys, _toConsumableArray(childKeys));\n      }\n    });\n    return newKeys;\n  };\n  const mergedSorterStates = React.useMemo(() => {\n    let validate = true;\n    const collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      const mergedColumnsKeys = getColumnKeys(mergedColumns);\n      return sortStates.filter(({\n        key\n      }) => mergedColumnsKeys.includes(key));\n    }\n    const validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(Object.assign(Object.assign({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    let multipleMode = null;\n    collectedStates.forEach(state => {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  const columnTitleSorterProps = React.useMemo(() => {\n    var _a, _b;\n    const sortColumns = mergedSorterStates.map(({\n      column,\n      sortOrder\n    }) => ({\n      column,\n      order: sortOrder\n    }));\n    return {\n      sortColumns,\n      // Legacy\n      sortColumn: (_a = sortColumns[0]) === null || _a === void 0 ? void 0 : _a.column,\n      sortOrder: (_b = sortColumns[0]) === null || _b === void 0 ? void 0 : _b.order\n    };\n  }, [mergedSorterStates]);\n  const triggerSorter = sortState => {\n    let newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(({\n        key\n      }) => key !== sortState.key)), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  };\n  const transformColumns = innerColumns => injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  const getSorters = () => generateSorterInfo(mergedSorterStates);\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n};\nexport default useFilterSorter;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,SAAS;AACxF,MAAMC,MAAM,GAAG,QAAQ;AACvB,MAAMC,OAAO,GAAG,SAAS;AACzB,MAAMC,mBAAmB,GAAGC,MAAM,IAAI;EACpC,IAAI,OAAOA,MAAM,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACC,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACnF,OAAOF,MAAM,CAACC,MAAM,CAACC,QAAQ;EAC/B;EACA,OAAO,KAAK;AACd,CAAC;AACD,MAAMC,eAAe,GAAGF,MAAM,IAAI;EAChC,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM;EACf;EACA,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACG,OAAO,EAAE;IAC1D,OAAOH,MAAM,CAACG,OAAO;EACvB;EACA,OAAO,KAAK;AACd,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAACC,cAAc,EAAEC,OAAO,KAAK;EACrD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOD,cAAc,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOA,cAAc,CAACA,cAAc,CAACE,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AACD,MAAME,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,KAAK;EAChD,IAAIC,UAAU,GAAG,EAAE;EACnB,MAAMC,SAAS,GAAGA,CAACd,MAAM,EAAEe,SAAS,KAAK;IACvCF,UAAU,CAACG,IAAI,CAAC;MACdhB,MAAM;MACNiB,GAAG,EAAExB,YAAY,CAACO,MAAM,EAAEe,SAAS,CAAC;MACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;MAC7CmB,SAAS,EAAEnB,MAAM,CAACmB;IACpB,CAAC,CAAC;EACJ,CAAC;EACD,CAACT,OAAO,IAAI,EAAE,EAAEU,OAAO,CAAC,CAACpB,MAAM,EAAEqB,KAAK,KAAK;IACzC,MAAMN,SAAS,GAAGrB,YAAY,CAAC2B,KAAK,EAAET,GAAG,CAAC;IAC1C,IAAIZ,MAAM,CAACsB,QAAQ,EAAE;MACnB,IAAI,WAAW,IAAItB,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B;MACAF,UAAU,GAAG,EAAE,CAACU,MAAM,CAACrC,kBAAkB,CAAC2B,UAAU,CAAC,EAAE3B,kBAAkB,CAACuB,iBAAiB,CAACT,MAAM,CAACsB,QAAQ,EAAEX,IAAI,EAAEI,SAAS,CAAC,CAAC,CAAC;IACjI,CAAC,MAAM,IAAIf,MAAM,CAACC,MAAM,EAAE;MACxB,IAAI,WAAW,IAAID,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B,CAAC,MAAM,IAAIJ,IAAI,IAAIX,MAAM,CAACwB,gBAAgB,EAAE;QAC1C;QACAX,UAAU,CAACG,IAAI,CAAC;UACdhB,MAAM;UACNiB,GAAG,EAAExB,YAAY,CAACO,MAAM,EAAEe,SAAS,CAAC;UACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;UAC7CmB,SAAS,EAAEnB,MAAM,CAACwB;QACpB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EACF,OAAOX,UAAU;AACnB,CAAC;AACD,MAAMY,YAAY,GAAGA,CAACC,SAAS,EAAEhB,OAAO,EAAEiB,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEnB,GAAG,KAAK;EACzI,MAAMoB,YAAY,GAAG,CAACtB,OAAO,IAAI,EAAE,EAAEuB,GAAG,CAAC,CAACjC,MAAM,EAAEqB,KAAK,KAAK;IAC1D,MAAMN,SAAS,GAAGrB,YAAY,CAAC2B,KAAK,EAAET,GAAG,CAAC;IAC1C,IAAIsB,SAAS,GAAGlC,MAAM;IACtB,IAAIkC,SAAS,CAACjC,MAAM,EAAE;MACpB,MAAMK,cAAc,GAAG4B,SAAS,CAAC5B,cAAc,IAAIuB,qBAAqB;MACxE,MAAMM,iBAAiB,GAAGD,SAAS,CAACC,iBAAiB,KAAKC,SAAS,GAAGL,sBAAsB,GAAGG,SAAS,CAACC,iBAAiB;MAC1H,MAAME,SAAS,GAAG5C,YAAY,CAACyC,SAAS,EAAEnB,SAAS,CAAC;MACpD,MAAMuB,WAAW,GAAGX,YAAY,CAACY,IAAI,CAAC,CAAC;QACrCtB;MACF,CAAC,KAAKA,GAAG,KAAKoB,SAAS,CAAC;MACxB,MAAMlB,SAAS,GAAGmB,WAAW,GAAGA,WAAW,CAACnB,SAAS,GAAG,IAAI;MAC5D,MAAMqB,aAAa,GAAGnC,iBAAiB,CAACC,cAAc,EAAEa,SAAS,CAAC;MAClE,IAAIlB,MAAM;MACV,IAAID,MAAM,CAACyC,QAAQ,EAAE;QACnBxC,MAAM,GAAGD,MAAM,CAACyC,QAAQ,CAAC;UACvBtB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMuB,MAAM,GAAGpC,cAAc,CAACqC,QAAQ,CAAC9C,MAAM,CAAC,KAAK,aAAaV,KAAK,CAACyD,aAAa,CAACvD,eAAe,EAAE;UACnGwD,SAAS,EAAEvD,UAAU,CAAC,GAAGoC,SAAS,mBAAmB,EAAE;YACrDoB,MAAM,EAAE3B,SAAS,KAAKtB;UACxB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAMkD,QAAQ,GAAGzC,cAAc,CAACqC,QAAQ,CAAC7C,OAAO,CAAC,KAAK,aAAaX,KAAK,CAACyD,aAAa,CAACxD,iBAAiB,EAAE;UACxGyD,SAAS,EAAEvD,UAAU,CAAC,GAAGoC,SAAS,qBAAqB,EAAE;YACvDoB,MAAM,EAAE3B,SAAS,KAAKrB;UACxB,CAAC;QACH,CAAC,CAAC,CAAC;QACHG,MAAM,GAAG,aAAad,KAAK,CAACyD,aAAa,CAAC,MAAM,EAAE;UAChDC,SAAS,EAAEvD,UAAU,CAAC,GAAGoC,SAAS,gBAAgB,EAAE;YAClD,CAAC,GAAGA,SAAS,qBAAqB,GAAG,CAAC,EAAEgB,MAAM,IAAIK,QAAQ;UAC5D,CAAC;QACH,CAAC,EAAE,aAAa5D,KAAK,CAACyD,aAAa,CAAC,MAAM,EAAE;UAC1CC,SAAS,EAAE,GAAGnB,SAAS,sBAAsB;UAC7C,aAAa,EAAE;QACjB,CAAC,EAAEgB,MAAM,EAAEK,QAAQ,CAAC,CAAC;MACvB;MACA,MAAM;QACJC,UAAU;QACVC,UAAU;QACVC;MACF,CAAC,GAAGpB,WAAW,IAAI,CAAC,CAAC;MACrB,IAAIqB,OAAO,GAAGH,UAAU;MACxB,IAAIR,aAAa,KAAK1C,OAAO,EAAE;QAC7BqD,OAAO,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAIV,aAAa,KAAK3C,MAAM,EAAE;QACnCsD,OAAO,GAAGF,UAAU;MACtB;MACA,MAAMG,YAAY,GAAG,OAAOjB,iBAAiB,KAAK,QAAQ,GAAGkB,MAAM,CAACC,MAAM,CAAC;QACzEC,KAAK,EAAEJ;MACT,CAAC,EAAEhB,iBAAiB,CAAC,GAAG;QACtBoB,KAAK,EAAEJ;MACT,CAAC;MACDjB,SAAS,GAAGmB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,SAAS,CAAC,EAAE;QACtDW,SAAS,EAAEvD,UAAU,CAAC4C,SAAS,CAACW,SAAS,EAAE;UACzC,CAAC,GAAGnB,SAAS,cAAc,GAAGP;QAChC,CAAC,CAAC;QACFoC,KAAK,EAAEC,WAAW,IAAI;UACpB,MAAMC,kBAAkB,GAAG,GAAG/B,SAAS,iBAAiB;UACxD,MAAMgC,wBAAwB,GAAG,aAAavE,KAAK,CAACyD,aAAa,CAAC,MAAM,EAAE;YACxEC,SAAS,EAAE,GAAGnB,SAAS;UACzB,CAAC,EAAE/B,iBAAiB,CAACK,MAAM,CAACuD,KAAK,EAAEC,WAAW,CAAC,CAAC;UAChD,MAAMG,eAAe,GAAG,aAAaxE,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;YAC9DC,SAAS,EAAEY;UACb,CAAC,EAAEC,wBAAwB,EAAEzD,MAAM,CAAC;UACpC,IAAIkC,iBAAiB,EAAE;YACrB,IAAI,OAAOA,iBAAiB,KAAK,SAAS,IAAI,CAACA,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACyB,MAAM,MAAM,aAAa,EAAE;cAChK,OAAO,aAAazE,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;gBAC7CC,SAAS,EAAE,GAAGY,kBAAkB,IAAI/B,SAAS;cAC/C,CAAC,EAAEgC,wBAAwB,EAAE,aAAavE,KAAK,CAACyD,aAAa,CAACpD,OAAO,EAAE6D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,CAAC,EAAEnD,MAAM,CAAC,CAAC;YAClH;YACA,OAAO,aAAad,KAAK,CAACyD,aAAa,CAACpD,OAAO,EAAE6D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,CAAC,EAAEO,eAAe,CAAC;UACpG;UACA,OAAOA,eAAe;QACxB,CAAC;QACDE,YAAY,EAAEC,GAAG,IAAI;UACnB,IAAIC,EAAE;UACN,MAAMC,IAAI,GAAG,CAAC,CAACD,EAAE,GAAG/D,MAAM,CAAC6D,YAAY,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACjE,MAAM,EAAE8D,GAAG,CAAC,KAAK,CAAC,CAAC;UACzG,MAAMI,aAAa,GAAGF,IAAI,CAACG,OAAO;UAClC,MAAMC,cAAc,GAAGJ,IAAI,CAACK,SAAS;UACrCL,IAAI,CAACG,OAAO,GAAGG,KAAK,IAAI;YACtB1C,aAAa,CAAC;cACZ5B,MAAM;cACNiB,GAAG,EAAEoB,SAAS;cACdlB,SAAS,EAAEqB,aAAa;cACxBtB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;YAC9C,CAAC,CAAC;YACFkE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,KAAK,CAAC;UACpF,CAAC;UACDN,IAAI,CAACK,SAAS,GAAGC,KAAK,IAAI;YACxB,IAAIA,KAAK,CAACC,OAAO,KAAKhF,OAAO,CAACiF,KAAK,EAAE;cACnC5C,aAAa,CAAC;gBACZ5B,MAAM;gBACNiB,GAAG,EAAEoB,SAAS;gBACdlB,SAAS,EAAEqB,aAAa;gBACxBtB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;cAC9C,CAAC,CAAC;cACFoE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,KAAK,CAAC;YACvF;UACF,CAAC;UACD,MAAMG,WAAW,GAAG7E,eAAe,CAACI,MAAM,CAACuD,KAAK,EAAE,CAAC,CAAC,CAAC;UACrD,MAAMmB,YAAY,GAAGD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,QAAQ,CAAC,CAAC;UACrG;UACA,IAAIxD,SAAS,EAAE;YACb6C,IAAI,CAAC,WAAW,CAAC,GAAG7C,SAAS,KAAK,QAAQ,GAAG,WAAW,GAAG,YAAY;UACzE;UACA6C,IAAI,CAAC,YAAY,CAAC,GAAGU,YAAY,IAAI,EAAE;UACvCV,IAAI,CAACnB,SAAS,GAAGvD,UAAU,CAAC0E,IAAI,CAACnB,SAAS,EAAE,GAAGnB,SAAS,qBAAqB,CAAC;UAC9EsC,IAAI,CAACY,QAAQ,GAAG,CAAC;UACjB,IAAI5E,MAAM,CAAC6E,QAAQ,EAAE;YACnBb,IAAI,CAACT,KAAK,GAAG,CAACkB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,EAAE,EAAEE,QAAQ,CAAC,CAAC;UAC7F;UACA,OAAOX,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IACA,IAAI,UAAU,IAAI9B,SAAS,EAAE;MAC3BA,SAAS,GAAGmB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,SAAS,CAAC,EAAE;QACtDZ,QAAQ,EAAEG,YAAY,CAACC,SAAS,EAAEQ,SAAS,CAACZ,QAAQ,EAAEK,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEhB,SAAS;MAC1J,CAAC,CAAC;IACJ;IACA,OAAOmB,SAAS;EAClB,CAAC,CAAC;EACF,OAAOF,YAAY;AACrB,CAAC;AACD,MAAM8C,WAAW,GAAGxC,WAAW,IAAI;EACjC,MAAM;IACJtC,MAAM;IACNmB;EACF,CAAC,GAAGmB,WAAW;EACf,OAAO;IACLtC,MAAM;IACN+E,KAAK,EAAE5D,SAAS;IAChB6D,KAAK,EAAEhF,MAAM,CAACiF,SAAS;IACvB5C,SAAS,EAAErC,MAAM,CAACiB;EACpB,CAAC;AACH,CAAC;AACD,MAAMiE,kBAAkB,GAAGvD,YAAY,IAAI;EACzC,MAAMwD,aAAa,GAAGxD,YAAY,CAACyD,MAAM,CAAC,CAAC;IACzCjE;EACF,CAAC,KAAKA,SAAS,CAAC,CAACc,GAAG,CAAC6C,WAAW,CAAC;EACjC;EACA;EACA,IAAIK,aAAa,CAACE,MAAM,KAAK,CAAC,IAAI1D,YAAY,CAAC0D,MAAM,EAAE;IACrD,MAAMC,SAAS,GAAG3D,YAAY,CAAC0D,MAAM,GAAG,CAAC;IACzC,OAAOhC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwB,WAAW,CAACnD,YAAY,CAAC2D,SAAS,CAAC,CAAC,CAAC,EAAE;MAC5EtF,MAAM,EAAEoC,SAAS;MACjB2C,KAAK,EAAE3C,SAAS;MAChB4C,KAAK,EAAE5C,SAAS;MAChBC,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;EACA,IAAI+C,aAAa,CAACE,MAAM,IAAI,CAAC,EAAE;IAC7B,OAAOF,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EAC/B;EACA,OAAOA,aAAa;AACtB,CAAC;AACD,OAAO,MAAMI,WAAW,GAAGA,CAACC,IAAI,EAAE3E,UAAU,EAAE4E,kBAAkB,KAAK;EACnE,MAAMC,iBAAiB,GAAG7E,UAAU,CAAC8E,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC5E,gBAAgB,GAAG2E,CAAC,CAAC3E,gBAAgB,CAAC;EACpG,MAAM6E,SAAS,GAAGP,IAAI,CAACG,KAAK,CAAC,CAAC;EAC9B,MAAMK,cAAc,GAAGN,iBAAiB,CAACN,MAAM,CAAC,CAAC;IAC/CpF,MAAM,EAAE;MACNC;IACF,CAAC;IACDkB;EACF,CAAC,KAAKhB,eAAe,CAACF,MAAM,CAAC,IAAIkB,SAAS,CAAC;EAC3C;EACA,IAAI,CAAC6E,cAAc,CAACX,MAAM,EAAE;IAC1B,OAAOU,SAAS;EAClB;EACA,OAAOA,SAAS,CAACH,IAAI,CAAC,CAACK,OAAO,EAAEC,OAAO,KAAK;IAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,cAAc,CAACX,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;MACjD,MAAM7D,WAAW,GAAG0D,cAAc,CAACG,CAAC,CAAC;MACrC,MAAM;QACJnG,MAAM,EAAE;UACNC;QACF,CAAC;QACDkB;MACF,CAAC,GAAGmB,WAAW;MACf,MAAM8D,SAAS,GAAGjG,eAAe,CAACF,MAAM,CAAC;MACzC,IAAImG,SAAS,IAAIjF,SAAS,EAAE;QAC1B,MAAMkF,aAAa,GAAGD,SAAS,CAACH,OAAO,EAAEC,OAAO,EAAE/E,SAAS,CAAC;QAC5D,IAAIkF,aAAa,KAAK,CAAC,EAAE;UACvB,OAAOlF,SAAS,KAAKtB,MAAM,GAAGwG,aAAa,GAAG,CAACA,aAAa;QAC9D;MACF;IACF;IACA,OAAO,CAAC;EACV,CAAC,CAAC,CAACpE,GAAG,CAACqE,MAAM,IAAI;IACf,MAAMC,UAAU,GAAGD,MAAM,CAACb,kBAAkB,CAAC;IAC7C,IAAIc,UAAU,EAAE;MACd,OAAOlD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgD,MAAM,CAAC,EAAE;QAC9C,CAACb,kBAAkB,GAAGF,WAAW,CAACgB,UAAU,EAAE1F,UAAU,EAAE4E,kBAAkB;MAC9E,CAAC,CAAC;IACJ;IACA,OAAOa,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AACD,MAAME,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJ/E,SAAS;IACTgF,aAAa;IACbpG,cAAc;IACdwB,WAAW;IACXK,iBAAiB;IACjBwE;EACF,CAAC,GAAGF,KAAK;EACT,MAAM,CAAC5F,UAAU,EAAE+F,aAAa,CAAC,GAAGzH,KAAK,CAAC0H,QAAQ,CAAC,MAAMpG,iBAAiB,CAACiG,aAAa,EAAE,IAAI,CAAC,CAAC;EAChG,MAAMI,aAAa,GAAGA,CAACpG,OAAO,EAAEE,GAAG,KAAK;IACtC,MAAMmG,OAAO,GAAG,EAAE;IAClBrG,OAAO,CAACU,OAAO,CAAC,CAAC4F,IAAI,EAAE3F,KAAK,KAAK;MAC/B,MAAMN,SAAS,GAAGrB,YAAY,CAAC2B,KAAK,EAAET,GAAG,CAAC;MAC1CmG,OAAO,CAAC/F,IAAI,CAACvB,YAAY,CAACuH,IAAI,EAAEjG,SAAS,CAAC,CAAC;MAC3C,IAAIkG,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC1F,QAAQ,CAAC,EAAE;QAChC,MAAM6F,SAAS,GAAGL,aAAa,CAACE,IAAI,CAAC1F,QAAQ,EAAEP,SAAS,CAAC;QACzDgG,OAAO,CAAC/F,IAAI,CAACoG,KAAK,CAACL,OAAO,EAAE7H,kBAAkB,CAACiI,SAAS,CAAC,CAAC;MAC5D;IACF,CAAC,CAAC;IACF,OAAOJ,OAAO;EAChB,CAAC;EACD,MAAMM,kBAAkB,GAAGlI,KAAK,CAACmI,OAAO,CAAC,MAAM;IAC7C,IAAIC,QAAQ,GAAG,IAAI;IACnB,MAAMC,eAAe,GAAG/G,iBAAiB,CAACiG,aAAa,EAAE,KAAK,CAAC;IAC/D;IACA,IAAI,CAACc,eAAe,CAACnC,MAAM,EAAE;MAC3B,MAAMoC,iBAAiB,GAAGX,aAAa,CAACJ,aAAa,CAAC;MACtD,OAAO7F,UAAU,CAACuE,MAAM,CAAC,CAAC;QACxBnE;MACF,CAAC,KAAKwG,iBAAiB,CAAC9E,QAAQ,CAAC1B,GAAG,CAAC,CAAC;IACxC;IACA,MAAMyG,cAAc,GAAG,EAAE;IACzB,SAASC,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIL,QAAQ,EAAE;QACZG,cAAc,CAAC1G,IAAI,CAAC4G,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLF,cAAc,CAAC1G,IAAI,CAACqC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC,EAAE;UAC1DzG,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL;IACF;IACA,IAAI0G,YAAY,GAAG,IAAI;IACvBL,eAAe,CAACpG,OAAO,CAACwG,KAAK,IAAI;MAC/B,IAAIC,YAAY,KAAK,IAAI,EAAE;QACzBF,WAAW,CAACC,KAAK,CAAC;QAClB,IAAIA,KAAK,CAACzG,SAAS,EAAE;UACnB,IAAIyG,KAAK,CAAC1G,gBAAgB,KAAK,KAAK,EAAE;YACpCqG,QAAQ,GAAG,KAAK;UAClB,CAAC,MAAM;YACLM,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC,MAAM,IAAIA,YAAY,IAAID,KAAK,CAAC1G,gBAAgB,KAAK,KAAK,EAAE;QAC3DyG,WAAW,CAACC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLL,QAAQ,GAAG,KAAK;QAChBI,WAAW,CAACC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAAChB,aAAa,EAAE7F,UAAU,CAAC,CAAC;EAC/B;EACA,MAAMiH,sBAAsB,GAAG3I,KAAK,CAACmI,OAAO,CAAC,MAAM;IACjD,IAAIvD,EAAE,EAAEgE,EAAE;IACV,MAAMC,WAAW,GAAGX,kBAAkB,CAACpF,GAAG,CAAC,CAAC;MAC1CjC,MAAM;MACNmB;IACF,CAAC,MAAM;MACLnB,MAAM;MACN+E,KAAK,EAAE5D;IACT,CAAC,CAAC,CAAC;IACH,OAAO;MACL6G,WAAW;MACX;MACAC,UAAU,EAAE,CAAClE,EAAE,GAAGiE,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIjE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/D,MAAM;MAChFmB,SAAS,EAAE,CAAC4G,EAAE,GAAGC,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChD;IAC3E,CAAC;EACH,CAAC,EAAE,CAACsC,kBAAkB,CAAC,CAAC;EACxB,MAAMzF,aAAa,GAAGsG,SAAS,IAAI;IACjC,IAAIC,eAAe;IACnB,IAAID,SAAS,CAAChH,gBAAgB,KAAK,KAAK,IAAI,CAACmG,kBAAkB,CAAChC,MAAM,IAAIgC,kBAAkB,CAAC,CAAC,CAAC,CAACnG,gBAAgB,KAAK,KAAK,EAAE;MAC1HiH,eAAe,GAAG,CAACD,SAAS,CAAC;IAC/B,CAAC,MAAM;MACLC,eAAe,GAAG,EAAE,CAAC5G,MAAM,CAACrC,kBAAkB,CAACmI,kBAAkB,CAACjC,MAAM,CAAC,CAAC;QACxEnE;MACF,CAAC,KAAKA,GAAG,KAAKiH,SAAS,CAACjH,GAAG,CAAC,CAAC,EAAE,CAACiH,SAAS,CAAC,CAAC;IAC7C;IACAtB,aAAa,CAACuB,eAAe,CAAC;IAC9BxB,cAAc,CAACzB,kBAAkB,CAACiD,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE,CAAC;EACD,MAAMC,gBAAgB,GAAGC,YAAY,IAAI5G,YAAY,CAACC,SAAS,EAAE2G,YAAY,EAAEhB,kBAAkB,EAAEzF,aAAa,EAAEtB,cAAc,EAAEwB,WAAW,EAAEK,iBAAiB,CAAC;EACjK,MAAMmG,UAAU,GAAGA,CAAA,KAAMpD,kBAAkB,CAACmC,kBAAkB,CAAC;EAC/D,OAAO,CAACe,gBAAgB,EAAEf,kBAAkB,EAAES,sBAAsB,EAAEQ,UAAU,CAAC;AACnF,CAAC;AACD,eAAe9B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"activeHandleRender\", \"draggingIndex\", \"draggingDelete\", \"onFocus\"];\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { getIndex } from \"../util\";\nimport Handle from \"./Handle\";\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    draggingIndex = props.draggingIndex,\n    draggingDelete = props.draggingDelete,\n    onFocus = props.onFocus,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n\n  // =========================== Active ===========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeVisible = _React$useState2[0],\n    setActiveVisible = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeIndex = _React$useState4[0],\n    setActiveIndex = _React$useState4[1];\n  var onActive = function onActive(index) {\n    setActiveIndex(index);\n    setActiveVisible(true);\n  };\n  var onHandleFocus = function onHandleFocus(e, index) {\n    onActive(index);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var onHandleMouseEnter = function onHandleMouseEnter(e, index) {\n    onActive(index);\n  };\n\n  // =========================== Render ===========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 || _handlesRef$current$i.focus();\n      },\n      hideHelp: function hideHelp() {\n        flushSync(function () {\n          setActiveVisible(false);\n        });\n      }\n    };\n  });\n\n  // =========================== Render ===========================\n  // Handle Props\n  var handleProps = _objectSpread({\n    prefixCls: prefixCls,\n    onStartMove: onStartMove,\n    onOffsetChange: onOffsetChange,\n    render: handleRender,\n    onFocus: onHandleFocus,\n    onMouseEnter: onHandleMouseEnter\n  }, restProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    var dragging = draggingIndex === index;\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: dragging,\n      draggingDelete: dragging && draggingDelete,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index\n    }, handleProps));\n  }), activeHandleRender && activeVisible && /*#__PURE__*/React.createElement(Handle, _extends({\n    key: \"a11y\"\n  }, handleProps, {\n    value: values[activeIndex],\n    valueIndex: null,\n    dragging: draggingIndex !== -1,\n    draggingDelete: draggingDelete,\n    render: activeHandleRender,\n    style: {\n      pointerEvents: 'none'\n    },\n    tabIndex: null,\n    \"aria-hidden\": true\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "flushSync", "getIndex", "<PERSON><PERSON>", "<PERSON><PERSON>", "forwardRef", "props", "ref", "prefixCls", "style", "onStartMove", "onOffsetChange", "values", "handleRender", "activeHandleRender", "draggingIndex", "draggingDelete", "onFocus", "restProps", "handlesRef", "useRef", "_React$useState", "useState", "_React$useState2", "activeVisible", "setActiveVisible", "_React$useState3", "_React$useState4", "activeIndex", "setActiveIndex", "onActive", "index", "onHandleFocus", "e", "onHandleMouseEnter", "useImperativeHandle", "focus", "_handlesRef$current$i", "current", "hideHelp", "handleProps", "render", "onMouseEnter", "createElement", "Fragment", "map", "value", "dragging", "node", "key", "valueIndex", "pointerEvents", "tabIndex", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-slider@11.1.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-slider/es/Handles/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"activeHandleRender\", \"draggingIndex\", \"draggingDelete\", \"onFocus\"];\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { getIndex } from \"../util\";\nimport Handle from \"./Handle\";\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    draggingIndex = props.draggingIndex,\n    draggingDelete = props.draggingDelete,\n    onFocus = props.onFocus,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n\n  // =========================== Active ===========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeVisible = _React$useState2[0],\n    setActiveVisible = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeIndex = _React$useState4[0],\n    setActiveIndex = _React$useState4[1];\n  var onActive = function onActive(index) {\n    setActiveIndex(index);\n    setActiveVisible(true);\n  };\n  var onHandleFocus = function onHandleFocus(e, index) {\n    onActive(index);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var onHandleMouseEnter = function onHandleMouseEnter(e, index) {\n    onActive(index);\n  };\n\n  // =========================== Render ===========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 || _handlesRef$current$i.focus();\n      },\n      hideHelp: function hideHelp() {\n        flushSync(function () {\n          setActiveVisible(false);\n        });\n      }\n    };\n  });\n\n  // =========================== Render ===========================\n  // Handle Props\n  var handleProps = _objectSpread({\n    prefixCls: prefixCls,\n    onStartMove: onStartMove,\n    onOffsetChange: onOffsetChange,\n    render: handleRender,\n    onFocus: onHandleFocus,\n    onMouseEnter: onHandleMouseEnter\n  }, restProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    var dragging = draggingIndex === index;\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: dragging,\n      draggingDelete: dragging && draggingDelete,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index\n    }, handleProps));\n  }), activeHandleRender && activeVisible && /*#__PURE__*/React.createElement(Handle, _extends({\n    key: \"a11y\"\n  }, handleProps, {\n    value: values[activeIndex],\n    valueIndex: null,\n    dragging: draggingIndex !== -1,\n    draggingDelete: draggingDelete,\n    render: activeHandleRender,\n    style: {\n      pointerEvents: 'none'\n    },\n    tabIndex: null,\n    \"aria-hidden\": true\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,oBAAoB,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,CAAC;AACrK,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,MAAM,MAAM,UAAU;AAC7B,IAAIC,OAAO,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAChE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,SAAS,GAAGpB,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACxD,IAAIoB,UAAU,GAAGnB,KAAK,CAACoB,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,IAAIC,eAAe,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAG1B,cAAc,CAACwB,eAAe,EAAE,CAAC,CAAC;IACrDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAG1B,KAAK,CAACsB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCK,gBAAgB,GAAG9B,cAAc,CAAC6B,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtCF,cAAc,CAACE,KAAK,CAAC;IACrBN,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EACD,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACC,CAAC,EAAEF,KAAK,EAAE;IACnDD,QAAQ,CAACC,KAAK,CAAC;IACfd,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACgB,CAAC,CAAC;EACtD,CAAC;EACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACD,CAAC,EAAEF,KAAK,EAAE;IAC7DD,QAAQ,CAACC,KAAK,CAAC;EACjB,CAAC;;EAED;EACA/B,KAAK,CAACmC,mBAAmB,CAAC5B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL6B,KAAK,EAAE,SAASA,KAAKA,CAACL,KAAK,EAAE;QAC3B,IAAIM,qBAAqB;QACzB,CAACA,qBAAqB,GAAGlB,UAAU,CAACmB,OAAO,CAACP,KAAK,CAAC,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACD,KAAK,CAAC,CAAC;MACnI,CAAC;MACDG,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5BtC,SAAS,CAAC,YAAY;UACpBwB,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA;EACA,IAAIe,WAAW,GAAG5C,aAAa,CAAC;IAC9BY,SAAS,EAAEA,SAAS;IACpBE,WAAW,EAAEA,WAAW;IACxBC,cAAc,EAAEA,cAAc;IAC9B8B,MAAM,EAAE5B,YAAY;IACpBI,OAAO,EAAEe,aAAa;IACtBU,YAAY,EAAER;EAChB,CAAC,EAAEhB,SAAS,CAAC;EACb,OAAO,aAAalB,KAAK,CAAC2C,aAAa,CAAC3C,KAAK,CAAC4C,QAAQ,EAAE,IAAI,EAAEhC,MAAM,CAACiC,GAAG,CAAC,UAAUC,KAAK,EAAEf,KAAK,EAAE;IAC/F,IAAIgB,QAAQ,GAAGhC,aAAa,KAAKgB,KAAK;IACtC,OAAO,aAAa/B,KAAK,CAAC2C,aAAa,CAACxC,MAAM,EAAER,QAAQ,CAAC;MACvDY,GAAG,EAAE,SAASA,GAAGA,CAACyC,IAAI,EAAE;QACtB,IAAI,CAACA,IAAI,EAAE;UACT,OAAO7B,UAAU,CAACmB,OAAO,CAACP,KAAK,CAAC;QAClC,CAAC,MAAM;UACLZ,UAAU,CAACmB,OAAO,CAACP,KAAK,CAAC,GAAGiB,IAAI;QAClC;MACF,CAAC;MACDD,QAAQ,EAAEA,QAAQ;MAClB/B,cAAc,EAAE+B,QAAQ,IAAI/B,cAAc;MAC1CP,KAAK,EAAEP,QAAQ,CAACO,KAAK,EAAEsB,KAAK,CAAC;MAC7BkB,GAAG,EAAElB,KAAK;MACVe,KAAK,EAAEA,KAAK;MACZI,UAAU,EAAEnB;IACd,CAAC,EAAES,WAAW,CAAC,CAAC;EAClB,CAAC,CAAC,EAAE1B,kBAAkB,IAAIU,aAAa,IAAI,aAAaxB,KAAK,CAAC2C,aAAa,CAACxC,MAAM,EAAER,QAAQ,CAAC;IAC3FsD,GAAG,EAAE;EACP,CAAC,EAAET,WAAW,EAAE;IACdM,KAAK,EAAElC,MAAM,CAACgB,WAAW,CAAC;IAC1BsB,UAAU,EAAE,IAAI;IAChBH,QAAQ,EAAEhC,aAAa,KAAK,CAAC,CAAC;IAC9BC,cAAc,EAAEA,cAAc;IAC9ByB,MAAM,EAAE3B,kBAAkB;IAC1BL,KAAK,EAAE;MACL0C,aAAa,EAAE;IACjB,CAAC;IACDC,QAAQ,EAAE,IAAI;IACd,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCnD,OAAO,CAACoD,WAAW,GAAG,SAAS;AACjC;AACA,eAAepD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
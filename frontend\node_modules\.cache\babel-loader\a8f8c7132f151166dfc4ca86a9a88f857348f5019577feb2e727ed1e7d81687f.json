{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport React from 'react';\nimport useItems from \"./hooks/useItems\";\nimport CollapsePanel from \"./Panel\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nfunction getActiveKeysArray(activeKey) {\n  var currentActiveKey = activeKey;\n  if (!Array.isArray(currentActiveKey)) {\n    var activeKeyType = _typeof(currentActiveKey);\n    currentActiveKey = activeKeyType === 'number' || activeKeyType === 'string' ? [currentActiveKey] : [];\n  }\n  return currentActiveKey.map(function (key) {\n    return String(key);\n  });\n}\nvar Collapse = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-collapse' : _props$prefixCls,\n    _props$destroyInactiv = props.destroyInactivePanel,\n    destroyInactivePanel = _props$destroyInactiv === void 0 ? false : _props$destroyInactiv,\n    style = props.style,\n    accordion = props.accordion,\n    className = props.className,\n    children = props.children,\n    collapsible = props.collapsible,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon,\n    rawActiveKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    _onChange = props.onChange,\n    items = props.items;\n  var collapseClassName = classNames(prefixCls, className);\n  var _useMergedState = useMergedState([], {\n      value: rawActiveKey,\n      onChange: function onChange(v) {\n        return _onChange === null || _onChange === void 0 ? void 0 : _onChange(v);\n      },\n      defaultValue: defaultActiveKey,\n      postState: getActiveKeysArray\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    activeKey = _useMergedState2[0],\n    setActiveKey = _useMergedState2[1];\n  var onItemClick = function onItemClick(key) {\n    return setActiveKey(function () {\n      if (accordion) {\n        return activeKey[0] === key ? [] : [key];\n      }\n      var index = activeKey.indexOf(key);\n      var isActive = index > -1;\n      if (isActive) {\n        return activeKey.filter(function (item) {\n          return item !== key;\n        });\n      }\n      return [].concat(_toConsumableArray(activeKey), [key]);\n    });\n  };\n\n  // ======================== Children ========================\n  warning(!children, '[rc-collapse] `children` will be removed in next major version. Please use `items` instead.');\n  var mergedChildren = useItems(items, children, {\n    prefixCls: prefixCls,\n    accordion: accordion,\n    openMotion: openMotion,\n    expandIcon: expandIcon,\n    collapsible: collapsible,\n    destroyInactivePanel: destroyInactivePanel,\n    onItemClick: onItemClick,\n    activeKey: activeKey\n  });\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: collapseClassName,\n    style: style,\n    role: accordion ? 'tablist' : undefined\n  }, pickAttrs(props, {\n    aria: true,\n    data: true\n  })), mergedChildren);\n});\nexport default Object.assign(Collapse, {\n  /**\n   * @deprecated use `items` instead, will be removed in `v4.0.0`\n   */\n  Panel: CollapsePanel\n});", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_slicedToArray", "_typeof", "classNames", "useMergedState", "warning", "React", "useItems", "CollapsePanel", "pickAttrs", "getActiveKeysArray", "active<PERSON><PERSON>", "currentActiveKey", "Array", "isArray", "activeKeyType", "map", "key", "String", "Collapse", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "_props$destroyInactiv", "destroyInactivePanel", "style", "accordion", "className", "children", "collapsible", "openMotion", "expandIcon", "rawActiveKey", "defaultActiveKey", "_onChange", "onChange", "items", "collapseClassName", "_useMergedState", "value", "v", "defaultValue", "postState", "_useMergedState2", "setActiveKey", "onItemClick", "index", "indexOf", "isActive", "filter", "item", "concat", "mergedChildren", "createElement", "role", "undefined", "aria", "data", "Object", "assign", "Panel"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-collapse@3.9.0_react-dom_fc0a782d916d1a18010b31eb7cc9b486/node_modules/rc-collapse/es/Collapse.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport React from 'react';\nimport useItems from \"./hooks/useItems\";\nimport CollapsePanel from \"./Panel\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nfunction getActiveKeysArray(activeKey) {\n  var currentActiveKey = activeKey;\n  if (!Array.isArray(currentActiveKey)) {\n    var activeKeyType = _typeof(currentActiveKey);\n    currentActiveKey = activeKeyType === 'number' || activeKeyType === 'string' ? [currentActiveKey] : [];\n  }\n  return currentActiveKey.map(function (key) {\n    return String(key);\n  });\n}\nvar Collapse = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-collapse' : _props$prefixCls,\n    _props$destroyInactiv = props.destroyInactivePanel,\n    destroyInactivePanel = _props$destroyInactiv === void 0 ? false : _props$destroyInactiv,\n    style = props.style,\n    accordion = props.accordion,\n    className = props.className,\n    children = props.children,\n    collapsible = props.collapsible,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon,\n    rawActiveKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    _onChange = props.onChange,\n    items = props.items;\n  var collapseClassName = classNames(prefixCls, className);\n  var _useMergedState = useMergedState([], {\n      value: rawActiveKey,\n      onChange: function onChange(v) {\n        return _onChange === null || _onChange === void 0 ? void 0 : _onChange(v);\n      },\n      defaultValue: defaultActiveKey,\n      postState: getActiveKeysArray\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    activeKey = _useMergedState2[0],\n    setActiveKey = _useMergedState2[1];\n  var onItemClick = function onItemClick(key) {\n    return setActiveKey(function () {\n      if (accordion) {\n        return activeKey[0] === key ? [] : [key];\n      }\n      var index = activeKey.indexOf(key);\n      var isActive = index > -1;\n      if (isActive) {\n        return activeKey.filter(function (item) {\n          return item !== key;\n        });\n      }\n      return [].concat(_toConsumableArray(activeKey), [key]);\n    });\n  };\n\n  // ======================== Children ========================\n  warning(!children, '[rc-collapse] `children` will be removed in next major version. Please use `items` instead.');\n  var mergedChildren = useItems(items, children, {\n    prefixCls: prefixCls,\n    accordion: accordion,\n    openMotion: openMotion,\n    expandIcon: expandIcon,\n    collapsible: collapsible,\n    destroyInactivePanel: destroyInactivePanel,\n    onItemClick: onItemClick,\n    activeKey: activeKey\n  });\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: collapseClassName,\n    style: style,\n    role: accordion ? 'tablist' : undefined\n  }, pickAttrs(props, {\n    aria: true,\n    data: true\n  })), mergedChildren);\n});\nexport default Object.assign(Collapse, {\n  /**\n   * @deprecated use `items` instead, will be removed in `v4.0.0`\n   */\n  Panel: CollapsePanel\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,kBAAkBA,CAACC,SAAS,EAAE;EACrC,IAAIC,gBAAgB,GAAGD,SAAS;EAChC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACF,gBAAgB,CAAC,EAAE;IACpC,IAAIG,aAAa,GAAGb,OAAO,CAACU,gBAAgB,CAAC;IAC7CA,gBAAgB,GAAGG,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,QAAQ,GAAG,CAACH,gBAAgB,CAAC,GAAG,EAAE;EACvG;EACA,OAAOA,gBAAgB,CAACI,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzC,OAAOC,MAAM,CAACD,GAAG,CAAC;EACpB,CAAC,CAAC;AACJ;AACA,IAAIE,QAAQ,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACjE,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,qBAAqB,GAAGJ,KAAK,CAACK,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACvFE,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,UAAU,GAAGZ,KAAK,CAACY,UAAU;IAC7BC,YAAY,GAAGb,KAAK,CAACV,SAAS;IAC9BwB,gBAAgB,GAAGd,KAAK,CAACc,gBAAgB;IACzCC,SAAS,GAAGf,KAAK,CAACgB,QAAQ;IAC1BC,KAAK,GAAGjB,KAAK,CAACiB,KAAK;EACrB,IAAIC,iBAAiB,GAAGpC,UAAU,CAACqB,SAAS,EAAEK,SAAS,CAAC;EACxD,IAAIW,eAAe,GAAGpC,cAAc,CAAC,EAAE,EAAE;MACrCqC,KAAK,EAAEP,YAAY;MACnBG,QAAQ,EAAE,SAASA,QAAQA,CAACK,CAAC,EAAE;QAC7B,OAAON,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,CAAC,CAAC;MAC3E,CAAC;MACDC,YAAY,EAAER,gBAAgB;MAC9BS,SAAS,EAAElC;IACb,CAAC,CAAC;IACFmC,gBAAgB,GAAG5C,cAAc,CAACuC,eAAe,EAAE,CAAC,CAAC;IACrD7B,SAAS,GAAGkC,gBAAgB,CAAC,CAAC,CAAC;IAC/BC,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAAC9B,GAAG,EAAE;IAC1C,OAAO6B,YAAY,CAAC,YAAY;MAC9B,IAAIlB,SAAS,EAAE;QACb,OAAOjB,SAAS,CAAC,CAAC,CAAC,KAAKM,GAAG,GAAG,EAAE,GAAG,CAACA,GAAG,CAAC;MAC1C;MACA,IAAI+B,KAAK,GAAGrC,SAAS,CAACsC,OAAO,CAAChC,GAAG,CAAC;MAClC,IAAIiC,QAAQ,GAAGF,KAAK,GAAG,CAAC,CAAC;MACzB,IAAIE,QAAQ,EAAE;QACZ,OAAOvC,SAAS,CAACwC,MAAM,CAAC,UAAUC,IAAI,EAAE;UACtC,OAAOA,IAAI,KAAKnC,GAAG;QACrB,CAAC,CAAC;MACJ;MACA,OAAO,EAAE,CAACoC,MAAM,CAACrD,kBAAkB,CAACW,SAAS,CAAC,EAAE,CAACM,GAAG,CAAC,CAAC;IACxD,CAAC,CAAC;EACJ,CAAC;;EAED;EACAZ,OAAO,CAAC,CAACyB,QAAQ,EAAE,6FAA6F,CAAC;EACjH,IAAIwB,cAAc,GAAG/C,QAAQ,CAAC+B,KAAK,EAAER,QAAQ,EAAE;IAC7CN,SAAS,EAAEA,SAAS;IACpBI,SAAS,EAAEA,SAAS;IACpBI,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA,UAAU;IACtBF,WAAW,EAAEA,WAAW;IACxBL,oBAAoB,EAAEA,oBAAoB;IAC1CqB,WAAW,EAAEA,WAAW;IACxBpC,SAAS,EAAEA;EACb,CAAC,CAAC;;EAEF;EACA,OAAO,aAAaL,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAExD,QAAQ,CAAC;IACtDuB,GAAG,EAAEA,GAAG;IACRO,SAAS,EAAEU,iBAAiB;IAC5BZ,KAAK,EAAEA,KAAK;IACZ6B,IAAI,EAAE5B,SAAS,GAAG,SAAS,GAAG6B;EAChC,CAAC,EAAEhD,SAAS,CAACY,KAAK,EAAE;IAClBqC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,EAAEL,cAAc,CAAC;AACtB,CAAC,CAAC;AACF,eAAeM,MAAM,CAACC,MAAM,CAAC1C,QAAQ,EAAE;EACrC;AACF;AACA;EACE2C,KAAK,EAAEtD;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
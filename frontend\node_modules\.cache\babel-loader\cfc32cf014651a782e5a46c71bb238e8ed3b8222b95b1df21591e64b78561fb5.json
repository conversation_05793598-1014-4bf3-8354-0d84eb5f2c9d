{"ast": null, "code": "var Option = function Option() {\n  return null;\n};\nexport default Option;", "map": {"version": 3, "names": ["Option"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-mentions@2.20.0_react-do_10ce709700cdbca87e7a23d808de1d8b/node_modules/rc-mentions/es/Option.js"], "sourcesContent": ["var Option = function Option() {\n  return null;\n};\nexport default Option;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAO,IAAI;AACb,CAAC;AACD,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export function isInViewPort(element) {\n  var viewWidth = window.innerWidth || document.documentElement.clientWidth;\n  var viewHeight = window.innerHeight || document.documentElement.clientHeight;\n  var _element$getBoundingC = element.getBoundingClientRect(),\n    top = _element$getBoundingC.top,\n    right = _element$getBoundingC.right,\n    bottom = _element$getBoundingC.bottom,\n    left = _element$getBoundingC.left;\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;\n}\nexport function getPlacement(targetElement, placement, stepPlacement) {\n  var _ref;\n  return (_ref = stepPlacement !== null && stepPlacement !== void 0 ? stepPlacement : placement) !== null && _ref !== void 0 ? _ref : targetElement === null ? 'center' : 'bottom';\n}", "map": {"version": 3, "names": ["isInViewPort", "element", "viewWidth", "window", "innerWidth", "document", "documentElement", "clientWidth", "viewHeight", "innerHeight", "clientHeight", "_element$getBoundingC", "getBoundingClientRect", "top", "right", "bottom", "left", "getPlacement", "targetElement", "placement", "stepPlacement", "_ref"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+tour@1.15.1_r_14d992225ad622c42cfc1bfce16fe219/node_modules/@rc-component/tour/es/util.js"], "sourcesContent": ["export function isInViewPort(element) {\n  var viewWidth = window.innerWidth || document.documentElement.clientWidth;\n  var viewHeight = window.innerHeight || document.documentElement.clientHeight;\n  var _element$getBoundingC = element.getBoundingClientRect(),\n    top = _element$getBoundingC.top,\n    right = _element$getBoundingC.right,\n    bottom = _element$getBoundingC.bottom,\n    left = _element$getBoundingC.left;\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;\n}\nexport function getPlacement(targetElement, placement, stepPlacement) {\n  var _ref;\n  return (_ref = stepPlacement !== null && stepPlacement !== void 0 ? stepPlacement : placement) !== null && _ref !== void 0 ? _ref : targetElement === null ? 'center' : 'bottom';\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,OAAO,EAAE;EACpC,IAAIC,SAAS,GAAGC,MAAM,CAACC,UAAU,IAAIC,QAAQ,CAACC,eAAe,CAACC,WAAW;EACzE,IAAIC,UAAU,GAAGL,MAAM,CAACM,WAAW,IAAIJ,QAAQ,CAACC,eAAe,CAACI,YAAY;EAC5E,IAAIC,qBAAqB,GAAGV,OAAO,CAACW,qBAAqB,CAAC,CAAC;IACzDC,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,KAAK,GAAGH,qBAAqB,CAACG,KAAK;IACnCC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM;IACrCC,IAAI,GAAGL,qBAAqB,CAACK,IAAI;EACnC,OAAOH,GAAG,IAAI,CAAC,IAAIG,IAAI,IAAI,CAAC,IAAIF,KAAK,IAAIZ,SAAS,IAAIa,MAAM,IAAIP,UAAU;AAC5E;AACA,OAAO,SAASS,YAAYA,CAACC,aAAa,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACpE,IAAIC,IAAI;EACR,OAAO,CAACA,IAAI,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGD,SAAS,MAAM,IAAI,IAAIE,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGH,aAAa,KAAK,IAAI,GAAG,QAAQ,GAAG,QAAQ;AAClL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function leftPad(str, length) {\n  var fill = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '0';\n  var current = String(str);\n  while (current.length < length) {\n    current = \"\".concat(fill).concat(current);\n  }\n  return current;\n}\n\n/**\n * Convert `value` to array. Will provide `[]` if is null or undefined.\n */\nexport function toArray(val) {\n  if (val === null || val === undefined) {\n    return [];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function fillIndex(ori, index, value) {\n  var clone = _toConsumableArray(ori);\n  clone[index] = value;\n  return clone;\n}\n\n/** Pick props from the key list. Will filter empty value */\nexport function pickProps(props, keys) {\n  var clone = {};\n  var mergedKeys = keys || Object.keys(props);\n  mergedKeys.forEach(function (key) {\n    if (props[key] !== undefined) {\n      clone[key] = props[key];\n    }\n  });\n  return clone;\n}\nexport function getRowFormat(picker, locale, format) {\n  if (format) {\n    return format;\n  }\n  switch (picker) {\n    // All from the `locale.fieldXXXFormat` first\n    case 'time':\n      return locale.fieldTimeFormat;\n    case 'datetime':\n      return locale.fieldDateTimeFormat;\n    case 'month':\n      return locale.fieldMonthFormat;\n    case 'year':\n      return locale.fieldYearFormat;\n    case 'quarter':\n      return locale.fieldQuarterFormat;\n    case 'week':\n      return locale.fieldWeekFormat;\n    default:\n      return locale.fieldDateFormat;\n  }\n}\nexport function getFromDate(calendarValues, activeIndexList, activeIndex) {\n  var mergedActiveIndex = activeIndex !== undefined ? activeIndex : activeIndexList[activeIndexList.length - 1];\n  var firstValuedIndex = activeIndexList.find(function (index) {\n    return calendarValues[index];\n  });\n  return mergedActiveIndex !== firstValuedIndex ? calendarValues[firstValuedIndex] : undefined;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "leftPad", "str", "length", "fill", "arguments", "undefined", "current", "String", "concat", "toArray", "val", "Array", "isArray", "fillIndex", "ori", "index", "value", "clone", "pickProps", "props", "keys", "mergedKeys", "Object", "for<PERSON>ach", "key", "getRowFormat", "picker", "locale", "format", "fieldTimeFormat", "fieldDateTimeFormat", "fieldMonthFormat", "fieldYearFormat", "fieldQuarterFormat", "fieldWeekFormat", "fieldDateFormat", "getFromDate", "calendarValues", "activeIndexList", "activeIndex", "mergedActiveIndex", "firstValuedIndex", "find"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/utils/miscUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function leftPad(str, length) {\n  var fill = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '0';\n  var current = String(str);\n  while (current.length < length) {\n    current = \"\".concat(fill).concat(current);\n  }\n  return current;\n}\n\n/**\n * Convert `value` to array. Will provide `[]` if is null or undefined.\n */\nexport function toArray(val) {\n  if (val === null || val === undefined) {\n    return [];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function fillIndex(ori, index, value) {\n  var clone = _toConsumableArray(ori);\n  clone[index] = value;\n  return clone;\n}\n\n/** Pick props from the key list. Will filter empty value */\nexport function pickProps(props, keys) {\n  var clone = {};\n  var mergedKeys = keys || Object.keys(props);\n  mergedKeys.forEach(function (key) {\n    if (props[key] !== undefined) {\n      clone[key] = props[key];\n    }\n  });\n  return clone;\n}\nexport function getRowFormat(picker, locale, format) {\n  if (format) {\n    return format;\n  }\n  switch (picker) {\n    // All from the `locale.fieldXXXFormat` first\n    case 'time':\n      return locale.fieldTimeFormat;\n    case 'datetime':\n      return locale.fieldDateTimeFormat;\n    case 'month':\n      return locale.fieldMonthFormat;\n    case 'year':\n      return locale.fieldYearFormat;\n    case 'quarter':\n      return locale.fieldQuarterFormat;\n    case 'week':\n      return locale.fieldWeekFormat;\n    default:\n      return locale.fieldDateFormat;\n  }\n}\nexport function getFromDate(calendarValues, activeIndexList, activeIndex) {\n  var mergedActiveIndex = activeIndex !== undefined ? activeIndex : activeIndexList[activeIndexList.length - 1];\n  var firstValuedIndex = activeIndexList.find(function (index) {\n    return calendarValues[index];\n  });\n  return mergedActiveIndex !== firstValuedIndex ? calendarValues[firstValuedIndex] : undefined;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACnC,IAAIC,IAAI,GAAGC,SAAS,CAACF,MAAM,GAAG,CAAC,IAAIE,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EAClF,IAAIE,OAAO,GAAGC,MAAM,CAACN,GAAG,CAAC;EACzB,OAAOK,OAAO,CAACJ,MAAM,GAAGA,MAAM,EAAE;IAC9BI,OAAO,GAAG,EAAE,CAACE,MAAM,CAACL,IAAI,CAAC,CAACK,MAAM,CAACF,OAAO,CAAC;EAC3C;EACA,OAAOA,OAAO;AAChB;;AAEA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKL,SAAS,EAAE;IACrC,OAAO,EAAE;EACX;EACA,OAAOM,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AACA,OAAO,SAASG,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAC3C,IAAIC,KAAK,GAAGlB,kBAAkB,CAACe,GAAG,CAAC;EACnCG,KAAK,CAACF,KAAK,CAAC,GAAGC,KAAK;EACpB,OAAOC,KAAK;AACd;;AAEA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC,IAAIH,KAAK,GAAG,CAAC,CAAC;EACd,IAAII,UAAU,GAAGD,IAAI,IAAIE,MAAM,CAACF,IAAI,CAACD,KAAK,CAAC;EAC3CE,UAAU,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;IAChC,IAAIL,KAAK,CAACK,GAAG,CAAC,KAAKnB,SAAS,EAAE;MAC5BY,KAAK,CAACO,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOP,KAAK;AACd;AACA,OAAO,SAASQ,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACnD,IAAIA,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,QAAQF,MAAM;IACZ;IACA,KAAK,MAAM;MACT,OAAOC,MAAM,CAACE,eAAe;IAC/B,KAAK,UAAU;MACb,OAAOF,MAAM,CAACG,mBAAmB;IACnC,KAAK,OAAO;MACV,OAAOH,MAAM,CAACI,gBAAgB;IAChC,KAAK,MAAM;MACT,OAAOJ,MAAM,CAACK,eAAe;IAC/B,KAAK,SAAS;MACZ,OAAOL,MAAM,CAACM,kBAAkB;IAClC,KAAK,MAAM;MACT,OAAON,MAAM,CAACO,eAAe;IAC/B;MACE,OAAOP,MAAM,CAACQ,eAAe;EACjC;AACF;AACA,OAAO,SAASC,WAAWA,CAACC,cAAc,EAAEC,eAAe,EAAEC,WAAW,EAAE;EACxE,IAAIC,iBAAiB,GAAGD,WAAW,KAAKlC,SAAS,GAAGkC,WAAW,GAAGD,eAAe,CAACA,eAAe,CAACpC,MAAM,GAAG,CAAC,CAAC;EAC7G,IAAIuC,gBAAgB,GAAGH,eAAe,CAACI,IAAI,CAAC,UAAU3B,KAAK,EAAE;IAC3D,OAAOsB,cAAc,CAACtB,KAAK,CAAC;EAC9B,CAAC,CAAC;EACF,OAAOyB,iBAAiB,KAAKC,gBAAgB,GAAGJ,cAAc,CAACI,gBAAgB,CAAC,GAAGpC,SAAS;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
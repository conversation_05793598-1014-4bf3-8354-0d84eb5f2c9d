{"version": 3, "sources": ["../src/index.ts"], "names": ["cosmiconfig", "moduleName", "options", "normalizedOptions", "normalizeOptions", "explorer", "Explorer", "search", "bind", "load", "clearLoadCache", "clearSearchCache", "clearCaches", "cosmiconfigSync", "explorerSync", "ExplorerSync", "searchSync", "loadSync", "defaultLoaders", "Object", "freeze", "loaders", "loadJs", "loadJson", "loadYaml", "noExt", "identity", "x", "defaults", "packageProp", "searchPlaces", "ignoreEmptySearchPlaces", "stopDir", "os", "homedir", "cache", "transform"], "mappings": ";;;;;;;;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AALA;AA8CA;AACA,SAASA,WAAT,CAAqBC,UAArB,EAAyCC,OAAgB,GAAG,EAA5D,EAAgE;AAC9D,QAAMC,iBAAkC,GAAGC,gBAAgB,CACzDH,UADyD,EAEzDC,OAFyD,CAA3D;AAKA,QAAMG,QAAQ,GAAG,IAAIC,kBAAJ,CAAaH,iBAAb,CAAjB;AAEA,SAAO;AACLI,IAAAA,MAAM,EAAEF,QAAQ,CAACE,MAAT,CAAgBC,IAAhB,CAAqBH,QAArB,CADH;AAELI,IAAAA,IAAI,EAAEJ,QAAQ,CAACI,IAAT,CAAcD,IAAd,CAAmBH,QAAnB,CAFD;AAGLK,IAAAA,cAAc,EAAEL,QAAQ,CAACK,cAAT,CAAwBF,IAAxB,CAA6BH,QAA7B,CAHX;AAILM,IAAAA,gBAAgB,EAAEN,QAAQ,CAACM,gBAAT,CAA0BH,IAA1B,CAA+BH,QAA/B,CAJb;AAKLO,IAAAA,WAAW,EAAEP,QAAQ,CAACO,WAAT,CAAqBJ,IAArB,CAA0BH,QAA1B;AALR,GAAP;AAOD,C,CAED;;;AACA,SAASQ,eAAT,CAAyBZ,UAAzB,EAA6CC,OAAoB,GAAG,EAApE,EAAwE;AACtE,QAAMC,iBAAsC,GAAGC,gBAAgB,CAC7DH,UAD6D,EAE7DC,OAF6D,CAA/D;AAKA,QAAMY,YAAY,GAAG,IAAIC,0BAAJ,CAAiBZ,iBAAjB,CAArB;AAEA,SAAO;AACLI,IAAAA,MAAM,EAAEO,YAAY,CAACE,UAAb,CAAwBR,IAAxB,CAA6BM,YAA7B,CADH;AAELL,IAAAA,IAAI,EAAEK,YAAY,CAACG,QAAb,CAAsBT,IAAtB,CAA2BM,YAA3B,CAFD;AAGLJ,IAAAA,cAAc,EAAEI,YAAY,CAACJ,cAAb,CAA4BF,IAA5B,CAAiCM,YAAjC,CAHX;AAILH,IAAAA,gBAAgB,EAAEG,YAAY,CAACH,gBAAb,CAA8BH,IAA9B,CAAmCM,YAAnC,CAJb;AAKLF,IAAAA,WAAW,EAAEE,YAAY,CAACF,WAAb,CAAyBJ,IAAzB,CAA8BM,YAA9B;AALR,GAAP;AAOD,C,CAED;;;AACA,MAAMI,cAAc,GAAGC,MAAM,CAACC,MAAP,CAAc;AACnC,UAAQC,iBAAQC,MADmB;AAEnC,SAAOD,iBAAQC,MAFoB;AAGnC,WAASD,iBAAQE,QAHkB;AAInC,WAASF,iBAAQG,QAJkB;AAKnC,UAAQH,iBAAQG,QALmB;AAMnCC,EAAAA,KAAK,EAAEJ,iBAAQG;AANoB,CAAd,CAAvB;;;AASA,MAAME,QAAuB,GAAG,SAASA,QAAT,CAAkBC,CAAlB,EAAqB;AACnD,SAAOA,CAAP;AACD,CAFD;;AAYA,SAASvB,gBAAT,CACEH,UADF,EAEEC,OAFF,EAGyC;AACvC,QAAM0B,QAA+C,GAAG;AACtDC,IAAAA,WAAW,EAAE5B,UADyC;AAEtD6B,IAAAA,YAAY,EAAE,CACZ,cADY,EAEX,IAAG7B,UAAW,IAFH,EAGX,IAAGA,UAAW,SAHH,EAIX,IAAGA,UAAW,SAJH,EAKX,IAAGA,UAAW,QALH,EAMX,IAAGA,UAAW,OANH,EAOX,IAAGA,UAAW,QAPH,EAQX,WAAUA,UAAW,IARV,EASX,WAAUA,UAAW,SATV,EAUX,WAAUA,UAAW,SAVV,EAWX,WAAUA,UAAW,QAXV,EAYX,WAAUA,UAAW,OAZV,EAaX,WAAUA,UAAW,QAbV,EAcX,GAAEA,UAAW,YAdF,EAeX,GAAEA,UAAW,aAfF,CAFwC;AAmBtD8B,IAAAA,uBAAuB,EAAE,IAnB6B;AAoBtDC,IAAAA,OAAO,EAAEC,YAAGC,OAAH,EApB6C;AAqBtDC,IAAAA,KAAK,EAAE,IArB+C;AAsBtDC,IAAAA,SAAS,EAAEV,QAtB2C;AAuBtDL,IAAAA,OAAO,EAAEH;AAvB6C,GAAxD;AA0BA,QAAMf,iBAAwD,GAAG,EAC/D,GAAGyB,QAD4D;AAE/D,OAAG1B,OAF4D;AAG/DmB,IAAAA,OAAO,EAAE,EACP,GAAGO,QAAQ,CAACP,OADL;AAEP,SAAGnB,OAAO,CAACmB;AAFJ;AAHsD,GAAjE;AASA,SAAOlB,iBAAP;AACD", "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport os from 'os';\nimport { Explorer } from './Explorer';\nimport { ExplorerSync } from './ExplorerSync';\nimport { loaders } from './loaders';\nimport {\n  Config,\n  CosmiconfigResult,\n  ExplorerOptions,\n  ExplorerOptionsSync,\n  Loaders,\n  LoadersSync,\n} from './types';\n\ntype LoaderResult = Config | null;\nexport type Loader =\n  | ((filepath: string, content: string) => Promise<LoaderResult>)\n  | LoaderSync;\nexport type LoaderSync = (filepath: string, content: string) => LoaderResult;\n\nexport type Transform =\n  | ((CosmiconfigResult: CosmiconfigResult) => Promise<CosmiconfigResult>)\n  | TransformSync;\n\nexport type TransformSync = (\n  CosmiconfigResult: CosmiconfigResult,\n) => CosmiconfigResult;\n\ninterface OptionsBase {\n  packageProp?: string | Array<string>;\n  searchPlaces?: Array<string>;\n  ignoreEmptySearchPlaces?: boolean;\n  stopDir?: string;\n  cache?: boolean;\n}\n\nexport interface Options extends OptionsBase {\n  loaders?: Loaders;\n  transform?: Transform;\n}\n\nexport interface OptionsSync extends OptionsBase {\n  loaders?: LoadersSync;\n  transform?: TransformSync;\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction cosmiconfig(moduleName: string, options: Options = {}) {\n  const normalizedOptions: ExplorerOptions = normalizeOptions(\n    moduleName,\n    options,\n  );\n\n  const explorer = new Explorer(normalizedOptions);\n\n  return {\n    search: explorer.search.bind(explorer),\n    load: explorer.load.bind(explorer),\n    clearLoadCache: explorer.clearLoadCache.bind(explorer),\n    clearSearchCache: explorer.clearSearchCache.bind(explorer),\n    clearCaches: explorer.clearCaches.bind(explorer),\n  } as const;\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction cosmiconfigSync(moduleName: string, options: OptionsSync = {}) {\n  const normalizedOptions: ExplorerOptionsSync = normalizeOptions(\n    moduleName,\n    options,\n  );\n\n  const explorerSync = new ExplorerSync(normalizedOptions);\n\n  return {\n    search: explorerSync.searchSync.bind(explorerSync),\n    load: explorerSync.loadSync.bind(explorerSync),\n    clearLoadCache: explorerSync.clearLoadCache.bind(explorerSync),\n    clearSearchCache: explorerSync.clearSearchCache.bind(explorerSync),\n    clearCaches: explorerSync.clearCaches.bind(explorerSync),\n  } as const;\n}\n\n// do not allow mutation of default loaders. Make sure it is set inside options\nconst defaultLoaders = Object.freeze({\n  '.cjs': loaders.loadJs,\n  '.js': loaders.loadJs,\n  '.json': loaders.loadJson,\n  '.yaml': loaders.loadYaml,\n  '.yml': loaders.loadYaml,\n  noExt: loaders.loadYaml,\n} as const);\n\nconst identity: TransformSync = function identity(x) {\n  return x;\n};\n\nfunction normalizeOptions(\n  moduleName: string,\n  options: OptionsSync,\n): ExplorerOptionsSync;\nfunction normalizeOptions(\n  moduleName: string,\n  options: Options,\n): ExplorerOptions;\nfunction normalizeOptions(\n  moduleName: string,\n  options: Options | OptionsSync,\n): ExplorerOptions | ExplorerOptionsSync {\n  const defaults: ExplorerOptions | ExplorerOptionsSync = {\n    packageProp: moduleName,\n    searchPlaces: [\n      'package.json',\n      `.${moduleName}rc`,\n      `.${moduleName}rc.json`,\n      `.${moduleName}rc.yaml`,\n      `.${moduleName}rc.yml`,\n      `.${moduleName}rc.js`,\n      `.${moduleName}rc.cjs`,\n      `.config/${moduleName}rc`,\n      `.config/${moduleName}rc.json`,\n      `.config/${moduleName}rc.yaml`,\n      `.config/${moduleName}rc.yml`,\n      `.config/${moduleName}rc.js`,\n      `.config/${moduleName}rc.cjs`,\n      `${moduleName}.config.js`,\n      `${moduleName}.config.cjs`,\n    ],\n    ignoreEmptySearchPlaces: true,\n    stopDir: os.homedir(),\n    cache: true,\n    transform: identity,\n    loaders: defaultLoaders,\n  };\n\n  const normalizedOptions: ExplorerOptions | ExplorerOptionsSync = {\n    ...defaults,\n    ...options,\n    loaders: {\n      ...defaults.loaders,\n      ...options.loaders,\n    },\n  };\n\n  return normalizedOptions;\n}\n\nexport { cosmiconfig, cosmiconfigSync, defaultLoaders };\n"], "file": "index.js"}
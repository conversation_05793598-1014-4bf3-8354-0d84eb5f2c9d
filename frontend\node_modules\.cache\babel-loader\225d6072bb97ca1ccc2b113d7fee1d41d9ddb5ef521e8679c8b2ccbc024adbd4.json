{"ast": null, "code": "const genStatusStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    colorError,\n    colorTextLightSolid\n  } = token;\n  const itemCls = `${menuCls}-item`;\n  return {\n    [`${componentCls}, ${componentCls}-menu-submenu`]: {\n      [`${menuCls} ${itemCls}`]: {\n        [`&${itemCls}-danger:not(${itemCls}-disabled)`]: {\n          color: colorError,\n          '&:hover': {\n            color: colorTextLightSolid,\n            backgroundColor: colorError\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStatusStyle;", "map": {"version": 3, "names": ["genStatusStyle", "token", "componentCls", "menuCls", "colorError", "colorTextLightSolid", "itemCls", "color", "backgroundColor"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/dropdown/style/status.js"], "sourcesContent": ["const genStatusStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    colorError,\n    colorTextLightSolid\n  } = token;\n  const itemCls = `${menuCls}-item`;\n  return {\n    [`${componentCls}, ${componentCls}-menu-submenu`]: {\n      [`${menuCls} ${itemCls}`]: {\n        [`&${itemCls}-danger:not(${itemCls}-disabled)`]: {\n          color: colorError,\n          '&:hover': {\n            color: colorTextLightSolid,\n            backgroundColor: colorError\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStatusStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,OAAO,GAAG,GAAGH,OAAO,OAAO;EACjC,OAAO;IACL,CAAC,GAAGD,YAAY,KAAKA,YAAY,eAAe,GAAG;MACjD,CAAC,GAAGC,OAAO,IAAIG,OAAO,EAAE,GAAG;QACzB,CAAC,IAAIA,OAAO,eAAeA,OAAO,YAAY,GAAG;UAC/CC,KAAK,EAAEH,UAAU;UACjB,SAAS,EAAE;YACTG,KAAK,EAAEF,mBAAmB;YAC1BG,eAAe,EAAEJ;UACnB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
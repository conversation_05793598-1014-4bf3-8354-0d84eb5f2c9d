const { Customer } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// 生成客户编号
const generateCustomerCode = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  
  // 查找今天创建的最后一个客户编号
  const lastCustomer = await Customer.findOne({
    where: {
      customer_code: {
        [Op.like]: `CUS${dateStr}%`
      }
    },
    order: [['customer_code', 'DESC']]
  });
  
  let sequence = 1;
  if (lastCustomer) {
    const lastSequence = parseInt(lastCustomer.customer_code.slice(-3));
    sequence = lastSequence + 1;
  }
  
  return `CUS${dateStr}${sequence.toString().padStart(3, '0')}`;
};

// 获取客户列表
exports.getCustomers = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      search,
      customer_type,
      status = 'active'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const where = {};

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { customer_code: { [Op.like]: `%${search}%` } },
        { company_name: { [Op.like]: `%${search}%` } },
        { contact_person: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    // 客户类型筛选
    if (customer_type) {
      where.customer_type = customer_type;
    }

    // 状态筛选
    if (status === 'active') {
      where.status = 'active';
    } else if (status === 'inactive') {
      where.status = 'inactive';
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: count,
          total_pages: Math.ceil(count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取客户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取客户列表失败',
      error: error.message
    });
  }
};

// 获取客户详情
exports.getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id);

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    res.json({
      success: true,
      data: { customer }
    });
  } catch (error) {
    console.error('获取客户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取客户详情失败',
      error: error.message
    });
  }
};

// 创建客户
exports.createCustomer = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const customerData = req.body;

    // 生成客户编号
    customerData.customer_code = await generateCustomerCode();

    // 创建客户
    const customer = await Customer.create(customerData);

    res.status(201).json({
      success: true,
      message: '客户创建成功',
      data: { customer }
    });
  } catch (error) {
    console.error('创建客户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建客户失败',
      error: error.message
    });
  }
};

// 更新客户
exports.updateCustomer = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    // 更新客户
    await customer.update(updateData);

    res.json({
      success: true,
      message: '客户更新成功',
      data: { customer }
    });
  } catch (error) {
    console.error('更新客户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新客户失败',
      error: error.message
    });
  }
};

// 删除客户
exports.deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    await customer.destroy();

    res.json({
      success: true,
      message: '客户删除成功'
    });
  } catch (error) {
    console.error('删除客户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除客户失败',
      error: error.message
    });
  }
};

// 更新客户状态
exports.updateCustomerStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      });
    }

    await customer.update({ status });

    res.json({
      success: true,
      message: '客户状态更新成功',
      data: { customer }
    });
  } catch (error) {
    console.error('更新客户状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新客户状态失败',
      error: error.message
    });
  }
};

{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { get, set } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nimport FallbackCmp from './style/fallbackCmp';\nconst GRID_MAX = 24;\nconst FormItemInput = props => {\n  const {\n    prefixCls,\n    status,\n    labelCol,\n    wrapperCol,\n    children,\n    errors,\n    warnings,\n    _internalItemRender: formItemRender,\n    extra,\n    help,\n    fieldId,\n    marginBottom,\n    onErrorVisibleChanged,\n    label\n  } = props;\n  const baseClassName = `${prefixCls}-item`;\n  const formContext = React.useContext(FormContext);\n  const mergedWrapperCol = React.useMemo(() => {\n    let mergedWrapper = Object.assign({}, wrapperCol || formContext.wrapperCol || {});\n    if (label === null && !labelCol && !wrapperCol && formContext.labelCol) {\n      const list = [undefined, 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'];\n      list.forEach(size => {\n        const _size = size ? [size] : [];\n        const formLabel = get(formContext.labelCol, _size);\n        const formLabelObj = typeof formLabel === 'object' ? formLabel : {};\n        const wrapper = get(mergedWrapper, _size);\n        const wrapperObj = typeof wrapper === 'object' ? wrapper : {};\n        if ('span' in formLabelObj && !('offset' in wrapperObj) && formLabelObj.span < GRID_MAX) {\n          mergedWrapper = set(mergedWrapper, [].concat(_size, ['offset']), formLabelObj.span);\n        }\n      });\n    }\n    return mergedWrapper;\n  }, [wrapperCol, formContext]);\n  const className = classNames(`${baseClassName}-control`, mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  const subFormContext = React.useMemo(() => {\n    const {\n        labelCol,\n        wrapperCol\n      } = formContext,\n      rest = __rest(formContext, [\"labelCol\", \"wrapperCol\"]);\n    return rest;\n  }, [formContext]);\n  const extraRef = React.useRef(null);\n  const [extraHeight, setExtraHeight] = React.useState(0);\n  useLayoutEffect(() => {\n    if (extra && extraRef.current) {\n      setExtraHeight(extraRef.current.clientHeight);\n    } else {\n      setExtraHeight(0);\n    }\n  }, [extra]);\n  const inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input-content`\n  }, children));\n  const formItemContext = React.useMemo(() => ({\n    prefixCls,\n    status\n  }), [prefixCls, status]);\n  const errorListDom = marginBottom !== null || errors.length || warnings.length ? (/*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: `${baseClassName}-explain-connected`,\n    onVisibleChanged: onErrorVisibleChanged\n  }))) : null;\n  const extraProps = {};\n  if (fieldId) {\n    extraProps.id = `${fieldId}_extra`;\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  const extraDom = extra ? (/*#__PURE__*/React.createElement(\"div\", Object.assign({}, extraProps, {\n    className: `${baseClassName}-extra`,\n    ref: extraRef\n  }), extra)) : null;\n  const additionalDom = errorListDom || extraDom ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-additional`,\n    style: marginBottom ? {\n      minHeight: marginBottom + extraHeight\n    } : {}\n  }, errorListDom, extraDom)) : null;\n  const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : (/*#__PURE__*/React.createElement(React.Fragment, null, inputDom, additionalDom));\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedWrapperCol, {\n    className: className\n  }), dom), /*#__PURE__*/React.createElement(FallbackCmp, {\n    prefixCls: prefixCls\n  }));\n};\nexport default FormItemInput;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "get", "set", "useLayoutEffect", "Col", "FormContext", "FormItemPrefixContext", "ErrorList", "FallbackCmp", "GRID_MAX", "FormItemInput", "props", "prefixCls", "status", "labelCol", "wrapperCol", "children", "errors", "warnings", "_internalItemRender", "formItemRender", "extra", "help", "fieldId", "marginBottom", "onErrorVisibleChanged", "label", "baseClassName", "formContext", "useContext", "mergedWrapperCol", "useMemo", "mergedWrapper", "assign", "list", "undefined", "for<PERSON>ach", "size", "_size", "formLabel", "formLabelObj", "wrapper", "wrapperObj", "span", "concat", "className", "subFormContext", "rest", "extraRef", "useRef", "extraHeight", "setExtraHeight", "useState", "current", "clientHeight", "inputDom", "createElement", "formItemContext", "errorListDom", "Provider", "value", "helpStatus", "onVisibleChanged", "extraProps", "id", "extraDom", "ref", "additionalDom", "style", "minHeight", "dom", "mark", "render", "input", "errorList", "Fragment"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/form/FormItemInput.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { get, set } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nimport FallbackCmp from './style/fallbackCmp';\nconst GRID_MAX = 24;\nconst FormItemInput = props => {\n  const {\n    prefixCls,\n    status,\n    labelCol,\n    wrapperCol,\n    children,\n    errors,\n    warnings,\n    _internalItemRender: formItemRender,\n    extra,\n    help,\n    fieldId,\n    marginBottom,\n    onErrorVisibleChanged,\n    label\n  } = props;\n  const baseClassName = `${prefixCls}-item`;\n  const formContext = React.useContext(FormContext);\n  const mergedWrapperCol = React.useMemo(() => {\n    let mergedWrapper = Object.assign({}, wrapperCol || formContext.wrapperCol || {});\n    if (label === null && !labelCol && !wrapperCol && formContext.labelCol) {\n      const list = [undefined, 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'];\n      list.forEach(size => {\n        const _size = size ? [size] : [];\n        const formLabel = get(formContext.labelCol, _size);\n        const formLabelObj = typeof formLabel === 'object' ? formLabel : {};\n        const wrapper = get(mergedWrapper, _size);\n        const wrapperObj = typeof wrapper === 'object' ? wrapper : {};\n        if ('span' in formLabelObj && !('offset' in wrapperObj) && formLabelObj.span < GRID_MAX) {\n          mergedWrapper = set(mergedWrapper, [].concat(_size, ['offset']), formLabelObj.span);\n        }\n      });\n    }\n    return mergedWrapper;\n  }, [wrapperCol, formContext]);\n  const className = classNames(`${baseClassName}-control`, mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  const subFormContext = React.useMemo(() => {\n    const {\n        labelCol,\n        wrapperCol\n      } = formContext,\n      rest = __rest(formContext, [\"labelCol\", \"wrapperCol\"]);\n    return rest;\n  }, [formContext]);\n  const extraRef = React.useRef(null);\n  const [extraHeight, setExtraHeight] = React.useState(0);\n  useLayoutEffect(() => {\n    if (extra && extraRef.current) {\n      setExtraHeight(extraRef.current.clientHeight);\n    } else {\n      setExtraHeight(0);\n    }\n  }, [extra]);\n  const inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input-content`\n  }, children));\n  const formItemContext = React.useMemo(() => ({\n    prefixCls,\n    status\n  }), [prefixCls, status]);\n  const errorListDom = marginBottom !== null || errors.length || warnings.length ? (/*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: `${baseClassName}-explain-connected`,\n    onVisibleChanged: onErrorVisibleChanged\n  }))) : null;\n  const extraProps = {};\n  if (fieldId) {\n    extraProps.id = `${fieldId}_extra`;\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  const extraDom = extra ? (/*#__PURE__*/React.createElement(\"div\", Object.assign({}, extraProps, {\n    className: `${baseClassName}-extra`,\n    ref: extraRef\n  }), extra)) : null;\n  const additionalDom = errorListDom || extraDom ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-additional`,\n    style: marginBottom ? {\n      minHeight: marginBottom + extraHeight\n    } : {}\n  }, errorListDom, extraDom)) : null;\n  const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : (/*#__PURE__*/React.createElement(React.Fragment, null, inputDom, additionalDom));\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedWrapperCol, {\n    className: className\n  }), dom), /*#__PURE__*/React.createElement(FallbackCmp, {\n    prefixCls: prefixCls\n  }));\n};\nexport default FormItemInput;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,GAAG,EAAEC,GAAG,QAAQ,SAAS;AAClC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,WAAW;AAC9D,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,mBAAmB,EAAEC,cAAc;IACnCC,KAAK;IACLC,IAAI;IACJC,OAAO;IACPC,YAAY;IACZC,qBAAqB;IACrBC;EACF,CAAC,GAAGf,KAAK;EACT,MAAMgB,aAAa,GAAG,GAAGf,SAAS,OAAO;EACzC,MAAMgB,WAAW,GAAG7B,KAAK,CAAC8B,UAAU,CAACxB,WAAW,CAAC;EACjD,MAAMyB,gBAAgB,GAAG/B,KAAK,CAACgC,OAAO,CAAC,MAAM;IAC3C,IAAIC,aAAa,GAAG1C,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAElB,UAAU,IAAIa,WAAW,CAACb,UAAU,IAAI,CAAC,CAAC,CAAC;IACjF,IAAIW,KAAK,KAAK,IAAI,IAAI,CAACZ,QAAQ,IAAI,CAACC,UAAU,IAAIa,WAAW,CAACd,QAAQ,EAAE;MACtE,MAAMoB,IAAI,GAAG,CAACC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;MAC7DD,IAAI,CAACE,OAAO,CAACC,IAAI,IAAI;QACnB,MAAMC,KAAK,GAAGD,IAAI,GAAG,CAACA,IAAI,CAAC,GAAG,EAAE;QAChC,MAAME,SAAS,GAAGtC,GAAG,CAAC2B,WAAW,CAACd,QAAQ,EAAEwB,KAAK,CAAC;QAClD,MAAME,YAAY,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,CAAC,CAAC;QACnE,MAAME,OAAO,GAAGxC,GAAG,CAAC+B,aAAa,EAAEM,KAAK,CAAC;QACzC,MAAMI,UAAU,GAAG,OAAOD,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC,CAAC;QAC7D,IAAI,MAAM,IAAID,YAAY,IAAI,EAAE,QAAQ,IAAIE,UAAU,CAAC,IAAIF,YAAY,CAACG,IAAI,GAAGlC,QAAQ,EAAE;UACvFuB,aAAa,GAAG9B,GAAG,CAAC8B,aAAa,EAAE,EAAE,CAACY,MAAM,CAACN,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAEE,YAAY,CAACG,IAAI,CAAC;QACrF;MACF,CAAC,CAAC;IACJ;IACA,OAAOX,aAAa;EACtB,CAAC,EAAE,CAACjB,UAAU,EAAEa,WAAW,CAAC,CAAC;EAC7B,MAAMiB,SAAS,GAAG7C,UAAU,CAAC,GAAG2B,aAAa,UAAU,EAAEG,gBAAgB,CAACe,SAAS,CAAC;EACpF;EACA,MAAMC,cAAc,GAAG/C,KAAK,CAACgC,OAAO,CAAC,MAAM;IACzC,MAAM;QACFjB,QAAQ;QACRC;MACF,CAAC,GAAGa,WAAW;MACfmB,IAAI,GAAG9D,MAAM,CAAC2C,WAAW,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IACxD,OAAOmB,IAAI;EACb,CAAC,EAAE,CAACnB,WAAW,CAAC,CAAC;EACjB,MAAMoB,QAAQ,GAAGjD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpD,KAAK,CAACqD,QAAQ,CAAC,CAAC,CAAC;EACvDjD,eAAe,CAAC,MAAM;IACpB,IAAIkB,KAAK,IAAI2B,QAAQ,CAACK,OAAO,EAAE;MAC7BF,cAAc,CAACH,QAAQ,CAACK,OAAO,CAACC,YAAY,CAAC;IAC/C,CAAC,MAAM;MACLH,cAAc,CAAC,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EACX,MAAMkC,QAAQ,GAAG,aAAaxD,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IACvDX,SAAS,EAAE,GAAGlB,aAAa;EAC7B,CAAC,EAAE,aAAa5B,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IACzCX,SAAS,EAAE,GAAGlB,aAAa;EAC7B,CAAC,EAAEX,QAAQ,CAAC,CAAC;EACb,MAAMyC,eAAe,GAAG1D,KAAK,CAACgC,OAAO,CAAC,OAAO;IAC3CnB,SAAS;IACTC;EACF,CAAC,CAAC,EAAE,CAACD,SAAS,EAAEC,MAAM,CAAC,CAAC;EACxB,MAAM6C,YAAY,GAAGlC,YAAY,KAAK,IAAI,IAAIP,MAAM,CAACpB,MAAM,IAAIqB,QAAQ,CAACrB,MAAM,IAAI,aAAaE,KAAK,CAACyD,aAAa,CAAClD,qBAAqB,CAACqD,QAAQ,EAAE;IACjJC,KAAK,EAAEH;EACT,CAAC,EAAE,aAAa1D,KAAK,CAACyD,aAAa,CAACjD,SAAS,EAAE;IAC7CgB,OAAO,EAAEA,OAAO;IAChBN,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBI,IAAI,EAAEA,IAAI;IACVuC,UAAU,EAAEhD,MAAM;IAClBgC,SAAS,EAAE,GAAGlB,aAAa,oBAAoB;IAC/CmC,gBAAgB,EAAErC;EACpB,CAAC,CAAC,CAAC,IAAI,IAAI;EACX,MAAMsC,UAAU,GAAG,CAAC,CAAC;EACrB,IAAIxC,OAAO,EAAE;IACXwC,UAAU,CAACC,EAAE,GAAG,GAAGzC,OAAO,QAAQ;EACpC;EACA;EACA;EACA,MAAM0C,QAAQ,GAAG5C,KAAK,IAAI,aAAatB,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAElE,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAE8B,UAAU,EAAE;IAC9FlB,SAAS,EAAE,GAAGlB,aAAa,QAAQ;IACnCuC,GAAG,EAAElB;EACP,CAAC,CAAC,EAAE3B,KAAK,CAAC,IAAI,IAAI;EAClB,MAAM8C,aAAa,GAAGT,YAAY,IAAIO,QAAQ,IAAI,aAAalE,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IACxFX,SAAS,EAAE,GAAGlB,aAAa,aAAa;IACxCyC,KAAK,EAAE5C,YAAY,GAAG;MACpB6C,SAAS,EAAE7C,YAAY,GAAG0B;IAC5B,CAAC,GAAG,CAAC;EACP,CAAC,EAAEQ,YAAY,EAAEO,QAAQ,CAAC,IAAI,IAAI;EAClC,MAAMK,GAAG,GAAGlD,cAAc,IAAIA,cAAc,CAACmD,IAAI,KAAK,kBAAkB,IAAInD,cAAc,CAACoD,MAAM,GAAGpD,cAAc,CAACoD,MAAM,CAAC7D,KAAK,EAAE;IAC/H8D,KAAK,EAAElB,QAAQ;IACfmB,SAAS,EAAEhB,YAAY;IACvBrC,KAAK,EAAE4C;EACT,CAAC,CAAC,IAAI,aAAalE,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC4E,QAAQ,EAAE,IAAI,EAAEpB,QAAQ,EAAEY,aAAa,CAAC,CAAC;EACtF,OAAO,aAAapE,KAAK,CAACyD,aAAa,CAACnD,WAAW,CAACsD,QAAQ,EAAE;IAC5DC,KAAK,EAAEd;EACT,CAAC,EAAE,aAAa/C,KAAK,CAACyD,aAAa,CAACpD,GAAG,EAAEd,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAEH,gBAAgB,EAAE;IAC3Ee,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEyB,GAAG,CAAC,EAAE,aAAavE,KAAK,CAACyD,aAAa,CAAChD,WAAW,EAAE;IACtDI,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
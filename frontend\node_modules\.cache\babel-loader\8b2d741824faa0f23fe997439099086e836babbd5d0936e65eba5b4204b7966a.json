{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { UnstableContext } from 'rc-slider';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport Slider from '../../slider';\nimport SliderInternalContext from '../../slider/Context';\nimport { getGradientPercentColor } from '../util';\nexport const GradientColorSlider = props => {\n  const {\n      prefixCls,\n      colors,\n      type,\n      color,\n      range = false,\n      className,\n      activeIndex,\n      onActive,\n      onDragStart,\n      onDragChange,\n      onKeyDelete\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"colors\", \"type\", \"color\", \"range\", \"className\", \"activeIndex\", \"onActive\", \"onDragStart\", \"onDragChange\", \"onKeyDelete\"]);\n  const sliderProps = Object.assign(Object.assign({}, restProps), {\n    track: false\n  });\n  // ========================== Background ==========================\n  const linearCss = React.useMemo(() => {\n    const colorsStr = colors.map(c => `${c.color} ${c.percent}%`).join(', ');\n    return `linear-gradient(90deg, ${colorsStr})`;\n  }, [colors]);\n  const pointColor = React.useMemo(() => {\n    if (!color || !type) {\n      return null;\n    }\n    if (type === 'alpha') {\n      return color.toRgbString();\n    }\n    return `hsl(${color.toHsb().h}, 100%, 50%)`;\n  }, [color, type]);\n  // ======================= Context: Slider ========================\n  const onInternalDragStart = useEvent(onDragStart);\n  const onInternalDragChange = useEvent(onDragChange);\n  const unstableContext = React.useMemo(() => ({\n    onDragStart: onInternalDragStart,\n    onDragChange: onInternalDragChange\n  }), []);\n  // ======================= Context: Render ========================\n  const handleRender = useEvent((ori, info) => {\n    const {\n      onFocus,\n      style,\n      className: handleCls,\n      onKeyDown\n    } = ori.props;\n    // Point Color\n    const mergedStyle = Object.assign({}, style);\n    if (type === 'gradient') {\n      mergedStyle.background = getGradientPercentColor(colors, info.value);\n    }\n    return /*#__PURE__*/React.cloneElement(ori, {\n      onFocus: e => {\n        onActive === null || onActive === void 0 ? void 0 : onActive(info.index);\n        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      },\n      style: mergedStyle,\n      className: classNames(handleCls, {\n        [`${prefixCls}-slider-handle-active`]: activeIndex === info.index\n      }),\n      onKeyDown: e => {\n        if ((e.key === 'Delete' || e.key === 'Backspace') && onKeyDelete) {\n          onKeyDelete(info.index);\n        }\n        onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n      }\n    });\n  });\n  const sliderContext = React.useMemo(() => ({\n    direction: 'ltr',\n    handleRender\n  }), []);\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderInternalContext.Provider, {\n    value: sliderContext\n  }, /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: unstableContext\n  }, /*#__PURE__*/React.createElement(Slider, Object.assign({}, sliderProps, {\n    className: classNames(className, `${prefixCls}-slider`),\n    tooltip: {\n      open: false\n    },\n    range: {\n      editable: range,\n      minCount: 2\n    },\n    styles: {\n      rail: {\n        background: linearCss\n      },\n      handle: pointColor ? {\n        background: pointColor\n      } : {}\n    },\n    classNames: {\n      rail: `${prefixCls}-slider-rail`,\n      handle: `${prefixCls}-slider-handle`\n    }\n  }))));\n};\nconst SingleColorSlider = props => {\n  const {\n    value,\n    onChange,\n    onChangeComplete\n  } = props;\n  const singleOnChange = v => onChange(v[0]);\n  const singleOnChangeComplete = v => onChangeComplete(v[0]);\n  return /*#__PURE__*/React.createElement(GradientColorSlider, Object.assign({}, props, {\n    value: [value],\n    onChange: singleOnChange,\n    onChangeComplete: singleOnChangeComplete\n  }));\n};\nexport default SingleColorSlider;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "UnstableContext", "useEvent", "Slide<PERSON>", "SliderInternalContext", "getGradientPercentColor", "GradientColorSlider", "props", "prefixCls", "colors", "type", "color", "range", "className", "activeIndex", "onActive", "onDragStart", "onDragChange", "onKeyDelete", "restProps", "sliderProps", "assign", "track", "linearCss", "useMemo", "colorsStr", "map", "c", "percent", "join", "pointColor", "toRgbString", "toHsb", "h", "onInternalDragStart", "onInternalDragChange", "unstableContext", "handleRender", "ori", "info", "onFocus", "style", "handleCls", "onKeyDown", "mergedStyle", "background", "value", "cloneElement", "index", "key", "slider<PERSON><PERSON>xt", "direction", "createElement", "Provider", "tooltip", "open", "editable", "minCount", "styles", "rail", "handle", "SingleColorSlider", "onChange", "onChangeComplete", "singleOnChange", "v", "singleOnChangeComplete"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/components/ColorSlider.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { UnstableContext } from 'rc-slider';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport Slider from '../../slider';\nimport SliderInternalContext from '../../slider/Context';\nimport { getGradientPercentColor } from '../util';\nexport const GradientColorSlider = props => {\n  const {\n      prefixCls,\n      colors,\n      type,\n      color,\n      range = false,\n      className,\n      activeIndex,\n      onActive,\n      onDragStart,\n      onDragChange,\n      onKeyDelete\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"colors\", \"type\", \"color\", \"range\", \"className\", \"activeIndex\", \"onActive\", \"onDragStart\", \"onDragChange\", \"onKeyDelete\"]);\n  const sliderProps = Object.assign(Object.assign({}, restProps), {\n    track: false\n  });\n  // ========================== Background ==========================\n  const linearCss = React.useMemo(() => {\n    const colorsStr = colors.map(c => `${c.color} ${c.percent}%`).join(', ');\n    return `linear-gradient(90deg, ${colorsStr})`;\n  }, [colors]);\n  const pointColor = React.useMemo(() => {\n    if (!color || !type) {\n      return null;\n    }\n    if (type === 'alpha') {\n      return color.toRgbString();\n    }\n    return `hsl(${color.toHsb().h}, 100%, 50%)`;\n  }, [color, type]);\n  // ======================= Context: Slider ========================\n  const onInternalDragStart = useEvent(onDragStart);\n  const onInternalDragChange = useEvent(onDragChange);\n  const unstableContext = React.useMemo(() => ({\n    onDragStart: onInternalDragStart,\n    onDragChange: onInternalDragChange\n  }), []);\n  // ======================= Context: Render ========================\n  const handleRender = useEvent((ori, info) => {\n    const {\n      onFocus,\n      style,\n      className: handleCls,\n      onKeyDown\n    } = ori.props;\n    // Point Color\n    const mergedStyle = Object.assign({}, style);\n    if (type === 'gradient') {\n      mergedStyle.background = getGradientPercentColor(colors, info.value);\n    }\n    return /*#__PURE__*/React.cloneElement(ori, {\n      onFocus: e => {\n        onActive === null || onActive === void 0 ? void 0 : onActive(info.index);\n        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      },\n      style: mergedStyle,\n      className: classNames(handleCls, {\n        [`${prefixCls}-slider-handle-active`]: activeIndex === info.index\n      }),\n      onKeyDown: e => {\n        if ((e.key === 'Delete' || e.key === 'Backspace') && onKeyDelete) {\n          onKeyDelete(info.index);\n        }\n        onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n      }\n    });\n  });\n  const sliderContext = React.useMemo(() => ({\n    direction: 'ltr',\n    handleRender\n  }), []);\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderInternalContext.Provider, {\n    value: sliderContext\n  }, /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: unstableContext\n  }, /*#__PURE__*/React.createElement(Slider, Object.assign({}, sliderProps, {\n    className: classNames(className, `${prefixCls}-slider`),\n    tooltip: {\n      open: false\n    },\n    range: {\n      editable: range,\n      minCount: 2\n    },\n    styles: {\n      rail: {\n        background: linearCss\n      },\n      handle: pointColor ? {\n        background: pointColor\n      } : {}\n    },\n    classNames: {\n      rail: `${prefixCls}-slider-rail`,\n      handle: `${prefixCls}-slider-handle`\n    }\n  }))));\n};\nconst SingleColorSlider = props => {\n  const {\n    value,\n    onChange,\n    onChangeComplete\n  } = props;\n  const singleOnChange = v => onChange(v[0]);\n  const singleOnChangeComplete = v => onChangeComplete(v[0]);\n  return /*#__PURE__*/React.createElement(GradientColorSlider, Object.assign({}, props, {\n    value: [value],\n    onChange: singleOnChange,\n    onChangeComplete: singleOnChangeComplete\n  }));\n};\nexport default SingleColorSlider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,WAAW;AAC3C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,qBAAqB,MAAM,sBAAsB;AACxD,SAASC,uBAAuB,QAAQ,SAAS;AACjD,OAAO,MAAMC,mBAAmB,GAAGC,KAAK,IAAI;EAC1C,MAAM;MACFC,SAAS;MACTC,MAAM;MACNC,IAAI;MACJC,KAAK;MACLC,KAAK,GAAG,KAAK;MACbC,SAAS;MACTC,WAAW;MACXC,QAAQ;MACRC,WAAW;MACXC,YAAY;MACZC;IACF,CAAC,GAAGX,KAAK;IACTY,SAAS,GAAGlC,MAAM,CAACsB,KAAK,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;EACpK,MAAMa,WAAW,GAAG9B,MAAM,CAAC+B,MAAM,CAAC/B,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAEF,SAAS,CAAC,EAAE;IAC9DG,KAAK,EAAE;EACT,CAAC,CAAC;EACF;EACA,MAAMC,SAAS,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IACpC,MAAMC,SAAS,GAAGhB,MAAM,CAACiB,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAAChB,KAAK,IAAIgB,CAAC,CAACC,OAAO,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACxE,OAAO,0BAA0BJ,SAAS,GAAG;EAC/C,CAAC,EAAE,CAAChB,MAAM,CAAC,CAAC;EACZ,MAAMqB,UAAU,GAAG/B,KAAK,CAACyB,OAAO,CAAC,MAAM;IACrC,IAAI,CAACb,KAAK,IAAI,CAACD,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOC,KAAK,CAACoB,WAAW,CAAC,CAAC;IAC5B;IACA,OAAO,OAAOpB,KAAK,CAACqB,KAAK,CAAC,CAAC,CAACC,CAAC,cAAc;EAC7C,CAAC,EAAE,CAACtB,KAAK,EAAED,IAAI,CAAC,CAAC;EACjB;EACA,MAAMwB,mBAAmB,GAAGhC,QAAQ,CAACc,WAAW,CAAC;EACjD,MAAMmB,oBAAoB,GAAGjC,QAAQ,CAACe,YAAY,CAAC;EACnD,MAAMmB,eAAe,GAAGrC,KAAK,CAACyB,OAAO,CAAC,OAAO;IAC3CR,WAAW,EAAEkB,mBAAmB;IAChCjB,YAAY,EAAEkB;EAChB,CAAC,CAAC,EAAE,EAAE,CAAC;EACP;EACA,MAAME,YAAY,GAAGnC,QAAQ,CAAC,CAACoC,GAAG,EAAEC,IAAI,KAAK;IAC3C,MAAM;MACJC,OAAO;MACPC,KAAK;MACL5B,SAAS,EAAE6B,SAAS;MACpBC;IACF,CAAC,GAAGL,GAAG,CAAC/B,KAAK;IACb;IACA,MAAMqC,WAAW,GAAGtD,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAEoB,KAAK,CAAC;IAC5C,IAAI/B,IAAI,KAAK,UAAU,EAAE;MACvBkC,WAAW,CAACC,UAAU,GAAGxC,uBAAuB,CAACI,MAAM,EAAE8B,IAAI,CAACO,KAAK,CAAC;IACtE;IACA,OAAO,aAAa/C,KAAK,CAACgD,YAAY,CAACT,GAAG,EAAE;MAC1CE,OAAO,EAAErD,CAAC,IAAI;QACZ4B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwB,IAAI,CAACS,KAAK,CAAC;QACxER,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACrD,CAAC,CAAC;MAC9D,CAAC;MACDsD,KAAK,EAAEG,WAAW;MAClB/B,SAAS,EAAEb,UAAU,CAAC0C,SAAS,EAAE;QAC/B,CAAC,GAAGlC,SAAS,uBAAuB,GAAGM,WAAW,KAAKyB,IAAI,CAACS;MAC9D,CAAC,CAAC;MACFL,SAAS,EAAExD,CAAC,IAAI;QACd,IAAI,CAACA,CAAC,CAAC8D,GAAG,KAAK,QAAQ,IAAI9D,CAAC,CAAC8D,GAAG,KAAK,WAAW,KAAK/B,WAAW,EAAE;UAChEA,WAAW,CAACqB,IAAI,CAACS,KAAK,CAAC;QACzB;QACAL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACxD,CAAC,CAAC;MACpE;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM+D,aAAa,GAAGnD,KAAK,CAACyB,OAAO,CAAC,OAAO;IACzC2B,SAAS,EAAE,KAAK;IAChBd;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP;EACA,OAAO,aAAatC,KAAK,CAACqD,aAAa,CAAChD,qBAAqB,CAACiD,QAAQ,EAAE;IACtEP,KAAK,EAAEI;EACT,CAAC,EAAE,aAAanD,KAAK,CAACqD,aAAa,CAACnD,eAAe,CAACoD,QAAQ,EAAE;IAC5DP,KAAK,EAAEV;EACT,CAAC,EAAE,aAAarC,KAAK,CAACqD,aAAa,CAACjD,MAAM,EAAEb,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,EAAE;IACzEP,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAE,GAAGL,SAAS,SAAS,CAAC;IACvD8C,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACD3C,KAAK,EAAE;MACL4C,QAAQ,EAAE5C,KAAK;MACf6C,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNC,IAAI,EAAE;QACJd,UAAU,EAAEtB;MACd,CAAC;MACDqC,MAAM,EAAE9B,UAAU,GAAG;QACnBe,UAAU,EAAEf;MACd,CAAC,GAAG,CAAC;IACP,CAAC;IACD9B,UAAU,EAAE;MACV2D,IAAI,EAAE,GAAGnD,SAAS,cAAc;MAChCoD,MAAM,EAAE,GAAGpD,SAAS;IACtB;EACF,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,MAAMqD,iBAAiB,GAAGtD,KAAK,IAAI;EACjC,MAAM;IACJuC,KAAK;IACLgB,QAAQ;IACRC;EACF,CAAC,GAAGxD,KAAK;EACT,MAAMyD,cAAc,GAAGC,CAAC,IAAIH,QAAQ,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAMC,sBAAsB,GAAGD,CAAC,IAAIF,gBAAgB,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,OAAO,aAAalE,KAAK,CAACqD,aAAa,CAAC9C,mBAAmB,EAAEhB,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAEd,KAAK,EAAE;IACpFuC,KAAK,EAAE,CAACA,KAAK,CAAC;IACdgB,QAAQ,EAAEE,cAAc;IACxBD,gBAAgB,EAAEG;EACpB,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
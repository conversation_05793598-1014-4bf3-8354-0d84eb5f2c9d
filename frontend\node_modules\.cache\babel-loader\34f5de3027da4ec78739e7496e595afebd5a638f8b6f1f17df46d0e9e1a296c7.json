{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n\n/** Format the value in the range of [min, max] */\n\n/** Format value align with step */\n\n/** Format value align with step & marks */\n\nexport default function useOffset(min, max, step, markList, allowCross, pushable) {\n  var formatRangeValue = React.useCallback(function (val) {\n    return Math.max(min, Math.min(max, val));\n  }, [min, max]);\n  var formatStepValue = React.useCallback(function (val) {\n    if (step !== null) {\n      var stepValue = min + Math.round((formatRangeValue(val) - min) / step) * step;\n\n      // Cut number in case to be like 0.30000000000000004\n      var getDecimal = function getDecimal(num) {\n        return (String(num).split('.')[1] || '').length;\n      };\n      var maxDecimal = Math.max(getDecimal(step), getDecimal(max), getDecimal(min));\n      var fixedValue = Number(stepValue.toFixed(maxDecimal));\n      return min <= fixedValue && fixedValue <= max ? fixedValue : null;\n    }\n    return null;\n  }, [step, min, max, formatRangeValue]);\n  var formatValue = React.useCallback(function (val) {\n    var formatNextValue = formatRangeValue(val);\n\n    // List align values\n    var alignValues = markList.map(function (mark) {\n      return mark.value;\n    });\n    if (step !== null) {\n      alignValues.push(formatStepValue(val));\n    }\n\n    // min & max\n    alignValues.push(min, max);\n\n    // Align with marks\n    var closeValue = alignValues[0];\n    var closeDist = max - min;\n    alignValues.forEach(function (alignValue) {\n      var dist = Math.abs(formatNextValue - alignValue);\n      if (dist <= closeDist) {\n        closeValue = alignValue;\n        closeDist = dist;\n      }\n    });\n    return closeValue;\n  }, [min, max, markList, step, formatRangeValue, formatStepValue]);\n\n  // ========================== Offset ==========================\n  // Single Value\n  var offsetValue = function offsetValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    if (typeof offset === 'number') {\n      var nextValue;\n      var originValue = values[valueIndex];\n\n      // Only used for `dist` mode\n      var targetDistValue = originValue + offset;\n\n      // Compare next step value & mark value which is best match\n      var potentialValues = [];\n      markList.forEach(function (mark) {\n        potentialValues.push(mark.value);\n      });\n\n      // Min & Max\n      potentialValues.push(min, max);\n\n      // In case origin value is align with mark but not with step\n      potentialValues.push(formatStepValue(originValue));\n\n      // Put offset step value also\n      var sign = offset > 0 ? 1 : -1;\n      if (mode === 'unit') {\n        potentialValues.push(formatStepValue(originValue + sign * step));\n      } else {\n        potentialValues.push(formatStepValue(targetDistValue));\n      }\n\n      // Find close one\n      potentialValues = potentialValues.filter(function (val) {\n        return val !== null;\n      })\n      // Remove reverse value\n      .filter(function (val) {\n        return offset < 0 ? val <= originValue : val >= originValue;\n      });\n      if (mode === 'unit') {\n        // `unit` mode can not contain itself\n        potentialValues = potentialValues.filter(function (val) {\n          return val !== originValue;\n        });\n      }\n      var compareValue = mode === 'unit' ? originValue : targetDistValue;\n      nextValue = potentialValues[0];\n      var valueDist = Math.abs(nextValue - compareValue);\n      potentialValues.forEach(function (potentialValue) {\n        var dist = Math.abs(potentialValue - compareValue);\n        if (dist < valueDist) {\n          nextValue = potentialValue;\n          valueDist = dist;\n        }\n      });\n\n      // Out of range will back to range\n      if (nextValue === undefined) {\n        return offset < 0 ? min : max;\n      }\n\n      // `dist` mode\n      if (mode === 'dist') {\n        return nextValue;\n      }\n\n      // `unit` mode may need another round\n      if (Math.abs(offset) > 1) {\n        var cloneValues = _toConsumableArray(values);\n        cloneValues[valueIndex] = nextValue;\n        return offsetValue(cloneValues, offset - sign, valueIndex, mode);\n      }\n      return nextValue;\n    } else if (offset === 'min') {\n      return min;\n    } else if (offset === 'max') {\n      return max;\n    }\n  };\n\n  /** Same as `offsetValue` but return `changed` mark to tell value changed */\n  var offsetChangedValue = function offsetChangedValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var originValue = values[valueIndex];\n    var nextValue = offsetValue(values, offset, valueIndex, mode);\n    return {\n      value: nextValue,\n      changed: nextValue !== originValue\n    };\n  };\n  var needPush = function needPush(dist) {\n    return pushable === null && dist === 0 || typeof pushable === 'number' && dist < pushable;\n  };\n\n  // Values\n  var offsetValues = function offsetValues(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var nextValues = values.map(formatValue);\n    var originValue = nextValues[valueIndex];\n    var nextValue = offsetValue(nextValues, offset, valueIndex, mode);\n    nextValues[valueIndex] = nextValue;\n    if (allowCross === false) {\n      // >>>>> Allow Cross\n      var pushNum = pushable || 0;\n\n      // ============ AllowCross ===============\n      if (valueIndex > 0 && nextValues[valueIndex - 1] !== originValue) {\n        nextValues[valueIndex] = Math.max(nextValues[valueIndex], nextValues[valueIndex - 1] + pushNum);\n      }\n      if (valueIndex < nextValues.length - 1 && nextValues[valueIndex + 1] !== originValue) {\n        nextValues[valueIndex] = Math.min(nextValues[valueIndex], nextValues[valueIndex + 1] - pushNum);\n      }\n    } else if (typeof pushable === 'number' || pushable === null) {\n      // >>>>> Pushable\n      // =============== Push ==================\n\n      // >>>>>> Basic push\n      // End values\n      for (var i = valueIndex + 1; i < nextValues.length; i += 1) {\n        var changed = true;\n        while (needPush(nextValues[i] - nextValues[i - 1]) && changed) {\n          var _offsetChangedValue = offsetChangedValue(nextValues, 1, i);\n          nextValues[i] = _offsetChangedValue.value;\n          changed = _offsetChangedValue.changed;\n        }\n      }\n\n      // Start values\n      for (var _i = valueIndex; _i > 0; _i -= 1) {\n        var _changed = true;\n        while (needPush(nextValues[_i] - nextValues[_i - 1]) && _changed) {\n          var _offsetChangedValue2 = offsetChangedValue(nextValues, -1, _i - 1);\n          nextValues[_i - 1] = _offsetChangedValue2.value;\n          _changed = _offsetChangedValue2.changed;\n        }\n      }\n\n      // >>>>> Revert back to safe push range\n      // End to Start\n      for (var _i2 = nextValues.length - 1; _i2 > 0; _i2 -= 1) {\n        var _changed2 = true;\n        while (needPush(nextValues[_i2] - nextValues[_i2 - 1]) && _changed2) {\n          var _offsetChangedValue3 = offsetChangedValue(nextValues, -1, _i2 - 1);\n          nextValues[_i2 - 1] = _offsetChangedValue3.value;\n          _changed2 = _offsetChangedValue3.changed;\n        }\n      }\n\n      // Start to End\n      for (var _i3 = 0; _i3 < nextValues.length - 1; _i3 += 1) {\n        var _changed3 = true;\n        while (needPush(nextValues[_i3 + 1] - nextValues[_i3]) && _changed3) {\n          var _offsetChangedValue4 = offsetChangedValue(nextValues, 1, _i3 + 1);\n          nextValues[_i3 + 1] = _offsetChangedValue4.value;\n          _changed3 = _offsetChangedValue4.changed;\n        }\n      }\n    }\n    return {\n      value: nextValues[valueIndex],\n      values: nextValues\n    };\n  };\n  return [formatValue, offsetValues];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "useOffset", "min", "max", "step", "markList", "allowCross", "pushable", "formatRangeValue", "useCallback", "val", "Math", "formatStepValue", "<PERSON><PERSON><PERSON><PERSON>", "round", "getDecimal", "num", "String", "split", "length", "maxDecimal", "fixedValue", "Number", "toFixed", "formatValue", "formatNextValue", "align<PERSON><PERSON><PERSON>", "map", "mark", "value", "push", "closeValue", "closeDist", "for<PERSON>ach", "alignValue", "dist", "abs", "offsetValue", "values", "offset", "valueIndex", "mode", "arguments", "undefined", "nextValue", "originValue", "targetDistValue", "potential<PERSON><PERSON><PERSON>", "sign", "filter", "compareValue", "valueDist", "potentialValue", "clone<PERSON><PERSON>ues", "offsetChangedValue", "changed", "needPush", "offsetValues", "nextV<PERSON>ues", "pushNum", "i", "_offsetChangedValue", "_i", "_changed", "_offsetChangedValue2", "_i2", "_changed2", "_offsetChangedValue3", "_i3", "_changed3", "_offsetChangedValue4"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-slider@11.1.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-slider/es/hooks/useOffset.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n\n/** Format the value in the range of [min, max] */\n\n/** Format value align with step */\n\n/** Format value align with step & marks */\n\nexport default function useOffset(min, max, step, markList, allowCross, pushable) {\n  var formatRangeValue = React.useCallback(function (val) {\n    return Math.max(min, Math.min(max, val));\n  }, [min, max]);\n  var formatStepValue = React.useCallback(function (val) {\n    if (step !== null) {\n      var stepValue = min + Math.round((formatRangeValue(val) - min) / step) * step;\n\n      // Cut number in case to be like 0.30000000000000004\n      var getDecimal = function getDecimal(num) {\n        return (String(num).split('.')[1] || '').length;\n      };\n      var maxDecimal = Math.max(getDecimal(step), getDecimal(max), getDecimal(min));\n      var fixedValue = Number(stepValue.toFixed(maxDecimal));\n      return min <= fixedValue && fixedValue <= max ? fixedValue : null;\n    }\n    return null;\n  }, [step, min, max, formatRangeValue]);\n  var formatValue = React.useCallback(function (val) {\n    var formatNextValue = formatRangeValue(val);\n\n    // List align values\n    var alignValues = markList.map(function (mark) {\n      return mark.value;\n    });\n    if (step !== null) {\n      alignValues.push(formatStepValue(val));\n    }\n\n    // min & max\n    alignValues.push(min, max);\n\n    // Align with marks\n    var closeValue = alignValues[0];\n    var closeDist = max - min;\n    alignValues.forEach(function (alignValue) {\n      var dist = Math.abs(formatNextValue - alignValue);\n      if (dist <= closeDist) {\n        closeValue = alignValue;\n        closeDist = dist;\n      }\n    });\n    return closeValue;\n  }, [min, max, markList, step, formatRangeValue, formatStepValue]);\n\n  // ========================== Offset ==========================\n  // Single Value\n  var offsetValue = function offsetValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    if (typeof offset === 'number') {\n      var nextValue;\n      var originValue = values[valueIndex];\n\n      // Only used for `dist` mode\n      var targetDistValue = originValue + offset;\n\n      // Compare next step value & mark value which is best match\n      var potentialValues = [];\n      markList.forEach(function (mark) {\n        potentialValues.push(mark.value);\n      });\n\n      // Min & Max\n      potentialValues.push(min, max);\n\n      // In case origin value is align with mark but not with step\n      potentialValues.push(formatStepValue(originValue));\n\n      // Put offset step value also\n      var sign = offset > 0 ? 1 : -1;\n      if (mode === 'unit') {\n        potentialValues.push(formatStepValue(originValue + sign * step));\n      } else {\n        potentialValues.push(formatStepValue(targetDistValue));\n      }\n\n      // Find close one\n      potentialValues = potentialValues.filter(function (val) {\n        return val !== null;\n      })\n      // Remove reverse value\n      .filter(function (val) {\n        return offset < 0 ? val <= originValue : val >= originValue;\n      });\n      if (mode === 'unit') {\n        // `unit` mode can not contain itself\n        potentialValues = potentialValues.filter(function (val) {\n          return val !== originValue;\n        });\n      }\n      var compareValue = mode === 'unit' ? originValue : targetDistValue;\n      nextValue = potentialValues[0];\n      var valueDist = Math.abs(nextValue - compareValue);\n      potentialValues.forEach(function (potentialValue) {\n        var dist = Math.abs(potentialValue - compareValue);\n        if (dist < valueDist) {\n          nextValue = potentialValue;\n          valueDist = dist;\n        }\n      });\n\n      // Out of range will back to range\n      if (nextValue === undefined) {\n        return offset < 0 ? min : max;\n      }\n\n      // `dist` mode\n      if (mode === 'dist') {\n        return nextValue;\n      }\n\n      // `unit` mode may need another round\n      if (Math.abs(offset) > 1) {\n        var cloneValues = _toConsumableArray(values);\n        cloneValues[valueIndex] = nextValue;\n        return offsetValue(cloneValues, offset - sign, valueIndex, mode);\n      }\n      return nextValue;\n    } else if (offset === 'min') {\n      return min;\n    } else if (offset === 'max') {\n      return max;\n    }\n  };\n\n  /** Same as `offsetValue` but return `changed` mark to tell value changed */\n  var offsetChangedValue = function offsetChangedValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var originValue = values[valueIndex];\n    var nextValue = offsetValue(values, offset, valueIndex, mode);\n    return {\n      value: nextValue,\n      changed: nextValue !== originValue\n    };\n  };\n  var needPush = function needPush(dist) {\n    return pushable === null && dist === 0 || typeof pushable === 'number' && dist < pushable;\n  };\n\n  // Values\n  var offsetValues = function offsetValues(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var nextValues = values.map(formatValue);\n    var originValue = nextValues[valueIndex];\n    var nextValue = offsetValue(nextValues, offset, valueIndex, mode);\n    nextValues[valueIndex] = nextValue;\n    if (allowCross === false) {\n      // >>>>> Allow Cross\n      var pushNum = pushable || 0;\n\n      // ============ AllowCross ===============\n      if (valueIndex > 0 && nextValues[valueIndex - 1] !== originValue) {\n        nextValues[valueIndex] = Math.max(nextValues[valueIndex], nextValues[valueIndex - 1] + pushNum);\n      }\n      if (valueIndex < nextValues.length - 1 && nextValues[valueIndex + 1] !== originValue) {\n        nextValues[valueIndex] = Math.min(nextValues[valueIndex], nextValues[valueIndex + 1] - pushNum);\n      }\n    } else if (typeof pushable === 'number' || pushable === null) {\n      // >>>>> Pushable\n      // =============== Push ==================\n\n      // >>>>>> Basic push\n      // End values\n      for (var i = valueIndex + 1; i < nextValues.length; i += 1) {\n        var changed = true;\n        while (needPush(nextValues[i] - nextValues[i - 1]) && changed) {\n          var _offsetChangedValue = offsetChangedValue(nextValues, 1, i);\n          nextValues[i] = _offsetChangedValue.value;\n          changed = _offsetChangedValue.changed;\n        }\n      }\n\n      // Start values\n      for (var _i = valueIndex; _i > 0; _i -= 1) {\n        var _changed = true;\n        while (needPush(nextValues[_i] - nextValues[_i - 1]) && _changed) {\n          var _offsetChangedValue2 = offsetChangedValue(nextValues, -1, _i - 1);\n          nextValues[_i - 1] = _offsetChangedValue2.value;\n          _changed = _offsetChangedValue2.changed;\n        }\n      }\n\n      // >>>>> Revert back to safe push range\n      // End to Start\n      for (var _i2 = nextValues.length - 1; _i2 > 0; _i2 -= 1) {\n        var _changed2 = true;\n        while (needPush(nextValues[_i2] - nextValues[_i2 - 1]) && _changed2) {\n          var _offsetChangedValue3 = offsetChangedValue(nextValues, -1, _i2 - 1);\n          nextValues[_i2 - 1] = _offsetChangedValue3.value;\n          _changed2 = _offsetChangedValue3.changed;\n        }\n      }\n\n      // Start to End\n      for (var _i3 = 0; _i3 < nextValues.length - 1; _i3 += 1) {\n        var _changed3 = true;\n        while (needPush(nextValues[_i3 + 1] - nextValues[_i3]) && _changed3) {\n          var _offsetChangedValue4 = offsetChangedValue(nextValues, 1, _i3 + 1);\n          nextValues[_i3 + 1] = _offsetChangedValue4.value;\n          _changed3 = _offsetChangedValue4.changed;\n        }\n      }\n    }\n    return {\n      value: nextValues[valueIndex],\n      values: nextValues\n    };\n  };\n  return [formatValue, offsetValues];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;;AAEA;;AAEA;;AAEA,eAAe,SAASC,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EAChF,IAAIC,gBAAgB,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACtD,OAAOC,IAAI,CAACR,GAAG,CAACD,GAAG,EAAES,IAAI,CAACT,GAAG,CAACC,GAAG,EAAEO,GAAG,CAAC,CAAC;EAC1C,CAAC,EAAE,CAACR,GAAG,EAAEC,GAAG,CAAC,CAAC;EACd,IAAIS,eAAe,GAAGZ,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACrD,IAAIN,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIS,SAAS,GAAGX,GAAG,GAAGS,IAAI,CAACG,KAAK,CAAC,CAACN,gBAAgB,CAACE,GAAG,CAAC,GAAGR,GAAG,IAAIE,IAAI,CAAC,GAAGA,IAAI;;MAE7E;MACA,IAAIW,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAE;QACxC,OAAO,CAACC,MAAM,CAACD,GAAG,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;MACjD,CAAC;MACD,IAAIC,UAAU,GAAGT,IAAI,CAACR,GAAG,CAACY,UAAU,CAACX,IAAI,CAAC,EAAEW,UAAU,CAACZ,GAAG,CAAC,EAAEY,UAAU,CAACb,GAAG,CAAC,CAAC;MAC7E,IAAImB,UAAU,GAAGC,MAAM,CAACT,SAAS,CAACU,OAAO,CAACH,UAAU,CAAC,CAAC;MACtD,OAAOlB,GAAG,IAAImB,UAAU,IAAIA,UAAU,IAAIlB,GAAG,GAAGkB,UAAU,GAAG,IAAI;IACnE;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACjB,IAAI,EAAEF,GAAG,EAAEC,GAAG,EAAEK,gBAAgB,CAAC,CAAC;EACtC,IAAIgB,WAAW,GAAGxB,KAAK,CAACS,WAAW,CAAC,UAAUC,GAAG,EAAE;IACjD,IAAIe,eAAe,GAAGjB,gBAAgB,CAACE,GAAG,CAAC;;IAE3C;IACA,IAAIgB,WAAW,GAAGrB,QAAQ,CAACsB,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC7C,OAAOA,IAAI,CAACC,KAAK;IACnB,CAAC,CAAC;IACF,IAAIzB,IAAI,KAAK,IAAI,EAAE;MACjBsB,WAAW,CAACI,IAAI,CAAClB,eAAe,CAACF,GAAG,CAAC,CAAC;IACxC;;IAEA;IACAgB,WAAW,CAACI,IAAI,CAAC5B,GAAG,EAAEC,GAAG,CAAC;;IAE1B;IACA,IAAI4B,UAAU,GAAGL,WAAW,CAAC,CAAC,CAAC;IAC/B,IAAIM,SAAS,GAAG7B,GAAG,GAAGD,GAAG;IACzBwB,WAAW,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAE;MACxC,IAAIC,IAAI,GAAGxB,IAAI,CAACyB,GAAG,CAACX,eAAe,GAAGS,UAAU,CAAC;MACjD,IAAIC,IAAI,IAAIH,SAAS,EAAE;QACrBD,UAAU,GAAGG,UAAU;QACvBF,SAAS,GAAGG,IAAI;MAClB;IACF,CAAC,CAAC;IACF,OAAOJ,UAAU;EACnB,CAAC,EAAE,CAAC7B,GAAG,EAAEC,GAAG,EAAEE,QAAQ,EAAED,IAAI,EAAEI,gBAAgB,EAAEI,eAAe,CAAC,CAAC;;EAEjE;EACA;EACA,IAAIyB,WAAW,GAAG,SAASA,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACjE,IAAIC,IAAI,GAAGC,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAIK,SAAS;MACb,IAAIC,WAAW,GAAGP,MAAM,CAACE,UAAU,CAAC;;MAEpC;MACA,IAAIM,eAAe,GAAGD,WAAW,GAAGN,MAAM;;MAE1C;MACA,IAAIQ,eAAe,GAAG,EAAE;MACxB1C,QAAQ,CAAC4B,OAAO,CAAC,UAAUL,IAAI,EAAE;QAC/BmB,eAAe,CAACjB,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC;MAClC,CAAC,CAAC;;MAEF;MACAkB,eAAe,CAACjB,IAAI,CAAC5B,GAAG,EAAEC,GAAG,CAAC;;MAE9B;MACA4C,eAAe,CAACjB,IAAI,CAAClB,eAAe,CAACiC,WAAW,CAAC,CAAC;;MAElD;MACA,IAAIG,IAAI,GAAGT,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9B,IAAIE,IAAI,KAAK,MAAM,EAAE;QACnBM,eAAe,CAACjB,IAAI,CAAClB,eAAe,CAACiC,WAAW,GAAGG,IAAI,GAAG5C,IAAI,CAAC,CAAC;MAClE,CAAC,MAAM;QACL2C,eAAe,CAACjB,IAAI,CAAClB,eAAe,CAACkC,eAAe,CAAC,CAAC;MACxD;;MAEA;MACAC,eAAe,GAAGA,eAAe,CAACE,MAAM,CAAC,UAAUvC,GAAG,EAAE;QACtD,OAAOA,GAAG,KAAK,IAAI;MACrB,CAAC;MACD;MAAA,CACCuC,MAAM,CAAC,UAAUvC,GAAG,EAAE;QACrB,OAAO6B,MAAM,GAAG,CAAC,GAAG7B,GAAG,IAAImC,WAAW,GAAGnC,GAAG,IAAImC,WAAW;MAC7D,CAAC,CAAC;MACF,IAAIJ,IAAI,KAAK,MAAM,EAAE;QACnB;QACAM,eAAe,GAAGA,eAAe,CAACE,MAAM,CAAC,UAAUvC,GAAG,EAAE;UACtD,OAAOA,GAAG,KAAKmC,WAAW;QAC5B,CAAC,CAAC;MACJ;MACA,IAAIK,YAAY,GAAGT,IAAI,KAAK,MAAM,GAAGI,WAAW,GAAGC,eAAe;MAClEF,SAAS,GAAGG,eAAe,CAAC,CAAC,CAAC;MAC9B,IAAII,SAAS,GAAGxC,IAAI,CAACyB,GAAG,CAACQ,SAAS,GAAGM,YAAY,CAAC;MAClDH,eAAe,CAACd,OAAO,CAAC,UAAUmB,cAAc,EAAE;QAChD,IAAIjB,IAAI,GAAGxB,IAAI,CAACyB,GAAG,CAACgB,cAAc,GAAGF,YAAY,CAAC;QAClD,IAAIf,IAAI,GAAGgB,SAAS,EAAE;UACpBP,SAAS,GAAGQ,cAAc;UAC1BD,SAAS,GAAGhB,IAAI;QAClB;MACF,CAAC,CAAC;;MAEF;MACA,IAAIS,SAAS,KAAKD,SAAS,EAAE;QAC3B,OAAOJ,MAAM,GAAG,CAAC,GAAGrC,GAAG,GAAGC,GAAG;MAC/B;;MAEA;MACA,IAAIsC,IAAI,KAAK,MAAM,EAAE;QACnB,OAAOG,SAAS;MAClB;;MAEA;MACA,IAAIjC,IAAI,CAACyB,GAAG,CAACG,MAAM,CAAC,GAAG,CAAC,EAAE;QACxB,IAAIc,WAAW,GAAGtD,kBAAkB,CAACuC,MAAM,CAAC;QAC5Ce,WAAW,CAACb,UAAU,CAAC,GAAGI,SAAS;QACnC,OAAOP,WAAW,CAACgB,WAAW,EAAEd,MAAM,GAAGS,IAAI,EAAER,UAAU,EAAEC,IAAI,CAAC;MAClE;MACA,OAAOG,SAAS;IAClB,CAAC,MAAM,IAAIL,MAAM,KAAK,KAAK,EAAE;MAC3B,OAAOrC,GAAG;IACZ,CAAC,MAAM,IAAIqC,MAAM,KAAK,KAAK,EAAE;MAC3B,OAAOpC,GAAG;IACZ;EACF,CAAC;;EAED;EACA,IAAImD,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAC/E,IAAIC,IAAI,GAAGC,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAIG,WAAW,GAAGP,MAAM,CAACE,UAAU,CAAC;IACpC,IAAII,SAAS,GAAGP,WAAW,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC7D,OAAO;MACLZ,KAAK,EAAEe,SAAS;MAChBW,OAAO,EAAEX,SAAS,KAAKC;IACzB,CAAC;EACH,CAAC;EACD,IAAIW,QAAQ,GAAG,SAASA,QAAQA,CAACrB,IAAI,EAAE;IACrC,OAAO5B,QAAQ,KAAK,IAAI,IAAI4B,IAAI,KAAK,CAAC,IAAI,OAAO5B,QAAQ,KAAK,QAAQ,IAAI4B,IAAI,GAAG5B,QAAQ;EAC3F,CAAC;;EAED;EACA,IAAIkD,YAAY,GAAG,SAASA,YAAYA,CAACnB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACnE,IAAIC,IAAI,GAAGC,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAIgB,UAAU,GAAGpB,MAAM,CAACX,GAAG,CAACH,WAAW,CAAC;IACxC,IAAIqB,WAAW,GAAGa,UAAU,CAAClB,UAAU,CAAC;IACxC,IAAII,SAAS,GAAGP,WAAW,CAACqB,UAAU,EAAEnB,MAAM,EAAEC,UAAU,EAAEC,IAAI,CAAC;IACjEiB,UAAU,CAAClB,UAAU,CAAC,GAAGI,SAAS;IAClC,IAAItC,UAAU,KAAK,KAAK,EAAE;MACxB;MACA,IAAIqD,OAAO,GAAGpD,QAAQ,IAAI,CAAC;;MAE3B;MACA,IAAIiC,UAAU,GAAG,CAAC,IAAIkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,KAAKK,WAAW,EAAE;QAChEa,UAAU,CAAClB,UAAU,CAAC,GAAG7B,IAAI,CAACR,GAAG,CAACuD,UAAU,CAAClB,UAAU,CAAC,EAAEkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,GAAGmB,OAAO,CAAC;MACjG;MACA,IAAInB,UAAU,GAAGkB,UAAU,CAACvC,MAAM,GAAG,CAAC,IAAIuC,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,KAAKK,WAAW,EAAE;QACpFa,UAAU,CAAClB,UAAU,CAAC,GAAG7B,IAAI,CAACT,GAAG,CAACwD,UAAU,CAAClB,UAAU,CAAC,EAAEkB,UAAU,CAAClB,UAAU,GAAG,CAAC,CAAC,GAAGmB,OAAO,CAAC;MACjG;IACF,CAAC,MAAM,IAAI,OAAOpD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MAC5D;MACA;;MAEA;MACA;MACA,KAAK,IAAIqD,CAAC,GAAGpB,UAAU,GAAG,CAAC,EAAEoB,CAAC,GAAGF,UAAU,CAACvC,MAAM,EAAEyC,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAIL,OAAO,GAAG,IAAI;QAClB,OAAOC,QAAQ,CAACE,UAAU,CAACE,CAAC,CAAC,GAAGF,UAAU,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIL,OAAO,EAAE;UAC7D,IAAIM,mBAAmB,GAAGP,kBAAkB,CAACI,UAAU,EAAE,CAAC,EAAEE,CAAC,CAAC;UAC9DF,UAAU,CAACE,CAAC,CAAC,GAAGC,mBAAmB,CAAChC,KAAK;UACzC0B,OAAO,GAAGM,mBAAmB,CAACN,OAAO;QACvC;MACF;;MAEA;MACA,KAAK,IAAIO,EAAE,GAAGtB,UAAU,EAAEsB,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;QACzC,IAAIC,QAAQ,GAAG,IAAI;QACnB,OAAOP,QAAQ,CAACE,UAAU,CAACI,EAAE,CAAC,GAAGJ,UAAU,CAACI,EAAE,GAAG,CAAC,CAAC,CAAC,IAAIC,QAAQ,EAAE;UAChE,IAAIC,oBAAoB,GAAGV,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC,EAAEI,EAAE,GAAG,CAAC,CAAC;UACrEJ,UAAU,CAACI,EAAE,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACnC,KAAK;UAC/CkC,QAAQ,GAAGC,oBAAoB,CAACT,OAAO;QACzC;MACF;;MAEA;MACA;MACA,KAAK,IAAIU,GAAG,GAAGP,UAAU,CAACvC,MAAM,GAAG,CAAC,EAAE8C,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;QACvD,IAAIC,SAAS,GAAG,IAAI;QACpB,OAAOV,QAAQ,CAACE,UAAU,CAACO,GAAG,CAAC,GAAGP,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC,CAAC,IAAIC,SAAS,EAAE;UACnE,IAAIC,oBAAoB,GAAGb,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC,EAAEO,GAAG,GAAG,CAAC,CAAC;UACtEP,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACtC,KAAK;UAChDqC,SAAS,GAAGC,oBAAoB,CAACZ,OAAO;QAC1C;MACF;;MAEA;MACA,KAAK,IAAIa,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGV,UAAU,CAACvC,MAAM,GAAG,CAAC,EAAEiD,GAAG,IAAI,CAAC,EAAE;QACvD,IAAIC,SAAS,GAAG,IAAI;QACpB,OAAOb,QAAQ,CAACE,UAAU,CAACU,GAAG,GAAG,CAAC,CAAC,GAAGV,UAAU,CAACU,GAAG,CAAC,CAAC,IAAIC,SAAS,EAAE;UACnE,IAAIC,oBAAoB,GAAGhB,kBAAkB,CAACI,UAAU,EAAE,CAAC,EAAEU,GAAG,GAAG,CAAC,CAAC;UACrEV,UAAU,CAACU,GAAG,GAAG,CAAC,CAAC,GAAGE,oBAAoB,CAACzC,KAAK;UAChDwC,SAAS,GAAGC,oBAAoB,CAACf,OAAO;QAC1C;MACF;IACF;IACA,OAAO;MACL1B,KAAK,EAAE6B,UAAU,CAAClB,UAAU,CAAC;MAC7BF,MAAM,EAAEoB;IACV,CAAC;EACH,CAAC;EACD,OAAO,CAAClC,WAAW,EAAEiC,YAAY,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
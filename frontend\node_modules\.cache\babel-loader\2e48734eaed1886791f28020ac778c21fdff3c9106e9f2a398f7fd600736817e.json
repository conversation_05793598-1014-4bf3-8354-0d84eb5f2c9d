{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function isValidCount(value) {\n  return typeof value !== 'undefined' && !Number.isNaN(value);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    if (!Array.isArray(list)) {\n      return;\n    }\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport var getSeparatedContent = function getSeparatedContent(text, tokens, end) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  var separate = function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(Boolean);\n  };\n  var list = separate(text, tokens);\n  if (match) {\n    return typeof end !== 'undefined' ? list.slice(0, end) : list;\n  } else {\n    return null;\n  }\n};", "map": {"version": 3, "names": ["_toConsumableArray", "_toArray", "_objectSpread", "warning", "<PERSON><PERSON><PERSON>", "data", "index", "key", "value", "undefined", "concat", "isValidCount", "Number", "isNaN", "fillFieldNames", "fieldNames", "childrenAsData", "_ref", "label", "options", "groupLabel", "mergedLabel", "flattenOptions", "_ref2", "arguments", "length", "flattenList", "_fillField<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "fieldValue", "fieldOptions", "dig", "list", "isGroupOption", "Array", "isArray", "for<PERSON>ach", "push", "groupOption", "grpLabel", "group", "injectPropsWithOption", "option", "newOption", "Object", "defineProperty", "get", "getSeparatedContent", "text", "tokens", "end", "match", "separate", "str", "_ref3", "_ref4", "token", "restTokens", "slice", "split", "reduce", "prevList", "unitStr", "filter", "Boolean"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function isValidCount(value) {\n  return typeof value !== 'undefined' && !Number.isNaN(value);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    if (!Array.isArray(list)) {\n      return;\n    }\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport var getSeparatedContent = function getSeparatedContent(text, tokens, end) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  var separate = function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(Boolean);\n  };\n  var list = separate(text, tokens);\n  if (match) {\n    return typeof end !== 'undefined' ? list.slice(0, end) : list;\n  } else {\n    return null;\n  }\n};"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC3B,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;EAClB,IAAIC,KAAK;EACT,IAAI,OAAO,IAAIH,IAAI,EAAE;IACnBG,KAAK,GAAGH,IAAI,CAACG,KAAK;EACpB;EACA,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACrC,OAAOF,GAAG;EACZ;EACA,IAAIC,KAAK,KAAKC,SAAS,EAAE;IACvB,OAAOD,KAAK;EACd;EACA,OAAO,eAAe,CAACE,MAAM,CAACJ,KAAK,CAAC;AACtC;AACA,OAAO,SAASK,YAAYA,CAACH,KAAK,EAAE;EAClC,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,KAAK,CAAC;AAC7D;AACA,OAAO,SAASM,cAAcA,CAACC,UAAU,EAAEC,cAAc,EAAE;EACzD,IAAIC,IAAI,GAAGF,UAAU,IAAI,CAAC,CAAC;IACzBG,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBV,KAAK,GAAGS,IAAI,CAACT,KAAK;IAClBW,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIC,WAAW,GAAGH,KAAK,KAAKF,cAAc,GAAG,UAAU,GAAG,OAAO,CAAC;EAClE,OAAO;IACLE,KAAK,EAAEG,WAAW;IAClBb,KAAK,EAAEA,KAAK,IAAI,OAAO;IACvBW,OAAO,EAAEA,OAAO,IAAI,SAAS;IAC7BC,UAAU,EAAEA,UAAU,IAAIC;EAC5B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACH,OAAO,EAAE;EACtC,IAAII,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChFT,UAAU,GAAGQ,KAAK,CAACR,UAAU;IAC7BC,cAAc,GAAGO,KAAK,CAACP,cAAc;EACvC,IAAIU,WAAW,GAAG,EAAE;EACpB,IAAIC,eAAe,GAAGb,cAAc,CAACC,UAAU,EAAE,KAAK,CAAC;IACrDa,UAAU,GAAGD,eAAe,CAACT,KAAK;IAClCW,UAAU,GAAGF,eAAe,CAACnB,KAAK;IAClCsB,YAAY,GAAGH,eAAe,CAACR,OAAO;IACtCC,UAAU,GAAGO,eAAe,CAACP,UAAU;EACzC,SAASW,GAAGA,CAACC,IAAI,EAAEC,aAAa,EAAE;IAChC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;MACxB;IACF;IACAA,IAAI,CAACI,OAAO,CAAC,UAAU/B,IAAI,EAAE;MAC3B,IAAI4B,aAAa,IAAI,EAAEH,YAAY,IAAIzB,IAAI,CAAC,EAAE;QAC5C,IAAIG,KAAK,GAAGH,IAAI,CAACwB,UAAU,CAAC;;QAE5B;QACAH,WAAW,CAACW,IAAI,CAAC;UACf9B,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEqB,WAAW,CAACD,MAAM,CAAC;UACrCa,WAAW,EAAEL,aAAa;UAC1B5B,IAAI,EAAEA,IAAI;UACVa,KAAK,EAAEb,IAAI,CAACuB,UAAU,CAAC;UACvBpB,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI+B,QAAQ,GAAGlC,IAAI,CAACe,UAAU,CAAC;QAC/B,IAAImB,QAAQ,KAAK9B,SAAS,IAAIO,cAAc,EAAE;UAC5CuB,QAAQ,GAAGlC,IAAI,CAACa,KAAK;QACvB;;QAEA;QACAQ,WAAW,CAACW,IAAI,CAAC;UACf9B,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEqB,WAAW,CAACD,MAAM,CAAC;UACrCe,KAAK,EAAE,IAAI;UACXnC,IAAI,EAAEA,IAAI;UACVa,KAAK,EAAEqB;QACT,CAAC,CAAC;QACFR,GAAG,CAAC1B,IAAI,CAACyB,YAAY,CAAC,EAAE,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EACAC,GAAG,CAACZ,OAAO,EAAE,KAAK,CAAC;EACnB,OAAOO,WAAW;AACpB;;AAEA;AACA;AACA;AACA,OAAO,SAASe,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,IAAIC,SAAS,GAAGzC,aAAa,CAAC,CAAC,CAAC,EAAEwC,MAAM,CAAC;EACzC,IAAI,EAAE,OAAO,IAAIC,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB3C,OAAO,CAAC,KAAK,EAAE,+GAA+G,CAAC;QAC/H,OAAOwC,SAAS;MAClB;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,SAAS;AAClB;AACA,OAAO,IAAII,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAE;EAC/E,IAAI,CAACD,MAAM,IAAI,CAACA,MAAM,CAACxB,MAAM,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAI0B,KAAK,GAAG,KAAK;EACjB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC3C,IAAIC,KAAK,GAAGtD,QAAQ,CAACqD,KAAK,CAAC;MACzBE,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;MAChBE,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACF,KAAK,EAAE;MACV,OAAO,CAACH,GAAG,CAAC;IACd;IACA,IAAIrB,IAAI,GAAGqB,GAAG,CAACM,KAAK,CAACH,KAAK,CAAC;IAC3BL,KAAK,GAAGA,KAAK,IAAInB,IAAI,CAACP,MAAM,GAAG,CAAC;IAChC,OAAOO,IAAI,CAAC4B,MAAM,CAAC,UAAUC,QAAQ,EAAEC,OAAO,EAAE;MAC9C,OAAO,EAAE,CAACpD,MAAM,CAACV,kBAAkB,CAAC6D,QAAQ,CAAC,EAAE7D,kBAAkB,CAACoD,QAAQ,CAACU,OAAO,EAAEL,UAAU,CAAC,CAAC,CAAC;IACnG,CAAC,EAAE,EAAE,CAAC,CAACM,MAAM,CAACC,OAAO,CAAC;EACxB,CAAC;EACD,IAAIhC,IAAI,GAAGoB,QAAQ,CAACJ,IAAI,EAAEC,MAAM,CAAC;EACjC,IAAIE,KAAK,EAAE;IACT,OAAO,OAAOD,GAAG,KAAK,WAAW,GAAGlB,IAAI,CAAC0B,KAAK,CAAC,CAAC,EAAER,GAAG,CAAC,GAAGlB,IAAI;EAC/D,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
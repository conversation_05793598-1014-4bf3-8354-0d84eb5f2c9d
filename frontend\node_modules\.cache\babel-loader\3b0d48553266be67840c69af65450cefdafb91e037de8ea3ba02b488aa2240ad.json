{"ast": null, "code": "import * as React from 'react';\nexport var IdContext = /*#__PURE__*/React.createContext(null);\nexport function getMenuId(uuid, eventKey) {\n  if (uuid === undefined) {\n    return null;\n  }\n  return \"\".concat(uuid, \"-\").concat(eventKey);\n}\n\n/**\n * Get `data-menu-id`\n */\nexport function useMenuId(eventKey) {\n  var id = React.useContext(IdContext);\n  return getMenuId(id, eventKey);\n}", "map": {"version": 3, "names": ["React", "IdContext", "createContext", "getMenuId", "uuid", "eventKey", "undefined", "concat", "useMenuId", "id", "useContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-menu@9.16.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-menu/es/context/IdContext.js"], "sourcesContent": ["import * as React from 'react';\nexport var IdContext = /*#__PURE__*/React.createContext(null);\nexport function getMenuId(uuid, eventKey) {\n  if (uuid === undefined) {\n    return null;\n  }\n  return \"\".concat(uuid, \"-\").concat(eventKey);\n}\n\n/**\n * Get `data-menu-id`\n */\nexport function useMenuId(eventKey) {\n  var id = React.useContext(IdContext);\n  return getMenuId(id, eventKey);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,SAAS,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC7D,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACxC,IAAID,IAAI,KAAKE,SAAS,EAAE;IACtB,OAAO,IAAI;EACb;EACA,OAAO,EAAE,CAACC,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,QAAQ,CAAC;AAC9C;;AAEA;AACA;AACA;AACA,OAAO,SAASG,SAASA,CAACH,QAAQ,EAAE;EAClC,IAAII,EAAE,GAAGT,KAAK,CAACU,UAAU,CAACT,SAAS,CAAC;EACpC,OAAOE,SAAS,CAACM,EAAE,EAAEJ,QAAQ,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
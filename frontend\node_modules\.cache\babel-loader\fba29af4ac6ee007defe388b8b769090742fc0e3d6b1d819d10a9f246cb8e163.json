{"ast": null, "code": "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { initMotion } from '../../style/motion/motion';\nconst floatButtonGroupMotion = token => {\n  const {\n    componentCls,\n    floatButtonSize,\n    motionDurationSlow,\n    motionEaseInOutCirc,\n    calc\n  } = token;\n  const moveTopIn = new Keyframes('antFloatButtonMoveTopIn', {\n    '0%': {\n      transform: `translate3d(0, ${unit(floatButtonSize)}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveTopOut = new Keyframes('antFloatButtonMoveTopOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(0, ${unit(floatButtonSize)}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveRightIn = new Keyframes('antFloatButtonMoveRightIn', {\n    '0%': {\n      transform: `translate3d(${unit(calc(floatButtonSize).mul(-1).equal())}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveRightOut = new Keyframes('antFloatButtonMoveRightOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(${unit(calc(floatButtonSize).mul(-1).equal())}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveBottomIn = new Keyframes('antFloatButtonMoveBottomIn', {\n    '0%': {\n      transform: `translate3d(0, ${unit(calc(floatButtonSize).mul(-1).equal())}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveBottomOut = new Keyframes('antFloatButtonMoveBottomOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(0, ${unit(calc(floatButtonSize).mul(-1).equal())}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveLeftIn = new Keyframes('antFloatButtonMoveLeftIn', {\n    '0%': {\n      transform: `translate3d(${unit(floatButtonSize)}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveLeftOut = new Keyframes('antFloatButtonMoveLeftOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(${unit(floatButtonSize)}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const groupPrefixCls = `${componentCls}-group`;\n  return [{\n    [groupPrefixCls]: {\n      [`&${groupPrefixCls}-top ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveTopIn, moveTopOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-bottom ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveBottomIn, moveBottomOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-left ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveLeftIn, moveLeftOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-right ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveRightIn, moveRightOut, motionDurationSlow, true)\n    }\n  }, {\n    [`${groupPrefixCls}-wrap`]: {\n      [`&${groupPrefixCls}-wrap-enter, &${groupPrefixCls}-wrap-appear`]: {\n        opacity: 0,\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`&${groupPrefixCls}-wrap-leave`]: {\n        opacity: 1,\n        animationTimingFunction: motionEaseInOutCirc\n      }\n    }\n  }];\n};\nexport default floatButtonGroupMotion;", "map": {"version": 3, "names": ["Keyframes", "unit", "initMotion", "floatButtonGroupMotion", "token", "componentCls", "floatButtonSize", "motionDurationSlow", "motionEaseInOutCirc", "calc", "moveTopIn", "transform", "transform<PERSON><PERSON>in", "opacity", "moveTopOut", "moveRightIn", "mul", "equal", "moveRightOut", "moveBottomIn", "moveBottomOut", "moveLeftIn", "moveLeftOut", "groupPrefixCls", "animationTimingFunction"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/float-button/style/keyframes.js"], "sourcesContent": ["import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { initMotion } from '../../style/motion/motion';\nconst floatButtonGroupMotion = token => {\n  const {\n    componentCls,\n    floatButtonSize,\n    motionDurationSlow,\n    motionEaseInOutCirc,\n    calc\n  } = token;\n  const moveTopIn = new Keyframes('antFloatButtonMoveTopIn', {\n    '0%': {\n      transform: `translate3d(0, ${unit(floatButtonSize)}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveTopOut = new Keyframes('antFloatButtonMoveTopOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(0, ${unit(floatButtonSize)}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveRightIn = new Keyframes('antFloatButtonMoveRightIn', {\n    '0%': {\n      transform: `translate3d(${unit(calc(floatButtonSize).mul(-1).equal())}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveRightOut = new Keyframes('antFloatButtonMoveRightOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(${unit(calc(floatButtonSize).mul(-1).equal())}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveBottomIn = new Keyframes('antFloatButtonMoveBottomIn', {\n    '0%': {\n      transform: `translate3d(0, ${unit(calc(floatButtonSize).mul(-1).equal())}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveBottomOut = new Keyframes('antFloatButtonMoveBottomOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(0, ${unit(calc(floatButtonSize).mul(-1).equal())}, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const moveLeftIn = new Keyframes('antFloatButtonMoveLeftIn', {\n    '0%': {\n      transform: `translate3d(${unit(floatButtonSize)}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    },\n    '100%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    }\n  });\n  const moveLeftOut = new Keyframes('antFloatButtonMoveLeftOut', {\n    '0%': {\n      transform: 'translate3d(0, 0, 0)',\n      transformOrigin: '0 0',\n      opacity: 1\n    },\n    '100%': {\n      transform: `translate3d(${unit(floatButtonSize)}, 0, 0)`,\n      transformOrigin: '0 0',\n      opacity: 0\n    }\n  });\n  const groupPrefixCls = `${componentCls}-group`;\n  return [{\n    [groupPrefixCls]: {\n      [`&${groupPrefixCls}-top ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveTopIn, moveTopOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-bottom ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveBottomIn, moveBottomOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-left ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveLeftIn, moveLeftOut, motionDurationSlow, true),\n      [`&${groupPrefixCls}-right ${groupPrefixCls}-wrap`]: initMotion(`${groupPrefixCls}-wrap`, moveRightIn, moveRightOut, motionDurationSlow, true)\n    }\n  }, {\n    [`${groupPrefixCls}-wrap`]: {\n      [`&${groupPrefixCls}-wrap-enter, &${groupPrefixCls}-wrap-appear`]: {\n        opacity: 0,\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`&${groupPrefixCls}-wrap-leave`]: {\n        opacity: 1,\n        animationTimingFunction: motionEaseInOutCirc\n      }\n    }\n  }];\n};\nexport default floatButtonGroupMotion;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EACtC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,kBAAkB;IAClBC,mBAAmB;IACnBC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,SAAS,GAAG,IAAIV,SAAS,CAAC,yBAAyB,EAAE;IACzD,IAAI,EAAE;MACJW,SAAS,EAAE,kBAAkBV,IAAI,CAACK,eAAe,CAAC,MAAM;MACxDM,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAG,IAAId,SAAS,CAAC,0BAA0B,EAAE;IAC3D,IAAI,EAAE;MACJW,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,kBAAkBV,IAAI,CAACK,eAAe,CAAC,MAAM;MACxDM,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAME,WAAW,GAAG,IAAIf,SAAS,CAAC,2BAA2B,EAAE;IAC7D,IAAI,EAAE;MACJW,SAAS,EAAE,eAAeV,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,SAAS;MAC9EL,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMK,YAAY,GAAG,IAAIlB,SAAS,CAAC,4BAA4B,EAAE;IAC/D,IAAI,EAAE;MACJW,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,eAAeV,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,SAAS;MAC9EL,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMM,YAAY,GAAG,IAAInB,SAAS,CAAC,4BAA4B,EAAE;IAC/D,IAAI,EAAE;MACJW,SAAS,EAAE,kBAAkBV,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,MAAM;MAC9EL,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMO,aAAa,GAAG,IAAIpB,SAAS,CAAC,6BAA6B,EAAE;IACjE,IAAI,EAAE;MACJW,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,kBAAkBV,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,MAAM;MAC9EL,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMQ,UAAU,GAAG,IAAIrB,SAAS,CAAC,0BAA0B,EAAE;IAC3D,IAAI,EAAE;MACJW,SAAS,EAAE,eAAeV,IAAI,CAACK,eAAe,CAAC,SAAS;MACxDM,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMS,WAAW,GAAG,IAAItB,SAAS,CAAC,2BAA2B,EAAE;IAC7D,IAAI,EAAE;MACJW,SAAS,EAAE,sBAAsB;MACjCC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,SAAS,EAAE,eAAeV,IAAI,CAACK,eAAe,CAAC,SAAS;MACxDM,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMU,cAAc,GAAG,GAAGlB,YAAY,QAAQ;EAC9C,OAAO,CAAC;IACN,CAACkB,cAAc,GAAG;MAChB,CAAC,IAAIA,cAAc,QAAQA,cAAc,OAAO,GAAGrB,UAAU,CAAC,GAAGqB,cAAc,OAAO,EAAEb,SAAS,EAAEI,UAAU,EAAEP,kBAAkB,EAAE,IAAI,CAAC;MACxI,CAAC,IAAIgB,cAAc,WAAWA,cAAc,OAAO,GAAGrB,UAAU,CAAC,GAAGqB,cAAc,OAAO,EAAEJ,YAAY,EAAEC,aAAa,EAAEb,kBAAkB,EAAE,IAAI,CAAC;MACjJ,CAAC,IAAIgB,cAAc,SAASA,cAAc,OAAO,GAAGrB,UAAU,CAAC,GAAGqB,cAAc,OAAO,EAAEF,UAAU,EAAEC,WAAW,EAAEf,kBAAkB,EAAE,IAAI,CAAC;MAC3I,CAAC,IAAIgB,cAAc,UAAUA,cAAc,OAAO,GAAGrB,UAAU,CAAC,GAAGqB,cAAc,OAAO,EAAER,WAAW,EAAEG,YAAY,EAAEX,kBAAkB,EAAE,IAAI;IAC/I;EACF,CAAC,EAAE;IACD,CAAC,GAAGgB,cAAc,OAAO,GAAG;MAC1B,CAAC,IAAIA,cAAc,iBAAiBA,cAAc,cAAc,GAAG;QACjEV,OAAO,EAAE,CAAC;QACVW,uBAAuB,EAAEhB;MAC3B,CAAC;MACD,CAAC,IAAIe,cAAc,aAAa,GAAG;QACjCV,OAAO,EAAE,CAAC;QACVW,uBAAuB,EAAEhB;MAC3B;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAeL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
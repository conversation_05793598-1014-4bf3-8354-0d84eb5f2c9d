{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLockEffect from \"./useLockEffect\";\n/**\n * When user first focus one input, any submit will trigger focus another one.\n * When second time focus one input, submit will not trigger focus again.\n * When click outside to close the panel, trigger event if it can trigger onChange.\n */\nexport default function useRangeActive(disabled) {\n  var empty = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var mergedOpen = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    focused = _React$useState4[0],\n    setFocused = _React$useState4[1];\n  var activeListRef = React.useRef([]);\n  var submitIndexRef = React.useRef(null);\n  var lastOperationRef = React.useRef(null);\n  var updateSubmitIndex = function updateSubmitIndex(index) {\n    submitIndexRef.current = index;\n  };\n  var hasActiveSubmitValue = function hasActiveSubmitValue(index) {\n    return submitIndexRef.current === index;\n  };\n  var triggerFocus = function triggerFocus(nextFocus) {\n    setFocused(nextFocus);\n  };\n\n  // ============================= Record =============================\n  var lastOperation = function lastOperation(type) {\n    if (type) {\n      lastOperationRef.current = type;\n    }\n    return lastOperationRef.current;\n  };\n\n  // ============================ Strategy ============================\n  // Trigger when input enter or input blur or panel close\n  var nextActiveIndex = function nextActiveIndex(nextValue) {\n    var list = activeListRef.current;\n    var filledActiveSet = new Set(list.filter(function (index) {\n      return nextValue[index] || empty[index];\n    }));\n    var nextIndex = list[list.length - 1] === 0 ? 1 : 0;\n    if (filledActiveSet.size >= 2 || disabled[nextIndex]) {\n      return null;\n    }\n    return nextIndex;\n  };\n\n  // ============================= Effect =============================\n  // Wait in case it's from the click outside to blur\n  useLockEffect(focused || mergedOpen, function () {\n    if (!focused) {\n      activeListRef.current = [];\n      updateSubmitIndex(null);\n    }\n  });\n  React.useEffect(function () {\n    if (focused) {\n      activeListRef.current.push(activeIndex);\n    }\n  }, [focused, activeIndex]);\n  return [focused, triggerFocus, lastOperation, activeIndex, setActiveIndex, nextActiveIndex, activeListRef.current, updateSubmitIndex, hasActiveSubmitValue];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useLockEffect", "useRangeActive", "disabled", "empty", "arguments", "length", "undefined", "mergedOpen", "_React$useState", "useState", "_React$useState2", "activeIndex", "setActiveIndex", "_React$useState3", "_React$useState4", "focused", "setFocused", "activeListRef", "useRef", "submitIndexRef", "lastOperationRef", "updateSubmitIndex", "index", "current", "hasActiveSubmitValue", "triggerFocus", "nextFocus", "lastOperation", "type", "nextActiveIndex", "nextValue", "list", "filledActiveSet", "Set", "filter", "nextIndex", "size", "useEffect", "push"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useRangeActive.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLockEffect from \"./useLockEffect\";\n/**\n * When user first focus one input, any submit will trigger focus another one.\n * When second time focus one input, submit will not trigger focus again.\n * When click outside to close the panel, trigger event if it can trigger onChange.\n */\nexport default function useRangeActive(disabled) {\n  var empty = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var mergedOpen = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    focused = _React$useState4[0],\n    setFocused = _React$useState4[1];\n  var activeListRef = React.useRef([]);\n  var submitIndexRef = React.useRef(null);\n  var lastOperationRef = React.useRef(null);\n  var updateSubmitIndex = function updateSubmitIndex(index) {\n    submitIndexRef.current = index;\n  };\n  var hasActiveSubmitValue = function hasActiveSubmitValue(index) {\n    return submitIndexRef.current === index;\n  };\n  var triggerFocus = function triggerFocus(nextFocus) {\n    setFocused(nextFocus);\n  };\n\n  // ============================= Record =============================\n  var lastOperation = function lastOperation(type) {\n    if (type) {\n      lastOperationRef.current = type;\n    }\n    return lastOperationRef.current;\n  };\n\n  // ============================ Strategy ============================\n  // Trigger when input enter or input blur or panel close\n  var nextActiveIndex = function nextActiveIndex(nextValue) {\n    var list = activeListRef.current;\n    var filledActiveSet = new Set(list.filter(function (index) {\n      return nextValue[index] || empty[index];\n    }));\n    var nextIndex = list[list.length - 1] === 0 ? 1 : 0;\n    if (filledActiveSet.size >= 2 || disabled[nextIndex]) {\n      return null;\n    }\n    return nextIndex;\n  };\n\n  // ============================= Effect =============================\n  // Wait in case it's from the click outside to blur\n  useLockEffect(focused || mergedOpen, function () {\n    if (!focused) {\n      activeListRef.current = [];\n      updateSubmitIndex(null);\n    }\n  });\n  React.useEffect(function () {\n    if (focused) {\n      activeListRef.current.push(activeIndex);\n    }\n  }, [focused, activeIndex]);\n  return [focused, triggerFocus, lastOperation, activeIndex, setActiveIndex, nextActiveIndex, activeListRef.current, updateSubmitIndex, hasActiveSubmitValue];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC/C,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAClF,IAAIG,UAAU,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1F,IAAII,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAGZ,cAAc,CAACU,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAGd,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;IAC1CK,gBAAgB,GAAGhB,cAAc,CAACe,gBAAgB,EAAE,CAAC,CAAC;IACtDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,aAAa,GAAGlB,KAAK,CAACmB,MAAM,CAAC,EAAE,CAAC;EACpC,IAAIC,cAAc,GAAGpB,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIE,gBAAgB,GAAGrB,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EACzC,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxDH,cAAc,CAACI,OAAO,GAAGD,KAAK;EAChC,CAAC;EACD,IAAIE,oBAAoB,GAAG,SAASA,oBAAoBA,CAACF,KAAK,EAAE;IAC9D,OAAOH,cAAc,CAACI,OAAO,KAAKD,KAAK;EACzC,CAAC;EACD,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAE;IAClDV,UAAU,CAACU,SAAS,CAAC;EACvB,CAAC;;EAED;EACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;IAC/C,IAAIA,IAAI,EAAE;MACRR,gBAAgB,CAACG,OAAO,GAAGK,IAAI;IACjC;IACA,OAAOR,gBAAgB,CAACG,OAAO;EACjC,CAAC;;EAED;EACA;EACA,IAAIM,eAAe,GAAG,SAASA,eAAeA,CAACC,SAAS,EAAE;IACxD,IAAIC,IAAI,GAAGd,aAAa,CAACM,OAAO;IAChC,IAAIS,eAAe,GAAG,IAAIC,GAAG,CAACF,IAAI,CAACG,MAAM,CAAC,UAAUZ,KAAK,EAAE;MACzD,OAAOQ,SAAS,CAACR,KAAK,CAAC,IAAInB,KAAK,CAACmB,KAAK,CAAC;IACzC,CAAC,CAAC,CAAC;IACH,IAAIa,SAAS,GAAGJ,IAAI,CAACA,IAAI,CAAC1B,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACnD,IAAI2B,eAAe,CAACI,IAAI,IAAI,CAAC,IAAIlC,QAAQ,CAACiC,SAAS,CAAC,EAAE;MACpD,OAAO,IAAI;IACb;IACA,OAAOA,SAAS;EAClB,CAAC;;EAED;EACA;EACAnC,aAAa,CAACe,OAAO,IAAIR,UAAU,EAAE,YAAY;IAC/C,IAAI,CAACQ,OAAO,EAAE;MACZE,aAAa,CAACM,OAAO,GAAG,EAAE;MAC1BF,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC,CAAC;EACFtB,KAAK,CAACsC,SAAS,CAAC,YAAY;IAC1B,IAAItB,OAAO,EAAE;MACXE,aAAa,CAACM,OAAO,CAACe,IAAI,CAAC3B,WAAW,CAAC;IACzC;EACF,CAAC,EAAE,CAACI,OAAO,EAAEJ,WAAW,CAAC,CAAC;EAC1B,OAAO,CAACI,OAAO,EAAEU,YAAY,EAAEE,aAAa,EAAEhB,WAAW,EAAEC,cAAc,EAAEiB,eAAe,EAAEZ,aAAa,CAACM,OAAO,EAAEF,iBAAiB,EAAEG,oBAAoB,CAAC;AAC7J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
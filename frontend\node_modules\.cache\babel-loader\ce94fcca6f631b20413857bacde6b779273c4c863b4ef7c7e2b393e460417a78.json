{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { ATTR_MARK } from \"../StyleContext\";\nexport var ATTR_CACHE_MAP = 'data-ant-cssinjs-cache-path';\n\n/**\n * This marks style from the css file.\n * Which means not exist in `<style />` tag.\n */\nexport var CSS_FILE_STYLE = '_FILE_STYLE__';\nexport function serialize(cachePathMap) {\n  return Object.keys(cachePathMap).map(function (path) {\n    var hash = cachePathMap[path];\n    return \"\".concat(path, \":\").concat(hash);\n  }).join(';');\n}\nvar cachePathMap;\nvar fromCSSFile = true;\n\n/**\n * @private Test usage only. Can save remove if no need.\n */\nexport function reset(mockCache) {\n  var fromFile = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  cachePathMap = mockCache;\n  fromCSSFile = fromFile;\n}\nexport function prepare() {\n  if (!cachePathMap) {\n    cachePathMap = {};\n    if (canUseDom()) {\n      var div = document.createElement('div');\n      div.className = ATTR_CACHE_MAP;\n      div.style.position = 'fixed';\n      div.style.visibility = 'hidden';\n      div.style.top = '-9999px';\n      document.body.appendChild(div);\n      var content = getComputedStyle(div).content || '';\n      content = content.replace(/^\"/, '').replace(/\"$/, '');\n\n      // Fill data\n      content.split(';').forEach(function (item) {\n        var _item$split = item.split(':'),\n          _item$split2 = _slicedToArray(_item$split, 2),\n          path = _item$split2[0],\n          hash = _item$split2[1];\n        cachePathMap[path] = hash;\n      });\n\n      // Remove inline record style\n      var inlineMapStyle = document.querySelector(\"style[\".concat(ATTR_CACHE_MAP, \"]\"));\n      if (inlineMapStyle) {\n        var _inlineMapStyle$paren;\n        fromCSSFile = false;\n        (_inlineMapStyle$paren = inlineMapStyle.parentNode) === null || _inlineMapStyle$paren === void 0 || _inlineMapStyle$paren.removeChild(inlineMapStyle);\n      }\n      document.body.removeChild(div);\n    }\n  }\n}\nexport function existPath(path) {\n  prepare();\n  return !!cachePathMap[path];\n}\nexport function getStyleAndHash(path) {\n  var hash = cachePathMap[path];\n  var styleStr = null;\n  if (hash && canUseDom()) {\n    if (fromCSSFile) {\n      styleStr = CSS_FILE_STYLE;\n    } else {\n      var _style = document.querySelector(\"style[\".concat(ATTR_MARK, \"=\\\"\").concat(cachePathMap[path], \"\\\"]\"));\n      if (_style) {\n        styleStr = _style.innerHTML;\n      } else {\n        // Clean up since not exist anymore\n        delete cachePathMap[path];\n      }\n    }\n  }\n  return [styleStr, hash];\n}", "map": {"version": 3, "names": ["_slicedToArray", "canUseDom", "ATTR_MARK", "ATTR_CACHE_MAP", "CSS_FILE_STYLE", "serialize", "cachePathMap", "Object", "keys", "map", "path", "hash", "concat", "join", "fromCSSFile", "reset", "mockCache", "fromFile", "arguments", "length", "undefined", "prepare", "div", "document", "createElement", "className", "style", "position", "visibility", "top", "body", "append<PERSON><PERSON><PERSON>", "content", "getComputedStyle", "replace", "split", "for<PERSON>ach", "item", "_item$split", "_item$split2", "inlineMapStyle", "querySelector", "_inlineMapStyle$paren", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "existPath", "getStyleAndHash", "styleStr", "_style", "innerHTML"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/util/cacheMapUtil.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { ATTR_MARK } from \"../StyleContext\";\nexport var ATTR_CACHE_MAP = 'data-ant-cssinjs-cache-path';\n\n/**\n * This marks style from the css file.\n * Which means not exist in `<style />` tag.\n */\nexport var CSS_FILE_STYLE = '_FILE_STYLE__';\nexport function serialize(cachePathMap) {\n  return Object.keys(cachePathMap).map(function (path) {\n    var hash = cachePathMap[path];\n    return \"\".concat(path, \":\").concat(hash);\n  }).join(';');\n}\nvar cachePathMap;\nvar fromCSSFile = true;\n\n/**\n * @private Test usage only. Can save remove if no need.\n */\nexport function reset(mockCache) {\n  var fromFile = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  cachePathMap = mockCache;\n  fromCSSFile = fromFile;\n}\nexport function prepare() {\n  if (!cachePathMap) {\n    cachePathMap = {};\n    if (canUseDom()) {\n      var div = document.createElement('div');\n      div.className = ATTR_CACHE_MAP;\n      div.style.position = 'fixed';\n      div.style.visibility = 'hidden';\n      div.style.top = '-9999px';\n      document.body.appendChild(div);\n      var content = getComputedStyle(div).content || '';\n      content = content.replace(/^\"/, '').replace(/\"$/, '');\n\n      // Fill data\n      content.split(';').forEach(function (item) {\n        var _item$split = item.split(':'),\n          _item$split2 = _slicedToArray(_item$split, 2),\n          path = _item$split2[0],\n          hash = _item$split2[1];\n        cachePathMap[path] = hash;\n      });\n\n      // Remove inline record style\n      var inlineMapStyle = document.querySelector(\"style[\".concat(ATTR_CACHE_MAP, \"]\"));\n      if (inlineMapStyle) {\n        var _inlineMapStyle$paren;\n        fromCSSFile = false;\n        (_inlineMapStyle$paren = inlineMapStyle.parentNode) === null || _inlineMapStyle$paren === void 0 || _inlineMapStyle$paren.removeChild(inlineMapStyle);\n      }\n      document.body.removeChild(div);\n    }\n  }\n}\nexport function existPath(path) {\n  prepare();\n  return !!cachePathMap[path];\n}\nexport function getStyleAndHash(path) {\n  var hash = cachePathMap[path];\n  var styleStr = null;\n  if (hash && canUseDom()) {\n    if (fromCSSFile) {\n      styleStr = CSS_FILE_STYLE;\n    } else {\n      var _style = document.querySelector(\"style[\".concat(ATTR_MARK, \"=\\\"\").concat(cachePathMap[path], \"\\\"]\"));\n      if (_style) {\n        styleStr = _style.innerHTML;\n      } else {\n        // Clean up since not exist anymore\n        delete cachePathMap[path];\n      }\n    }\n  }\n  return [styleStr, hash];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,IAAIC,cAAc,GAAG,6BAA6B;;AAEzD;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,eAAe;AAC3C,OAAO,SAASC,SAASA,CAACC,YAAY,EAAE;EACtC,OAAOC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAE;IACnD,IAAIC,IAAI,GAAGL,YAAY,CAACI,IAAI,CAAC;IAC7B,OAAO,EAAE,CAACE,MAAM,CAACF,IAAI,EAAE,GAAG,CAAC,CAACE,MAAM,CAACD,IAAI,CAAC;EAC1C,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;AACd;AACA,IAAIP,YAAY;AAChB,IAAIQ,WAAW,GAAG,IAAI;;AAEtB;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAE;EAC/B,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvFZ,YAAY,GAAGU,SAAS;EACxBF,WAAW,GAAGG,QAAQ;AACxB;AACA,OAAO,SAASI,OAAOA,CAAA,EAAG;EACxB,IAAI,CAACf,YAAY,EAAE;IACjBA,YAAY,GAAG,CAAC,CAAC;IACjB,IAAIL,SAAS,CAAC,CAAC,EAAE;MACf,IAAIqB,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvCF,GAAG,CAACG,SAAS,GAAGtB,cAAc;MAC9BmB,GAAG,CAACI,KAAK,CAACC,QAAQ,GAAG,OAAO;MAC5BL,GAAG,CAACI,KAAK,CAACE,UAAU,GAAG,QAAQ;MAC/BN,GAAG,CAACI,KAAK,CAACG,GAAG,GAAG,SAAS;MACzBN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,GAAG,CAAC;MAC9B,IAAIU,OAAO,GAAGC,gBAAgB,CAACX,GAAG,CAAC,CAACU,OAAO,IAAI,EAAE;MACjDA,OAAO,GAAGA,OAAO,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;;MAErD;MACAF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;QACzC,IAAIC,WAAW,GAAGD,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;UAC/BI,YAAY,GAAGvC,cAAc,CAACsC,WAAW,EAAE,CAAC,CAAC;UAC7C5B,IAAI,GAAG6B,YAAY,CAAC,CAAC,CAAC;UACtB5B,IAAI,GAAG4B,YAAY,CAAC,CAAC,CAAC;QACxBjC,YAAY,CAACI,IAAI,CAAC,GAAGC,IAAI;MAC3B,CAAC,CAAC;;MAEF;MACA,IAAI6B,cAAc,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,QAAQ,CAAC7B,MAAM,CAACT,cAAc,EAAE,GAAG,CAAC,CAAC;MACjF,IAAIqC,cAAc,EAAE;QAClB,IAAIE,qBAAqB;QACzB5B,WAAW,GAAG,KAAK;QACnB,CAAC4B,qBAAqB,GAAGF,cAAc,CAACG,UAAU,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACE,WAAW,CAACJ,cAAc,CAAC;MACvJ;MACAjB,QAAQ,CAACO,IAAI,CAACc,WAAW,CAACtB,GAAG,CAAC;IAChC;EACF;AACF;AACA,OAAO,SAASuB,SAASA,CAACnC,IAAI,EAAE;EAC9BW,OAAO,CAAC,CAAC;EACT,OAAO,CAAC,CAACf,YAAY,CAACI,IAAI,CAAC;AAC7B;AACA,OAAO,SAASoC,eAAeA,CAACpC,IAAI,EAAE;EACpC,IAAIC,IAAI,GAAGL,YAAY,CAACI,IAAI,CAAC;EAC7B,IAAIqC,QAAQ,GAAG,IAAI;EACnB,IAAIpC,IAAI,IAAIV,SAAS,CAAC,CAAC,EAAE;IACvB,IAAIa,WAAW,EAAE;MACfiC,QAAQ,GAAG3C,cAAc;IAC3B,CAAC,MAAM;MACL,IAAI4C,MAAM,GAAGzB,QAAQ,CAACkB,aAAa,CAAC,QAAQ,CAAC7B,MAAM,CAACV,SAAS,EAAE,KAAK,CAAC,CAACU,MAAM,CAACN,YAAY,CAACI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;MACxG,IAAIsC,MAAM,EAAE;QACVD,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAC7B,CAAC,MAAM;QACL;QACA,OAAO3C,YAAY,CAACI,IAAI,CAAC;MAC3B;IACF;EACF;EACA,OAAO,CAACqC,QAAQ,EAAEpC,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
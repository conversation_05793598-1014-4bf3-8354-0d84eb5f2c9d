{"ast": null, "code": "import * as React from 'react';\nexport var PreviewGroupContext = /*#__PURE__*/React.createContext(null);", "map": {"version": 3, "names": ["React", "PreviewGroupContext", "createContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-image@7.12.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-image/es/context.js"], "sourcesContent": ["import * as React from 'react';\nexport var PreviewGroupContext = /*#__PURE__*/React.createContext(null);"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,mBAAmB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport useToggleDates from \"../hooks/useToggleDates\";\nimport PickerTrigger from \"../PickerTrigger\";\nimport { pickTriggerProps } from \"../PickerTrigger/util\";\nimport { toArray } from \"../utils/miscUtil\";\nimport PickerContext from \"./context\";\nimport useCellRender from \"./hooks/useCellRender\";\nimport useFieldsInvalidate from \"./hooks/useFieldsInvalidate\";\nimport useFilledProps from \"./hooks/useFilledProps\";\nimport useOpen from \"./hooks/useOpen\";\nimport usePickerRef from \"./hooks/usePickerRef\";\nimport usePresets from \"./hooks/usePresets\";\nimport useRangeActive from \"./hooks/useRangeActive\";\nimport useRangePickerValue from \"./hooks/useRangePickerValue\";\nimport useRangeValue, { useInnerValue } from \"./hooks/useRangeValue\";\nimport useShowNow from \"./hooks/useShowNow\";\nimport Popup from \"./Popup\";\nimport SingleSelector from \"./Selector/SingleSelector\";\n\n// TODO: isInvalidateDate with showTime.disabledTime should not provide `range` prop\n\n/** Internal usage. For cross function get same aligned props */\n\nfunction Picker(props, ref) {\n  // ========================= Prop =========================\n  var _useFilledProps = useFilledProps(props),\n    _useFilledProps2 = _slicedToArray(_useFilledProps, 6),\n    filledProps = _useFilledProps2[0],\n    internalPicker = _useFilledProps2[1],\n    complexPicker = _useFilledProps2[2],\n    formatList = _useFilledProps2[3],\n    maskFormat = _useFilledProps2[4],\n    isInvalidateDate = _useFilledProps2[5];\n  var _ref = filledProps,\n    prefixCls = _ref.prefixCls,\n    styles = _ref.styles,\n    classNames = _ref.classNames,\n    order = _ref.order,\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    needConfirm = _ref.needConfirm,\n    onChange = _ref.onChange,\n    onKeyDown = _ref.onKeyDown,\n    disabled = _ref.disabled,\n    disabledDate = _ref.disabledDate,\n    minDate = _ref.minDate,\n    maxDate = _ref.maxDate,\n    defaultOpen = _ref.defaultOpen,\n    open = _ref.open,\n    onOpenChange = _ref.onOpenChange,\n    locale = _ref.locale,\n    generateConfig = _ref.generateConfig,\n    picker = _ref.picker,\n    showNow = _ref.showNow,\n    showToday = _ref.showToday,\n    showTime = _ref.showTime,\n    mode = _ref.mode,\n    onPanelChange = _ref.onPanelChange,\n    onCalendarChange = _ref.onCalendarChange,\n    onOk = _ref.onOk,\n    multiple = _ref.multiple,\n    defaultPickerValue = _ref.defaultPickerValue,\n    pickerValue = _ref.pickerValue,\n    onPickerValueChange = _ref.onPickerValueChange,\n    inputReadOnly = _ref.inputReadOnly,\n    suffixIcon = _ref.suffixIcon,\n    removeIcon = _ref.removeIcon,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    presets = _ref.presets,\n    components = _ref.components,\n    cellRender = _ref.cellRender,\n    dateRender = _ref.dateRender,\n    monthCellRender = _ref.monthCellRender,\n    onClick = _ref.onClick;\n\n  // ========================= Refs =========================\n  var selectorRef = usePickerRef(ref);\n\n  // ========================= Util =========================\n  function pickerParam(values) {\n    if (values === null) {\n      return null;\n    }\n    return multiple ? values : values[0];\n  }\n  var toggleDates = useToggleDates(generateConfig, locale, internalPicker);\n\n  // ========================= Open =========================\n  var _useOpen = useOpen(open, defaultOpen, [disabled], onOpenChange),\n    _useOpen2 = _slicedToArray(_useOpen, 2),\n    mergedOpen = _useOpen2[0],\n    triggerOpen = _useOpen2[1];\n\n  // ======================= Calendar =======================\n  var onInternalCalendarChange = function onInternalCalendarChange(dates, dateStrings, info) {\n    if (onCalendarChange) {\n      var filteredInfo = _objectSpread({}, info);\n      delete filteredInfo.range;\n      onCalendarChange(pickerParam(dates), pickerParam(dateStrings), filteredInfo);\n    }\n  };\n  var onInternalOk = function onInternalOk(dates) {\n    onOk === null || onOk === void 0 || onOk(pickerParam(dates));\n  };\n\n  // ======================== Values ========================\n  var _useInnerValue = useInnerValue(generateConfig, locale, formatList, false, order, defaultValue, value, onInternalCalendarChange, onInternalOk),\n    _useInnerValue2 = _slicedToArray(_useInnerValue, 5),\n    mergedValue = _useInnerValue2[0],\n    setInnerValue = _useInnerValue2[1],\n    getCalendarValue = _useInnerValue2[2],\n    triggerCalendarChange = _useInnerValue2[3],\n    triggerOk = _useInnerValue2[4];\n  var calendarValue = getCalendarValue();\n\n  // ======================== Active ========================\n  // In SinglePicker, we will always get `activeIndex` is 0.\n  var _useRangeActive = useRangeActive([disabled]),\n    _useRangeActive2 = _slicedToArray(_useRangeActive, 4),\n    focused = _useRangeActive2[0],\n    triggerFocus = _useRangeActive2[1],\n    lastOperation = _useRangeActive2[2],\n    activeIndex = _useRangeActive2[3];\n  var onSharedFocus = function onSharedFocus(event) {\n    triggerFocus(true);\n    onFocus === null || onFocus === void 0 || onFocus(event, {});\n  };\n  var onSharedBlur = function onSharedBlur(event) {\n    triggerFocus(false);\n    onBlur === null || onBlur === void 0 || onBlur(event, {});\n  };\n\n  // ========================= Mode =========================\n  var _useMergedState = useMergedState(picker, {\n      value: mode\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedMode = _useMergedState2[0],\n    setMode = _useMergedState2[1];\n\n  /** Extends from `mergedMode` to patch `datetime` mode */\n  var internalMode = mergedMode === 'date' && showTime ? 'datetime' : mergedMode;\n\n  // ======================= Show Now =======================\n  var mergedShowNow = useShowNow(picker, mergedMode, showNow, showToday);\n\n  // ======================== Value =========================\n  var onInternalChange = onChange && function (dates, dateStrings) {\n    onChange(pickerParam(dates), pickerParam(dateStrings));\n  };\n  var _useRangeValue = useRangeValue(_objectSpread(_objectSpread({}, filledProps), {}, {\n      onChange: onInternalChange\n    }), mergedValue, setInnerValue, getCalendarValue, triggerCalendarChange, [],\n    //disabled,\n    formatList, focused, mergedOpen, isInvalidateDate),\n    _useRangeValue2 = _slicedToArray(_useRangeValue, 2),\n    /** Trigger `onChange` directly without check `disabledDate` */\n    triggerSubmitChange = _useRangeValue2[1];\n\n  // ======================= Validate =======================\n  var _useFieldsInvalidate = useFieldsInvalidate(calendarValue, isInvalidateDate),\n    _useFieldsInvalidate2 = _slicedToArray(_useFieldsInvalidate, 2),\n    submitInvalidates = _useFieldsInvalidate2[0],\n    onSelectorInvalid = _useFieldsInvalidate2[1];\n  var submitInvalidate = React.useMemo(function () {\n    return submitInvalidates.some(function (invalidated) {\n      return invalidated;\n    });\n  }, [submitInvalidates]);\n\n  // ===================== Picker Value =====================\n  // Proxy to single pickerValue\n  var onInternalPickerValueChange = function onInternalPickerValueChange(dates, info) {\n    if (onPickerValueChange) {\n      var cleanInfo = _objectSpread(_objectSpread({}, info), {}, {\n        mode: info.mode[0]\n      });\n      delete cleanInfo.range;\n      onPickerValueChange(dates[0], cleanInfo);\n    }\n  };\n  var _useRangePickerValue = useRangePickerValue(generateConfig, locale, calendarValue, [mergedMode], mergedOpen, activeIndex, internalPicker, false,\n    // multiplePanel,\n    defaultPickerValue, pickerValue, toArray(showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue), onInternalPickerValueChange, minDate, maxDate),\n    _useRangePickerValue2 = _slicedToArray(_useRangePickerValue, 2),\n    currentPickerValue = _useRangePickerValue2[0],\n    setCurrentPickerValue = _useRangePickerValue2[1];\n\n  // >>> Mode need wait for `pickerValue`\n  var triggerModeChange = useEvent(function (nextPickerValue, nextMode, triggerEvent) {\n    setMode(nextMode);\n\n    // Compatible with `onPanelChange`\n    if (onPanelChange && triggerEvent !== false) {\n      var lastPickerValue = nextPickerValue || calendarValue[calendarValue.length - 1];\n      onPanelChange(lastPickerValue, nextMode);\n    }\n  });\n\n  // ======================== Submit ========================\n  /**\n   * Different with RangePicker, confirm should check `multiple` logic.\n   * This will never provide `date` instead.\n   */\n  var triggerConfirm = function triggerConfirm() {\n    triggerSubmitChange(getCalendarValue());\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Click =========================\n  var onSelectorClick = function onSelectorClick(event) {\n    if (!disabled && !selectorRef.current.nativeElement.contains(document.activeElement)) {\n      // Click to focus the enabled input\n      selectorRef.current.focus();\n    }\n    triggerOpen(true);\n    onClick === null || onClick === void 0 || onClick(event);\n  };\n  var onSelectorClear = function onSelectorClear() {\n    triggerSubmitChange(null);\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Hover =========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hoverSource = _React$useState2[0],\n    setHoverSource = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalHoverValue = _React$useState4[0],\n    setInternalHoverValue = _React$useState4[1];\n  var hoverValues = React.useMemo(function () {\n    var values = [internalHoverValue].concat(_toConsumableArray(calendarValue)).filter(function (date) {\n      return date;\n    });\n    return multiple ? values : values.slice(0, 1);\n  }, [calendarValue, internalHoverValue, multiple]);\n\n  // Selector values is different with RangePicker\n  // which can not use `hoverValue` directly\n  var selectorValues = React.useMemo(function () {\n    if (!multiple && internalHoverValue) {\n      return [internalHoverValue];\n    }\n    return calendarValue.filter(function (date) {\n      return date;\n    });\n  }, [calendarValue, internalHoverValue, multiple]);\n\n  // Clean up `internalHoverValues` when closed\n  React.useEffect(function () {\n    if (!mergedOpen) {\n      setInternalHoverValue(null);\n    }\n  }, [mergedOpen]);\n\n  // ========================================================\n  // ==                       Panels                       ==\n  // ========================================================\n  // ======================= Presets ========================\n  var presetList = usePresets(presets);\n  var onPresetHover = function onPresetHover(nextValue) {\n    setInternalHoverValue(nextValue);\n    setHoverSource('preset');\n  };\n\n  // TODO: handle this\n  var onPresetSubmit = function onPresetSubmit(nextValue) {\n    var nextCalendarValues = multiple ? toggleDates(getCalendarValue(), nextValue) : [nextValue];\n    var passed = triggerSubmitChange(nextCalendarValues);\n    if (passed && !multiple) {\n      triggerOpen(false, {\n        force: true\n      });\n    }\n  };\n  var onNow = function onNow(now) {\n    onPresetSubmit(now);\n  };\n\n  // ======================== Panel =========================\n  var onPanelHover = function onPanelHover(date) {\n    setInternalHoverValue(date);\n    setHoverSource('cell');\n  };\n\n  // >>> Focus\n  var onPanelFocus = function onPanelFocus(event) {\n    triggerOpen(true);\n    onSharedFocus(event);\n  };\n\n  // >>> Calendar\n  var onPanelSelect = function onPanelSelect(date) {\n    lastOperation('panel');\n\n    // Not change values if multiple and current panel is to match with picker\n    if (multiple && internalMode !== picker) {\n      return;\n    }\n    var nextValues = multiple ? toggleDates(getCalendarValue(), date) : [date];\n\n    // Only trigger calendar event but not update internal `calendarValue` state\n    triggerCalendarChange(nextValues);\n\n    // >>> Trigger next active if !needConfirm\n    // Fully logic check `useRangeValue` hook\n    if (!needConfirm && !complexPicker && internalPicker === internalMode) {\n      triggerConfirm();\n    }\n  };\n\n  // >>> Close\n  var onPopupClose = function onPopupClose() {\n    // Close popup\n    triggerOpen(false);\n  };\n\n  // >>> cellRender\n  var onInternalCellRender = useCellRender(cellRender, dateRender, monthCellRender);\n\n  // >>> invalid\n\n  var panelProps = React.useMemo(function () {\n    var domProps = pickAttrs(filledProps, false);\n    var restProps = omit(filledProps, [].concat(_toConsumableArray(Object.keys(domProps)), ['onChange', 'onCalendarChange', 'style', 'className', 'onPanelChange']));\n    return _objectSpread(_objectSpread({}, restProps), {}, {\n      multiple: filledProps.multiple\n    });\n  }, [filledProps]);\n\n  // >>> Render\n  var panel = /*#__PURE__*/React.createElement(Popup, _extends({}, panelProps, {\n    showNow: mergedShowNow,\n    showTime: showTime\n    // Disabled\n    ,\n\n    disabledDate: disabledDate\n    // Focus\n    ,\n\n    onFocus: onPanelFocus,\n    onBlur: onSharedBlur\n    // Mode\n    ,\n\n    picker: picker,\n    mode: mergedMode,\n    internalMode: internalMode,\n    onPanelChange: triggerModeChange\n    // Value\n    ,\n\n    format: maskFormat,\n    value: calendarValue,\n    isInvalid: isInvalidateDate,\n    onChange: null,\n    onSelect: onPanelSelect\n    // PickerValue\n    ,\n\n    pickerValue: currentPickerValue,\n    defaultOpenValue: showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue,\n    onPickerValueChange: setCurrentPickerValue\n    // Hover\n    ,\n\n    hoverValue: hoverValues,\n    onHover: onPanelHover\n    // Submit\n    ,\n\n    needConfirm: needConfirm,\n    onSubmit: triggerConfirm,\n    onOk: triggerOk\n    // Preset\n    ,\n\n    presets: presetList,\n    onPresetHover: onPresetHover,\n    onPresetSubmit: onPresetSubmit,\n    onNow: onNow\n    // Render\n    ,\n\n    cellRender: onInternalCellRender\n  }));\n\n  // ========================================================\n  // ==                      Selector                      ==\n  // ========================================================\n\n  // ======================== Change ========================\n  var onSelectorChange = function onSelectorChange(date) {\n    triggerCalendarChange(date);\n  };\n  var onSelectorInputChange = function onSelectorInputChange() {\n    lastOperation('input');\n  };\n\n  // ======================= Selector =======================\n  var onSelectorFocus = function onSelectorFocus(event) {\n    lastOperation('input');\n    triggerOpen(true, {\n      inherit: true\n    });\n\n    // setActiveIndex(index);\n\n    onSharedFocus(event);\n  };\n  var onSelectorBlur = function onSelectorBlur(event) {\n    triggerOpen(false);\n    onSharedBlur(event);\n  };\n  var onSelectorKeyDown = function onSelectorKeyDown(event, preventDefault) {\n    if (event.key === 'Tab') {\n      triggerConfirm();\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event, preventDefault);\n  };\n\n  // ======================= Context ========================\n  var context = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      locale: locale,\n      generateConfig: generateConfig,\n      button: components.button,\n      input: components.input\n    };\n  }, [prefixCls, locale, generateConfig, components.button, components.input]);\n\n  // ======================== Effect ========================\n  // >>> Mode\n  // Reset for every active\n  useLayoutEffect(function () {\n    if (mergedOpen && activeIndex !== undefined) {\n      // Legacy compatible. This effect update should not trigger `onPanelChange`\n      triggerModeChange(null, picker, false);\n    }\n  }, [mergedOpen, activeIndex, picker]);\n\n  // >>> For complex picker, we need check if need to focus next one\n  useLayoutEffect(function () {\n    var lastOp = lastOperation();\n\n    // Trade as confirm on field leave\n    if (!mergedOpen && lastOp === 'input') {\n      triggerOpen(false);\n      triggerConfirm();\n    }\n\n    // Submit with complex picker\n    if (!mergedOpen && complexPicker && !needConfirm && lastOp === 'panel') {\n      triggerConfirm();\n    }\n  }, [mergedOpen]);\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(PickerContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(PickerTrigger, _extends({}, pickTriggerProps(filledProps), {\n    popupElement: panel,\n    popupStyle: styles.popup,\n    popupClassName: classNames.popup\n    // Visible\n    ,\n\n    visible: mergedOpen,\n    onClose: onPopupClose\n  }), /*#__PURE__*/React.createElement(SingleSelector\n  // Shared\n  , _extends({}, filledProps, {\n    // Ref\n    ref: selectorRef\n    // Icon\n    ,\n\n    suffixIcon: suffixIcon,\n    removeIcon: removeIcon\n    // Active\n    ,\n\n    activeHelp: !!internalHoverValue,\n    allHelp: !!internalHoverValue && hoverSource === 'preset',\n    focused: focused,\n    onFocus: onSelectorFocus,\n    onBlur: onSelectorBlur,\n    onKeyDown: onSelectorKeyDown,\n    onSubmit: triggerConfirm\n    // Change\n    ,\n\n    value: selectorValues,\n    maskFormat: maskFormat,\n    onChange: onSelectorChange,\n    onInputChange: onSelectorInputChange,\n    internalPicker: internalPicker\n    // Format\n    ,\n\n    format: formatList,\n    inputReadOnly: inputReadOnly\n    // Disabled\n    ,\n\n    disabled: disabled\n    // Open\n    ,\n\n    open: mergedOpen,\n    onOpenChange: triggerOpen\n    // Click\n    ,\n\n    onClick: onSelectorClick,\n    onClear: onSelectorClear\n    // Invalid\n    ,\n\n    invalid: submitInvalidate,\n    onInvalid: function onInvalid(invalid) {\n      // Only `single` mode support type date.\n      // `multiple` mode can not typing.\n      onSelectorInvalid(invalid, 0);\n    }\n  }))));\n}\nvar RefPicker = /*#__PURE__*/React.forwardRef(Picker);\nif (process.env.NODE_ENV !== 'production') {\n  RefPicker.displayName = 'RefPicker';\n}\nexport default RefPicker;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_objectSpread", "_slicedToArray", "useEvent", "useMergedState", "useLayoutEffect", "omit", "pickAttrs", "React", "useToggleDates", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickTriggerProps", "toArray", "<PERSON>er<PERSON>ontext", "useCellRender", "useFieldsInvalidate", "useFilledProps", "useOpen", "usePickerRef", "usePresets", "useRangeActive", "useRangePickerValue", "useRangeValue", "useInnerValue", "useShowNow", "Popup", "SingleSelector", "Picker", "props", "ref", "_useFilledProps", "_useFilledProps2", "filledProps", "internalPicker", "complexPicker", "formatList", "maskFormat", "isInvalidateDate", "_ref", "prefixCls", "styles", "classNames", "order", "defaultValue", "value", "needConfirm", "onChange", "onKeyDown", "disabled", "disabledDate", "minDate", "maxDate", "defaultOpen", "open", "onOpenChange", "locale", "generateConfig", "picker", "showNow", "showToday", "showTime", "mode", "onPanelChange", "onCalendarChange", "onOk", "multiple", "defaultPickerValue", "picker<PERSON><PERSON><PERSON>", "onPickerValueChange", "inputReadOnly", "suffixIcon", "removeIcon", "onFocus", "onBlur", "presets", "components", "cellRender", "dateRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "selectorRef", "picker<PERSON><PERSON><PERSON>", "values", "toggleDates", "_useOpen", "_useOpen2", "mergedOpen", "triggerOpen", "onInternalCalendarChange", "dates", "dateStrings", "info", "filteredInfo", "range", "onInternalOk", "_useInnerValue", "_useInnerValue2", "mergedValue", "setInnerValue", "getCalendarValue", "triggerCalendarChange", "triggerOk", "calendarValue", "_useRangeActive", "_useRangeActive2", "focused", "triggerFocus", "lastOperation", "activeIndex", "onSharedFocus", "event", "onSharedBlur", "_useMergedState", "_useMergedState2", "mergedMode", "setMode", "internalMode", "mergedShowNow", "onInternalChange", "_useRangeValue", "_useRangeValue2", "triggerSubmitChange", "_useFieldsInvalidate", "_useFieldsInvalidate2", "submitInvalidates", "onSelectorInvalid", "submitInvalidate", "useMemo", "some", "invalidated", "onInternalPickerValueChange", "cleanInfo", "_useRangePickerValue", "defaultOpenValue", "_useRangePickerValue2", "currentPickerValue", "setCurrentPickerValue", "triggerModeChange", "nextPickerValue", "nextMode", "triggerEvent", "lastPickerValue", "length", "triggerConfirm", "force", "onSelectorClick", "current", "nativeElement", "contains", "document", "activeElement", "focus", "onSelectorClear", "_React$useState", "useState", "_React$useState2", "hoverSource", "setHoverSource", "_React$useState3", "_React$useState4", "internalHoverValue", "setInternalHoverValue", "hoverValues", "concat", "filter", "date", "slice", "selector<PERSON><PERSON><PERSON>", "useEffect", "presetList", "onPresetHover", "nextValue", "onPresetSubmit", "nextCalendarValues", "passed", "onNow", "now", "onPanelHover", "onPanelFocus", "onPanelSelect", "nextV<PERSON>ues", "onPopupClose", "onInternalCellRender", "panelProps", "domProps", "restProps", "Object", "keys", "panel", "createElement", "format", "isInvalid", "onSelect", "hoverValue", "onHover", "onSubmit", "onSelectorChange", "onSelectorInputChange", "onSelectorFocus", "inherit", "onSelectorBlur", "onSelectorKeyDown", "preventDefault", "key", "context", "button", "input", "undefined", "lastOp", "Provider", "popupElement", "popupStyle", "popup", "popupClassName", "visible", "onClose", "activeHelp", "allHelp", "onInputChange", "onClear", "invalid", "onInvalid", "RefPicker", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/SinglePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport useToggleDates from \"../hooks/useToggleDates\";\nimport PickerTrigger from \"../PickerTrigger\";\nimport { pickTriggerProps } from \"../PickerTrigger/util\";\nimport { toArray } from \"../utils/miscUtil\";\nimport PickerContext from \"./context\";\nimport useCellRender from \"./hooks/useCellRender\";\nimport useFieldsInvalidate from \"./hooks/useFieldsInvalidate\";\nimport useFilledProps from \"./hooks/useFilledProps\";\nimport useOpen from \"./hooks/useOpen\";\nimport usePickerRef from \"./hooks/usePickerRef\";\nimport usePresets from \"./hooks/usePresets\";\nimport useRangeActive from \"./hooks/useRangeActive\";\nimport useRangePickerValue from \"./hooks/useRangePickerValue\";\nimport useRangeValue, { useInnerValue } from \"./hooks/useRangeValue\";\nimport useShowNow from \"./hooks/useShowNow\";\nimport Popup from \"./Popup\";\nimport SingleSelector from \"./Selector/SingleSelector\";\n\n// TODO: isInvalidateDate with showTime.disabledTime should not provide `range` prop\n\n/** Internal usage. For cross function get same aligned props */\n\nfunction Picker(props, ref) {\n  // ========================= Prop =========================\n  var _useFilledProps = useFilledProps(props),\n    _useFilledProps2 = _slicedToArray(_useFilledProps, 6),\n    filledProps = _useFilledProps2[0],\n    internalPicker = _useFilledProps2[1],\n    complexPicker = _useFilledProps2[2],\n    formatList = _useFilledProps2[3],\n    maskFormat = _useFilledProps2[4],\n    isInvalidateDate = _useFilledProps2[5];\n  var _ref = filledProps,\n    prefixCls = _ref.prefixCls,\n    styles = _ref.styles,\n    classNames = _ref.classNames,\n    order = _ref.order,\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    needConfirm = _ref.needConfirm,\n    onChange = _ref.onChange,\n    onKeyDown = _ref.onKeyDown,\n    disabled = _ref.disabled,\n    disabledDate = _ref.disabledDate,\n    minDate = _ref.minDate,\n    maxDate = _ref.maxDate,\n    defaultOpen = _ref.defaultOpen,\n    open = _ref.open,\n    onOpenChange = _ref.onOpenChange,\n    locale = _ref.locale,\n    generateConfig = _ref.generateConfig,\n    picker = _ref.picker,\n    showNow = _ref.showNow,\n    showToday = _ref.showToday,\n    showTime = _ref.showTime,\n    mode = _ref.mode,\n    onPanelChange = _ref.onPanelChange,\n    onCalendarChange = _ref.onCalendarChange,\n    onOk = _ref.onOk,\n    multiple = _ref.multiple,\n    defaultPickerValue = _ref.defaultPickerValue,\n    pickerValue = _ref.pickerValue,\n    onPickerValueChange = _ref.onPickerValueChange,\n    inputReadOnly = _ref.inputReadOnly,\n    suffixIcon = _ref.suffixIcon,\n    removeIcon = _ref.removeIcon,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    presets = _ref.presets,\n    components = _ref.components,\n    cellRender = _ref.cellRender,\n    dateRender = _ref.dateRender,\n    monthCellRender = _ref.monthCellRender,\n    onClick = _ref.onClick;\n\n  // ========================= Refs =========================\n  var selectorRef = usePickerRef(ref);\n\n  // ========================= Util =========================\n  function pickerParam(values) {\n    if (values === null) {\n      return null;\n    }\n    return multiple ? values : values[0];\n  }\n  var toggleDates = useToggleDates(generateConfig, locale, internalPicker);\n\n  // ========================= Open =========================\n  var _useOpen = useOpen(open, defaultOpen, [disabled], onOpenChange),\n    _useOpen2 = _slicedToArray(_useOpen, 2),\n    mergedOpen = _useOpen2[0],\n    triggerOpen = _useOpen2[1];\n\n  // ======================= Calendar =======================\n  var onInternalCalendarChange = function onInternalCalendarChange(dates, dateStrings, info) {\n    if (onCalendarChange) {\n      var filteredInfo = _objectSpread({}, info);\n      delete filteredInfo.range;\n      onCalendarChange(pickerParam(dates), pickerParam(dateStrings), filteredInfo);\n    }\n  };\n  var onInternalOk = function onInternalOk(dates) {\n    onOk === null || onOk === void 0 || onOk(pickerParam(dates));\n  };\n\n  // ======================== Values ========================\n  var _useInnerValue = useInnerValue(generateConfig, locale, formatList, false, order, defaultValue, value, onInternalCalendarChange, onInternalOk),\n    _useInnerValue2 = _slicedToArray(_useInnerValue, 5),\n    mergedValue = _useInnerValue2[0],\n    setInnerValue = _useInnerValue2[1],\n    getCalendarValue = _useInnerValue2[2],\n    triggerCalendarChange = _useInnerValue2[3],\n    triggerOk = _useInnerValue2[4];\n  var calendarValue = getCalendarValue();\n\n  // ======================== Active ========================\n  // In SinglePicker, we will always get `activeIndex` is 0.\n  var _useRangeActive = useRangeActive([disabled]),\n    _useRangeActive2 = _slicedToArray(_useRangeActive, 4),\n    focused = _useRangeActive2[0],\n    triggerFocus = _useRangeActive2[1],\n    lastOperation = _useRangeActive2[2],\n    activeIndex = _useRangeActive2[3];\n  var onSharedFocus = function onSharedFocus(event) {\n    triggerFocus(true);\n    onFocus === null || onFocus === void 0 || onFocus(event, {});\n  };\n  var onSharedBlur = function onSharedBlur(event) {\n    triggerFocus(false);\n    onBlur === null || onBlur === void 0 || onBlur(event, {});\n  };\n\n  // ========================= Mode =========================\n  var _useMergedState = useMergedState(picker, {\n      value: mode\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedMode = _useMergedState2[0],\n    setMode = _useMergedState2[1];\n\n  /** Extends from `mergedMode` to patch `datetime` mode */\n  var internalMode = mergedMode === 'date' && showTime ? 'datetime' : mergedMode;\n\n  // ======================= Show Now =======================\n  var mergedShowNow = useShowNow(picker, mergedMode, showNow, showToday);\n\n  // ======================== Value =========================\n  var onInternalChange = onChange && function (dates, dateStrings) {\n    onChange(pickerParam(dates), pickerParam(dateStrings));\n  };\n  var _useRangeValue = useRangeValue(_objectSpread(_objectSpread({}, filledProps), {}, {\n      onChange: onInternalChange\n    }), mergedValue, setInnerValue, getCalendarValue, triggerCalendarChange, [],\n    //disabled,\n    formatList, focused, mergedOpen, isInvalidateDate),\n    _useRangeValue2 = _slicedToArray(_useRangeValue, 2),\n    /** Trigger `onChange` directly without check `disabledDate` */\n    triggerSubmitChange = _useRangeValue2[1];\n\n  // ======================= Validate =======================\n  var _useFieldsInvalidate = useFieldsInvalidate(calendarValue, isInvalidateDate),\n    _useFieldsInvalidate2 = _slicedToArray(_useFieldsInvalidate, 2),\n    submitInvalidates = _useFieldsInvalidate2[0],\n    onSelectorInvalid = _useFieldsInvalidate2[1];\n  var submitInvalidate = React.useMemo(function () {\n    return submitInvalidates.some(function (invalidated) {\n      return invalidated;\n    });\n  }, [submitInvalidates]);\n\n  // ===================== Picker Value =====================\n  // Proxy to single pickerValue\n  var onInternalPickerValueChange = function onInternalPickerValueChange(dates, info) {\n    if (onPickerValueChange) {\n      var cleanInfo = _objectSpread(_objectSpread({}, info), {}, {\n        mode: info.mode[0]\n      });\n      delete cleanInfo.range;\n      onPickerValueChange(dates[0], cleanInfo);\n    }\n  };\n  var _useRangePickerValue = useRangePickerValue(generateConfig, locale, calendarValue, [mergedMode], mergedOpen, activeIndex, internalPicker, false,\n    // multiplePanel,\n    defaultPickerValue, pickerValue, toArray(showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue), onInternalPickerValueChange, minDate, maxDate),\n    _useRangePickerValue2 = _slicedToArray(_useRangePickerValue, 2),\n    currentPickerValue = _useRangePickerValue2[0],\n    setCurrentPickerValue = _useRangePickerValue2[1];\n\n  // >>> Mode need wait for `pickerValue`\n  var triggerModeChange = useEvent(function (nextPickerValue, nextMode, triggerEvent) {\n    setMode(nextMode);\n\n    // Compatible with `onPanelChange`\n    if (onPanelChange && triggerEvent !== false) {\n      var lastPickerValue = nextPickerValue || calendarValue[calendarValue.length - 1];\n      onPanelChange(lastPickerValue, nextMode);\n    }\n  });\n\n  // ======================== Submit ========================\n  /**\n   * Different with RangePicker, confirm should check `multiple` logic.\n   * This will never provide `date` instead.\n   */\n  var triggerConfirm = function triggerConfirm() {\n    triggerSubmitChange(getCalendarValue());\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Click =========================\n  var onSelectorClick = function onSelectorClick(event) {\n    if (!disabled && !selectorRef.current.nativeElement.contains(document.activeElement)) {\n      // Click to focus the enabled input\n      selectorRef.current.focus();\n    }\n    triggerOpen(true);\n    onClick === null || onClick === void 0 || onClick(event);\n  };\n  var onSelectorClear = function onSelectorClear() {\n    triggerSubmitChange(null);\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Hover =========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hoverSource = _React$useState2[0],\n    setHoverSource = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalHoverValue = _React$useState4[0],\n    setInternalHoverValue = _React$useState4[1];\n  var hoverValues = React.useMemo(function () {\n    var values = [internalHoverValue].concat(_toConsumableArray(calendarValue)).filter(function (date) {\n      return date;\n    });\n    return multiple ? values : values.slice(0, 1);\n  }, [calendarValue, internalHoverValue, multiple]);\n\n  // Selector values is different with RangePicker\n  // which can not use `hoverValue` directly\n  var selectorValues = React.useMemo(function () {\n    if (!multiple && internalHoverValue) {\n      return [internalHoverValue];\n    }\n    return calendarValue.filter(function (date) {\n      return date;\n    });\n  }, [calendarValue, internalHoverValue, multiple]);\n\n  // Clean up `internalHoverValues` when closed\n  React.useEffect(function () {\n    if (!mergedOpen) {\n      setInternalHoverValue(null);\n    }\n  }, [mergedOpen]);\n\n  // ========================================================\n  // ==                       Panels                       ==\n  // ========================================================\n  // ======================= Presets ========================\n  var presetList = usePresets(presets);\n  var onPresetHover = function onPresetHover(nextValue) {\n    setInternalHoverValue(nextValue);\n    setHoverSource('preset');\n  };\n\n  // TODO: handle this\n  var onPresetSubmit = function onPresetSubmit(nextValue) {\n    var nextCalendarValues = multiple ? toggleDates(getCalendarValue(), nextValue) : [nextValue];\n    var passed = triggerSubmitChange(nextCalendarValues);\n    if (passed && !multiple) {\n      triggerOpen(false, {\n        force: true\n      });\n    }\n  };\n  var onNow = function onNow(now) {\n    onPresetSubmit(now);\n  };\n\n  // ======================== Panel =========================\n  var onPanelHover = function onPanelHover(date) {\n    setInternalHoverValue(date);\n    setHoverSource('cell');\n  };\n\n  // >>> Focus\n  var onPanelFocus = function onPanelFocus(event) {\n    triggerOpen(true);\n    onSharedFocus(event);\n  };\n\n  // >>> Calendar\n  var onPanelSelect = function onPanelSelect(date) {\n    lastOperation('panel');\n\n    // Not change values if multiple and current panel is to match with picker\n    if (multiple && internalMode !== picker) {\n      return;\n    }\n    var nextValues = multiple ? toggleDates(getCalendarValue(), date) : [date];\n\n    // Only trigger calendar event but not update internal `calendarValue` state\n    triggerCalendarChange(nextValues);\n\n    // >>> Trigger next active if !needConfirm\n    // Fully logic check `useRangeValue` hook\n    if (!needConfirm && !complexPicker && internalPicker === internalMode) {\n      triggerConfirm();\n    }\n  };\n\n  // >>> Close\n  var onPopupClose = function onPopupClose() {\n    // Close popup\n    triggerOpen(false);\n  };\n\n  // >>> cellRender\n  var onInternalCellRender = useCellRender(cellRender, dateRender, monthCellRender);\n\n  // >>> invalid\n\n  var panelProps = React.useMemo(function () {\n    var domProps = pickAttrs(filledProps, false);\n    var restProps = omit(filledProps, [].concat(_toConsumableArray(Object.keys(domProps)), ['onChange', 'onCalendarChange', 'style', 'className', 'onPanelChange']));\n    return _objectSpread(_objectSpread({}, restProps), {}, {\n      multiple: filledProps.multiple\n    });\n  }, [filledProps]);\n\n  // >>> Render\n  var panel = /*#__PURE__*/React.createElement(Popup, _extends({}, panelProps, {\n    showNow: mergedShowNow,\n    showTime: showTime\n    // Disabled\n    ,\n    disabledDate: disabledDate\n    // Focus\n    ,\n    onFocus: onPanelFocus,\n    onBlur: onSharedBlur\n    // Mode\n    ,\n    picker: picker,\n    mode: mergedMode,\n    internalMode: internalMode,\n    onPanelChange: triggerModeChange\n    // Value\n    ,\n    format: maskFormat,\n    value: calendarValue,\n    isInvalid: isInvalidateDate,\n    onChange: null,\n    onSelect: onPanelSelect\n    // PickerValue\n    ,\n    pickerValue: currentPickerValue,\n    defaultOpenValue: showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue,\n    onPickerValueChange: setCurrentPickerValue\n    // Hover\n    ,\n    hoverValue: hoverValues,\n    onHover: onPanelHover\n    // Submit\n    ,\n    needConfirm: needConfirm,\n    onSubmit: triggerConfirm,\n    onOk: triggerOk\n    // Preset\n    ,\n    presets: presetList,\n    onPresetHover: onPresetHover,\n    onPresetSubmit: onPresetSubmit,\n    onNow: onNow\n    // Render\n    ,\n    cellRender: onInternalCellRender\n  }));\n\n  // ========================================================\n  // ==                      Selector                      ==\n  // ========================================================\n\n  // ======================== Change ========================\n  var onSelectorChange = function onSelectorChange(date) {\n    triggerCalendarChange(date);\n  };\n  var onSelectorInputChange = function onSelectorInputChange() {\n    lastOperation('input');\n  };\n\n  // ======================= Selector =======================\n  var onSelectorFocus = function onSelectorFocus(event) {\n    lastOperation('input');\n    triggerOpen(true, {\n      inherit: true\n    });\n\n    // setActiveIndex(index);\n\n    onSharedFocus(event);\n  };\n  var onSelectorBlur = function onSelectorBlur(event) {\n    triggerOpen(false);\n    onSharedBlur(event);\n  };\n  var onSelectorKeyDown = function onSelectorKeyDown(event, preventDefault) {\n    if (event.key === 'Tab') {\n      triggerConfirm();\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event, preventDefault);\n  };\n\n  // ======================= Context ========================\n  var context = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      locale: locale,\n      generateConfig: generateConfig,\n      button: components.button,\n      input: components.input\n    };\n  }, [prefixCls, locale, generateConfig, components.button, components.input]);\n\n  // ======================== Effect ========================\n  // >>> Mode\n  // Reset for every active\n  useLayoutEffect(function () {\n    if (mergedOpen && activeIndex !== undefined) {\n      // Legacy compatible. This effect update should not trigger `onPanelChange`\n      triggerModeChange(null, picker, false);\n    }\n  }, [mergedOpen, activeIndex, picker]);\n\n  // >>> For complex picker, we need check if need to focus next one\n  useLayoutEffect(function () {\n    var lastOp = lastOperation();\n\n    // Trade as confirm on field leave\n    if (!mergedOpen && lastOp === 'input') {\n      triggerOpen(false);\n      triggerConfirm();\n    }\n\n    // Submit with complex picker\n    if (!mergedOpen && complexPicker && !needConfirm && lastOp === 'panel') {\n      triggerConfirm();\n    }\n  }, [mergedOpen]);\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(PickerContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(PickerTrigger, _extends({}, pickTriggerProps(filledProps), {\n    popupElement: panel,\n    popupStyle: styles.popup,\n    popupClassName: classNames.popup\n    // Visible\n    ,\n    visible: mergedOpen,\n    onClose: onPopupClose\n  }), /*#__PURE__*/React.createElement(SingleSelector\n  // Shared\n  , _extends({}, filledProps, {\n    // Ref\n    ref: selectorRef\n    // Icon\n    ,\n    suffixIcon: suffixIcon,\n    removeIcon: removeIcon\n    // Active\n    ,\n    activeHelp: !!internalHoverValue,\n    allHelp: !!internalHoverValue && hoverSource === 'preset',\n    focused: focused,\n    onFocus: onSelectorFocus,\n    onBlur: onSelectorBlur,\n    onKeyDown: onSelectorKeyDown,\n    onSubmit: triggerConfirm\n    // Change\n    ,\n    value: selectorValues,\n    maskFormat: maskFormat,\n    onChange: onSelectorChange,\n    onInputChange: onSelectorInputChange,\n    internalPicker: internalPicker\n    // Format\n    ,\n    format: formatList,\n    inputReadOnly: inputReadOnly\n    // Disabled\n    ,\n    disabled: disabled\n    // Open\n    ,\n    open: mergedOpen,\n    onOpenChange: triggerOpen\n    // Click\n    ,\n    onClick: onSelectorClick,\n    onClear: onSelectorClear\n    // Invalid\n    ,\n    invalid: submitInvalidate,\n    onInvalid: function onInvalid(invalid) {\n      // Only `single` mode support type date.\n      // `multiple` mode can not typing.\n      onSelectorInvalid(invalid, 0);\n    }\n  }))));\n}\nvar RefPicker = /*#__PURE__*/React.forwardRef(Picker);\nif (process.env.NODE_ENV !== 'production') {\n  RefPicker.displayName = 'RefPicker';\n}\nexport default RefPicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,cAAc,QAAQ,SAAS;AAClD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,aAAa,IAAIC,aAAa,QAAQ,uBAAuB;AACpE,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,2BAA2B;;AAEtD;;AAEA;;AAEA,SAASC,MAAMA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC1B;EACA,IAAIC,eAAe,GAAGd,cAAc,CAACY,KAAK,CAAC;IACzCG,gBAAgB,GAAG7B,cAAc,CAAC4B,eAAe,EAAE,CAAC,CAAC;IACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACpCG,aAAa,GAAGH,gBAAgB,CAAC,CAAC,CAAC;IACnCI,UAAU,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;IAChCK,UAAU,GAAGL,gBAAgB,CAAC,CAAC,CAAC;IAChCM,gBAAgB,GAAGN,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIO,IAAI,GAAGN,WAAW;IACpBO,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,YAAY,GAAGL,IAAI,CAACK,YAAY;IAChCC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,WAAW,GAAGP,IAAI,CAACO,WAAW;IAC9BC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,SAAS,GAAGT,IAAI,CAACS,SAAS;IAC1BC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;IACxBC,YAAY,GAAGX,IAAI,CAACW,YAAY;IAChCC,OAAO,GAAGZ,IAAI,CAACY,OAAO;IACtBC,OAAO,GAAGb,IAAI,CAACa,OAAO;IACtBC,WAAW,GAAGd,IAAI,CAACc,WAAW;IAC9BC,IAAI,GAAGf,IAAI,CAACe,IAAI;IAChBC,YAAY,GAAGhB,IAAI,CAACgB,YAAY;IAChCC,MAAM,GAAGjB,IAAI,CAACiB,MAAM;IACpBC,cAAc,GAAGlB,IAAI,CAACkB,cAAc;IACpCC,MAAM,GAAGnB,IAAI,CAACmB,MAAM;IACpBC,OAAO,GAAGpB,IAAI,CAACoB,OAAO;IACtBC,SAAS,GAAGrB,IAAI,CAACqB,SAAS;IAC1BC,QAAQ,GAAGtB,IAAI,CAACsB,QAAQ;IACxBC,IAAI,GAAGvB,IAAI,CAACuB,IAAI;IAChBC,aAAa,GAAGxB,IAAI,CAACwB,aAAa;IAClCC,gBAAgB,GAAGzB,IAAI,CAACyB,gBAAgB;IACxCC,IAAI,GAAG1B,IAAI,CAAC0B,IAAI;IAChBC,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ;IACxBC,kBAAkB,GAAG5B,IAAI,CAAC4B,kBAAkB;IAC5CC,WAAW,GAAG7B,IAAI,CAAC6B,WAAW;IAC9BC,mBAAmB,GAAG9B,IAAI,CAAC8B,mBAAmB;IAC9CC,aAAa,GAAG/B,IAAI,CAAC+B,aAAa;IAClCC,UAAU,GAAGhC,IAAI,CAACgC,UAAU;IAC5BC,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5BC,OAAO,GAAGlC,IAAI,CAACkC,OAAO;IACtBC,MAAM,GAAGnC,IAAI,CAACmC,MAAM;IACpBC,OAAO,GAAGpC,IAAI,CAACoC,OAAO;IACtBC,UAAU,GAAGrC,IAAI,CAACqC,UAAU;IAC5BC,UAAU,GAAGtC,IAAI,CAACsC,UAAU;IAC5BC,UAAU,GAAGvC,IAAI,CAACuC,UAAU;IAC5BC,eAAe,GAAGxC,IAAI,CAACwC,eAAe;IACtCC,OAAO,GAAGzC,IAAI,CAACyC,OAAO;;EAExB;EACA,IAAIC,WAAW,GAAG9D,YAAY,CAACW,GAAG,CAAC;;EAEnC;EACA,SAASoD,WAAWA,CAACC,MAAM,EAAE;IAC3B,IAAIA,MAAM,KAAK,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,OAAOjB,QAAQ,GAAGiB,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EACtC;EACA,IAAIC,WAAW,GAAG1E,cAAc,CAAC+C,cAAc,EAAED,MAAM,EAAEtB,cAAc,CAAC;;EAExE;EACA,IAAImD,QAAQ,GAAGnE,OAAO,CAACoC,IAAI,EAAED,WAAW,EAAE,CAACJ,QAAQ,CAAC,EAAEM,YAAY,CAAC;IACjE+B,SAAS,GAAGnF,cAAc,CAACkF,QAAQ,EAAE,CAAC,CAAC;IACvCE,UAAU,GAAGD,SAAS,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,SAAS,CAAC,CAAC,CAAC;;EAE5B;EACA,IAAIG,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,WAAW,EAAEC,IAAI,EAAE;IACzF,IAAI5B,gBAAgB,EAAE;MACpB,IAAI6B,YAAY,GAAG3F,aAAa,CAAC,CAAC,CAAC,EAAE0F,IAAI,CAAC;MAC1C,OAAOC,YAAY,CAACC,KAAK;MACzB9B,gBAAgB,CAACkB,WAAW,CAACQ,KAAK,CAAC,EAAER,WAAW,CAACS,WAAW,CAAC,EAAEE,YAAY,CAAC;IAC9E;EACF,CAAC;EACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACL,KAAK,EAAE;IAC9CzB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,CAACiB,WAAW,CAACQ,KAAK,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,IAAIM,cAAc,GAAGxE,aAAa,CAACiC,cAAc,EAAED,MAAM,EAAEpB,UAAU,EAAE,KAAK,EAAEO,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAE4C,wBAAwB,EAAEM,YAAY,CAAC;IAC/IE,eAAe,GAAG9F,cAAc,CAAC6F,cAAc,EAAE,CAAC,CAAC;IACnDE,WAAW,GAAGD,eAAe,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;IAClCG,gBAAgB,GAAGH,eAAe,CAAC,CAAC,CAAC;IACrCI,qBAAqB,GAAGJ,eAAe,CAAC,CAAC,CAAC;IAC1CK,SAAS,GAAGL,eAAe,CAAC,CAAC,CAAC;EAChC,IAAIM,aAAa,GAAGH,gBAAgB,CAAC,CAAC;;EAEtC;EACA;EACA,IAAII,eAAe,GAAGnF,cAAc,CAAC,CAAC4B,QAAQ,CAAC,CAAC;IAC9CwD,gBAAgB,GAAGtG,cAAc,CAACqG,eAAe,EAAE,CAAC,CAAC;IACrDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAClCG,aAAa,GAAGH,gBAAgB,CAAC,CAAC,CAAC;IACnCI,WAAW,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIK,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChDJ,YAAY,CAAC,IAAI,CAAC;IAClBlC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACsC,KAAK,EAAE,CAAC,CAAC,CAAC;EAC9D,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACD,KAAK,EAAE;IAC9CJ,YAAY,CAAC,KAAK,CAAC;IACnBjC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACqC,KAAK,EAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,IAAIE,eAAe,GAAG5G,cAAc,CAACqD,MAAM,EAAE;MACzCb,KAAK,EAAEiB;IACT,CAAC,CAAC;IACFoD,gBAAgB,GAAG/G,cAAc,CAAC8G,eAAe,EAAE,CAAC,CAAC;IACrDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAE/B;EACA,IAAIG,YAAY,GAAGF,UAAU,KAAK,MAAM,IAAItD,QAAQ,GAAG,UAAU,GAAGsD,UAAU;;EAE9E;EACA,IAAIG,aAAa,GAAG7F,UAAU,CAACiC,MAAM,EAAEyD,UAAU,EAAExD,OAAO,EAAEC,SAAS,CAAC;;EAEtE;EACA,IAAI2D,gBAAgB,GAAGxE,QAAQ,IAAI,UAAU2C,KAAK,EAAEC,WAAW,EAAE;IAC/D5C,QAAQ,CAACmC,WAAW,CAACQ,KAAK,CAAC,EAAER,WAAW,CAACS,WAAW,CAAC,CAAC;EACxD,CAAC;EACD,IAAI6B,cAAc,GAAGjG,aAAa,CAACrB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MACjFc,QAAQ,EAAEwE;IACZ,CAAC,CAAC,EAAErB,WAAW,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAE,EAAE;IAC3E;IACAjE,UAAU,EAAEsE,OAAO,EAAEnB,UAAU,EAAEjD,gBAAgB,CAAC;IAClDmF,eAAe,GAAGtH,cAAc,CAACqH,cAAc,EAAE,CAAC,CAAC;IACnD;IACAE,mBAAmB,GAAGD,eAAe,CAAC,CAAC,CAAC;;EAE1C;EACA,IAAIE,oBAAoB,GAAG3G,mBAAmB,CAACuF,aAAa,EAAEjE,gBAAgB,CAAC;IAC7EsF,qBAAqB,GAAGzH,cAAc,CAACwH,oBAAoB,EAAE,CAAC,CAAC;IAC/DE,iBAAiB,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IAC5CE,iBAAiB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;EAC9C,IAAIG,gBAAgB,GAAGtH,KAAK,CAACuH,OAAO,CAAC,YAAY;IAC/C,OAAOH,iBAAiB,CAACI,IAAI,CAAC,UAAUC,WAAW,EAAE;MACnD,OAAOA,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;;EAEvB;EACA;EACA,IAAIM,2BAA2B,GAAG,SAASA,2BAA2BA,CAACzC,KAAK,EAAEE,IAAI,EAAE;IAClF,IAAIvB,mBAAmB,EAAE;MACvB,IAAI+D,SAAS,GAAGlI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0F,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACzD9B,IAAI,EAAE8B,IAAI,CAAC9B,IAAI,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,OAAOsE,SAAS,CAACtC,KAAK;MACtBzB,mBAAmB,CAACqB,KAAK,CAAC,CAAC,CAAC,EAAE0C,SAAS,CAAC;IAC1C;EACF,CAAC;EACD,IAAIC,oBAAoB,GAAG/G,mBAAmB,CAACmC,cAAc,EAAED,MAAM,EAAE+C,aAAa,EAAE,CAACY,UAAU,CAAC,EAAE5B,UAAU,EAAEsB,WAAW,EAAE3E,cAAc,EAAE,KAAK;IAChJ;IACAiC,kBAAkB,EAAEC,WAAW,EAAEvD,OAAO,CAACgD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyE,gBAAgB,CAAC,EAAEH,2BAA2B,EAAEhF,OAAO,EAAEC,OAAO,CAAC;IACvKmF,qBAAqB,GAAGpI,cAAc,CAACkI,oBAAoB,EAAE,CAAC,CAAC;IAC/DG,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IAC7CE,qBAAqB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;;EAElD;EACA,IAAIG,iBAAiB,GAAGtI,QAAQ,CAAC,UAAUuI,eAAe,EAAEC,QAAQ,EAAEC,YAAY,EAAE;IAClFzB,OAAO,CAACwB,QAAQ,CAAC;;IAEjB;IACA,IAAI7E,aAAa,IAAI8E,YAAY,KAAK,KAAK,EAAE;MAC3C,IAAIC,eAAe,GAAGH,eAAe,IAAIpC,aAAa,CAACA,aAAa,CAACwC,MAAM,GAAG,CAAC,CAAC;MAChFhF,aAAa,CAAC+E,eAAe,EAAEF,QAAQ,CAAC;IAC1C;EACF,CAAC,CAAC;;EAEF;EACA;AACF;AACA;AACA;EACE,IAAII,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CtB,mBAAmB,CAACtB,gBAAgB,CAAC,CAAC,CAAC;IACvCZ,WAAW,CAAC,KAAK,EAAE;MACjByD,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACnC,KAAK,EAAE;IACpD,IAAI,CAAC9D,QAAQ,IAAI,CAACgC,WAAW,CAACkE,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;MACpF;MACAtE,WAAW,CAACkE,OAAO,CAACK,KAAK,CAAC,CAAC;IAC7B;IACAhE,WAAW,CAAC,IAAI,CAAC;IACjBR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAC+B,KAAK,CAAC;EAC1D,CAAC;EACD,IAAI0C,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C/B,mBAAmB,CAAC,IAAI,CAAC;IACzBlC,WAAW,CAAC,KAAK,EAAE;MACjByD,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIS,eAAe,GAAGjJ,KAAK,CAACkJ,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGzJ,cAAc,CAACuJ,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAGtJ,KAAK,CAACkJ,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAG7J,cAAc,CAAC4J,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,WAAW,GAAG1J,KAAK,CAACuH,OAAO,CAAC,YAAY;IAC1C,IAAI7C,MAAM,GAAG,CAAC8E,kBAAkB,CAAC,CAACG,MAAM,CAACnK,kBAAkB,CAACsG,aAAa,CAAC,CAAC,CAAC8D,MAAM,CAAC,UAAUC,IAAI,EAAE;MACjG,OAAOA,IAAI;IACb,CAAC,CAAC;IACF,OAAOpG,QAAQ,GAAGiB,MAAM,GAAGA,MAAM,CAACoF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,CAAC,EAAE,CAAChE,aAAa,EAAE0D,kBAAkB,EAAE/F,QAAQ,CAAC,CAAC;;EAEjD;EACA;EACA,IAAIsG,cAAc,GAAG/J,KAAK,CAACuH,OAAO,CAAC,YAAY;IAC7C,IAAI,CAAC9D,QAAQ,IAAI+F,kBAAkB,EAAE;MACnC,OAAO,CAACA,kBAAkB,CAAC;IAC7B;IACA,OAAO1D,aAAa,CAAC8D,MAAM,CAAC,UAAUC,IAAI,EAAE;MAC1C,OAAOA,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/D,aAAa,EAAE0D,kBAAkB,EAAE/F,QAAQ,CAAC,CAAC;;EAEjD;EACAzD,KAAK,CAACgK,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAClF,UAAU,EAAE;MACf2E,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC3E,UAAU,CAAC,CAAC;;EAEhB;EACA;EACA;EACA;EACA,IAAImF,UAAU,GAAGtJ,UAAU,CAACuD,OAAO,CAAC;EACpC,IAAIgG,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAE;IACpDV,qBAAqB,CAACU,SAAS,CAAC;IAChCd,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;;EAED;EACA,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACD,SAAS,EAAE;IACtD,IAAIE,kBAAkB,GAAG5G,QAAQ,GAAGkB,WAAW,CAACgB,gBAAgB,CAAC,CAAC,EAAEwE,SAAS,CAAC,GAAG,CAACA,SAAS,CAAC;IAC5F,IAAIG,MAAM,GAAGrD,mBAAmB,CAACoD,kBAAkB,CAAC;IACpD,IAAIC,MAAM,IAAI,CAAC7G,QAAQ,EAAE;MACvBsB,WAAW,CAAC,KAAK,EAAE;QACjByD,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAI+B,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;IAC9BJ,cAAc,CAACI,GAAG,CAAC;EACrB,CAAC;;EAED;EACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACZ,IAAI,EAAE;IAC7CJ,qBAAqB,CAACI,IAAI,CAAC;IAC3BR,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;;EAED;EACA,IAAIqB,YAAY,GAAG,SAASA,YAAYA,CAACpE,KAAK,EAAE;IAC9CvB,WAAW,CAAC,IAAI,CAAC;IACjBsB,aAAa,CAACC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,IAAIqE,aAAa,GAAG,SAASA,aAAaA,CAACd,IAAI,EAAE;IAC/C1D,aAAa,CAAC,OAAO,CAAC;;IAEtB;IACA,IAAI1C,QAAQ,IAAImD,YAAY,KAAK3D,MAAM,EAAE;MACvC;IACF;IACA,IAAI2H,UAAU,GAAGnH,QAAQ,GAAGkB,WAAW,CAACgB,gBAAgB,CAAC,CAAC,EAAEkE,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC;;IAE1E;IACAjE,qBAAqB,CAACgF,UAAU,CAAC;;IAEjC;IACA;IACA,IAAI,CAACvI,WAAW,IAAI,CAACX,aAAa,IAAID,cAAc,KAAKmF,YAAY,EAAE;MACrE2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,IAAIsC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC;IACA9F,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI+F,oBAAoB,GAAGxK,aAAa,CAAC8D,UAAU,EAAEC,UAAU,EAAEC,eAAe,CAAC;;EAEjF;;EAEA,IAAIyG,UAAU,GAAG/K,KAAK,CAACuH,OAAO,CAAC,YAAY;IACzC,IAAIyD,QAAQ,GAAGjL,SAAS,CAACyB,WAAW,EAAE,KAAK,CAAC;IAC5C,IAAIyJ,SAAS,GAAGnL,IAAI,CAAC0B,WAAW,EAAE,EAAE,CAACmI,MAAM,CAACnK,kBAAkB,CAAC0L,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,kBAAkB,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC;IAChK,OAAOvL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwL,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACrDxH,QAAQ,EAAEjC,WAAW,CAACiC;IACxB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAI4J,KAAK,GAAG,aAAapL,KAAK,CAACqL,aAAa,CAACpK,KAAK,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEwL,UAAU,EAAE;IAC3E7H,OAAO,EAAE2D,aAAa;IACtBzD,QAAQ,EAAEA;IACV;IAAA;;IAEAX,YAAY,EAAEA;IACd;IAAA;;IAEAuB,OAAO,EAAE0G,YAAY;IACrBzG,MAAM,EAAEsC;IACR;IAAA;;IAEAtD,MAAM,EAAEA,MAAM;IACdI,IAAI,EAAEqD,UAAU;IAChBE,YAAY,EAAEA,YAAY;IAC1BtD,aAAa,EAAE2E;IACf;IAAA;;IAEAqD,MAAM,EAAE1J,UAAU;IAClBQ,KAAK,EAAE0D,aAAa;IACpByF,SAAS,EAAE1J,gBAAgB;IAC3BS,QAAQ,EAAE,IAAI;IACdkJ,QAAQ,EAAEb;IACV;IAAA;;IAEAhH,WAAW,EAAEoE,kBAAkB;IAC/BF,gBAAgB,EAAEzE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyE,gBAAgB;IAC/FjE,mBAAmB,EAAEoE;IACrB;IAAA;;IAEAyD,UAAU,EAAE/B,WAAW;IACvBgC,OAAO,EAAEjB;IACT;IAAA;;IAEApI,WAAW,EAAEA,WAAW;IACxBsJ,QAAQ,EAAEpD,cAAc;IACxB/E,IAAI,EAAEqC;IACN;IAAA;;IAEA3B,OAAO,EAAE+F,UAAU;IACnBC,aAAa,EAAEA,aAAa;IAC5BE,cAAc,EAAEA,cAAc;IAC9BG,KAAK,EAAEA;IACP;IAAA;;IAEAnG,UAAU,EAAE0G;EACd,CAAC,CAAC,CAAC;;EAEH;EACA;EACA;;EAEA;EACA,IAAIc,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC/B,IAAI,EAAE;IACrDjE,qBAAqB,CAACiE,IAAI,CAAC;EAC7B,CAAC;EACD,IAAIgC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D1F,aAAa,CAAC,OAAO,CAAC;EACxB,CAAC;;EAED;EACA,IAAI2F,eAAe,GAAG,SAASA,eAAeA,CAACxF,KAAK,EAAE;IACpDH,aAAa,CAAC,OAAO,CAAC;IACtBpB,WAAW,CAAC,IAAI,EAAE;MAChBgH,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;;IAEA1F,aAAa,CAACC,KAAK,CAAC;EACtB,CAAC;EACD,IAAI0F,cAAc,GAAG,SAASA,cAAcA,CAAC1F,KAAK,EAAE;IAClDvB,WAAW,CAAC,KAAK,CAAC;IAClBwB,YAAY,CAACD,KAAK,CAAC;EACrB,CAAC;EACD,IAAI2F,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC3F,KAAK,EAAE4F,cAAc,EAAE;IACxE,IAAI5F,KAAK,CAAC6F,GAAG,KAAK,KAAK,EAAE;MACvB5D,cAAc,CAAC,CAAC;IAClB;IACAhG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC+D,KAAK,EAAE4F,cAAc,CAAC;EAChF,CAAC;;EAED;EACA,IAAIE,OAAO,GAAGpM,KAAK,CAACuH,OAAO,CAAC,YAAY;IACtC,OAAO;MACLxF,SAAS,EAAEA,SAAS;MACpBgB,MAAM,EAAEA,MAAM;MACdC,cAAc,EAAEA,cAAc;MAC9BqJ,MAAM,EAAElI,UAAU,CAACkI,MAAM;MACzBC,KAAK,EAAEnI,UAAU,CAACmI;IACpB,CAAC;EACH,CAAC,EAAE,CAACvK,SAAS,EAAEgB,MAAM,EAAEC,cAAc,EAAEmB,UAAU,CAACkI,MAAM,EAAElI,UAAU,CAACmI,KAAK,CAAC,CAAC;;EAE5E;EACA;EACA;EACAzM,eAAe,CAAC,YAAY;IAC1B,IAAIiF,UAAU,IAAIsB,WAAW,KAAKmG,SAAS,EAAE;MAC3C;MACAtE,iBAAiB,CAAC,IAAI,EAAEhF,MAAM,EAAE,KAAK,CAAC;IACxC;EACF,CAAC,EAAE,CAAC6B,UAAU,EAAEsB,WAAW,EAAEnD,MAAM,CAAC,CAAC;;EAErC;EACApD,eAAe,CAAC,YAAY;IAC1B,IAAI2M,MAAM,GAAGrG,aAAa,CAAC,CAAC;;IAE5B;IACA,IAAI,CAACrB,UAAU,IAAI0H,MAAM,KAAK,OAAO,EAAE;MACrCzH,WAAW,CAAC,KAAK,CAAC;MAClBwD,cAAc,CAAC,CAAC;IAClB;;IAEA;IACA,IAAI,CAACzD,UAAU,IAAIpD,aAAa,IAAI,CAACW,WAAW,IAAImK,MAAM,KAAK,OAAO,EAAE;MACtEjE,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzD,UAAU,CAAC,CAAC;;EAEhB;EACA,OAAO,aAAa9E,KAAK,CAACqL,aAAa,CAAChL,aAAa,CAACoM,QAAQ,EAAE;IAC9DrK,KAAK,EAAEgK;EACT,CAAC,EAAE,aAAapM,KAAK,CAACqL,aAAa,CAACnL,aAAa,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEY,gBAAgB,CAACqB,WAAW,CAAC,EAAE;IAC7FkL,YAAY,EAAEtB,KAAK;IACnBuB,UAAU,EAAE3K,MAAM,CAAC4K,KAAK;IACxBC,cAAc,EAAE5K,UAAU,CAAC2K;IAC3B;IAAA;;IAEAE,OAAO,EAAEhI,UAAU;IACnBiI,OAAO,EAAElC;EACX,CAAC,CAAC,EAAE,aAAa7K,KAAK,CAACqL,aAAa,CAACnK;EACrC;EAAA,EACE3B,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAE;IAC1B;IACAH,GAAG,EAAEmD;IACL;IAAA;;IAEAV,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA;IACZ;IAAA;;IAEAiJ,UAAU,EAAE,CAAC,CAACxD,kBAAkB;IAChCyD,OAAO,EAAE,CAAC,CAACzD,kBAAkB,IAAIJ,WAAW,KAAK,QAAQ;IACzDnD,OAAO,EAAEA,OAAO;IAChBjC,OAAO,EAAE8H,eAAe;IACxB7H,MAAM,EAAE+H,cAAc;IACtBzJ,SAAS,EAAE0J,iBAAiB;IAC5BN,QAAQ,EAAEpD;IACV;IAAA;;IAEAnG,KAAK,EAAE2H,cAAc;IACrBnI,UAAU,EAAEA,UAAU;IACtBU,QAAQ,EAAEsJ,gBAAgB;IAC1BsB,aAAa,EAAErB,qBAAqB;IACpCpK,cAAc,EAAEA;IAChB;IAAA;;IAEA6J,MAAM,EAAE3J,UAAU;IAClBkC,aAAa,EAAEA;IACf;IAAA;;IAEArB,QAAQ,EAAEA;IACV;IAAA;;IAEAK,IAAI,EAAEiC,UAAU;IAChBhC,YAAY,EAAEiC;IACd;IAAA;;IAEAR,OAAO,EAAEkE,eAAe;IACxB0E,OAAO,EAAEnE;IACT;IAAA;;IAEAoE,OAAO,EAAE9F,gBAAgB;IACzB+F,SAAS,EAAE,SAASA,SAASA,CAACD,OAAO,EAAE;MACrC;MACA;MACA/F,iBAAiB,CAAC+F,OAAO,EAAE,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACA,IAAIE,SAAS,GAAG,aAAatN,KAAK,CAACuN,UAAU,CAACpM,MAAM,CAAC;AACrD,IAAIqM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,SAAS,CAACK,WAAW,GAAG,WAAW;AACrC;AACA,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
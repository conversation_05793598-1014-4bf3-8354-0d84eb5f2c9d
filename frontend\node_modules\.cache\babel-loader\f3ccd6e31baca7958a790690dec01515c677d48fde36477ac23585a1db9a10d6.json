{"ast": null, "code": "/**\n * Proxy object if environment supported\n */\nexport default function proxyObject(obj, extendProps) {\n  if (typeof Proxy !== 'undefined' && obj) {\n    return new Proxy(obj, {\n      get: function get(target, prop) {\n        if (extendProps[prop]) {\n          return extendProps[prop];\n        }\n\n        // Proxy origin property\n        var originProp = target[prop];\n        return typeof originProp === 'function' ? originProp.bind(target) : originProp;\n      }\n    });\n  }\n  return obj;\n}", "map": {"version": 3, "names": ["proxyObject", "obj", "extendProps", "Proxy", "get", "target", "prop", "originProp", "bind"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-util@5.44.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-util/es/proxyObject.js"], "sourcesContent": ["/**\n * Proxy object if environment supported\n */\nexport default function proxyObject(obj, extendProps) {\n  if (typeof Proxy !== 'undefined' && obj) {\n    return new Proxy(obj, {\n      get: function get(target, prop) {\n        if (extendProps[prop]) {\n          return extendProps[prop];\n        }\n\n        // Proxy origin property\n        var originProp = target[prop];\n        return typeof originProp === 'function' ? originProp.bind(target) : originProp;\n      }\n    });\n  }\n  return obj;\n}"], "mappings": "AAAA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,GAAG,EAAEC,WAAW,EAAE;EACpD,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAIF,GAAG,EAAE;IACvC,OAAO,IAAIE,KAAK,CAACF,GAAG,EAAE;MACpBG,GAAG,EAAE,SAASA,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAE;QAC9B,IAAIJ,WAAW,CAACI,IAAI,CAAC,EAAE;UACrB,OAAOJ,WAAW,CAACI,IAAI,CAAC;QAC1B;;QAEA;QACA,IAAIC,UAAU,GAAGF,MAAM,CAACC,IAAI,CAAC;QAC7B,OAAO,OAAOC,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACC,IAAI,CAACH,MAAM,CAAC,GAAGE,UAAU;MAChF;IACF,CAAC,CAAC;EACJ;EACA,OAAON,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
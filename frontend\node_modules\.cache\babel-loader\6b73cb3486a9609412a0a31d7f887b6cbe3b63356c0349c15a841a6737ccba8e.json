{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useDelayState from \"./useDelayState\";\n\n/**\n * Control the open state.\n * Will not close if activeElement is on the popup.\n */\nexport default function useOpen(open, defaultOpen) {\n  var disabledList = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var onOpenChange = arguments.length > 3 ? arguments[3] : undefined;\n  var mergedOpen = disabledList.every(function (disabled) {\n    return disabled;\n  }) ? false : open;\n\n  // Delay for handle the open state, in case fast shift from `open` -> `close` -> `open`\n  // const [rafOpen, setRafOpen] = useLockState(open, defaultOpen || false, onOpenChange);\n  var _useDelayState = useDelayState(mergedOpen, defaultOpen || false, onOpenChange),\n    _useDelayState2 = _slicedToArray(_useDelayState, 2),\n    rafOpen = _useDelayState2[0],\n    setRafOpen = _useDelayState2[1];\n  function setOpen(next) {\n    var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!config.inherit || rafOpen) {\n      setRafOpen(next, config.force);\n    }\n  }\n  return [rafOpen, setOpen];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useDelayState", "useOpen", "open", "defaultOpen", "disabledList", "arguments", "length", "undefined", "onOpenChange", "mergedOpen", "every", "disabled", "_useDelayState", "_useDelayState2", "rafOpen", "setRafOpen", "<PERSON><PERSON><PERSON>", "next", "config", "inherit", "force"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useOpen.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useDelayState from \"./useDelayState\";\n\n/**\n * Control the open state.\n * Will not close if activeElement is on the popup.\n */\nexport default function useOpen(open, defaultOpen) {\n  var disabledList = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var onOpenChange = arguments.length > 3 ? arguments[3] : undefined;\n  var mergedOpen = disabledList.every(function (disabled) {\n    return disabled;\n  }) ? false : open;\n\n  // Delay for handle the open state, in case fast shift from `open` -> `close` -> `open`\n  // const [rafOpen, setRafOpen] = useLockState(open, defaultOpen || false, onOpenChange);\n  var _useDelayState = useDelayState(mergedOpen, defaultOpen || false, onOpenChange),\n    _useDelayState2 = _slicedToArray(_useDelayState, 2),\n    rafOpen = _useDelayState2[0],\n    setRafOpen = _useDelayState2[1];\n  function setOpen(next) {\n    var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!config.inherit || rafOpen) {\n      setRafOpen(next, config.force);\n    }\n  }\n  return [rafOpen, setOpen];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACjD,IAAIC,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACzF,IAAIG,YAAY,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAClE,IAAIE,UAAU,GAAGL,YAAY,CAACM,KAAK,CAAC,UAAUC,QAAQ,EAAE;IACtD,OAAOA,QAAQ;EACjB,CAAC,CAAC,GAAG,KAAK,GAAGT,IAAI;;EAEjB;EACA;EACA,IAAIU,cAAc,GAAGZ,aAAa,CAACS,UAAU,EAAEN,WAAW,IAAI,KAAK,EAAEK,YAAY,CAAC;IAChFK,eAAe,GAAGd,cAAc,CAACa,cAAc,EAAE,CAAC,CAAC;IACnDE,OAAO,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC5BE,UAAU,GAAGF,eAAe,CAAC,CAAC,CAAC;EACjC,SAASG,OAAOA,CAACC,IAAI,EAAE;IACrB,IAAIC,MAAM,GAAGb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,IAAI,CAACa,MAAM,CAACC,OAAO,IAAIL,OAAO,EAAE;MAC9BC,UAAU,CAACE,IAAI,EAAEC,MAAM,CAACE,KAAK,CAAC;IAChC;EACF;EACA,OAAO,CAACN,OAAO,EAAEE,OAAO,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
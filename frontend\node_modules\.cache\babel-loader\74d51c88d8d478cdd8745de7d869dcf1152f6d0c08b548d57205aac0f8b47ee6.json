{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useEffect } from 'react';\nimport zhCN from \"./locale/zh_CN\";\nimport Options from \"./Options\";\nimport Pager from \"./Pager\";\nvar defaultItemRender = function defaultItemRender(page, type, element) {\n  return element;\n};\nfunction noop() {}\nfunction isInteger(v) {\n  var value = Number(v);\n  return typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value;\n}\nfunction calculatePage(p, pageSize, total) {\n  var _pageSize = typeof p === 'undefined' ? pageSize : p;\n  return Math.floor((total - 1) / _pageSize) + 1;\n}\nvar Pagination = function Pagination(props) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-pagination' : _props$prefixCls,\n    _props$selectPrefixCl = props.selectPrefixCls,\n    selectPrefixCls = _props$selectPrefixCl === void 0 ? 'rc-select' : _props$selectPrefixCl,\n    className = props.className,\n    currentProp = props.current,\n    _props$defaultCurrent = props.defaultCurrent,\n    defaultCurrent = _props$defaultCurrent === void 0 ? 1 : _props$defaultCurrent,\n    _props$total = props.total,\n    total = _props$total === void 0 ? 0 : _props$total,\n    pageSizeProp = props.pageSize,\n    _props$defaultPageSiz = props.defaultPageSize,\n    defaultPageSize = _props$defaultPageSiz === void 0 ? 10 : _props$defaultPageSiz,\n    _props$onChange = props.onChange,\n    onChange = _props$onChange === void 0 ? noop : _props$onChange,\n    hideOnSinglePage = props.hideOnSinglePage,\n    align = props.align,\n    _props$showPrevNextJu = props.showPrevNextJumpers,\n    showPrevNextJumpers = _props$showPrevNextJu === void 0 ? true : _props$showPrevNextJu,\n    showQuickJumper = props.showQuickJumper,\n    showLessItems = props.showLessItems,\n    _props$showTitle = props.showTitle,\n    showTitle = _props$showTitle === void 0 ? true : _props$showTitle,\n    _props$onShowSizeChan = props.onShowSizeChange,\n    onShowSizeChange = _props$onShowSizeChan === void 0 ? noop : _props$onShowSizeChan,\n    _props$locale = props.locale,\n    locale = _props$locale === void 0 ? zhCN : _props$locale,\n    style = props.style,\n    _props$totalBoundaryS = props.totalBoundaryShowSizeChanger,\n    totalBoundaryShowSizeChanger = _props$totalBoundaryS === void 0 ? 50 : _props$totalBoundaryS,\n    disabled = props.disabled,\n    simple = props.simple,\n    showTotal = props.showTotal,\n    _props$showSizeChange = props.showSizeChanger,\n    showSizeChanger = _props$showSizeChange === void 0 ? total > totalBoundaryShowSizeChanger : _props$showSizeChange,\n    sizeChangerRender = props.sizeChangerRender,\n    pageSizeOptions = props.pageSizeOptions,\n    _props$itemRender = props.itemRender,\n    itemRender = _props$itemRender === void 0 ? defaultItemRender : _props$itemRender,\n    jumpPrevIcon = props.jumpPrevIcon,\n    jumpNextIcon = props.jumpNextIcon,\n    prevIcon = props.prevIcon,\n    nextIcon = props.nextIcon;\n  var paginationRef = React.useRef(null);\n  var _useMergedState = useMergedState(10, {\n      value: pageSizeProp,\n      defaultValue: defaultPageSize\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    pageSize = _useMergedState2[0],\n    setPageSize = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(1, {\n      value: currentProp,\n      defaultValue: defaultCurrent,\n      postState: function postState(c) {\n        return Math.max(1, Math.min(c, calculatePage(undefined, pageSize, total)));\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    current = _useMergedState4[0],\n    setCurrent = _useMergedState4[1];\n  var _React$useState = React.useState(current),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    internalInputVal = _React$useState2[0],\n    setInternalInputVal = _React$useState2[1];\n  useEffect(function () {\n    setInternalInputVal(current);\n  }, [current]);\n  var hasOnChange = onChange !== noop;\n  var hasCurrent = 'current' in props;\n  if (process.env.NODE_ENV !== 'production') {\n    warning(hasCurrent ? hasOnChange : true, 'You provided a `current` prop to a Pagination component without an `onChange` handler. This will render a read-only component.');\n  }\n  var jumpPrevPage = Math.max(1, current - (showLessItems ? 3 : 5));\n  var jumpNextPage = Math.min(calculatePage(undefined, pageSize, total), current + (showLessItems ? 3 : 5));\n  function getItemIcon(icon, label) {\n    var iconNode = icon || /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": label,\n      className: \"\".concat(prefixCls, \"-item-link\")\n    });\n    if (typeof icon === 'function') {\n      iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n    }\n    return iconNode;\n  }\n  function getValidValue(e) {\n    var inputValue = e.target.value;\n    var allPages = calculatePage(undefined, pageSize, total);\n    var value;\n    if (inputValue === '') {\n      value = inputValue;\n    } else if (Number.isNaN(Number(inputValue))) {\n      value = internalInputVal;\n    } else if (inputValue >= allPages) {\n      value = allPages;\n    } else {\n      value = Number(inputValue);\n    }\n    return value;\n  }\n  function isValid(page) {\n    return isInteger(page) && page !== current && isInteger(total) && total > 0;\n  }\n  var shouldDisplayQuickJumper = total > pageSize ? showQuickJumper : false;\n\n  /**\n   * prevent \"up arrow\" key reseting cursor position within textbox\n   * @see https://stackoverflow.com/a/1081114\n   */\n  function handleKeyDown(event) {\n    if (event.keyCode === KeyCode.UP || event.keyCode === KeyCode.DOWN) {\n      event.preventDefault();\n    }\n  }\n  function handleKeyUp(event) {\n    var value = getValidValue(event);\n    if (value !== internalInputVal) {\n      setInternalInputVal(value);\n    }\n    switch (event.keyCode) {\n      case KeyCode.ENTER:\n        handleChange(value);\n        break;\n      case KeyCode.UP:\n        handleChange(value - 1);\n        break;\n      case KeyCode.DOWN:\n        handleChange(value + 1);\n        break;\n      default:\n        break;\n    }\n  }\n  function handleBlur(event) {\n    handleChange(getValidValue(event));\n  }\n  function changePageSize(size) {\n    var newCurrent = calculatePage(size, pageSize, total);\n    var nextCurrent = current > newCurrent && newCurrent !== 0 ? newCurrent : current;\n    setPageSize(size);\n    setInternalInputVal(nextCurrent);\n    onShowSizeChange === null || onShowSizeChange === void 0 || onShowSizeChange(current, size);\n    setCurrent(nextCurrent);\n    onChange === null || onChange === void 0 || onChange(nextCurrent, size);\n  }\n  function handleChange(page) {\n    if (isValid(page) && !disabled) {\n      var currentPage = calculatePage(undefined, pageSize, total);\n      var newPage = page;\n      if (page > currentPage) {\n        newPage = currentPage;\n      } else if (page < 1) {\n        newPage = 1;\n      }\n      if (newPage !== internalInputVal) {\n        setInternalInputVal(newPage);\n      }\n      setCurrent(newPage);\n      onChange === null || onChange === void 0 || onChange(newPage, pageSize);\n      return newPage;\n    }\n    return current;\n  }\n  var hasPrev = current > 1;\n  var hasNext = current < calculatePage(undefined, pageSize, total);\n  function prevHandle() {\n    if (hasPrev) handleChange(current - 1);\n  }\n  function nextHandle() {\n    if (hasNext) handleChange(current + 1);\n  }\n  function jumpPrevHandle() {\n    handleChange(jumpPrevPage);\n  }\n  function jumpNextHandle() {\n    handleChange(jumpNextPage);\n  }\n  function runIfEnter(event, callback) {\n    if (event.key === 'Enter' || event.charCode === KeyCode.ENTER || event.keyCode === KeyCode.ENTER) {\n      for (var _len = arguments.length, restParams = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n        restParams[_key - 2] = arguments[_key];\n      }\n      callback.apply(void 0, restParams);\n    }\n  }\n  function runIfEnterPrev(event) {\n    runIfEnter(event, prevHandle);\n  }\n  function runIfEnterNext(event) {\n    runIfEnter(event, nextHandle);\n  }\n  function runIfEnterJumpPrev(event) {\n    runIfEnter(event, jumpPrevHandle);\n  }\n  function runIfEnterJumpNext(event) {\n    runIfEnter(event, jumpNextHandle);\n  }\n  function renderPrev(prevPage) {\n    var prevButton = itemRender(prevPage, 'prev', getItemIcon(prevIcon, 'prev page'));\n    return /*#__PURE__*/React.isValidElement(prevButton) ? /*#__PURE__*/React.cloneElement(prevButton, {\n      disabled: !hasPrev\n    }) : prevButton;\n  }\n  function renderNext(nextPage) {\n    var nextButton = itemRender(nextPage, 'next', getItemIcon(nextIcon, 'next page'));\n    return /*#__PURE__*/React.isValidElement(nextButton) ? /*#__PURE__*/React.cloneElement(nextButton, {\n      disabled: !hasNext\n    }) : nextButton;\n  }\n  function handleGoTO(event) {\n    if (event.type === 'click' || event.keyCode === KeyCode.ENTER) {\n      handleChange(internalInputVal);\n    }\n  }\n  var jumpPrev = null;\n  var dataOrAriaAttributeProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var totalText = showTotal && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-total-text\")\n  }, showTotal(total, [total === 0 ? 0 : (current - 1) * pageSize + 1, current * pageSize > total ? total : current * pageSize]));\n  var jumpNext = null;\n  var allPages = calculatePage(undefined, pageSize, total);\n\n  // ================== Render ==================\n  // When hideOnSinglePage is true and there is only 1 page, hide the pager\n  if (hideOnSinglePage && total <= pageSize) {\n    return null;\n  }\n  var pagerList = [];\n  var pagerProps = {\n    rootPrefixCls: prefixCls,\n    onClick: handleChange,\n    onKeyPress: runIfEnter,\n    showTitle: showTitle,\n    itemRender: itemRender,\n    page: -1\n  };\n  var prevPage = current - 1 > 0 ? current - 1 : 0;\n  var nextPage = current + 1 < allPages ? current + 1 : allPages;\n  var goButton = showQuickJumper && showQuickJumper.goButton;\n\n  // ================== Simple ==================\n  // FIXME: ts type\n  var isReadOnly = _typeof(simple) === 'object' ? simple.readOnly : !simple;\n  var gotoButton = goButton;\n  var simplePager = null;\n  if (simple) {\n    // ====== Simple quick jump ======\n    if (goButton) {\n      if (typeof goButton === 'boolean') {\n        gotoButton = /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          onClick: handleGoTO,\n          onKeyUp: handleGoTO\n        }, locale.jump_to_confirm);\n      } else {\n        gotoButton = /*#__PURE__*/React.createElement(\"span\", {\n          onClick: handleGoTO,\n          onKeyUp: handleGoTO\n        }, goButton);\n      }\n      gotoButton = /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? \"\".concat(locale.jump_to).concat(current, \"/\").concat(allPages) : null,\n        className: \"\".concat(prefixCls, \"-simple-pager\")\n      }, gotoButton);\n    }\n    simplePager = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? \"\".concat(current, \"/\").concat(allPages) : null,\n      className: \"\".concat(prefixCls, \"-simple-pager\")\n    }, isReadOnly ? internalInputVal : /*#__PURE__*/React.createElement(\"input\", {\n      type: \"text\",\n      \"aria-label\": locale.jump_to,\n      value: internalInputVal,\n      disabled: disabled,\n      onKeyDown: handleKeyDown,\n      onKeyUp: handleKeyUp,\n      onChange: handleKeyUp,\n      onBlur: handleBlur,\n      size: 3\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-slash\")\n    }, \"/\"), allPages);\n  }\n\n  // ====================== Normal ======================\n  var pageBufferSize = showLessItems ? 1 : 2;\n  if (allPages <= 3 + pageBufferSize * 2) {\n    if (!allPages) {\n      pagerList.push(/*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: \"noPager\",\n        page: 1,\n        className: \"\".concat(prefixCls, \"-item-disabled\")\n      })));\n    }\n    for (var i = 1; i <= allPages; i += 1) {\n      pagerList.push(/*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: i,\n        page: i,\n        active: current === i\n      })));\n    }\n  } else {\n    var prevItemTitle = showLessItems ? locale.prev_3 : locale.prev_5;\n    var nextItemTitle = showLessItems ? locale.next_3 : locale.next_5;\n    var jumpPrevContent = itemRender(jumpPrevPage, 'jump-prev', getItemIcon(jumpPrevIcon, 'prev page'));\n    var jumpNextContent = itemRender(jumpNextPage, 'jump-next', getItemIcon(jumpNextIcon, 'next page'));\n    if (showPrevNextJumpers) {\n      jumpPrev = jumpPrevContent ? /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? prevItemTitle : null,\n        key: \"prev\",\n        onClick: jumpPrevHandle,\n        tabIndex: 0,\n        onKeyDown: runIfEnterJumpPrev,\n        className: classNames(\"\".concat(prefixCls, \"-jump-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-prev-custom-icon\"), !!jumpPrevIcon))\n      }, jumpPrevContent) : null;\n      jumpNext = jumpNextContent ? /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? nextItemTitle : null,\n        key: \"next\",\n        onClick: jumpNextHandle,\n        tabIndex: 0,\n        onKeyDown: runIfEnterJumpNext,\n        className: classNames(\"\".concat(prefixCls, \"-jump-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-next-custom-icon\"), !!jumpNextIcon))\n      }, jumpNextContent) : null;\n    }\n    var left = Math.max(1, current - pageBufferSize);\n    var right = Math.min(current + pageBufferSize, allPages);\n    if (current - 1 <= pageBufferSize) {\n      right = 1 + pageBufferSize * 2;\n    }\n    if (allPages - current <= pageBufferSize) {\n      left = allPages - pageBufferSize * 2;\n    }\n    for (var _i = left; _i <= right; _i += 1) {\n      pagerList.push(/*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: _i,\n        page: _i,\n        active: current === _i\n      })));\n    }\n    if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {\n      pagerList[0] = /*#__PURE__*/React.cloneElement(pagerList[0], {\n        className: classNames(\"\".concat(prefixCls, \"-item-after-jump-prev\"), pagerList[0].props.className)\n      });\n      pagerList.unshift(jumpPrev);\n    }\n    if (allPages - current >= pageBufferSize * 2 && current !== allPages - 2) {\n      var lastOne = pagerList[pagerList.length - 1];\n      pagerList[pagerList.length - 1] = /*#__PURE__*/React.cloneElement(lastOne, {\n        className: classNames(\"\".concat(prefixCls, \"-item-before-jump-next\"), lastOne.props.className)\n      });\n      pagerList.push(jumpNext);\n    }\n    if (left !== 1) {\n      pagerList.unshift(/*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: 1,\n        page: 1\n      })));\n    }\n    if (right !== allPages) {\n      pagerList.push(/*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: allPages,\n        page: allPages\n      })));\n    }\n  }\n  var prev = renderPrev(prevPage);\n  if (prev) {\n    var prevDisabled = !hasPrev || !allPages;\n    prev = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? locale.prev_page : null,\n      onClick: prevHandle,\n      tabIndex: prevDisabled ? null : 0,\n      onKeyDown: runIfEnterPrev,\n      className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), prevDisabled)),\n      \"aria-disabled\": prevDisabled\n    }, prev);\n  }\n  var next = renderNext(nextPage);\n  if (next) {\n    var nextDisabled, nextTabIndex;\n    if (simple) {\n      nextDisabled = !hasNext;\n      nextTabIndex = hasPrev ? 0 : null;\n    } else {\n      nextDisabled = !hasNext || !allPages;\n      nextTabIndex = nextDisabled ? null : 0;\n    }\n    next = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? locale.next_page : null,\n      onClick: nextHandle,\n      tabIndex: nextTabIndex,\n      onKeyDown: runIfEnterNext,\n      className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), nextDisabled)),\n      \"aria-disabled\": nextDisabled\n    }, next);\n  }\n  var cls = classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-start\"), align === 'start'), \"\".concat(prefixCls, \"-center\"), align === 'center'), \"\".concat(prefixCls, \"-end\"), align === 'end'), \"\".concat(prefixCls, \"-simple\"), simple), \"\".concat(prefixCls, \"-disabled\"), disabled));\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: cls,\n    style: style,\n    ref: paginationRef\n  }, dataOrAriaAttributeProps), totalText, prev, simple ? simplePager : pagerList, next, /*#__PURE__*/React.createElement(Options, {\n    locale: locale,\n    rootPrefixCls: prefixCls,\n    disabled: disabled,\n    selectPrefixCls: selectPrefixCls,\n    changeSize: changePageSize,\n    pageSize: pageSize,\n    pageSizeOptions: pageSizeOptions,\n    quickGo: shouldDisplayQuickJumper ? handleChange : null,\n    goButton: gotoButton,\n    showSizeChanger: showSizeChanger,\n    sizeChangerRender: sizeChangerRender\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Pagination.displayName = 'Pagination';\n}\nexport default Pagination;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_typeof", "_objectSpread", "_slicedToArray", "classNames", "useMergedState", "KeyCode", "pickAttrs", "warning", "React", "useEffect", "zhCN", "Options", "Pager", "defaultItemRender", "page", "type", "element", "noop", "isInteger", "v", "value", "Number", "isNaN", "isFinite", "Math", "floor", "calculatePage", "p", "pageSize", "total", "_pageSize", "Pagination", "props", "_props$prefixCls", "prefixCls", "_props$selectPrefixCl", "selectPrefixCls", "className", "currentProp", "current", "_props$defaultCurrent", "defaultCurrent", "_props$total", "pageSizeProp", "_props$defaultPageSiz", "defaultPageSize", "_props$onChange", "onChange", "hideOnSinglePage", "align", "_props$showPrevNextJu", "showPrevNextJumpers", "showQuickJumper", "showLessItems", "_props$showTitle", "showTitle", "_props$onShowSizeChan", "onShowSizeChange", "_props$locale", "locale", "style", "_props$totalBoundaryS", "totalBoundaryShowSizeChanger", "disabled", "simple", "showTotal", "_props$showSizeChange", "showSizeChanger", "sizeChangerRender", "pageSizeOptions", "_props$itemRender", "itemRender", "jumpPrevIcon", "jumpNextIcon", "prevIcon", "nextIcon", "paginationRef", "useRef", "_useMergedState", "defaultValue", "_useMergedState2", "setPageSize", "_useMergedState3", "postState", "c", "max", "min", "undefined", "_useMergedState4", "setCurrent", "_React$useState", "useState", "_React$useState2", "internalInputVal", "setInternalInputVal", "hasOnChange", "has<PERSON><PERSON>rent", "process", "env", "NODE_ENV", "jumpPrevPage", "jumpNextPage", "getItemIcon", "icon", "label", "iconNode", "createElement", "concat", "getValidValue", "e", "inputValue", "target", "allPages", "<PERSON><PERSON><PERSON><PERSON>", "shouldDisplayQuickJumper", "handleKeyDown", "event", "keyCode", "UP", "DOWN", "preventDefault", "handleKeyUp", "ENTER", "handleChange", "handleBlur", "changePageSize", "size", "newCurrent", "nextCurrent", "currentPage", "newPage", "has<PERSON>rev", "hasNext", "prevHandle", "nextH<PERSON>le", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jumpNextHandle", "runIfEnter", "callback", "key", "charCode", "_len", "arguments", "length", "restParams", "Array", "_key", "apply", "runIfEnterPrev", "runIfEnterNext", "runIfEnterJumpPrev", "runIfEnterJumpNext", "renderPrev", "prevPage", "prevButton", "isValidElement", "cloneElement", "renderNext", "nextPage", "nextButton", "handleGoTO", "jump<PERSON>rev", "dataOrAriaAttributeProps", "aria", "data", "totalText", "jumpNext", "pagerList", "pagerProps", "rootPrefixCls", "onClick", "onKeyPress", "goButton", "isReadOnly", "readOnly", "gotoButton", "simplePager", "onKeyUp", "jump_to_confirm", "title", "jump_to", "onKeyDown", "onBlur", "pageBufferSize", "push", "i", "active", "prevItemTitle", "prev_3", "prev_5", "nextItemTitle", "next_3", "next_5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jumpNextContent", "tabIndex", "left", "right", "_i", "unshift", "lastOne", "prev", "prevDisabled", "prev_page", "next", "nextDisabled", "nextTabIndex", "next_page", "cls", "ref", "changeSize", "quickGo", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-pagination@5.1.0_react-d_459b2e8d0467f517bd67196250306427/node_modules/rc-pagination/es/Pagination.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useEffect } from 'react';\nimport zhCN from \"./locale/zh_CN\";\nimport Options from \"./Options\";\nimport Pager from \"./Pager\";\nvar defaultItemRender = function defaultItemRender(page, type, element) {\n  return element;\n};\nfunction noop() {}\nfunction isInteger(v) {\n  var value = Number(v);\n  return typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value;\n}\nfunction calculatePage(p, pageSize, total) {\n  var _pageSize = typeof p === 'undefined' ? pageSize : p;\n  return Math.floor((total - 1) / _pageSize) + 1;\n}\nvar Pagination = function Pagination(props) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-pagination' : _props$prefixCls,\n    _props$selectPrefixCl = props.selectPrefixCls,\n    selectPrefixCls = _props$selectPrefixCl === void 0 ? 'rc-select' : _props$selectPrefixCl,\n    className = props.className,\n    currentProp = props.current,\n    _props$defaultCurrent = props.defaultCurrent,\n    defaultCurrent = _props$defaultCurrent === void 0 ? 1 : _props$defaultCurrent,\n    _props$total = props.total,\n    total = _props$total === void 0 ? 0 : _props$total,\n    pageSizeProp = props.pageSize,\n    _props$defaultPageSiz = props.defaultPageSize,\n    defaultPageSize = _props$defaultPageSiz === void 0 ? 10 : _props$defaultPageSiz,\n    _props$onChange = props.onChange,\n    onChange = _props$onChange === void 0 ? noop : _props$onChange,\n    hideOnSinglePage = props.hideOnSinglePage,\n    align = props.align,\n    _props$showPrevNextJu = props.showPrevNextJumpers,\n    showPrevNextJumpers = _props$showPrevNextJu === void 0 ? true : _props$showPrevNextJu,\n    showQuickJumper = props.showQuickJumper,\n    showLessItems = props.showLessItems,\n    _props$showTitle = props.showTitle,\n    showTitle = _props$showTitle === void 0 ? true : _props$showTitle,\n    _props$onShowSizeChan = props.onShowSizeChange,\n    onShowSizeChange = _props$onShowSizeChan === void 0 ? noop : _props$onShowSizeChan,\n    _props$locale = props.locale,\n    locale = _props$locale === void 0 ? zhCN : _props$locale,\n    style = props.style,\n    _props$totalBoundaryS = props.totalBoundaryShowSizeChanger,\n    totalBoundaryShowSizeChanger = _props$totalBoundaryS === void 0 ? 50 : _props$totalBoundaryS,\n    disabled = props.disabled,\n    simple = props.simple,\n    showTotal = props.showTotal,\n    _props$showSizeChange = props.showSizeChanger,\n    showSizeChanger = _props$showSizeChange === void 0 ? total > totalBoundaryShowSizeChanger : _props$showSizeChange,\n    sizeChangerRender = props.sizeChangerRender,\n    pageSizeOptions = props.pageSizeOptions,\n    _props$itemRender = props.itemRender,\n    itemRender = _props$itemRender === void 0 ? defaultItemRender : _props$itemRender,\n    jumpPrevIcon = props.jumpPrevIcon,\n    jumpNextIcon = props.jumpNextIcon,\n    prevIcon = props.prevIcon,\n    nextIcon = props.nextIcon;\n  var paginationRef = React.useRef(null);\n  var _useMergedState = useMergedState(10, {\n      value: pageSizeProp,\n      defaultValue: defaultPageSize\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    pageSize = _useMergedState2[0],\n    setPageSize = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(1, {\n      value: currentProp,\n      defaultValue: defaultCurrent,\n      postState: function postState(c) {\n        return Math.max(1, Math.min(c, calculatePage(undefined, pageSize, total)));\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    current = _useMergedState4[0],\n    setCurrent = _useMergedState4[1];\n  var _React$useState = React.useState(current),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    internalInputVal = _React$useState2[0],\n    setInternalInputVal = _React$useState2[1];\n  useEffect(function () {\n    setInternalInputVal(current);\n  }, [current]);\n  var hasOnChange = onChange !== noop;\n  var hasCurrent = ('current' in props);\n  if (process.env.NODE_ENV !== 'production') {\n    warning(hasCurrent ? hasOnChange : true, 'You provided a `current` prop to a Pagination component without an `onChange` handler. This will render a read-only component.');\n  }\n  var jumpPrevPage = Math.max(1, current - (showLessItems ? 3 : 5));\n  var jumpNextPage = Math.min(calculatePage(undefined, pageSize, total), current + (showLessItems ? 3 : 5));\n  function getItemIcon(icon, label) {\n    var iconNode = icon || /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": label,\n      className: \"\".concat(prefixCls, \"-item-link\")\n    });\n    if (typeof icon === 'function') {\n      iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n    }\n    return iconNode;\n  }\n  function getValidValue(e) {\n    var inputValue = e.target.value;\n    var allPages = calculatePage(undefined, pageSize, total);\n    var value;\n    if (inputValue === '') {\n      value = inputValue;\n    } else if (Number.isNaN(Number(inputValue))) {\n      value = internalInputVal;\n    } else if (inputValue >= allPages) {\n      value = allPages;\n    } else {\n      value = Number(inputValue);\n    }\n    return value;\n  }\n  function isValid(page) {\n    return isInteger(page) && page !== current && isInteger(total) && total > 0;\n  }\n  var shouldDisplayQuickJumper = total > pageSize ? showQuickJumper : false;\n\n  /**\n   * prevent \"up arrow\" key reseting cursor position within textbox\n   * @see https://stackoverflow.com/a/1081114\n   */\n  function handleKeyDown(event) {\n    if (event.keyCode === KeyCode.UP || event.keyCode === KeyCode.DOWN) {\n      event.preventDefault();\n    }\n  }\n  function handleKeyUp(event) {\n    var value = getValidValue(event);\n    if (value !== internalInputVal) {\n      setInternalInputVal(value);\n    }\n    switch (event.keyCode) {\n      case KeyCode.ENTER:\n        handleChange(value);\n        break;\n      case KeyCode.UP:\n        handleChange(value - 1);\n        break;\n      case KeyCode.DOWN:\n        handleChange(value + 1);\n        break;\n      default:\n        break;\n    }\n  }\n  function handleBlur(event) {\n    handleChange(getValidValue(event));\n  }\n  function changePageSize(size) {\n    var newCurrent = calculatePage(size, pageSize, total);\n    var nextCurrent = current > newCurrent && newCurrent !== 0 ? newCurrent : current;\n    setPageSize(size);\n    setInternalInputVal(nextCurrent);\n    onShowSizeChange === null || onShowSizeChange === void 0 || onShowSizeChange(current, size);\n    setCurrent(nextCurrent);\n    onChange === null || onChange === void 0 || onChange(nextCurrent, size);\n  }\n  function handleChange(page) {\n    if (isValid(page) && !disabled) {\n      var currentPage = calculatePage(undefined, pageSize, total);\n      var newPage = page;\n      if (page > currentPage) {\n        newPage = currentPage;\n      } else if (page < 1) {\n        newPage = 1;\n      }\n      if (newPage !== internalInputVal) {\n        setInternalInputVal(newPage);\n      }\n      setCurrent(newPage);\n      onChange === null || onChange === void 0 || onChange(newPage, pageSize);\n      return newPage;\n    }\n    return current;\n  }\n  var hasPrev = current > 1;\n  var hasNext = current < calculatePage(undefined, pageSize, total);\n  function prevHandle() {\n    if (hasPrev) handleChange(current - 1);\n  }\n  function nextHandle() {\n    if (hasNext) handleChange(current + 1);\n  }\n  function jumpPrevHandle() {\n    handleChange(jumpPrevPage);\n  }\n  function jumpNextHandle() {\n    handleChange(jumpNextPage);\n  }\n  function runIfEnter(event, callback) {\n    if (event.key === 'Enter' || event.charCode === KeyCode.ENTER || event.keyCode === KeyCode.ENTER) {\n      for (var _len = arguments.length, restParams = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n        restParams[_key - 2] = arguments[_key];\n      }\n      callback.apply(void 0, restParams);\n    }\n  }\n  function runIfEnterPrev(event) {\n    runIfEnter(event, prevHandle);\n  }\n  function runIfEnterNext(event) {\n    runIfEnter(event, nextHandle);\n  }\n  function runIfEnterJumpPrev(event) {\n    runIfEnter(event, jumpPrevHandle);\n  }\n  function runIfEnterJumpNext(event) {\n    runIfEnter(event, jumpNextHandle);\n  }\n  function renderPrev(prevPage) {\n    var prevButton = itemRender(prevPage, 'prev', getItemIcon(prevIcon, 'prev page'));\n    return /*#__PURE__*/React.isValidElement(prevButton) ? /*#__PURE__*/React.cloneElement(prevButton, {\n      disabled: !hasPrev\n    }) : prevButton;\n  }\n  function renderNext(nextPage) {\n    var nextButton = itemRender(nextPage, 'next', getItemIcon(nextIcon, 'next page'));\n    return /*#__PURE__*/React.isValidElement(nextButton) ? /*#__PURE__*/React.cloneElement(nextButton, {\n      disabled: !hasNext\n    }) : nextButton;\n  }\n  function handleGoTO(event) {\n    if (event.type === 'click' || event.keyCode === KeyCode.ENTER) {\n      handleChange(internalInputVal);\n    }\n  }\n  var jumpPrev = null;\n  var dataOrAriaAttributeProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var totalText = showTotal && /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-total-text\")\n  }, showTotal(total, [total === 0 ? 0 : (current - 1) * pageSize + 1, current * pageSize > total ? total : current * pageSize]));\n  var jumpNext = null;\n  var allPages = calculatePage(undefined, pageSize, total);\n\n  // ================== Render ==================\n  // When hideOnSinglePage is true and there is only 1 page, hide the pager\n  if (hideOnSinglePage && total <= pageSize) {\n    return null;\n  }\n  var pagerList = [];\n  var pagerProps = {\n    rootPrefixCls: prefixCls,\n    onClick: handleChange,\n    onKeyPress: runIfEnter,\n    showTitle: showTitle,\n    itemRender: itemRender,\n    page: -1\n  };\n  var prevPage = current - 1 > 0 ? current - 1 : 0;\n  var nextPage = current + 1 < allPages ? current + 1 : allPages;\n  var goButton = showQuickJumper && showQuickJumper.goButton;\n\n  // ================== Simple ==================\n  // FIXME: ts type\n  var isReadOnly = _typeof(simple) === 'object' ? simple.readOnly : !simple;\n  var gotoButton = goButton;\n  var simplePager = null;\n  if (simple) {\n    // ====== Simple quick jump ======\n    if (goButton) {\n      if (typeof goButton === 'boolean') {\n        gotoButton = /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          onClick: handleGoTO,\n          onKeyUp: handleGoTO\n        }, locale.jump_to_confirm);\n      } else {\n        gotoButton = /*#__PURE__*/React.createElement(\"span\", {\n          onClick: handleGoTO,\n          onKeyUp: handleGoTO\n        }, goButton);\n      }\n      gotoButton = /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? \"\".concat(locale.jump_to).concat(current, \"/\").concat(allPages) : null,\n        className: \"\".concat(prefixCls, \"-simple-pager\")\n      }, gotoButton);\n    }\n    simplePager = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? \"\".concat(current, \"/\").concat(allPages) : null,\n      className: \"\".concat(prefixCls, \"-simple-pager\")\n    }, isReadOnly ? internalInputVal : /*#__PURE__*/React.createElement(\"input\", {\n      type: \"text\",\n      \"aria-label\": locale.jump_to,\n      value: internalInputVal,\n      disabled: disabled,\n      onKeyDown: handleKeyDown,\n      onKeyUp: handleKeyUp,\n      onChange: handleKeyUp,\n      onBlur: handleBlur,\n      size: 3\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-slash\")\n    }, \"/\"), allPages);\n  }\n\n  // ====================== Normal ======================\n  var pageBufferSize = showLessItems ? 1 : 2;\n  if (allPages <= 3 + pageBufferSize * 2) {\n    if (!allPages) {\n      pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: \"noPager\",\n        page: 1,\n        className: \"\".concat(prefixCls, \"-item-disabled\")\n      })));\n    }\n    for (var i = 1; i <= allPages; i += 1) {\n      pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: i,\n        page: i,\n        active: current === i\n      })));\n    }\n  } else {\n    var prevItemTitle = showLessItems ? locale.prev_3 : locale.prev_5;\n    var nextItemTitle = showLessItems ? locale.next_3 : locale.next_5;\n    var jumpPrevContent = itemRender(jumpPrevPage, 'jump-prev', getItemIcon(jumpPrevIcon, 'prev page'));\n    var jumpNextContent = itemRender(jumpNextPage, 'jump-next', getItemIcon(jumpNextIcon, 'next page'));\n    if (showPrevNextJumpers) {\n      jumpPrev = jumpPrevContent ? /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? prevItemTitle : null,\n        key: \"prev\",\n        onClick: jumpPrevHandle,\n        tabIndex: 0,\n        onKeyDown: runIfEnterJumpPrev,\n        className: classNames(\"\".concat(prefixCls, \"-jump-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-prev-custom-icon\"), !!jumpPrevIcon))\n      }, jumpPrevContent) : null;\n      jumpNext = jumpNextContent ? /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? nextItemTitle : null,\n        key: \"next\",\n        onClick: jumpNextHandle,\n        tabIndex: 0,\n        onKeyDown: runIfEnterJumpNext,\n        className: classNames(\"\".concat(prefixCls, \"-jump-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-next-custom-icon\"), !!jumpNextIcon))\n      }, jumpNextContent) : null;\n    }\n    var left = Math.max(1, current - pageBufferSize);\n    var right = Math.min(current + pageBufferSize, allPages);\n    if (current - 1 <= pageBufferSize) {\n      right = 1 + pageBufferSize * 2;\n    }\n    if (allPages - current <= pageBufferSize) {\n      left = allPages - pageBufferSize * 2;\n    }\n    for (var _i = left; _i <= right; _i += 1) {\n      pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: _i,\n        page: _i,\n        active: current === _i\n      })));\n    }\n    if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {\n      pagerList[0] = /*#__PURE__*/React.cloneElement(pagerList[0], {\n        className: classNames(\"\".concat(prefixCls, \"-item-after-jump-prev\"), pagerList[0].props.className)\n      });\n      pagerList.unshift(jumpPrev);\n    }\n    if (allPages - current >= pageBufferSize * 2 && current !== allPages - 2) {\n      var lastOne = pagerList[pagerList.length - 1];\n      pagerList[pagerList.length - 1] = /*#__PURE__*/React.cloneElement(lastOne, {\n        className: classNames(\"\".concat(prefixCls, \"-item-before-jump-next\"), lastOne.props.className)\n      });\n      pagerList.push(jumpNext);\n    }\n    if (left !== 1) {\n      pagerList.unshift( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: 1,\n        page: 1\n      })));\n    }\n    if (right !== allPages) {\n      pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n        key: allPages,\n        page: allPages\n      })));\n    }\n  }\n  var prev = renderPrev(prevPage);\n  if (prev) {\n    var prevDisabled = !hasPrev || !allPages;\n    prev = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? locale.prev_page : null,\n      onClick: prevHandle,\n      tabIndex: prevDisabled ? null : 0,\n      onKeyDown: runIfEnterPrev,\n      className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), prevDisabled)),\n      \"aria-disabled\": prevDisabled\n    }, prev);\n  }\n  var next = renderNext(nextPage);\n  if (next) {\n    var nextDisabled, nextTabIndex;\n    if (simple) {\n      nextDisabled = !hasNext;\n      nextTabIndex = hasPrev ? 0 : null;\n    } else {\n      nextDisabled = !hasNext || !allPages;\n      nextTabIndex = nextDisabled ? null : 0;\n    }\n    next = /*#__PURE__*/React.createElement(\"li\", {\n      title: showTitle ? locale.next_page : null,\n      onClick: nextHandle,\n      tabIndex: nextTabIndex,\n      onKeyDown: runIfEnterNext,\n      className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), nextDisabled)),\n      \"aria-disabled\": nextDisabled\n    }, next);\n  }\n  var cls = classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-start\"), align === 'start'), \"\".concat(prefixCls, \"-center\"), align === 'center'), \"\".concat(prefixCls, \"-end\"), align === 'end'), \"\".concat(prefixCls, \"-simple\"), simple), \"\".concat(prefixCls, \"-disabled\"), disabled));\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: cls,\n    style: style,\n    ref: paginationRef\n  }, dataOrAriaAttributeProps), totalText, prev, simple ? simplePager : pagerList, next, /*#__PURE__*/React.createElement(Options, {\n    locale: locale,\n    rootPrefixCls: prefixCls,\n    disabled: disabled,\n    selectPrefixCls: selectPrefixCls,\n    changeSize: changePageSize,\n    pageSize: pageSize,\n    pageSizeOptions: pageSizeOptions,\n    quickGo: shouldDisplayQuickJumper ? handleChange : null,\n    goButton: gotoButton,\n    showSizeChanger: showSizeChanger,\n    sizeChangerRender: sizeChangerRender\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Pagination.displayName = 'Pagination';\n}\nexport default Pagination;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACtE,OAAOA,OAAO;AAChB,CAAC;AACD,SAASC,IAAIA,CAAA,EAAG,CAAC;AACjB,SAASC,SAASA,CAACC,CAAC,EAAE;EACpB,IAAIC,KAAK,GAAGC,MAAM,CAACF,CAAC,CAAC;EACrB,OAAO,OAAOC,KAAK,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC,IAAII,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC,KAAKA,KAAK;AAC5G;AACA,SAASM,aAAaA,CAACC,CAAC,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAG,OAAOH,CAAC,KAAK,WAAW,GAAGC,QAAQ,GAAGD,CAAC;EACvD,OAAOH,IAAI,CAACC,KAAK,CAAC,CAACI,KAAK,GAAG,CAAC,IAAIC,SAAS,CAAC,GAAG,CAAC;AAChD;AACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,gBAAgB,GAAGD,KAAK,CAACE,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,eAAe,GAAGA,gBAAgB;IAC5EE,qBAAqB,GAAGH,KAAK,CAACI,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,qBAAqB;IACxFE,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,WAAW,GAAGN,KAAK,CAACO,OAAO;IAC3BC,qBAAqB,GAAGR,KAAK,CAACS,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAC7EE,YAAY,GAAGV,KAAK,CAACH,KAAK;IAC1BA,KAAK,GAAGa,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IAClDC,YAAY,GAAGX,KAAK,CAACJ,QAAQ;IAC7BgB,qBAAqB,GAAGZ,KAAK,CAACa,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC/EE,eAAe,GAAGd,KAAK,CAACe,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG7B,IAAI,GAAG6B,eAAe;IAC9DE,gBAAgB,GAAGhB,KAAK,CAACgB,gBAAgB;IACzCC,KAAK,GAAGjB,KAAK,CAACiB,KAAK;IACnBC,qBAAqB,GAAGlB,KAAK,CAACmB,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACrFE,eAAe,GAAGpB,KAAK,CAACoB,eAAe;IACvCC,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,gBAAgB,GAAGtB,KAAK,CAACuB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,qBAAqB,GAAGxB,KAAK,CAACyB,gBAAgB;IAC9CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGvC,IAAI,GAAGuC,qBAAqB;IAClFE,aAAa,GAAG1B,KAAK,CAAC2B,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGhD,IAAI,GAAGgD,aAAa;IACxDE,KAAK,GAAG5B,KAAK,CAAC4B,KAAK;IACnBC,qBAAqB,GAAG7B,KAAK,CAAC8B,4BAA4B;IAC1DA,4BAA4B,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC5FE,QAAQ,GAAG/B,KAAK,CAAC+B,QAAQ;IACzBC,MAAM,GAAGhC,KAAK,CAACgC,MAAM;IACrBC,SAAS,GAAGjC,KAAK,CAACiC,SAAS;IAC3BC,qBAAqB,GAAGlC,KAAK,CAACmC,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrC,KAAK,GAAGiC,4BAA4B,GAAGI,qBAAqB;IACjHE,iBAAiB,GAAGpC,KAAK,CAACoC,iBAAiB;IAC3CC,eAAe,GAAGrC,KAAK,CAACqC,eAAe;IACvCC,iBAAiB,GAAGtC,KAAK,CAACuC,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGzD,iBAAiB,GAAGyD,iBAAiB;IACjFE,YAAY,GAAGxC,KAAK,CAACwC,YAAY;IACjCC,YAAY,GAAGzC,KAAK,CAACyC,YAAY;IACjCC,QAAQ,GAAG1C,KAAK,CAAC0C,QAAQ;IACzBC,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ;EAC3B,IAAIC,aAAa,GAAGpE,KAAK,CAACqE,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIC,eAAe,GAAG1E,cAAc,CAAC,EAAE,EAAE;MACrCgB,KAAK,EAAEuB,YAAY;MACnBoC,YAAY,EAAElC;IAChB,CAAC,CAAC;IACFmC,gBAAgB,GAAG9E,cAAc,CAAC4E,eAAe,EAAE,CAAC,CAAC;IACrDlD,QAAQ,GAAGoD,gBAAgB,CAAC,CAAC,CAAC;IAC9BC,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIE,gBAAgB,GAAG9E,cAAc,CAAC,CAAC,EAAE;MACrCgB,KAAK,EAAEkB,WAAW;MAClByC,YAAY,EAAEtC,cAAc;MAC5B0C,SAAS,EAAE,SAASA,SAASA,CAACC,CAAC,EAAE;QAC/B,OAAO5D,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAE7D,IAAI,CAAC8D,GAAG,CAACF,CAAC,EAAE1D,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC;IACF2D,gBAAgB,GAAGtF,cAAc,CAACgF,gBAAgB,EAAE,CAAC,CAAC;IACtD3C,OAAO,GAAGiD,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIE,eAAe,GAAGlF,KAAK,CAACmF,QAAQ,CAACpD,OAAO,CAAC;IAC3CqD,gBAAgB,GAAG1F,cAAc,CAACwF,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3CnF,SAAS,CAAC,YAAY;IACpBqF,mBAAmB,CAACvD,OAAO,CAAC;EAC9B,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,IAAIwD,WAAW,GAAGhD,QAAQ,KAAK9B,IAAI;EACnC,IAAI+E,UAAU,GAAI,SAAS,IAAIhE,KAAM;EACrC,IAAIiE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5F,OAAO,CAACyF,UAAU,GAAGD,WAAW,GAAG,IAAI,EAAE,gIAAgI,CAAC;EAC5K;EACA,IAAIK,YAAY,GAAG5E,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAE9C,OAAO,IAAIc,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE,IAAIgD,YAAY,GAAG7E,IAAI,CAAC8D,GAAG,CAAC5D,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC,EAAEU,OAAO,IAAIc,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACzG,SAASiD,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAChC,IAAIC,QAAQ,GAAGF,IAAI,IAAI,aAAa/F,KAAK,CAACkG,aAAa,CAAC,QAAQ,EAAE;MAChE3F,IAAI,EAAE,QAAQ;MACd,YAAY,EAAEyF,KAAK;MACnBnE,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,YAAY;IAC9C,CAAC,CAAC;IACF,IAAI,OAAOqE,IAAI,KAAK,UAAU,EAAE;MAC9BE,QAAQ,GAAG,aAAajG,KAAK,CAACkG,aAAa,CAACH,IAAI,EAAEtG,aAAa,CAAC,CAAC,CAAC,EAAE+B,KAAK,CAAC,CAAC;IAC7E;IACA,OAAOyE,QAAQ;EACjB;EACA,SAASG,aAAaA,CAACC,CAAC,EAAE;IACxB,IAAIC,UAAU,GAAGD,CAAC,CAACE,MAAM,CAAC3F,KAAK;IAC/B,IAAI4F,QAAQ,GAAGtF,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC;IACxD,IAAIT,KAAK;IACT,IAAI0F,UAAU,KAAK,EAAE,EAAE;MACrB1F,KAAK,GAAG0F,UAAU;IACpB,CAAC,MAAM,IAAIzF,MAAM,CAACC,KAAK,CAACD,MAAM,CAACyF,UAAU,CAAC,CAAC,EAAE;MAC3C1F,KAAK,GAAGyE,gBAAgB;IAC1B,CAAC,MAAM,IAAIiB,UAAU,IAAIE,QAAQ,EAAE;MACjC5F,KAAK,GAAG4F,QAAQ;IAClB,CAAC,MAAM;MACL5F,KAAK,GAAGC,MAAM,CAACyF,UAAU,CAAC;IAC5B;IACA,OAAO1F,KAAK;EACd;EACA,SAAS6F,OAAOA,CAACnG,IAAI,EAAE;IACrB,OAAOI,SAAS,CAACJ,IAAI,CAAC,IAAIA,IAAI,KAAKyB,OAAO,IAAIrB,SAAS,CAACW,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC;EAC7E;EACA,IAAIqF,wBAAwB,GAAGrF,KAAK,GAAGD,QAAQ,GAAGwB,eAAe,GAAG,KAAK;;EAEzE;AACF;AACA;AACA;EACE,SAAS+D,aAAaA,CAACC,KAAK,EAAE;IAC5B,IAAIA,KAAK,CAACC,OAAO,KAAKhH,OAAO,CAACiH,EAAE,IAAIF,KAAK,CAACC,OAAO,KAAKhH,OAAO,CAACkH,IAAI,EAAE;MAClEH,KAAK,CAACI,cAAc,CAAC,CAAC;IACxB;EACF;EACA,SAASC,WAAWA,CAACL,KAAK,EAAE;IAC1B,IAAIhG,KAAK,GAAGwF,aAAa,CAACQ,KAAK,CAAC;IAChC,IAAIhG,KAAK,KAAKyE,gBAAgB,EAAE;MAC9BC,mBAAmB,CAAC1E,KAAK,CAAC;IAC5B;IACA,QAAQgG,KAAK,CAACC,OAAO;MACnB,KAAKhH,OAAO,CAACqH,KAAK;QAChBC,YAAY,CAACvG,KAAK,CAAC;QACnB;MACF,KAAKf,OAAO,CAACiH,EAAE;QACbK,YAAY,CAACvG,KAAK,GAAG,CAAC,CAAC;QACvB;MACF,KAAKf,OAAO,CAACkH,IAAI;QACfI,YAAY,CAACvG,KAAK,GAAG,CAAC,CAAC;QACvB;MACF;QACE;IACJ;EACF;EACA,SAASwG,UAAUA,CAACR,KAAK,EAAE;IACzBO,YAAY,CAACf,aAAa,CAACQ,KAAK,CAAC,CAAC;EACpC;EACA,SAASS,cAAcA,CAACC,IAAI,EAAE;IAC5B,IAAIC,UAAU,GAAGrG,aAAa,CAACoG,IAAI,EAAElG,QAAQ,EAAEC,KAAK,CAAC;IACrD,IAAImG,WAAW,GAAGzF,OAAO,GAAGwF,UAAU,IAAIA,UAAU,KAAK,CAAC,GAAGA,UAAU,GAAGxF,OAAO;IACjF0C,WAAW,CAAC6C,IAAI,CAAC;IACjBhC,mBAAmB,CAACkC,WAAW,CAAC;IAChCvE,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAAClB,OAAO,EAAEuF,IAAI,CAAC;IAC3FrC,UAAU,CAACuC,WAAW,CAAC;IACvBjF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACiF,WAAW,EAAEF,IAAI,CAAC;EACzE;EACA,SAASH,YAAYA,CAAC7G,IAAI,EAAE;IAC1B,IAAImG,OAAO,CAACnG,IAAI,CAAC,IAAI,CAACiD,QAAQ,EAAE;MAC9B,IAAIkE,WAAW,GAAGvG,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC;MAC3D,IAAIqG,OAAO,GAAGpH,IAAI;MAClB,IAAIA,IAAI,GAAGmH,WAAW,EAAE;QACtBC,OAAO,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAInH,IAAI,GAAG,CAAC,EAAE;QACnBoH,OAAO,GAAG,CAAC;MACb;MACA,IAAIA,OAAO,KAAKrC,gBAAgB,EAAE;QAChCC,mBAAmB,CAACoC,OAAO,CAAC;MAC9B;MACAzC,UAAU,CAACyC,OAAO,CAAC;MACnBnF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACmF,OAAO,EAAEtG,QAAQ,CAAC;MACvE,OAAOsG,OAAO;IAChB;IACA,OAAO3F,OAAO;EAChB;EACA,IAAI4F,OAAO,GAAG5F,OAAO,GAAG,CAAC;EACzB,IAAI6F,OAAO,GAAG7F,OAAO,GAAGb,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC;EACjE,SAASwG,UAAUA,CAAA,EAAG;IACpB,IAAIF,OAAO,EAAER,YAAY,CAACpF,OAAO,GAAG,CAAC,CAAC;EACxC;EACA,SAAS+F,UAAUA,CAAA,EAAG;IACpB,IAAIF,OAAO,EAAET,YAAY,CAACpF,OAAO,GAAG,CAAC,CAAC;EACxC;EACA,SAASgG,cAAcA,CAAA,EAAG;IACxBZ,YAAY,CAACvB,YAAY,CAAC;EAC5B;EACA,SAASoC,cAAcA,CAAA,EAAG;IACxBb,YAAY,CAACtB,YAAY,CAAC;EAC5B;EACA,SAASoC,UAAUA,CAACrB,KAAK,EAAEsB,QAAQ,EAAE;IACnC,IAAItB,KAAK,CAACuB,GAAG,KAAK,OAAO,IAAIvB,KAAK,CAACwB,QAAQ,KAAKvI,OAAO,CAACqH,KAAK,IAAIN,KAAK,CAACC,OAAO,KAAKhH,OAAO,CAACqH,KAAK,EAAE;MAChG,KAAK,IAAImB,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,UAAU,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAChHF,UAAU,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MACxC;MACAR,QAAQ,CAACS,KAAK,CAAC,KAAK,CAAC,EAAEH,UAAU,CAAC;IACpC;EACF;EACA,SAASI,cAAcA,CAAChC,KAAK,EAAE;IAC7BqB,UAAU,CAACrB,KAAK,EAAEiB,UAAU,CAAC;EAC/B;EACA,SAASgB,cAAcA,CAACjC,KAAK,EAAE;IAC7BqB,UAAU,CAACrB,KAAK,EAAEkB,UAAU,CAAC;EAC/B;EACA,SAASgB,kBAAkBA,CAAClC,KAAK,EAAE;IACjCqB,UAAU,CAACrB,KAAK,EAAEmB,cAAc,CAAC;EACnC;EACA,SAASgB,kBAAkBA,CAACnC,KAAK,EAAE;IACjCqB,UAAU,CAACrB,KAAK,EAAEoB,cAAc,CAAC;EACnC;EACA,SAASgB,UAAUA,CAACC,QAAQ,EAAE;IAC5B,IAAIC,UAAU,GAAGnF,UAAU,CAACkF,QAAQ,EAAE,MAAM,EAAEnD,WAAW,CAAC5B,QAAQ,EAAE,WAAW,CAAC,CAAC;IACjF,OAAO,aAAalE,KAAK,CAACmJ,cAAc,CAACD,UAAU,CAAC,GAAG,aAAalJ,KAAK,CAACoJ,YAAY,CAACF,UAAU,EAAE;MACjG3F,QAAQ,EAAE,CAACoE;IACb,CAAC,CAAC,GAAGuB,UAAU;EACjB;EACA,SAASG,UAAUA,CAACC,QAAQ,EAAE;IAC5B,IAAIC,UAAU,GAAGxF,UAAU,CAACuF,QAAQ,EAAE,MAAM,EAAExD,WAAW,CAAC3B,QAAQ,EAAE,WAAW,CAAC,CAAC;IACjF,OAAO,aAAanE,KAAK,CAACmJ,cAAc,CAACI,UAAU,CAAC,GAAG,aAAavJ,KAAK,CAACoJ,YAAY,CAACG,UAAU,EAAE;MACjGhG,QAAQ,EAAE,CAACqE;IACb,CAAC,CAAC,GAAG2B,UAAU;EACjB;EACA,SAASC,UAAUA,CAAC5C,KAAK,EAAE;IACzB,IAAIA,KAAK,CAACrG,IAAI,KAAK,OAAO,IAAIqG,KAAK,CAACC,OAAO,KAAKhH,OAAO,CAACqH,KAAK,EAAE;MAC7DC,YAAY,CAAC9B,gBAAgB,CAAC;IAChC;EACF;EACA,IAAIoE,QAAQ,GAAG,IAAI;EACnB,IAAIC,wBAAwB,GAAG5J,SAAS,CAAC0B,KAAK,EAAE;IAC9CmI,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIC,SAAS,GAAGpG,SAAS,IAAI,aAAazD,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;IAClErE,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,aAAa;EAC/C,CAAC,EAAE+B,SAAS,CAACpC,KAAK,EAAE,CAACA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAACU,OAAO,GAAG,CAAC,IAAIX,QAAQ,GAAG,CAAC,EAAEW,OAAO,GAAGX,QAAQ,GAAGC,KAAK,GAAGA,KAAK,GAAGU,OAAO,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC/H,IAAI0I,QAAQ,GAAG,IAAI;EACnB,IAAItD,QAAQ,GAAGtF,aAAa,CAAC6D,SAAS,EAAE3D,QAAQ,EAAEC,KAAK,CAAC;;EAExD;EACA;EACA,IAAImB,gBAAgB,IAAInB,KAAK,IAAID,QAAQ,EAAE;IACzC,OAAO,IAAI;EACb;EACA,IAAI2I,SAAS,GAAG,EAAE;EAClB,IAAIC,UAAU,GAAG;IACfC,aAAa,EAAEvI,SAAS;IACxBwI,OAAO,EAAE/C,YAAY;IACrBgD,UAAU,EAAElC,UAAU;IACtBlF,SAAS,EAAEA,SAAS;IACpBgB,UAAU,EAAEA,UAAU;IACtBzD,IAAI,EAAE,CAAC;EACT,CAAC;EACD,IAAI2I,QAAQ,GAAGlH,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGA,OAAO,GAAG,CAAC,GAAG,CAAC;EAChD,IAAIuH,QAAQ,GAAGvH,OAAO,GAAG,CAAC,GAAGyE,QAAQ,GAAGzE,OAAO,GAAG,CAAC,GAAGyE,QAAQ;EAC9D,IAAI4D,QAAQ,GAAGxH,eAAe,IAAIA,eAAe,CAACwH,QAAQ;;EAE1D;EACA;EACA,IAAIC,UAAU,GAAG7K,OAAO,CAACgE,MAAM,CAAC,KAAK,QAAQ,GAAGA,MAAM,CAAC8G,QAAQ,GAAG,CAAC9G,MAAM;EACzE,IAAI+G,UAAU,GAAGH,QAAQ;EACzB,IAAII,WAAW,GAAG,IAAI;EACtB,IAAIhH,MAAM,EAAE;IACV;IACA,IAAI4G,QAAQ,EAAE;MACZ,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;QACjCG,UAAU,GAAG,aAAavK,KAAK,CAACkG,aAAa,CAAC,QAAQ,EAAE;UACtD3F,IAAI,EAAE,QAAQ;UACd2J,OAAO,EAAEV,UAAU;UACnBiB,OAAO,EAAEjB;QACX,CAAC,EAAErG,MAAM,CAACuH,eAAe,CAAC;MAC5B,CAAC,MAAM;QACLH,UAAU,GAAG,aAAavK,KAAK,CAACkG,aAAa,CAAC,MAAM,EAAE;UACpDgE,OAAO,EAAEV,UAAU;UACnBiB,OAAO,EAAEjB;QACX,CAAC,EAAEY,QAAQ,CAAC;MACd;MACAG,UAAU,GAAG,aAAavK,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;QAClDyE,KAAK,EAAE5H,SAAS,GAAG,EAAE,CAACoD,MAAM,CAAChD,MAAM,CAACyH,OAAO,CAAC,CAACzE,MAAM,CAACpE,OAAO,EAAE,GAAG,CAAC,CAACoE,MAAM,CAACK,QAAQ,CAAC,GAAG,IAAI;QACzF3E,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,eAAe;MACjD,CAAC,EAAE6I,UAAU,CAAC;IAChB;IACAC,WAAW,GAAG,aAAaxK,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;MACnDyE,KAAK,EAAE5H,SAAS,GAAG,EAAE,CAACoD,MAAM,CAACpE,OAAO,EAAE,GAAG,CAAC,CAACoE,MAAM,CAACK,QAAQ,CAAC,GAAG,IAAI;MAClE3E,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,eAAe;IACjD,CAAC,EAAE2I,UAAU,GAAGhF,gBAAgB,GAAG,aAAarF,KAAK,CAACkG,aAAa,CAAC,OAAO,EAAE;MAC3E3F,IAAI,EAAE,MAAM;MACZ,YAAY,EAAE4C,MAAM,CAACyH,OAAO;MAC5BhK,KAAK,EAAEyE,gBAAgB;MACvB9B,QAAQ,EAAEA,QAAQ;MAClBsH,SAAS,EAAElE,aAAa;MACxB8D,OAAO,EAAExD,WAAW;MACpB1E,QAAQ,EAAE0E,WAAW;MACrB6D,MAAM,EAAE1D,UAAU;MAClBE,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAatH,KAAK,CAACkG,aAAa,CAAC,MAAM,EAAE;MAC3CrE,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,QAAQ;IAC1C,CAAC,EAAE,GAAG,CAAC,EAAE8E,QAAQ,CAAC;EACpB;;EAEA;EACA,IAAIuE,cAAc,GAAGlI,aAAa,GAAG,CAAC,GAAG,CAAC;EAC1C,IAAI2D,QAAQ,IAAI,CAAC,GAAGuE,cAAc,GAAG,CAAC,EAAE;IACtC,IAAI,CAACvE,QAAQ,EAAE;MACbuD,SAAS,CAACiB,IAAI,CAAE,aAAahL,KAAK,CAACkG,aAAa,CAAC9F,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyK,UAAU,EAAE;QAC/E7B,GAAG,EAAE,SAAS;QACd7H,IAAI,EAAE,CAAC;QACPuB,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACzE,SAAS,EAAE,gBAAgB;MAClD,CAAC,CAAC,CAAC,CAAC;IACN;IACA,KAAK,IAAIuJ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzE,QAAQ,EAAEyE,CAAC,IAAI,CAAC,EAAE;MACrClB,SAAS,CAACiB,IAAI,CAAE,aAAahL,KAAK,CAACkG,aAAa,CAAC9F,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyK,UAAU,EAAE;QAC/E7B,GAAG,EAAE8C,CAAC;QACN3K,IAAI,EAAE2K,CAAC;QACPC,MAAM,EAAEnJ,OAAO,KAAKkJ;MACtB,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,MAAM;IACL,IAAIE,aAAa,GAAGtI,aAAa,GAAGM,MAAM,CAACiI,MAAM,GAAGjI,MAAM,CAACkI,MAAM;IACjE,IAAIC,aAAa,GAAGzI,aAAa,GAAGM,MAAM,CAACoI,MAAM,GAAGpI,MAAM,CAACqI,MAAM;IACjE,IAAIC,eAAe,GAAG1H,UAAU,CAAC6B,YAAY,EAAE,WAAW,EAAEE,WAAW,CAAC9B,YAAY,EAAE,WAAW,CAAC,CAAC;IACnG,IAAI0H,eAAe,GAAG3H,UAAU,CAAC8B,YAAY,EAAE,WAAW,EAAEC,WAAW,CAAC7B,YAAY,EAAE,WAAW,CAAC,CAAC;IACnG,IAAItB,mBAAmB,EAAE;MACvB8G,QAAQ,GAAGgC,eAAe,GAAG,aAAazL,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;QAClEyE,KAAK,EAAE5H,SAAS,GAAGoI,aAAa,GAAG,IAAI;QACvChD,GAAG,EAAE,MAAM;QACX+B,OAAO,EAAEnC,cAAc;QACvB4D,QAAQ,EAAE,CAAC;QACXd,SAAS,EAAE/B,kBAAkB;QAC7BjH,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,YAAY,CAAC,EAAEpC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACzE,SAAS,EAAE,wBAAwB,CAAC,EAAE,CAAC,CAACsC,YAAY,CAAC;MAC/I,CAAC,EAAEyH,eAAe,CAAC,GAAG,IAAI;MAC1B3B,QAAQ,GAAG4B,eAAe,GAAG,aAAa1L,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;QAClEyE,KAAK,EAAE5H,SAAS,GAAGuI,aAAa,GAAG,IAAI;QACvCnD,GAAG,EAAE,MAAM;QACX+B,OAAO,EAAElC,cAAc;QACvB2D,QAAQ,EAAE,CAAC;QACXd,SAAS,EAAE9B,kBAAkB;QAC7BlH,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,YAAY,CAAC,EAAEpC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACzE,SAAS,EAAE,wBAAwB,CAAC,EAAE,CAAC,CAACuC,YAAY,CAAC;MAC/I,CAAC,EAAEyH,eAAe,CAAC,GAAG,IAAI;IAC5B;IACA,IAAIE,IAAI,GAAG5K,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAE9C,OAAO,GAAGgJ,cAAc,CAAC;IAChD,IAAIc,KAAK,GAAG7K,IAAI,CAAC8D,GAAG,CAAC/C,OAAO,GAAGgJ,cAAc,EAAEvE,QAAQ,CAAC;IACxD,IAAIzE,OAAO,GAAG,CAAC,IAAIgJ,cAAc,EAAE;MACjCc,KAAK,GAAG,CAAC,GAAGd,cAAc,GAAG,CAAC;IAChC;IACA,IAAIvE,QAAQ,GAAGzE,OAAO,IAAIgJ,cAAc,EAAE;MACxCa,IAAI,GAAGpF,QAAQ,GAAGuE,cAAc,GAAG,CAAC;IACtC;IACA,KAAK,IAAIe,EAAE,GAAGF,IAAI,EAAEE,EAAE,IAAID,KAAK,EAAEC,EAAE,IAAI,CAAC,EAAE;MACxC/B,SAAS,CAACiB,IAAI,CAAE,aAAahL,KAAK,CAACkG,aAAa,CAAC9F,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyK,UAAU,EAAE;QAC/E7B,GAAG,EAAE2D,EAAE;QACPxL,IAAI,EAAEwL,EAAE;QACRZ,MAAM,EAAEnJ,OAAO,KAAK+J;MACtB,CAAC,CAAC,CAAC,CAAC;IACN;IACA,IAAI/J,OAAO,GAAG,CAAC,IAAIgJ,cAAc,GAAG,CAAC,IAAIhJ,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE;MAC1DgI,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa/J,KAAK,CAACoJ,YAAY,CAACW,SAAS,CAAC,CAAC,CAAC,EAAE;QAC3DlI,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,uBAAuB,CAAC,EAAEqI,SAAS,CAAC,CAAC,CAAC,CAACvI,KAAK,CAACK,SAAS;MACnG,CAAC,CAAC;MACFkI,SAAS,CAACgC,OAAO,CAACtC,QAAQ,CAAC;IAC7B;IACA,IAAIjD,QAAQ,GAAGzE,OAAO,IAAIgJ,cAAc,GAAG,CAAC,IAAIhJ,OAAO,KAAKyE,QAAQ,GAAG,CAAC,EAAE;MACxE,IAAIwF,OAAO,GAAGjC,SAAS,CAACA,SAAS,CAACxB,MAAM,GAAG,CAAC,CAAC;MAC7CwB,SAAS,CAACA,SAAS,CAACxB,MAAM,GAAG,CAAC,CAAC,GAAG,aAAavI,KAAK,CAACoJ,YAAY,CAAC4C,OAAO,EAAE;QACzEnK,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,wBAAwB,CAAC,EAAEsK,OAAO,CAACxK,KAAK,CAACK,SAAS;MAC/F,CAAC,CAAC;MACFkI,SAAS,CAACiB,IAAI,CAAClB,QAAQ,CAAC;IAC1B;IACA,IAAI8B,IAAI,KAAK,CAAC,EAAE;MACd7B,SAAS,CAACgC,OAAO,CAAE,aAAa/L,KAAK,CAACkG,aAAa,CAAC9F,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyK,UAAU,EAAE;QAClF7B,GAAG,EAAE,CAAC;QACN7H,IAAI,EAAE;MACR,CAAC,CAAC,CAAC,CAAC;IACN;IACA,IAAIuL,KAAK,KAAKrF,QAAQ,EAAE;MACtBuD,SAAS,CAACiB,IAAI,CAAE,aAAahL,KAAK,CAACkG,aAAa,CAAC9F,KAAK,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyK,UAAU,EAAE;QAC/E7B,GAAG,EAAE3B,QAAQ;QACblG,IAAI,EAAEkG;MACR,CAAC,CAAC,CAAC,CAAC;IACN;EACF;EACA,IAAIyF,IAAI,GAAGjD,UAAU,CAACC,QAAQ,CAAC;EAC/B,IAAIgD,IAAI,EAAE;IACR,IAAIC,YAAY,GAAG,CAACvE,OAAO,IAAI,CAACnB,QAAQ;IACxCyF,IAAI,GAAG,aAAajM,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;MAC5CyE,KAAK,EAAE5H,SAAS,GAAGI,MAAM,CAACgJ,SAAS,GAAG,IAAI;MAC1CjC,OAAO,EAAErC,UAAU;MACnB8D,QAAQ,EAAEO,YAAY,GAAG,IAAI,GAAG,CAAC;MACjCrB,SAAS,EAAEjC,cAAc;MACzB/G,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,OAAO,CAAC,EAAEpC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACzE,SAAS,EAAE,WAAW,CAAC,EAAEwK,YAAY,CAAC,CAAC;MAC1H,eAAe,EAAEA;IACnB,CAAC,EAAED,IAAI,CAAC;EACV;EACA,IAAIG,IAAI,GAAG/C,UAAU,CAACC,QAAQ,CAAC;EAC/B,IAAI8C,IAAI,EAAE;IACR,IAAIC,YAAY,EAAEC,YAAY;IAC9B,IAAI9I,MAAM,EAAE;MACV6I,YAAY,GAAG,CAACzE,OAAO;MACvB0E,YAAY,GAAG3E,OAAO,GAAG,CAAC,GAAG,IAAI;IACnC,CAAC,MAAM;MACL0E,YAAY,GAAG,CAACzE,OAAO,IAAI,CAACpB,QAAQ;MACpC8F,YAAY,GAAGD,YAAY,GAAG,IAAI,GAAG,CAAC;IACxC;IACAD,IAAI,GAAG,aAAapM,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE;MAC5CyE,KAAK,EAAE5H,SAAS,GAAGI,MAAM,CAACoJ,SAAS,GAAG,IAAI;MAC1CrC,OAAO,EAAEpC,UAAU;MACnB6D,QAAQ,EAAEW,YAAY;MACtBzB,SAAS,EAAEhC,cAAc;MACzBhH,SAAS,EAAElC,UAAU,CAAC,EAAE,CAACwG,MAAM,CAACzE,SAAS,EAAE,OAAO,CAAC,EAAEpC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACzE,SAAS,EAAE,WAAW,CAAC,EAAE2K,YAAY,CAAC,CAAC;MAC1H,eAAe,EAAEA;IACnB,CAAC,EAAED,IAAI,CAAC;EACV;EACA,IAAII,GAAG,GAAG7M,UAAU,CAAC+B,SAAS,EAAEG,SAAS,EAAEvC,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACzE,SAAS,EAAE,QAAQ,CAAC,EAAEe,KAAK,KAAK,OAAO,CAAC,EAAE,EAAE,CAAC0D,MAAM,CAACzE,SAAS,EAAE,SAAS,CAAC,EAAEe,KAAK,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC0D,MAAM,CAACzE,SAAS,EAAE,MAAM,CAAC,EAAEe,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC0D,MAAM,CAACzE,SAAS,EAAE,SAAS,CAAC,EAAE8B,MAAM,CAAC,EAAE,EAAE,CAAC2C,MAAM,CAACzE,SAAS,EAAE,WAAW,CAAC,EAAE6B,QAAQ,CAAC,CAAC;EAChX,OAAO,aAAavD,KAAK,CAACkG,aAAa,CAAC,IAAI,EAAE3G,QAAQ,CAAC;IACrDsC,SAAS,EAAE2K,GAAG;IACdpJ,KAAK,EAAEA,KAAK;IACZqJ,GAAG,EAAErI;EACP,CAAC,EAAEsF,wBAAwB,CAAC,EAAEG,SAAS,EAAEoC,IAAI,EAAEzI,MAAM,GAAGgH,WAAW,GAAGT,SAAS,EAAEqC,IAAI,EAAE,aAAapM,KAAK,CAACkG,aAAa,CAAC/F,OAAO,EAAE;IAC/HgD,MAAM,EAAEA,MAAM;IACd8G,aAAa,EAAEvI,SAAS;IACxB6B,QAAQ,EAAEA,QAAQ;IAClB3B,eAAe,EAAEA,eAAe;IAChC8K,UAAU,EAAErF,cAAc;IAC1BjG,QAAQ,EAAEA,QAAQ;IAClByC,eAAe,EAAEA,eAAe;IAChC8I,OAAO,EAAEjG,wBAAwB,GAAGS,YAAY,GAAG,IAAI;IACvDiD,QAAQ,EAAEG,UAAU;IACpB5G,eAAe,EAAEA,eAAe;IAChCC,iBAAiB,EAAEA;EACrB,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAI6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpE,UAAU,CAACqL,WAAW,GAAG,YAAY;AACvC;AACA,eAAerL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nvar useFilterTreeData = function useFilterTreeData(treeData, searchValue, options) {\n  var fieldNames = options.fieldNames,\n    treeNodeFilterProp = options.treeNodeFilterProp,\n    filterTreeNode = options.filterTreeNode;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc = typeof filterTreeNode === 'function' ? filterTreeNode : function (_, dataNode) {\n      return String(dataNode[treeNodeFilterProp]).toUpperCase().includes(searchValue.toUpperCase());\n    };\n    var filterTreeNodes = function filterTreeNodes(nodes) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return nodes.reduce(function (filtered, node) {\n        var children = node[fieldChildren];\n        var isMatch = keepAll || filterOptionFunc(searchValue, fillLegacyProps(node));\n        var filteredChildren = filterTreeNodes(children || [], isMatch);\n        if (isMatch || filteredChildren.length) {\n          filtered.push(_objectSpread(_objectSpread({}, node), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, filteredChildren)));\n        }\n        return filtered;\n      }, []);\n    };\n    return filterTreeNodes(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n};\nexport default useFilterTreeData;", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "fillLegacyProps", "useFilterTreeData", "treeData", "searchValue", "options", "fieldNames", "treeNodeFilterProp", "filterTreeNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "useMemo", "filterOptionFunc", "_", "dataNode", "String", "toUpperCase", "includes", "filterTreeNodes", "nodes", "keepAll", "arguments", "length", "undefined", "reduce", "filtered", "node", "isMatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree-select@5.27.0_react_436633a6a2f00ab1ebd4c6e59066bdb2/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nvar useFilterTreeData = function useFilterTreeData(treeData, searchValue, options) {\n  var fieldNames = options.fieldNames,\n    treeNodeFilterProp = options.treeNodeFilterProp,\n    filterTreeNode = options.filterTreeNode;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc = typeof filterTreeNode === 'function' ? filterTreeNode : function (_, dataNode) {\n      return String(dataNode[treeNodeFilterProp]).toUpperCase().includes(searchValue.toUpperCase());\n    };\n    var filterTreeNodes = function filterTreeNodes(nodes) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return nodes.reduce(function (filtered, node) {\n        var children = node[fieldChildren];\n        var isMatch = keepAll || filterOptionFunc(searchValue, fillLegacyProps(node));\n        var filteredChildren = filterTreeNodes(children || [], isMatch);\n        if (isMatch || filteredChildren.length) {\n          filtered.push(_objectSpread(_objectSpread({}, node), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, filteredChildren)));\n        }\n        return filtered;\n      }, []);\n    };\n    return filterTreeNodes(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n};\nexport default useFilterTreeData;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,qBAAqB;AACrD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjF,IAAIC,UAAU,GAAGD,OAAO,CAACC,UAAU;IACjCC,kBAAkB,GAAGF,OAAO,CAACE,kBAAkB;IAC/CC,cAAc,GAAGH,OAAO,CAACG,cAAc;EACzC,IAAIC,aAAa,GAAGH,UAAU,CAACI,QAAQ;EACvC,OAAOV,KAAK,CAACW,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACP,WAAW,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5C,OAAOL,QAAQ;IACjB;IACA,IAAIS,gBAAgB,GAAG,OAAOJ,cAAc,KAAK,UAAU,GAAGA,cAAc,GAAG,UAAUK,CAAC,EAAEC,QAAQ,EAAE;MACpG,OAAOC,MAAM,CAACD,QAAQ,CAACP,kBAAkB,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,CAAC;IAC/F,CAAC;IACD,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;MACpD,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACvF,OAAOF,KAAK,CAACK,MAAM,CAAC,UAAUC,QAAQ,EAAEC,IAAI,EAAE;QAC5C,IAAIhB,QAAQ,GAAGgB,IAAI,CAACjB,aAAa,CAAC;QAClC,IAAIkB,OAAO,GAAGP,OAAO,IAAIR,gBAAgB,CAACR,WAAW,EAAEH,eAAe,CAACyB,IAAI,CAAC,CAAC;QAC7E,IAAIE,gBAAgB,GAAGV,eAAe,CAACR,QAAQ,IAAI,EAAE,EAAEiB,OAAO,CAAC;QAC/D,IAAIA,OAAO,IAAIC,gBAAgB,CAACN,MAAM,EAAE;UACtCG,QAAQ,CAACI,IAAI,CAAC9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE5B,eAAe,CAAC;YACvEgC,MAAM,EAAEP;UACV,CAAC,EAAEd,aAAa,EAAEmB,gBAAgB,CAAC,CAAC,CAAC;QACvC;QACA,OAAOH,QAAQ;MACjB,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IACD,OAAOP,eAAe,CAACf,QAAQ,CAAC;EAClC,CAAC,EAAE,CAACA,QAAQ,EAAEC,WAAW,EAAEK,aAAa,EAAEF,kBAAkB,EAAEC,cAAc,CAAC,CAAC;AAChF,CAAC;AACD,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
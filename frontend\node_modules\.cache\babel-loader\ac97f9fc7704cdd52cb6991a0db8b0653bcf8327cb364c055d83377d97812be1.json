{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;\nexport function hasValue(value) {\n  return value !== undefined && value !== null;\n}\n\n/** combo mode no value judgment function */\nexport function isComboNoValue(value) {\n  return !value && value !== 0;\n}\nfunction isTitleType(title) {\n  return ['string', 'number'].includes(_typeof(title));\n}\nexport function getTitle(item) {\n  var title = undefined;\n  if (item) {\n    if (isTitleType(item.title)) {\n      title = item.title.toString();\n    } else if (isTitleType(item.label)) {\n      title = item.label.toString();\n    }\n  }\n  return title;\n}", "map": {"version": 3, "names": ["_typeof", "toArray", "value", "Array", "isArray", "undefined", "isClient", "window", "document", "documentElement", "isBrowserClient", "process", "env", "NODE_ENV", "hasValue", "isComboNoValue", "isTitleType", "title", "includes", "getTitle", "item", "toString", "label"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/utils/commonUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;\nexport function hasValue(value) {\n  return value !== undefined && value !== null;\n}\n\n/** combo mode no value judgment function */\nexport function isComboNoValue(value) {\n  return !value && value !== 0;\n}\nfunction isTitleType(title) {\n  return ['string', 'number'].includes(_typeof(title));\n}\nexport function getTitle(item) {\n  var title = undefined;\n  if (item) {\n    if (isTitleType(item.title)) {\n      title = item.title.toString();\n    } else if (isTitleType(item.label)) {\n      title = item.label.toString();\n    }\n  }\n  return title;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EACA,OAAOA,KAAK,KAAKG,SAAS,GAAG,CAACH,KAAK,CAAC,GAAG,EAAE;AAC3C;AACA,OAAO,IAAII,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,eAAe;;AAEzG;AACA,OAAO,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIP,QAAQ;AACxE,OAAO,SAASQ,QAAQA,CAACZ,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAK,IAAI;AAC9C;;AAEA;AACA,OAAO,SAASa,cAAcA,CAACb,KAAK,EAAE;EACpC,OAAO,CAACA,KAAK,IAAIA,KAAK,KAAK,CAAC;AAC9B;AACA,SAASc,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAAClB,OAAO,CAACiB,KAAK,CAAC,CAAC;AACtD;AACA,OAAO,SAASE,QAAQA,CAACC,IAAI,EAAE;EAC7B,IAAIH,KAAK,GAAGZ,SAAS;EACrB,IAAIe,IAAI,EAAE;IACR,IAAIJ,WAAW,CAACI,IAAI,CAACH,KAAK,CAAC,EAAE;MAC3BA,KAAK,GAAGG,IAAI,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIL,WAAW,CAACI,IAAI,CAACE,KAAK,CAAC,EAAE;MAClCL,KAAK,GAAGG,IAAI,CAACE,KAAK,CAACD,QAAQ,CAAC,CAAC;IAC/B;EACF;EACA,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
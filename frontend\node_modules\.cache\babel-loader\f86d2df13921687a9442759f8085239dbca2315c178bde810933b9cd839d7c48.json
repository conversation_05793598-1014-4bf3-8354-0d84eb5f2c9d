{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    horizontalLineHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    itemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: horizontalLineHeight,\n      border: 0,\n      borderBottom: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: itemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;", "map": {"version": 3, "names": ["unit", "getHorizontalStyle", "token", "componentCls", "motionDurationSlow", "horizontalLineHeight", "colorSplit", "lineWidth", "lineType", "itemPaddingInline", "lineHeight", "border", "borderBottom", "boxShadow", "display", "clear", "height", "content", "position", "verticalAlign", "paddingInline", "backgroundColor", "transition", "join"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/menu/style/horizontal.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    horizontalLineHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    itemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: horizontalLineHeight,\n      border: 0,\n      borderBottom: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: itemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC,kBAAkB;IAClBC,oBAAoB;IACpBC,UAAU;IACVC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGP,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,aAAa,GAAG;MAC9BO,UAAU,EAAEL,oBAAoB;MAChCM,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,GAAGZ,IAAI,CAACO,SAAS,CAAC,IAAIC,QAAQ,IAAIF,UAAU,EAAE;MAC5DO,SAAS,EAAE,MAAM;MACjB,UAAU,EAAE;QACVC,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;MACD;MACA,CAAC,GAAGd,YAAY,UAAUA,YAAY,UAAU,GAAG;QACjDe,QAAQ,EAAE,UAAU;QACpBJ,OAAO,EAAE,cAAc;QACvBK,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAEX;MACjB,CAAC;MACD,CAAC,KAAKN,YAAY;AACxB,YAAYA,YAAY;AACxB,YAAYA,YAAY,YAAYA,YAAY,sBAAsB,GAAG;QACjEkB,eAAe,EAAE;MACnB,CAAC;MACD,CAAC,GAAGlB,YAAY,UAAUA,YAAY,gBAAgB,GAAG;QACvDmB,UAAU,EAAE,CAAC,gBAAgBlB,kBAAkB,EAAE,EAAE,cAAcA,kBAAkB,EAAE,CAAC,CAACmB,IAAI,CAAC,GAAG;MACjG,CAAC;MACD;MACA,CAAC,GAAGpB,YAAY,gBAAgB,GAAG;QACjCW,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
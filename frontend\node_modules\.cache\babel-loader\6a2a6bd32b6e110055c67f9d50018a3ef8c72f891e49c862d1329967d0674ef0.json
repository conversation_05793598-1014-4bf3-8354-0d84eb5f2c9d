{"ast": null, "code": "import { getDOM } from \"rc-util/es/Dom/findDOMNode\";\n\n// Copy from `rc-util/Dom/css.js`\nexport function getOffset(node) {\n  var element = getDOM(node);\n  var box = element.getBoundingClientRect();\n  var docElem = document.documentElement;\n\n  // < ie8 not support win.pageXOffset, use docElem.scrollLeft instead\n  return {\n    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),\n    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)\n  };\n}", "map": {"version": 3, "names": ["getDOM", "getOffset", "node", "element", "box", "getBoundingClientRect", "doc<PERSON><PERSON>", "document", "documentElement", "left", "window", "pageXOffset", "scrollLeft", "clientLeft", "body", "top", "pageYOffset", "scrollTop", "clientTop"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/utils/offsetUtil.js"], "sourcesContent": ["import { getDOM } from \"rc-util/es/Dom/findDOMNode\";\n\n// Copy from `rc-util/Dom/css.js`\nexport function getOffset(node) {\n  var element = getDOM(node);\n  var box = element.getBoundingClientRect();\n  var docElem = document.documentElement;\n\n  // < ie8 not support win.pageXOffset, use docElem.scrollLeft instead\n  return {\n    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),\n    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,4BAA4B;;AAEnD;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAIC,OAAO,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,IAAIE,GAAG,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;EACzC,IAAIC,OAAO,GAAGC,QAAQ,CAACC,eAAe;;EAEtC;EACA,OAAO;IACLC,IAAI,EAAEL,GAAG,CAACK,IAAI,IAAIC,MAAM,CAACC,WAAW,IAAIL,OAAO,CAACM,UAAU,CAAC,IAAIN,OAAO,CAACO,UAAU,IAAIN,QAAQ,CAACO,IAAI,CAACD,UAAU,IAAI,CAAC,CAAC;IACnHE,GAAG,EAAEX,GAAG,CAACW,GAAG,IAAIL,MAAM,CAACM,WAAW,IAAIV,OAAO,CAACW,SAAS,CAAC,IAAIX,OAAO,CAACY,SAAS,IAAIX,QAAQ,CAACO,IAAI,CAACI,SAAS,IAAI,CAAC;EAC/G,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
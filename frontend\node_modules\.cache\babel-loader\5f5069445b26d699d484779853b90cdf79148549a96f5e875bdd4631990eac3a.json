{"ast": null, "code": "import React from 'react';\nexport const ModalContext = /*#__PURE__*/React.createContext({});\nexport const {\n  Provider: ModalContextProvider\n} = ModalContext;", "map": {"version": 3, "names": ["React", "ModalContext", "createContext", "Provider", "ModalContextProvider"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/modal/context.js"], "sourcesContent": ["import React from 'react';\nexport const ModalContext = /*#__PURE__*/React.createContext({});\nexport const {\n  Provider: ModalContextProvider\n} = ModalContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAChE,OAAO,MAAM;EACXC,QAAQ,EAAEC;AACZ,CAAC,GAAGH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
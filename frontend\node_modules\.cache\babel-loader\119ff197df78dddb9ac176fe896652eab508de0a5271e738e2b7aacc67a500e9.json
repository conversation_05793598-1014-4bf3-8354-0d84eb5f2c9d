{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"getContainer\", \"motion\", \"prefixCls\", \"maxCount\", \"className\", \"style\", \"onAllRemoved\", \"stack\", \"renderNotifications\"];\nimport * as React from 'react';\nimport Notifications from \"../Notifications\";\nimport { useEvent } from 'rc-util';\nvar defaultGetContainer = function defaultGetContainer() {\n  return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n  var clone = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          clone[key] = val;\n        }\n      });\n    }\n  });\n  return clone;\n}\nexport default function useNotification() {\n  var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _rootConfig$getContai = rootConfig.getContainer,\n    getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai,\n    motion = rootConfig.motion,\n    prefixCls = rootConfig.prefixCls,\n    maxCount = rootConfig.maxCount,\n    className = rootConfig.className,\n    style = rootConfig.style,\n    onAllRemoved = rootConfig.onAllRemoved,\n    stack = rootConfig.stack,\n    renderNotifications = rootConfig.renderNotifications,\n    shareConfig = _objectWithoutProperties(rootConfig, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    container = _React$useState2[0],\n    setContainer = _React$useState2[1];\n  var notificationsRef = React.useRef();\n  var contextHolder = /*#__PURE__*/React.createElement(Notifications, {\n    container: container,\n    ref: notificationsRef,\n    prefixCls: prefixCls,\n    motion: motion,\n    maxCount: maxCount,\n    className: className,\n    style: style,\n    onAllRemoved: onAllRemoved,\n    stack: stack,\n    renderNotifications: renderNotifications\n  });\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    taskQueue = _React$useState4[0],\n    setTaskQueue = _React$useState4[1];\n  var open = useEvent(function (config) {\n    var mergedConfig = mergeConfig(shareConfig, config);\n    if (mergedConfig.key === null || mergedConfig.key === undefined) {\n      mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n      uniqueKey += 1;\n    }\n    setTaskQueue(function (queue) {\n      return [].concat(_toConsumableArray(queue), [{\n        type: 'open',\n        config: mergedConfig\n      }]);\n    });\n  });\n\n  // ========================= Refs =========================\n  var api = React.useMemo(function () {\n    return {\n      open: open,\n      close: function close(key) {\n        setTaskQueue(function (queue) {\n          return [].concat(_toConsumableArray(queue), [{\n            type: 'close',\n            key: key\n          }]);\n        });\n      },\n      destroy: function destroy() {\n        setTaskQueue(function (queue) {\n          return [].concat(_toConsumableArray(queue), [{\n            type: 'destroy'\n          }]);\n        });\n      }\n    };\n  }, []);\n\n  // ======================= Container ======================\n  // React 18 should all in effect that we will check container in each render\n  // Which means getContainer should be stable.\n  React.useEffect(function () {\n    setContainer(getContainer());\n  });\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    // Flush task when node ready\n    if (notificationsRef.current && taskQueue.length) {\n      taskQueue.forEach(function (task) {\n        switch (task.type) {\n          case 'open':\n            notificationsRef.current.open(task.config);\n            break;\n          case 'close':\n            notificationsRef.current.close(task.key);\n            break;\n          case 'destroy':\n            notificationsRef.current.destroy();\n            break;\n        }\n      });\n\n      // https://github.com/ant-design/ant-design/issues/52590\n      // React `startTransition` will run once `useEffect` but many times `setState`,\n      // So `setTaskQueue` with filtered array will cause infinite loop.\n      // We cache the first match queue instead.\n      var oriTaskQueue;\n      var tgtTaskQueue;\n\n      // React 17 will mix order of effect & setState in async\n      // - open: setState[0]\n      // - effect[0]\n      // - open: setState[1]\n      // - effect setState([]) * here will clean up [0, 1] in React 17\n      setTaskQueue(function (oriQueue) {\n        if (oriTaskQueue !== oriQueue || !tgtTaskQueue) {\n          oriTaskQueue = oriQueue;\n          tgtTaskQueue = oriQueue.filter(function (task) {\n            return !taskQueue.includes(task);\n          });\n        }\n        return tgtTaskQueue;\n      });\n    }\n  }, [taskQueue]);\n\n  // ======================== Return ========================\n  return [api, contextHolder];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "Notifications", "useEvent", "defaultGetContainer", "document", "body", "<PERSON><PERSON><PERSON>", "mergeConfig", "clone", "_len", "arguments", "length", "objList", "Array", "_key", "for<PERSON>ach", "obj", "Object", "keys", "key", "val", "undefined", "useNotification", "rootConfig", "_rootConfig$getContai", "getContainer", "motion", "prefixCls", "maxCount", "className", "style", "onAllRemoved", "stack", "renderNotifications", "shareConfig", "_React$useState", "useState", "_React$useState2", "container", "<PERSON><PERSON><PERSON><PERSON>", "notificationsRef", "useRef", "contextHolder", "createElement", "ref", "_React$useState3", "_React$useState4", "taskQueue", "setTaskQueue", "open", "config", "mergedConfig", "concat", "queue", "type", "api", "useMemo", "close", "destroy", "useEffect", "current", "task", "oriTaskQueue", "tgtTaskQueue", "oriQueue", "filter", "includes"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-notification@5.6.4_react_4c4d643d02667c590803e33b81c2ec6f/node_modules/rc-notification/es/hooks/useNotification.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"getContainer\", \"motion\", \"prefixCls\", \"maxCount\", \"className\", \"style\", \"onAllRemoved\", \"stack\", \"renderNotifications\"];\nimport * as React from 'react';\nimport Notifications from \"../Notifications\";\nimport { useEvent } from 'rc-util';\nvar defaultGetContainer = function defaultGetContainer() {\n  return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n  var clone = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          clone[key] = val;\n        }\n      });\n    }\n  });\n  return clone;\n}\nexport default function useNotification() {\n  var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _rootConfig$getContai = rootConfig.getContainer,\n    getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai,\n    motion = rootConfig.motion,\n    prefixCls = rootConfig.prefixCls,\n    maxCount = rootConfig.maxCount,\n    className = rootConfig.className,\n    style = rootConfig.style,\n    onAllRemoved = rootConfig.onAllRemoved,\n    stack = rootConfig.stack,\n    renderNotifications = rootConfig.renderNotifications,\n    shareConfig = _objectWithoutProperties(rootConfig, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    container = _React$useState2[0],\n    setContainer = _React$useState2[1];\n  var notificationsRef = React.useRef();\n  var contextHolder = /*#__PURE__*/React.createElement(Notifications, {\n    container: container,\n    ref: notificationsRef,\n    prefixCls: prefixCls,\n    motion: motion,\n    maxCount: maxCount,\n    className: className,\n    style: style,\n    onAllRemoved: onAllRemoved,\n    stack: stack,\n    renderNotifications: renderNotifications\n  });\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    taskQueue = _React$useState4[0],\n    setTaskQueue = _React$useState4[1];\n  var open = useEvent(function (config) {\n    var mergedConfig = mergeConfig(shareConfig, config);\n    if (mergedConfig.key === null || mergedConfig.key === undefined) {\n      mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n      uniqueKey += 1;\n    }\n    setTaskQueue(function (queue) {\n      return [].concat(_toConsumableArray(queue), [{\n        type: 'open',\n        config: mergedConfig\n      }]);\n    });\n  });\n\n  // ========================= Refs =========================\n  var api = React.useMemo(function () {\n    return {\n      open: open,\n      close: function close(key) {\n        setTaskQueue(function (queue) {\n          return [].concat(_toConsumableArray(queue), [{\n            type: 'close',\n            key: key\n          }]);\n        });\n      },\n      destroy: function destroy() {\n        setTaskQueue(function (queue) {\n          return [].concat(_toConsumableArray(queue), [{\n            type: 'destroy'\n          }]);\n        });\n      }\n    };\n  }, []);\n\n  // ======================= Container ======================\n  // React 18 should all in effect that we will check container in each render\n  // Which means getContainer should be stable.\n  React.useEffect(function () {\n    setContainer(getContainer());\n  });\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    // Flush task when node ready\n    if (notificationsRef.current && taskQueue.length) {\n      taskQueue.forEach(function (task) {\n        switch (task.type) {\n          case 'open':\n            notificationsRef.current.open(task.config);\n            break;\n          case 'close':\n            notificationsRef.current.close(task.key);\n            break;\n          case 'destroy':\n            notificationsRef.current.destroy();\n            break;\n        }\n      });\n\n      // https://github.com/ant-design/ant-design/issues/52590\n      // React `startTransition` will run once `useEffect` but many times `setState`,\n      // So `setTaskQueue` with filtered array will cause infinite loop.\n      // We cache the first match queue instead.\n      var oriTaskQueue;\n      var tgtTaskQueue;\n\n      // React 17 will mix order of effect & setState in async\n      // - open: setState[0]\n      // - effect[0]\n      // - open: setState[1]\n      // - effect setState([]) * here will clean up [0, 1] in React 17\n      setTaskQueue(function (oriQueue) {\n        if (oriTaskQueue !== oriQueue || !tgtTaskQueue) {\n          oriTaskQueue = oriQueue;\n          tgtTaskQueue = oriQueue.filter(function (task) {\n            return !taskQueue.includes(task);\n          });\n        }\n        return tgtTaskQueue;\n      });\n    }\n  }, [taskQueue]);\n\n  // ======================== Return ========================\n  return [api, contextHolder];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,qBAAqB,CAAC;AACzI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,SAAS;AAClC,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;EACvD,OAAOC,QAAQ,CAACC,IAAI;AACtB,CAAC;AACD,IAAIC,SAAS,GAAG,CAAC;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IAC1FF,OAAO,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EACjC;EACAF,OAAO,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7B,IAAIA,GAAG,EAAE;MACPC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACD,OAAO,CAAC,UAAUI,GAAG,EAAE;QACtC,IAAIC,GAAG,GAAGJ,GAAG,CAACG,GAAG,CAAC;QAClB,IAAIC,GAAG,KAAKC,SAAS,EAAE;UACrBb,KAAK,CAACW,GAAG,CAAC,GAAGC,GAAG;QAClB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOZ,KAAK;AACd;AACA,eAAe,SAASc,eAAeA,CAAA,EAAG;EACxC,IAAIC,UAAU,GAAGb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKW,SAAS,GAAGX,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvF,IAAIc,qBAAqB,GAAGD,UAAU,CAACE,YAAY;IACjDA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrB,mBAAmB,GAAGqB,qBAAqB;IAC7FE,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC1BC,SAAS,GAAGJ,UAAU,CAACI,SAAS;IAChCC,QAAQ,GAAGL,UAAU,CAACK,QAAQ;IAC9BC,SAAS,GAAGN,UAAU,CAACM,SAAS;IAChCC,KAAK,GAAGP,UAAU,CAACO,KAAK;IACxBC,YAAY,GAAGR,UAAU,CAACQ,YAAY;IACtCC,KAAK,GAAGT,UAAU,CAACS,KAAK;IACxBC,mBAAmB,GAAGV,UAAU,CAACU,mBAAmB;IACpDC,WAAW,GAAGpC,wBAAwB,CAACyB,UAAU,EAAExB,SAAS,CAAC;EAC/D,IAAIoC,eAAe,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,CAAC;IACpCC,gBAAgB,GAAGxC,cAAc,CAACsC,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAGxC,KAAK,CAACyC,MAAM,CAAC,CAAC;EACrC,IAAIC,aAAa,GAAG,aAAa1C,KAAK,CAAC2C,aAAa,CAAC1C,aAAa,EAAE;IAClEqC,SAAS,EAAEA,SAAS;IACpBM,GAAG,EAAEJ,gBAAgB;IACrBb,SAAS,EAAEA,SAAS;IACpBD,MAAM,EAAEA,MAAM;IACdE,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA,KAAK;IACZC,YAAY,EAAEA,YAAY;IAC1BC,KAAK,EAAEA,KAAK;IACZC,mBAAmB,EAAEA;EACvB,CAAC,CAAC;EACF,IAAIY,gBAAgB,GAAG7C,KAAK,CAACoC,QAAQ,CAAC,EAAE,CAAC;IACvCU,gBAAgB,GAAGjD,cAAc,CAACgD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,IAAI,GAAG/C,QAAQ,CAAC,UAAUgD,MAAM,EAAE;IACpC,IAAIC,YAAY,GAAG5C,WAAW,CAAC2B,WAAW,EAAEgB,MAAM,CAAC;IACnD,IAAIC,YAAY,CAAChC,GAAG,KAAK,IAAI,IAAIgC,YAAY,CAAChC,GAAG,KAAKE,SAAS,EAAE;MAC/D8B,YAAY,CAAChC,GAAG,GAAG,kBAAkB,CAACiC,MAAM,CAAC9C,SAAS,CAAC;MACvDA,SAAS,IAAI,CAAC;IAChB;IACA0C,YAAY,CAAC,UAAUK,KAAK,EAAE;MAC5B,OAAO,EAAE,CAACD,MAAM,CAACxD,kBAAkB,CAACyD,KAAK,CAAC,EAAE,CAAC;QAC3CC,IAAI,EAAE,MAAM;QACZJ,MAAM,EAAEC;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,IAAII,GAAG,GAAGvD,KAAK,CAACwD,OAAO,CAAC,YAAY;IAClC,OAAO;MACLP,IAAI,EAAEA,IAAI;MACVQ,KAAK,EAAE,SAASA,KAAKA,CAACtC,GAAG,EAAE;QACzB6B,YAAY,CAAC,UAAUK,KAAK,EAAE;UAC5B,OAAO,EAAE,CAACD,MAAM,CAACxD,kBAAkB,CAACyD,KAAK,CAAC,EAAE,CAAC;YAC3CC,IAAI,EAAE,OAAO;YACbnC,GAAG,EAAEA;UACP,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ,CAAC;MACDuC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BV,YAAY,CAAC,UAAUK,KAAK,EAAE;UAC5B,OAAO,EAAE,CAACD,MAAM,CAACxD,kBAAkB,CAACyD,KAAK,CAAC,EAAE,CAAC;YAC3CC,IAAI,EAAE;UACR,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACAtD,KAAK,CAAC2D,SAAS,CAAC,YAAY;IAC1BpB,YAAY,CAACd,YAAY,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC;;EAEF;EACAzB,KAAK,CAAC2D,SAAS,CAAC,YAAY;IAC1B;IACA,IAAInB,gBAAgB,CAACoB,OAAO,IAAIb,SAAS,CAACpC,MAAM,EAAE;MAChDoC,SAAS,CAAChC,OAAO,CAAC,UAAU8C,IAAI,EAAE;QAChC,QAAQA,IAAI,CAACP,IAAI;UACf,KAAK,MAAM;YACTd,gBAAgB,CAACoB,OAAO,CAACX,IAAI,CAACY,IAAI,CAACX,MAAM,CAAC;YAC1C;UACF,KAAK,OAAO;YACVV,gBAAgB,CAACoB,OAAO,CAACH,KAAK,CAACI,IAAI,CAAC1C,GAAG,CAAC;YACxC;UACF,KAAK,SAAS;YACZqB,gBAAgB,CAACoB,OAAO,CAACF,OAAO,CAAC,CAAC;YAClC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA;MACA;MACA;MACA,IAAII,YAAY;MAChB,IAAIC,YAAY;;MAEhB;MACA;MACA;MACA;MACA;MACAf,YAAY,CAAC,UAAUgB,QAAQ,EAAE;QAC/B,IAAIF,YAAY,KAAKE,QAAQ,IAAI,CAACD,YAAY,EAAE;UAC9CD,YAAY,GAAGE,QAAQ;UACvBD,YAAY,GAAGC,QAAQ,CAACC,MAAM,CAAC,UAAUJ,IAAI,EAAE;YAC7C,OAAO,CAACd,SAAS,CAACmB,QAAQ,CAACL,IAAI,CAAC;UAClC,CAAC,CAAC;QACJ;QACA,OAAOE,YAAY;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;;EAEf;EACA,OAAO,CAACQ,GAAG,EAAEb,aAAa,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useTimeInfo from \"../../hooks/useTimeInfo\";\nimport { fillTime } from \"../../utils/dateUtil\";\nimport DatePanel from \"../DatePanel\";\nimport TimePanel from \"../TimePanel\";\nexport default function DateTimePanel(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    showTime = props.showTime,\n    onSelect = props.onSelect,\n    value = props.value,\n    pickerValue = props.pickerValue,\n    onHover = props.onHover;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-datetime-panel\");\n\n  // =============================== Time ===============================\n  var _useTimeInfo = useTimeInfo(generateConfig, showTime),\n    _useTimeInfo2 = _slicedToArray(_useTimeInfo, 1),\n    getValidTime = _useTimeInfo2[0];\n\n  // Merge the time info from `value` or `pickerValue`\n  var mergeTime = function mergeTime(date) {\n    if (value) {\n      return fillTime(generateConfig, date, value);\n    }\n    return fillTime(generateConfig, date, pickerValue);\n  };\n\n  // ============================== Hover ===============================\n  var onDateHover = function onDateHover(date) {\n    onHover === null || onHover === void 0 || onHover(date ? mergeTime(date) : date);\n  };\n\n  // ============================== Select ==============================\n  var onDateSelect = function onDateSelect(date) {\n    // Merge with current time\n    var cloneDate = mergeTime(date);\n    onSelect(getValidTime(cloneDate, cloneDate));\n  };\n\n  // ============================== Render ==============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(DatePanel, _extends({}, props, {\n    onSelect: onDateSelect,\n    onHover: onDateHover\n  })), /*#__PURE__*/React.createElement(TimePanel, props));\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "useTimeInfo", "fillTime", "DatePanel", "TimePanel", "DateTimePanel", "props", "prefixCls", "generateConfig", "showTime", "onSelect", "value", "picker<PERSON><PERSON><PERSON>", "onHover", "panelPrefixCls", "concat", "_useTimeInfo", "_useTimeInfo2", "getValidTime", "mergeTime", "date", "onDateHover", "onDateSelect", "cloneDate", "createElement", "className"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerPanel/DateTimePanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useTimeInfo from \"../../hooks/useTimeInfo\";\nimport { fillTime } from \"../../utils/dateUtil\";\nimport DatePanel from \"../DatePanel\";\nimport TimePanel from \"../TimePanel\";\nexport default function DateTimePanel(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    showTime = props.showTime,\n    onSelect = props.onSelect,\n    value = props.value,\n    pickerValue = props.pickerValue,\n    onHover = props.onHover;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-datetime-panel\");\n\n  // =============================== Time ===============================\n  var _useTimeInfo = useTimeInfo(generateConfig, showTime),\n    _useTimeInfo2 = _slicedToArray(_useTimeInfo, 1),\n    getValidTime = _useTimeInfo2[0];\n\n  // Merge the time info from `value` or `pickerValue`\n  var mergeTime = function mergeTime(date) {\n    if (value) {\n      return fillTime(generateConfig, date, value);\n    }\n    return fillTime(generateConfig, date, pickerValue);\n  };\n\n  // ============================== Hover ===============================\n  var onDateHover = function onDateHover(date) {\n    onHover === null || onHover === void 0 || onHover(date ? mergeTime(date) : date);\n  };\n\n  // ============================== Select ==============================\n  var onDateSelect = function onDateSelect(date) {\n    // Merge with current time\n    var cloneDate = mergeTime(date);\n    onSelect(getValidTime(cloneDate, cloneDate));\n  };\n\n  // ============================== Render ==============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(DatePanel, _extends({}, props, {\n    onSelect: onDateSelect,\n    onHover: onDateHover\n  })), /*#__PURE__*/React.createElement(TimePanel, props));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,SAAS,MAAM,cAAc;AACpC,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,OAAO,GAAGP,KAAK,CAACO,OAAO;EACzB,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,iBAAiB,CAAC;;EAE5D;EACA,IAAIS,YAAY,GAAGf,WAAW,CAACO,cAAc,EAAEC,QAAQ,CAAC;IACtDQ,aAAa,GAAGlB,cAAc,CAACiB,YAAY,EAAE,CAAC,CAAC;IAC/CE,YAAY,GAAGD,aAAa,CAAC,CAAC,CAAC;;EAEjC;EACA,IAAIE,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;IACvC,IAAIT,KAAK,EAAE;MACT,OAAOT,QAAQ,CAACM,cAAc,EAAEY,IAAI,EAAET,KAAK,CAAC;IAC9C;IACA,OAAOT,QAAQ,CAACM,cAAc,EAAEY,IAAI,EAAER,WAAW,CAAC;EACpD,CAAC;;EAED;EACA,IAAIS,WAAW,GAAG,SAASA,WAAWA,CAACD,IAAI,EAAE;IAC3CP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACO,IAAI,GAAGD,SAAS,CAACC,IAAI,CAAC,GAAGA,IAAI,CAAC;EAClF,CAAC;;EAED;EACA,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACF,IAAI,EAAE;IAC7C;IACA,IAAIG,SAAS,GAAGJ,SAAS,CAACC,IAAI,CAAC;IAC/BV,QAAQ,CAACQ,YAAY,CAACK,SAAS,EAAEA,SAAS,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,OAAO,aAAavB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEX;EACb,CAAC,EAAE,aAAad,KAAK,CAACwB,aAAa,CAACrB,SAAS,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IACjEI,QAAQ,EAAEY,YAAY;IACtBT,OAAO,EAAEQ;EACX,CAAC,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACwB,aAAa,CAACpB,SAAS,EAAEE,KAAK,CAAC,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
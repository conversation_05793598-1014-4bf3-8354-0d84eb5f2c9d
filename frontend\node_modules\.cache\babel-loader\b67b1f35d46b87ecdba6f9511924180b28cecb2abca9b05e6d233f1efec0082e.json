{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { fillIndex } from \"../../utils/miscUtil\";\nimport * as React from 'react';\n/**\n * Used to control each fields invalidate status\n */\nexport default function useFieldsInvalidate(calendarValue, isInvalidateDate) {\n  var allowEmpty = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var _React$useState = React.useState([false, false]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    fieldsInvalidates = _React$useState2[0],\n    setFieldsInvalidates = _React$useState2[1];\n  var onSelectorInvalid = function onSelectorInvalid(invalid, index) {\n    setFieldsInvalidates(function (ori) {\n      return fillIndex(ori, index, invalid);\n    });\n  };\n\n  /**\n   * For the Selector Input to mark as `aria-disabled`\n   */\n  var submitInvalidates = React.useMemo(function () {\n    return fieldsInvalidates.map(function (invalid, index) {\n      // If typing invalidate\n      if (invalid) {\n        return true;\n      }\n      var current = calendarValue[index];\n\n      // Not check if all empty\n      if (!current) {\n        return false;\n      }\n\n      // Not allow empty\n      if (!allowEmpty[index] && !current) {\n        return true;\n      }\n\n      // Invalidate\n      if (current && isInvalidateDate(current, {\n        activeIndex: index\n      })) {\n        return true;\n      }\n      return false;\n    });\n  }, [calendarValue, fieldsInvalidates, isInvalidateDate, allowEmpty]);\n  return [submitInvalidates, onSelectorInvalid];\n}", "map": {"version": 3, "names": ["_slicedToArray", "fillIndex", "React", "useFieldsInvalidate", "calendarValue", "isInvalidateDate", "allowEmpty", "arguments", "length", "undefined", "_React$useState", "useState", "_React$useState2", "fieldsInvalidates", "setFieldsInvalidates", "onSelectorInvalid", "invalid", "index", "ori", "submitInvalidates", "useMemo", "map", "current", "activeIndex"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useFieldsInvalidate.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { fillIndex } from \"../../utils/miscUtil\";\nimport * as React from 'react';\n/**\n * Used to control each fields invalidate status\n */\nexport default function useFieldsInvalidate(calendarValue, isInvalidateDate) {\n  var allowEmpty = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var _React$useState = React.useState([false, false]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    fieldsInvalidates = _React$useState2[0],\n    setFieldsInvalidates = _React$useState2[1];\n  var onSelectorInvalid = function onSelectorInvalid(invalid, index) {\n    setFieldsInvalidates(function (ori) {\n      return fillIndex(ori, index, invalid);\n    });\n  };\n\n  /**\n   * For the Selector Input to mark as `aria-disabled`\n   */\n  var submitInvalidates = React.useMemo(function () {\n    return fieldsInvalidates.map(function (invalid, index) {\n      // If typing invalidate\n      if (invalid) {\n        return true;\n      }\n      var current = calendarValue[index];\n\n      // Not check if all empty\n      if (!current) {\n        return false;\n      }\n\n      // Not allow empty\n      if (!allowEmpty[index] && !current) {\n        return true;\n      }\n\n      // Invalidate\n      if (current && isInvalidateDate(current, {\n        activeIndex: index\n      })) {\n        return true;\n      }\n      return false;\n    });\n  }, [calendarValue, fieldsInvalidates, isInvalidateDate, allowEmpty]);\n  return [submitInvalidates, onSelectorInvalid];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,aAAa,EAAEC,gBAAgB,EAAE;EAC3E,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACvF,IAAIG,eAAe,GAAGR,KAAK,CAACS,QAAQ,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClDC,gBAAgB,GAAGZ,cAAc,CAACU,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,OAAO,EAAEC,KAAK,EAAE;IACjEH,oBAAoB,CAAC,UAAUI,GAAG,EAAE;MAClC,OAAOjB,SAAS,CAACiB,GAAG,EAAED,KAAK,EAAED,OAAO,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;EACE,IAAIG,iBAAiB,GAAGjB,KAAK,CAACkB,OAAO,CAAC,YAAY;IAChD,OAAOP,iBAAiB,CAACQ,GAAG,CAAC,UAAUL,OAAO,EAAEC,KAAK,EAAE;MACrD;MACA,IAAID,OAAO,EAAE;QACX,OAAO,IAAI;MACb;MACA,IAAIM,OAAO,GAAGlB,aAAa,CAACa,KAAK,CAAC;;MAElC;MACA,IAAI,CAACK,OAAO,EAAE;QACZ,OAAO,KAAK;MACd;;MAEA;MACA,IAAI,CAAChB,UAAU,CAACW,KAAK,CAAC,IAAI,CAACK,OAAO,EAAE;QAClC,OAAO,IAAI;MACb;;MAEA;MACA,IAAIA,OAAO,IAAIjB,gBAAgB,CAACiB,OAAO,EAAE;QACvCC,WAAW,EAAEN;MACf,CAAC,CAAC,EAAE;QACF,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACb,aAAa,EAAES,iBAAiB,EAAER,gBAAgB,EAAEC,UAAU,CAAC,CAAC;EACpE,OAAO,CAACa,iBAAiB,EAAEJ,iBAAiB,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from 'react';\nvar Palette = function Palette(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    prefixCls = _ref.prefixCls;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-palette\"),\n    style: _objectSpread({\n      position: 'relative'\n    }, style)\n  }, children);\n};\nexport default Palette;", "map": {"version": 3, "names": ["_objectSpread", "React", "Palette", "_ref", "children", "style", "prefixCls", "createElement", "className", "concat", "position"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+color-picker@_34570774f6314db73ae9bf418879662f/node_modules/@rc-component/color-picker/es/components/Palette.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from 'react';\nvar Palette = function Palette(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    prefixCls = _ref.prefixCls;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-palette\"),\n    style: _objectSpread({\n      position: 'relative'\n    }, style)\n  }, children);\n};\nexport default Palette;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnC,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,SAAS,GAAGH,IAAI,CAACG,SAAS;EAC5B,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACH,SAAS,EAAE,UAAU,CAAC;IAC3CD,KAAK,EAAEL,aAAa,CAAC;MACnBU,QAAQ,EAAE;IACZ,CAAC,EAAEL,KAAK;EACV,CAAC,EAAED,QAAQ,CAAC;AACd,CAAC;AACD,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
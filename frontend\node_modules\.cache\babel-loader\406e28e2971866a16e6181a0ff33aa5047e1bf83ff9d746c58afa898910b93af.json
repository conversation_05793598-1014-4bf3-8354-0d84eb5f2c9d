{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${unit(token.lineWidth)} dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: token.padding\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none',\n          borderRadius: token.borderRadiusLG,\n          '&:focus-visible': {\n            outline: `${unit(token.lineWidthFocus)} solid ${token.colorPrimaryBorder}`\n          }\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`\n          &:not(${componentCls}-disabled):hover,\n          &-hover:not(${componentCls}-disabled)\n        `]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${unit(token.marginXXS)}`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;", "map": {"version": 3, "names": ["unit", "genDraggerStyle", "token", "componentCls", "iconCls", "position", "width", "height", "textAlign", "background", "colorFillAlter", "border", "lineWidth", "colorBorder", "borderRadius", "borderRadiusLG", "cursor", "transition", "motionDurationSlow", "padding", "display", "outline", "lineWidthFocus", "colorPrimaryBorder", "verticalAlign", "borderColor", "colorPrimaryHover", "marginBottom", "margin", "color", "colorPrimary", "fontSize", "uploadThumbnailSize", "marginXXS", "colorTextHeading", "fontSizeLG", "colorTextDescription", "colorTextDisabled"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/upload/style/dragger.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${unit(token.lineWidth)} dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: token.padding\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none',\n          borderRadius: token.borderRadiusLG,\n          '&:focus-visible': {\n            outline: `${unit(token.lineWidthFocus)} solid ${token.colorPrimaryBorder}`\n          }\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`\n          &:not(${componentCls}-disabled):hover,\n          &-hover:not(${componentCls}-disabled)\n        `]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${unit(token.marginXXS)}`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,OAAO,GAAG;QACxBE,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,QAAQ;QACnBC,UAAU,EAAEP,KAAK,CAACQ,cAAc;QAChCC,MAAM,EAAE,GAAGX,IAAI,CAACE,KAAK,CAACU,SAAS,CAAC,WAAWV,KAAK,CAACW,WAAW,EAAE;QAC9DC,YAAY,EAAEZ,KAAK,CAACa,cAAc;QAClCC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,gBAAgBf,KAAK,CAACgB,kBAAkB,EAAE;QACtD,CAACf,YAAY,GAAG;UACdgB,OAAO,EAAEjB,KAAK,CAACiB;QACjB,CAAC;QACD,CAAC,GAAGhB,YAAY,MAAM,GAAG;UACvBiB,OAAO,EAAE,OAAO;UAChBd,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdc,OAAO,EAAE,MAAM;UACfP,YAAY,EAAEZ,KAAK,CAACa,cAAc;UAClC,iBAAiB,EAAE;YACjBM,OAAO,EAAE,GAAGrB,IAAI,CAACE,KAAK,CAACoB,cAAc,CAAC,UAAUpB,KAAK,CAACqB,kBAAkB;UAC1E;QACF,CAAC;QACD,CAAC,GAAGpB,YAAY,iBAAiB,GAAG;UAClCiB,OAAO,EAAE,YAAY;UACrBI,aAAa,EAAE;QACjB,CAAC;QACD,CAAC;AACT,kBAAkBrB,YAAY;AAC9B,wBAAwBA,YAAY;AACpC,SAAS,GAAG;UACFsB,WAAW,EAAEvB,KAAK,CAACwB;QACrB,CAAC;QACD,CAAC,IAAIvB,YAAY,YAAY,GAAG;UAC9BwB,YAAY,EAAEzB,KAAK,CAAC0B,MAAM;UAC1B,CAACxB,OAAO,GAAG;YACTyB,KAAK,EAAE3B,KAAK,CAAC4B,YAAY;YACzBC,QAAQ,EAAE7B,KAAK,CAAC8B;UAClB;QACF,CAAC;QACD,CAAC,IAAI7B,YAAY,OAAO,GAAG;UACzByB,MAAM,EAAE,OAAO5B,IAAI,CAACE,KAAK,CAAC+B,SAAS,CAAC,EAAE;UACtCJ,KAAK,EAAE3B,KAAK,CAACgC,gBAAgB;UAC7BH,QAAQ,EAAE7B,KAAK,CAACiC;QAClB,CAAC;QACD,CAAC,IAAIhC,YAAY,OAAO,GAAG;UACzB0B,KAAK,EAAE3B,KAAK,CAACkC,oBAAoB;UACjCL,QAAQ,EAAE7B,KAAK,CAAC6B;QAClB,CAAC;QACD;QACA,CAAC,IAAI5B,YAAY,WAAW,GAAG;UAC7B,CAAC,IAAIA,YAAY,cAAcC,OAAO;AAChD,eAAeD,YAAY;AAC3B,eAAeA,YAAY;AAC3B,WAAW,GAAG;YACF0B,KAAK,EAAE3B,KAAK,CAACmC;UACf;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAepC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const fadeIn = new Keyframes('antFadeIn', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\nexport const fadeOut = new Keyframes('antFadeOut', {\n  '0%': {\n    opacity: 1\n  },\n  '100%': {\n    opacity: 0\n  }\n});\nexport const initFadeMotion = (token, sameLevel = false) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-fade`;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return [initMotion(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel), {\n    [`\n        ${sameLevelPrefix}${motionCls}-enter,\n        ${sameLevelPrefix}${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: 'linear'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave`]: {\n      animationTimingFunction: 'linear'\n    }\n  }];\n};", "map": {"version": 3, "names": ["Keyframes", "initMotion", "fadeIn", "opacity", "fadeOut", "initFadeMotion", "token", "sameLevel", "antCls", "motionCls", "sameLevelPrefix", "motionDurationMid", "animationTimingFunction"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/style/motion/fade.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const fadeIn = new Keyframes('antFadeIn', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\nexport const fadeOut = new Keyframes('antFadeOut', {\n  '0%': {\n    opacity: 1\n  },\n  '100%': {\n    opacity: 0\n  }\n});\nexport const initFadeMotion = (token, sameLevel = false) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-fade`;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return [initMotion(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel), {\n    [`\n        ${sameLevelPrefix}${motionCls}-enter,\n        ${sameLevelPrefix}${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: 'linear'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave`]: {\n      animationTimingFunction: 'linear'\n    }\n  }];\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAO,MAAMC,MAAM,GAAG,IAAIF,SAAS,CAAC,WAAW,EAAE;EAC/C,IAAI,EAAE;IACJG,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACNA,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,OAAO,GAAG,IAAIJ,SAAS,CAAC,YAAY,EAAE;EACjD,IAAI,EAAE;IACJG,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACNA,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;EAC1D,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,SAAS,GAAG,GAAGD,MAAM,OAAO;EAClC,MAAME,eAAe,GAAGH,SAAS,GAAG,GAAG,GAAG,EAAE;EAC5C,OAAO,CAACN,UAAU,CAACQ,SAAS,EAAEP,MAAM,EAAEE,OAAO,EAAEE,KAAK,CAACK,iBAAiB,EAAEJ,SAAS,CAAC,EAAE;IAClF,CAAC;AACL,UAAUG,eAAe,GAAGD,SAAS;AACrC,UAAUC,eAAe,GAAGD,SAAS;AACrC,OAAO,GAAG;MACJN,OAAO,EAAE,CAAC;MACVS,uBAAuB,EAAE;IAC3B,CAAC;IACD,CAAC,GAAGF,eAAe,GAAGD,SAAS,QAAQ,GAAG;MACxCG,uBAAuB,EAAE;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
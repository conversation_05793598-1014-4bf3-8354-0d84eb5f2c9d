{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar Block = function Block(_ref) {\n  var bg = _ref.bg,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      background: bg\n    }\n  }, children);\n};\nfunction getPtgColors(color, scale) {\n  return Object.keys(color).map(function (key) {\n    var parsedKey = parseFloat(key);\n    var ptgKey = \"\".concat(Math.floor(parsedKey * scale), \"%\");\n    return \"\".concat(color[key], \" \").concat(ptgKey);\n  });\n}\nvar PtgCircle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    color = props.color,\n    gradientId = props.gradientId,\n    radius = props.radius,\n    circleStyleForStack = props.style,\n    ptg = props.ptg,\n    strokeLinecap = props.strokeLinecap,\n    strokeWidth = props.strokeWidth,\n    size = props.size,\n    gapDegree = props.gapDegree;\n  var isGradient = color && _typeof(color) === 'object';\n  var stroke = isGradient ? \"#FFF\" : undefined;\n\n  // ========================== Circle ==========================\n  var halfSize = size / 2;\n  var circleNode = /*#__PURE__*/React.createElement(\"circle\", {\n    className: \"\".concat(prefixCls, \"-circle-path\"),\n    r: radius,\n    cx: halfSize,\n    cy: halfSize,\n    stroke: stroke,\n    strokeLinecap: strokeLinecap,\n    strokeWidth: strokeWidth,\n    opacity: ptg === 0 ? 0 : 1,\n    style: circleStyleForStack,\n    ref: ref\n  });\n\n  // ========================== Render ==========================\n  if (!isGradient) {\n    return circleNode;\n  }\n  var maskId = \"\".concat(gradientId, \"-conic\");\n  var fromDeg = gapDegree ? \"\".concat(180 + gapDegree / 2, \"deg\") : '0deg';\n  var conicColors = getPtgColors(color, (360 - gapDegree) / 360);\n  var linearColors = getPtgColors(color, 1);\n  var conicColorBg = \"conic-gradient(from \".concat(fromDeg, \", \").concat(conicColors.join(', '), \")\");\n  var linearColorBg = \"linear-gradient(to \".concat(gapDegree ? 'bottom' : 'top', \", \").concat(linearColors.join(', '), \")\");\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, circleNode), /*#__PURE__*/React.createElement(\"foreignObject\", {\n    x: 0,\n    y: 0,\n    width: size,\n    height: size,\n    mask: \"url(#\".concat(maskId, \")\")\n  }, /*#__PURE__*/React.createElement(Block, {\n    bg: linearColorBg\n  }, /*#__PURE__*/React.createElement(Block, {\n    bg: conicColorBg\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  PtgCircle.displayName = 'PtgCircle';\n}\nexport default PtgCircle;", "map": {"version": 3, "names": ["_typeof", "React", "Block", "_ref", "bg", "children", "createElement", "style", "width", "height", "background", "getPtgColors", "color", "scale", "Object", "keys", "map", "key", "parsed<PERSON><PERSON>", "parseFloat", "ptgKey", "concat", "Math", "floor", "PtgCircle", "forwardRef", "props", "ref", "prefixCls", "gradientId", "radius", "circleStyleForStack", "ptg", "strokeLinecap", "strokeWidth", "size", "gapDegree", "isGradient", "stroke", "undefined", "halfSize", "circleNode", "className", "r", "cx", "cy", "opacity", "maskId", "fromDeg", "conicColors", "linearColors", "conicColorBg", "join", "linearColorBg", "Fragment", "id", "x", "y", "mask", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-progress@4.0.0_react-dom_afb1fc91faddcefa842d6a42aeb6897a/node_modules/rc-progress/es/Circle/PtgCircle.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar Block = function Block(_ref) {\n  var bg = _ref.bg,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      background: bg\n    }\n  }, children);\n};\nfunction getPtgColors(color, scale) {\n  return Object.keys(color).map(function (key) {\n    var parsedKey = parseFloat(key);\n    var ptgKey = \"\".concat(Math.floor(parsedKey * scale), \"%\");\n    return \"\".concat(color[key], \" \").concat(ptgKey);\n  });\n}\nvar PtgCircle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    color = props.color,\n    gradientId = props.gradientId,\n    radius = props.radius,\n    circleStyleForStack = props.style,\n    ptg = props.ptg,\n    strokeLinecap = props.strokeLinecap,\n    strokeWidth = props.strokeWidth,\n    size = props.size,\n    gapDegree = props.gapDegree;\n  var isGradient = color && _typeof(color) === 'object';\n  var stroke = isGradient ? \"#FFF\" : undefined;\n\n  // ========================== Circle ==========================\n  var halfSize = size / 2;\n  var circleNode = /*#__PURE__*/React.createElement(\"circle\", {\n    className: \"\".concat(prefixCls, \"-circle-path\"),\n    r: radius,\n    cx: halfSize,\n    cy: halfSize,\n    stroke: stroke,\n    strokeLinecap: strokeLinecap,\n    strokeWidth: strokeWidth,\n    opacity: ptg === 0 ? 0 : 1,\n    style: circleStyleForStack,\n    ref: ref\n  });\n\n  // ========================== Render ==========================\n  if (!isGradient) {\n    return circleNode;\n  }\n  var maskId = \"\".concat(gradientId, \"-conic\");\n  var fromDeg = gapDegree ? \"\".concat(180 + gapDegree / 2, \"deg\") : '0deg';\n  var conicColors = getPtgColors(color, (360 - gapDegree) / 360);\n  var linearColors = getPtgColors(color, 1);\n  var conicColorBg = \"conic-gradient(from \".concat(fromDeg, \", \").concat(conicColors.join(', '), \")\");\n  var linearColorBg = \"linear-gradient(to \".concat(gapDegree ? 'bottom' : 'top', \", \").concat(linearColors.join(', '), \")\");\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, circleNode), /*#__PURE__*/React.createElement(\"foreignObject\", {\n    x: 0,\n    y: 0,\n    width: size,\n    height: size,\n    mask: \"url(#\".concat(maskId, \")\")\n  }, /*#__PURE__*/React.createElement(Block, {\n    bg: linearColorBg\n  }, /*#__PURE__*/React.createElement(Block, {\n    bg: conicColorBg\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  PtgCircle.displayName = 'PtgCircle';\n}\nexport default PtgCircle;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IACdC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE;MACLC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAEN;IACd;EACF,CAAC,EAAEC,QAAQ,CAAC;AACd,CAAC;AACD,SAASM,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,OAAOC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAAC,UAAUC,GAAG,EAAE;IAC3C,IAAIC,SAAS,GAAGC,UAAU,CAACF,GAAG,CAAC;IAC/B,IAAIG,MAAM,GAAG,EAAE,CAACC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACL,SAAS,GAAGL,KAAK,CAAC,EAAE,GAAG,CAAC;IAC1D,OAAO,EAAE,CAACQ,MAAM,CAACT,KAAK,CAACK,GAAG,CAAC,EAAE,GAAG,CAAC,CAACI,MAAM,CAACD,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ;AACA,IAAII,SAAS,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAClE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BhB,KAAK,GAAGc,KAAK,CAACd,KAAK;IACnBiB,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,mBAAmB,GAAGL,KAAK,CAACnB,KAAK;IACjCyB,GAAG,GAAGN,KAAK,CAACM,GAAG;IACfC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,SAAS,GAAGV,KAAK,CAACU,SAAS;EAC7B,IAAIC,UAAU,GAAGzB,KAAK,IAAIZ,OAAO,CAACY,KAAK,CAAC,KAAK,QAAQ;EACrD,IAAI0B,MAAM,GAAGD,UAAU,GAAG,MAAM,GAAGE,SAAS;;EAE5C;EACA,IAAIC,QAAQ,GAAGL,IAAI,GAAG,CAAC;EACvB,IAAIM,UAAU,GAAG,aAAaxC,KAAK,CAACK,aAAa,CAAC,QAAQ,EAAE;IAC1DoC,SAAS,EAAE,EAAE,CAACrB,MAAM,CAACO,SAAS,EAAE,cAAc,CAAC;IAC/Ce,CAAC,EAAEb,MAAM;IACTc,EAAE,EAAEJ,QAAQ;IACZK,EAAE,EAAEL,QAAQ;IACZF,MAAM,EAAEA,MAAM;IACdL,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA,WAAW;IACxBY,OAAO,EAAEd,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1BzB,KAAK,EAAEwB,mBAAmB;IAC1BJ,GAAG,EAAEA;EACP,CAAC,CAAC;;EAEF;EACA,IAAI,CAACU,UAAU,EAAE;IACf,OAAOI,UAAU;EACnB;EACA,IAAIM,MAAM,GAAG,EAAE,CAAC1B,MAAM,CAACQ,UAAU,EAAE,QAAQ,CAAC;EAC5C,IAAImB,OAAO,GAAGZ,SAAS,GAAG,EAAE,CAACf,MAAM,CAAC,GAAG,GAAGe,SAAS,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;EACxE,IAAIa,WAAW,GAAGtC,YAAY,CAACC,KAAK,EAAE,CAAC,GAAG,GAAGwB,SAAS,IAAI,GAAG,CAAC;EAC9D,IAAIc,YAAY,GAAGvC,YAAY,CAACC,KAAK,EAAE,CAAC,CAAC;EACzC,IAAIuC,YAAY,GAAG,sBAAsB,CAAC9B,MAAM,CAAC2B,OAAO,EAAE,IAAI,CAAC,CAAC3B,MAAM,CAAC4B,WAAW,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACnG,IAAIC,aAAa,GAAG,qBAAqB,CAAChC,MAAM,CAACe,SAAS,GAAG,QAAQ,GAAG,KAAK,EAAE,IAAI,CAAC,CAACf,MAAM,CAAC6B,YAAY,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACzH,OAAO,aAAanD,KAAK,CAACK,aAAa,CAACL,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAE,aAAarD,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;IACrGiD,EAAE,EAAER;EACN,CAAC,EAAEN,UAAU,CAAC,EAAE,aAAaxC,KAAK,CAACK,aAAa,CAAC,eAAe,EAAE;IAChEkD,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJjD,KAAK,EAAE2B,IAAI;IACX1B,MAAM,EAAE0B,IAAI;IACZuB,IAAI,EAAE,OAAO,CAACrC,MAAM,CAAC0B,MAAM,EAAE,GAAG;EAClC,CAAC,EAAE,aAAa9C,KAAK,CAACK,aAAa,CAACJ,KAAK,EAAE;IACzCE,EAAE,EAAEiD;EACN,CAAC,EAAE,aAAapD,KAAK,CAACK,aAAa,CAACJ,KAAK,EAAE;IACzCE,EAAE,EAAE+C;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrC,SAAS,CAACsC,WAAW,GAAG,WAAW;AACrC;AACA,eAAetC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
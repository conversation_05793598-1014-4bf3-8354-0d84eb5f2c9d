{"ast": null, "code": "const genFormValidateMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const helpCls = `${componentCls}-show-help`;\n  const helpItemCls = `${componentCls}-show-help-item`;\n  return {\n    [helpCls]: {\n      // Explain holder\n      transition: `opacity ${token.motionDurationFast} ${token.motionEaseInOut}`,\n      '&-appear, &-enter': {\n        opacity: 0,\n        '&-active': {\n          opacity: 1\n        }\n      },\n      '&-leave': {\n        opacity: 1,\n        '&-active': {\n          opacity: 0\n        }\n      },\n      // Explain\n      [helpItemCls]: {\n        overflow: 'hidden',\n        transition: `height ${token.motionDurationFast} ${token.motionEaseInOut},\n                     opacity ${token.motionDurationFast} ${token.motionEaseInOut},\n                     transform ${token.motionDurationFast} ${token.motionEaseInOut} !important`,\n        [`&${helpItemCls}-appear, &${helpItemCls}-enter`]: {\n          transform: `translateY(-5px)`,\n          opacity: 0,\n          '&-active': {\n            transform: 'translateY(0)',\n            opacity: 1\n          }\n        },\n        [`&${helpItemCls}-leave-active`]: {\n          transform: `translateY(-5px)`\n        }\n      }\n    }\n  };\n};\nexport default genFormValidateMotionStyle;", "map": {"version": 3, "names": ["genFormValidateMotionStyle", "token", "componentCls", "helpCls", "helpItemCls", "transition", "motionDurationFast", "motionEaseInOut", "opacity", "overflow", "transform"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/form/style/explain.js"], "sourcesContent": ["const genFormValidateMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const helpCls = `${componentCls}-show-help`;\n  const helpItemCls = `${componentCls}-show-help-item`;\n  return {\n    [helpCls]: {\n      // Explain holder\n      transition: `opacity ${token.motionDurationFast} ${token.motionEaseInOut}`,\n      '&-appear, &-enter': {\n        opacity: 0,\n        '&-active': {\n          opacity: 1\n        }\n      },\n      '&-leave': {\n        opacity: 1,\n        '&-active': {\n          opacity: 0\n        }\n      },\n      // Explain\n      [helpItemCls]: {\n        overflow: 'hidden',\n        transition: `height ${token.motionDurationFast} ${token.motionEaseInOut},\n                     opacity ${token.motionDurationFast} ${token.motionEaseInOut},\n                     transform ${token.motionDurationFast} ${token.motionEaseInOut} !important`,\n        [`&${helpItemCls}-appear, &${helpItemCls}-enter`]: {\n          transform: `translateY(-5px)`,\n          opacity: 0,\n          '&-active': {\n            transform: 'translateY(0)',\n            opacity: 1\n          }\n        },\n        [`&${helpItemCls}-leave-active`]: {\n          transform: `translateY(-5px)`\n        }\n      }\n    }\n  };\n};\nexport default genFormValidateMotionStyle;"], "mappings": "AAAA,MAAMA,0BAA0B,GAAGC,KAAK,IAAI;EAC1C,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,OAAO,GAAG,GAAGD,YAAY,YAAY;EAC3C,MAAME,WAAW,GAAG,GAAGF,YAAY,iBAAiB;EACpD,OAAO;IACL,CAACC,OAAO,GAAG;MACT;MACAE,UAAU,EAAE,WAAWJ,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACM,eAAe,EAAE;MAC1E,mBAAmB,EAAE;QACnBC,OAAO,EAAE,CAAC;QACV,UAAU,EAAE;UACVA,OAAO,EAAE;QACX;MACF,CAAC;MACD,SAAS,EAAE;QACTA,OAAO,EAAE,CAAC;QACV,UAAU,EAAE;UACVA,OAAO,EAAE;QACX;MACF,CAAC;MACD;MACA,CAACJ,WAAW,GAAG;QACbK,QAAQ,EAAE,QAAQ;QAClBJ,UAAU,EAAE,UAAUJ,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACM,eAAe;AAC/E,+BAA+BN,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACM,eAAe;AAChF,iCAAiCN,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACM,eAAe,aAAa;QACvF,CAAC,IAAIH,WAAW,aAAaA,WAAW,QAAQ,GAAG;UACjDM,SAAS,EAAE,kBAAkB;UAC7BF,OAAO,EAAE,CAAC;UACV,UAAU,EAAE;YACVE,SAAS,EAAE,eAAe;YAC1BF,OAAO,EAAE;UACX;QACF,CAAC;QACD,CAAC,IAAIJ,WAAW,eAAe,GAAG;UAChCM,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
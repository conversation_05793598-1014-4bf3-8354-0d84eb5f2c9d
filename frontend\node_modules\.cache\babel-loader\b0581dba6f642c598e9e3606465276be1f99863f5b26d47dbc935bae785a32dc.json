{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getClientSize } from \"rc-util/es/Dom/css\";\nfunction fixPoint(key, start, width, clientWidth) {\n  var startAddWidth = start + width;\n  var offsetStart = (width - clientWidth) / 2;\n  if (width > clientWidth) {\n    if (start > 0) {\n      return _defineProperty({}, key, offsetStart);\n    }\n    if (start < 0 && startAddWidth < clientWidth) {\n      return _defineProperty({}, key, -offsetStart);\n    }\n  } else if (start < 0 || startAddWidth > clientWidth) {\n    return _defineProperty({}, key, start < 0 ? offsetStart : -offsetStart);\n  }\n  return {};\n}\n\n/**\n * Fix positon x,y point when\n *\n * Ele width && height < client\n * - Back origin\n *\n * - Ele width | height > clientWidth | clientHeight\n * - left | top > 0 -> Back 0\n * - left | top + width | height < clientWidth | clientHeight -> Back left | top + width | height === clientWidth | clientHeight\n *\n * Regardless of other\n */\nexport default function getFixScaleEleTransPosition(width, height, left, top) {\n  var _getClientSize = getClientSize(),\n    clientWidth = _getClientSize.width,\n    clientHeight = _getClientSize.height;\n  var fixPos = null;\n  if (width <= clientWidth && height <= clientHeight) {\n    fixPos = {\n      x: 0,\n      y: 0\n    };\n  } else if (width > clientWidth || height > clientHeight) {\n    fixPos = _objectSpread(_objectSpread({}, fixPoint('x', left, width, clientWidth)), fixPoint('y', top, height, clientHeight));\n  }\n  return fixPos;\n}", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "getClientSize", "fixPoint", "key", "start", "width", "clientWidth", "startAddWidth", "offsetStart", "getFixScaleEleTransPosition", "height", "left", "top", "_getClientSize", "clientHeight", "fixPos", "x", "y"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-image@7.12.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-image/es/getFixScaleEleTransPosition.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getClientSize } from \"rc-util/es/Dom/css\";\nfunction fixPoint(key, start, width, clientWidth) {\n  var startAddWidth = start + width;\n  var offsetStart = (width - clientWidth) / 2;\n  if (width > clientWidth) {\n    if (start > 0) {\n      return _defineProperty({}, key, offsetStart);\n    }\n    if (start < 0 && startAddWidth < clientWidth) {\n      return _defineProperty({}, key, -offsetStart);\n    }\n  } else if (start < 0 || startAddWidth > clientWidth) {\n    return _defineProperty({}, key, start < 0 ? offsetStart : -offsetStart);\n  }\n  return {};\n}\n\n/**\n * Fix positon x,y point when\n *\n * Ele width && height < client\n * - Back origin\n *\n * - Ele width | height > clientWidth | clientHeight\n * - left | top > 0 -> Back 0\n * - left | top + width | height < clientWidth | clientHeight -> Back left | top + width | height === clientWidth | clientHeight\n *\n * Regardless of other\n */\nexport default function getFixScaleEleTransPosition(width, height, left, top) {\n  var _getClientSize = getClientSize(),\n    clientWidth = _getClientSize.width,\n    clientHeight = _getClientSize.height;\n  var fixPos = null;\n  if (width <= clientWidth && height <= clientHeight) {\n    fixPos = {\n      x: 0,\n      y: 0\n    };\n  } else if (width > clientWidth || height > clientHeight) {\n    fixPos = _objectSpread(_objectSpread({}, fixPoint('x', left, width, clientWidth)), fixPoint('y', top, height, clientHeight));\n  }\n  return fixPos;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAChD,IAAIC,aAAa,GAAGH,KAAK,GAAGC,KAAK;EACjC,IAAIG,WAAW,GAAG,CAACH,KAAK,GAAGC,WAAW,IAAI,CAAC;EAC3C,IAAID,KAAK,GAAGC,WAAW,EAAE;IACvB,IAAIF,KAAK,GAAG,CAAC,EAAE;MACb,OAAOJ,eAAe,CAAC,CAAC,CAAC,EAAEG,GAAG,EAAEK,WAAW,CAAC;IAC9C;IACA,IAAIJ,KAAK,GAAG,CAAC,IAAIG,aAAa,GAAGD,WAAW,EAAE;MAC5C,OAAON,eAAe,CAAC,CAAC,CAAC,EAAEG,GAAG,EAAE,CAACK,WAAW,CAAC;IAC/C;EACF,CAAC,MAAM,IAAIJ,KAAK,GAAG,CAAC,IAAIG,aAAa,GAAGD,WAAW,EAAE;IACnD,OAAON,eAAe,CAAC,CAAC,CAAC,EAAEG,GAAG,EAAEC,KAAK,GAAG,CAAC,GAAGI,WAAW,GAAG,CAACA,WAAW,CAAC;EACzE;EACA,OAAO,CAAC,CAAC;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,2BAA2BA,CAACJ,KAAK,EAAEK,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC5E,IAAIC,cAAc,GAAGZ,aAAa,CAAC,CAAC;IAClCK,WAAW,GAAGO,cAAc,CAACR,KAAK;IAClCS,YAAY,GAAGD,cAAc,CAACH,MAAM;EACtC,IAAIK,MAAM,GAAG,IAAI;EACjB,IAAIV,KAAK,IAAIC,WAAW,IAAII,MAAM,IAAII,YAAY,EAAE;IAClDC,MAAM,GAAG;MACPC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;EACH,CAAC,MAAM,IAAIZ,KAAK,GAAGC,WAAW,IAAII,MAAM,GAAGI,YAAY,EAAE;IACvDC,MAAM,GAAGhB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEG,QAAQ,CAAC,GAAG,EAAES,IAAI,EAAEN,KAAK,EAAEC,WAAW,CAAC,CAAC,EAAEJ,QAAQ,CAAC,GAAG,EAAEU,GAAG,EAAEF,MAAM,EAAEI,YAAY,CAAC,CAAC;EAC9H;EACA,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
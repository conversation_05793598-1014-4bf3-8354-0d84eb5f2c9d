const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const customerController = require('../controllers/customerController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createCustomerValidation = [
  body('contact_person')
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('order_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('订单号长度不能超过50个字符')
    .trim(),
  body('shop_account')
    .optional()
    .isLength({ max: 100 })
    .withMessage('店铺账号长度不能超过100个字符')
    .trim(),
  body('shop_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('店铺名称长度不能超过100个字符')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('mobile')
    .optional()
    .isLength({ max: 20 })
    .withMessage('手机号长度不能超过20个字符'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('customer_type')
    .isIn(['retail', 'wholesale', 'distributor', 'online'])
    .withMessage('客户类型必须是: retail, wholesale, distributor, online'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('信用额度必须是非负数')
];

const updateCustomerValidation = [
  body('contact_person')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('order_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('订单号长度不能超过50个字符')
    .trim(),
  body('shop_account')
    .optional()
    .isLength({ max: 100 })
    .withMessage('店铺账号长度不能超过100个字符')
    .trim(),
  body('shop_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('店铺名称长度不能超过100个字符')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('mobile')
    .optional()
    .isLength({ max: 20 })
    .withMessage('手机号长度不能超过20个字符'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('customer_type')
    .optional()
    .isIn(['retail', 'wholesale', 'distributor', 'online'])
    .withMessage('客户类型必须是: retail, wholesale, distributor, online'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('信用额度必须是非负数')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取客户列表
router.get('/', 
  auth,
  paginationValidation,
  customerController.getCustomers
);

// 获取客户详情
router.get('/:id', 
  auth,
  customerController.getCustomerById
);

// 创建客户
router.post('/', 
  auth,
  authorize('admin', 'manager', 'employee'),
  createCustomerValidation,
  customerController.createCustomer
);

// 更新客户
router.put('/:id', 
  auth,
  authorize('admin', 'manager', 'employee'),
  updateCustomerValidation,
  customerController.updateCustomer
);

// 删除客户
router.delete('/:id', 
  auth,
  authorize('admin', 'manager'),
  customerController.deleteCustomer
);

// 更新客户状态
router.patch('/:id/status', 
  auth,
  authorize('admin', 'manager'),
  body('status').isIn(['active', 'inactive']).withMessage('状态必须是: active, inactive'),
  customerController.updateCustomerStatus
);

module.exports = router;

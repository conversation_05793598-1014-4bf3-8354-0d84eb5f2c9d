const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const customerController = require('../controllers/customerController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createCustomerValidation = [
  body('company_name')
    .isLength({ min: 2, max: 200 })
    .withMessage('公司名称长度必须在2-200个字符之间')
    .trim(),
  body('contact_person')
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('customer_type')
    .isIn(['individual', 'company'])
    .withMessage('客户类型必须是: individual, company'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('信用额度必须是非负数'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('付款期限必须是非负整数')
];

const updateCustomerValidation = [
  body('company_name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('公司名称长度必须在2-200个字符之间')
    .trim(),
  body('contact_person')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('customer_type')
    .optional()
    .isIn(['individual', 'company'])
    .withMessage('客户类型必须是: individual, company'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('信用额度必须是非负数'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('付款期限必须是非负整数')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取客户列表
router.get('/', 
  auth,
  paginationValidation,
  customerController.getCustomers
);

// 获取客户详情
router.get('/:id', 
  auth,
  customerController.getCustomerById
);

// 创建客户
router.post('/', 
  auth,
  authorize('admin', 'manager', 'employee'),
  createCustomerValidation,
  customerController.createCustomer
);

// 更新客户
router.put('/:id', 
  auth,
  authorize('admin', 'manager', 'employee'),
  updateCustomerValidation,
  customerController.updateCustomer
);

// 删除客户
router.delete('/:id', 
  auth,
  authorize('admin', 'manager'),
  customerController.deleteCustomer
);

// 更新客户状态
router.patch('/:id/status', 
  auth,
  authorize('admin', 'manager'),
  body('status').isIn(['active', 'inactive']).withMessage('状态必须是: active, inactive'),
  customerController.updateCustomerStatus
);

module.exports = router;

@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\prebuild-install@7.1.3\node_modules\prebuild-install\node_modules;D:\code\erp1\backend\node_modules\.pnpm\prebuild-install@7.1.3\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\prebuild-install@7.1.3\node_modules\prebuild-install\node_modules;D:\code\erp1\backend\node_modules\.pnpm\prebuild-install@7.1.3\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin.js" %*
)

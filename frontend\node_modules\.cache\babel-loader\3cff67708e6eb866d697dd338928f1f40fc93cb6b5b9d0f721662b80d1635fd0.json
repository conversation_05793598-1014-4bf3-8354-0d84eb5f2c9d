{"ast": null, "code": "import React from 'react';\nvar DropIndicator = function DropIndicator(props) {\n  var dropPosition = props.dropPosition,\n    dropLevelOffset = props.dropLevelOffset,\n    indent = props.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  DropIndicator.displayName = 'DropIndicator';\n}\nexport default DropIndicator;", "map": {"version": 3, "names": ["React", "DropIndicator", "props", "dropPosition", "dropLevelOffset", "indent", "style", "pointerEvents", "position", "right", "backgroundColor", "height", "top", "left", "bottom", "createElement", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree@5.13.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tree/es/DropIndicator.js"], "sourcesContent": ["import React from 'react';\nvar DropIndicator = function DropIndicator(props) {\n  var dropPosition = props.dropPosition,\n    dropLevelOffset = props.dropLevelOffset,\n    indent = props.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  DropIndicator.displayName = 'DropIndicator';\n}\nexport default DropIndicator;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACnCC,eAAe,GAAGF,KAAK,CAACE,eAAe;IACvCC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACvB,IAAIC,KAAK,GAAG;IACVC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,CAAC;IACRC,eAAe,EAAE,KAAK;IACtBC,MAAM,EAAE;EACV,CAAC;EACD,QAAQR,YAAY;IAClB,KAAK,CAAC,CAAC;MACLG,KAAK,CAACM,GAAG,GAAG,CAAC;MACbN,KAAK,CAACO,IAAI,GAAG,CAACT,eAAe,GAAGC,MAAM;MACtC;IACF,KAAK,CAAC;MACJC,KAAK,CAACQ,MAAM,GAAG,CAAC;MAChBR,KAAK,CAACO,IAAI,GAAG,CAACT,eAAe,GAAGC,MAAM;MACtC;IACF,KAAK,CAAC;MACJC,KAAK,CAACQ,MAAM,GAAG,CAAC;MAChBR,KAAK,CAACO,IAAI,GAAGR,MAAM;MACnB;EACJ;EACA,OAAO,aAAaL,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC7CT,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AACD,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,aAAa,CAACkB,WAAW,GAAG,eAAe;AAC7C;AACA,eAAelB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
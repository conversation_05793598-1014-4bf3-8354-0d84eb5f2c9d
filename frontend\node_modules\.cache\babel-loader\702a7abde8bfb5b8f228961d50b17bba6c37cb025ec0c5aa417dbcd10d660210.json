{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport Button from '../../button';\nimport { ModalContext } from '../context';\nconst NormalCancelBtn = () => {\n  const {\n    cancelButtonProps,\n    cancelTextLocale,\n    onCancel\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(Button, Object.assign({\n    onClick: onCancel\n  }, cancelButtonProps), cancelTextLocale);\n};\nexport default NormalCancelBtn;", "map": {"version": 3, "names": ["React", "useContext", "<PERSON><PERSON>", "ModalContext", "NormalCancelBtn", "cancelButtonProps", "cancelTextLocale", "onCancel", "createElement", "Object", "assign", "onClick"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/modal/components/NormalCancelBtn.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport Button from '../../button';\nimport { ModalContext } from '../context';\nconst NormalCancelBtn = () => {\n  const {\n    cancelButtonProps,\n    cancelTextLocale,\n    onCancel\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(Button, Object.assign({\n    onClick: onCancel\n  }, cancelButtonProps), cancelTextLocale);\n};\nexport default NormalCancelBtn;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,YAAY,QAAQ,YAAY;AACzC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAM;IACJC,iBAAiB;IACjBC,gBAAgB;IAChBC;EACF,CAAC,GAAGN,UAAU,CAACE,YAAY,CAAC;EAC5B,OAAO,aAAaH,KAAK,CAACQ,aAAa,CAACN,MAAM,EAAEO,MAAM,CAACC,MAAM,CAAC;IAC5DC,OAAO,EAAEJ;EACX,CAAC,EAAEF,iBAAiB,CAAC,EAAEC,gBAAgB,CAAC;AAC1C,CAAC;AACD,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
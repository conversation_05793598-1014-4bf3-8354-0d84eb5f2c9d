const { Order, OrderItem, Customer, Product, User } = require('../models');
const { Op } = require('sequelize');
const { validationResult } = require('express-validator');

// 生成订单号
const generateOrderNumber = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  const prefix = `SO${dateStr}`;
  
  const lastOrder = await Order.findOne({
    where: {
      order_number: {
        [Op.like]: `${prefix}%`
      }
    },
    order: [['order_number', 'DESC']]
  });
  
  let sequence = 1;
  if (lastOrder) {
    const lastSequence = parseInt(lastOrder.order_number.slice(-4));
    sequence = lastSequence + 1;
  }
  
  return `${prefix}${sequence.toString().padStart(4, '0')}`;
};

// 计算订单总额
const calculateOrderTotals = (items) => {
  let subtotal = 0;
  let taxAmount = 0;
  
  items.forEach(item => {
    const lineTotal = item.quantity * item.unit_price;
    const discountAmount = lineTotal * (item.discount_rate / 100);
    const netAmount = lineTotal - discountAmount;
    const itemTax = netAmount * (item.tax_rate / 100);
    
    subtotal += netAmount;
    taxAmount += itemTax;
  });
  
  return { subtotal, taxAmount };
};

// 获取所有订单
exports.getAllOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      customer_id,
      start_date,
      end_date,
      search
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    // 状态筛选
    if (status) {
      where.status = status;
    }
    
    // 客户筛选
    if (customer_id) {
      where.customer_id = customer_id;
    }
    
    // 日期范围筛选
    if (start_date && end_date) {
      where.order_date = {
        [Op.between]: [start_date, end_date]
      };
    }
    
    // 搜索订单号
    if (search) {
      where.order_number = {
        [Op.like]: `%${search}%`
      };
    }
    
    const { count, rows } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'contact_person', 'shop_name']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'full_name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        orders: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败',
      error: error.message
    });
  }
};

// 获取单个订单详情
exports.getOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const order = await Order.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'customer_code', 'contact_person', 'email', 'phone', 'mobile', 'address', 'city', 'province', 'postal_code', 'tax_number', 'credit_limit', 'discount_rate', 'customer_type', 'status', 'notes', 'order_number', 'shop_account', 'shop_name']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_code', 'name', 'unit']
            }
          ]
        }
      ]
    });
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败',
      error: error.message
    });
  }
};

// 创建订单
exports.createOrder = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }
    
    const {
      customer_id,
      delivery_date,
      priority = 'normal',
      discount_amount = 0,
      shipping_cost = 0,
      payment_method,
      shipping_address,
      billing_address,
      notes,
      items
    } = req.body;
    
    // 验证客户是否存在
    const customer = await Customer.findByPk(customer_id);
    if (!customer) {
      return res.status(400).json({
        success: false,
        message: '客户不存在'
      });
    }
    
    // 验证订单明细
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '订单明细不能为空'
      });
    }
    
    // 生成订单号
    const orderNumber = await generateOrderNumber();
    
    // 计算订单总额
    const { subtotal, taxAmount } = calculateOrderTotals(items);
    const totalAmount = subtotal + taxAmount + shipping_cost - discount_amount;
    
    // 创建订单
    const order = await Order.create({
      order_number: orderNumber,
      customer_id,
      user_id: req.user.id,
      delivery_date,
      priority,
      subtotal,
      tax_amount: taxAmount,
      discount_amount,
      shipping_cost,
      total_amount: totalAmount,
      payment_method,
      shipping_address,
      billing_address,
      notes
    });
    
    // 创建订单明细
    const orderItems = items.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      unit_cost: item.unit_cost || 0,
      discount_rate: item.discount_rate || 0,
      discount_amount: (item.quantity * item.unit_price) * ((item.discount_rate || 0) / 100),
      tax_rate: item.tax_rate || 0,
      tax_amount: ((item.quantity * item.unit_price) - ((item.quantity * item.unit_price) * ((item.discount_rate || 0) / 100))) * ((item.tax_rate || 0) / 100),
      line_total: item.quantity * item.unit_price,
      notes: item.notes
    }));
    
    await OrderItem.bulkCreate(orderItems);
    
    // 返回完整的订单信息
    const createdOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_code', 'name', 'unit']
            }
          ]
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      message: '订单创建成功',
      data: createdOrder
    });
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建订单失败',
      error: error.message
    });
  }
};

// 更新订单
exports.updateOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      customer_id,
      delivery_date,
      priority,
      discount_amount,
      shipping_cost,
      payment_method,
      shipping_address,
      billing_address,
      notes,
      items
    } = req.body;

    // 查找订单
    const order = await Order.findByPk(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 检查订单状态是否允许修改
    if (order.status === 'shipped' || order.status === 'delivered') {
      return res.status(400).json({
        success: false,
        message: '已发货或已交付的订单不能修改'
      });
    }

    // 如果提供了新的订单明细，重新计算总额
    let updateData = {
      customer_id,
      delivery_date,
      priority,
      discount_amount,
      shipping_cost,
      payment_method,
      shipping_address,
      billing_address,
      notes
    };

    if (items && items.length > 0) {
      const { subtotal, taxAmount } = calculateOrderTotals(items);
      updateData.subtotal = subtotal;
      updateData.tax_amount = taxAmount;
      updateData.total_amount = subtotal + taxAmount + (shipping_cost || 0) - (discount_amount || 0);

      // 删除旧的订单明细
      await OrderItem.destroy({ where: { order_id: id } });

      // 创建新的订单明细
      const orderItems = items.map(item => ({
        order_id: id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        unit_cost: item.unit_cost || 0,
        discount_rate: item.discount_rate || 0,
        discount_amount: (item.quantity * item.unit_price) * ((item.discount_rate || 0) / 100),
        tax_rate: item.tax_rate || 0,
        tax_amount: ((item.quantity * item.unit_price) - ((item.quantity * item.unit_price) * ((item.discount_rate || 0) / 100))) * ((item.tax_rate || 0) / 100),
        line_total: item.quantity * item.unit_price,
        notes: item.notes
      }));

      await OrderItem.bulkCreate(orderItems);
    }

    // 更新订单
    await order.update(updateData);

    // 返回更新后的订单
    const updatedOrder = await Order.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_code', 'name', 'unit']
            }
          ]
        }
      ]
    });

    res.json({
      success: true,
      message: '订单更新成功',
      data: updatedOrder
    });
  } catch (error) {
    console.error('更新订单失败:', error);
    res.status(500).json({
      success: false,
      message: '更新订单失败',
      error: error.message
    });
  }
};

// 更新订单状态
exports.updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['draft', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的订单状态'
      });
    }

    const order = await Order.findByPk(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    await order.update({ status });

    res.json({
      success: true,
      message: '订单状态更新成功',
      data: order
    });
  } catch (error) {
    console.error('更新订单状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新订单状态失败',
      error: error.message
    });
  }
};

// 删除订单
exports.deleteOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await Order.findByPk(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 只有草稿状态的订单可以删除
    if (order.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: '只有草稿状态的订单可以删除'
      });
    }

    // 删除订单明细
    await OrderItem.destroy({ where: { order_id: id } });

    // 删除订单
    await order.destroy();

    res.json({
      success: true,
      message: '订单删除成功'
    });
  } catch (error) {
    console.error('删除订单失败:', error);
    res.status(500).json({
      success: false,
      message: '删除订单失败',
      error: error.message
    });
  }
};

{"ast": null, "code": "\"use client\";\n\nimport React, { useMemo } from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Select from '../../select';\nimport { FORMAT_HEX, FORMAT_HSB, FORMAT_RGB } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [FORMAT_HEX, FORMAT_HSB, FORMAT_RGB].map(format => ({\n  value: format,\n  label: format.toUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange,\n    disabledFormat\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(FORMAT_HEX, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = `${prefixCls}-input`;\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case FORMAT_HSB:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case FORMAT_RGB:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      // case FORMAT_HEX:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorInputPrefixCls}-container`\n  }, !disabledFormat && (/*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    variant: \"borderless\",\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: `${prefixCls}-format-select`,\n    size: \"small\",\n    options: selectOptions\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && (/*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  })));\n};\nexport default ColorInput;", "map": {"version": 3, "names": ["React", "useMemo", "useMergedState", "Select", "FORMAT_HEX", "FORMAT_HSB", "FORMAT_RGB", "ColorAlphaInput", "ColorHexInput", "ColorHsbInput", "ColorRgbInput", "selectOptions", "map", "format", "value", "label", "toUpperCase", "ColorInput", "props", "prefixCls", "disabledAlpha", "onFormatChange", "onChange", "disabledFormat", "colorFormat", "setColorFormat", "colorInputPrefixCls", "handleFormatChange", "newFormat", "steppersNode", "inputProps", "createElement", "Object", "assign", "className", "variant", "getPopupContainer", "current", "popupMatchSelectWidth", "placement", "size", "options"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/components/ColorInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useMemo } from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Select from '../../select';\nimport { FORMAT_HEX, FORMAT_HSB, FORMAT_RGB } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [FORMAT_HEX, FORMAT_HSB, FORMAT_RGB].map(format => ({\n  value: format,\n  label: format.toUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange,\n    disabledFormat\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(FORMAT_HEX, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = `${prefixCls}-input`;\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case FORMAT_HSB:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case FORMAT_RGB:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      // case FORMAT_HEX:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorInputPrefixCls}-container`\n  }, !disabledFormat && (/*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    variant: \"borderless\",\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: `${prefixCls}-format-select`,\n    size: \"small\",\n    options: selectOptions\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && (/*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  })));\n};\nexport default ColorInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,QAAQ,cAAc;AACjE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAG,CAACP,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC,CAACM,GAAG,CAACC,MAAM,KAAK;EACxEC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAEF,MAAM,CAACG,WAAW,CAAC;AAC5B,CAAC,CAAC,CAAC;AACH,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;IACJC,SAAS;IACTN,MAAM;IACNC,KAAK;IACLM,aAAa;IACbC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAGvB,cAAc,CAACE,UAAU,EAAE;IAC/DU,KAAK,EAAED,MAAM;IACbS,QAAQ,EAAED;EACZ,CAAC,CAAC;EACF,MAAMK,mBAAmB,GAAG,GAAGP,SAAS,QAAQ;EAChD,MAAMQ,kBAAkB,GAAGC,SAAS,IAAI;IACtCH,cAAc,CAACG,SAAS,CAAC;EAC3B,CAAC;EACD,MAAMC,YAAY,GAAG5B,OAAO,CAAC,MAAM;IACjC,MAAM6B,UAAU,GAAG;MACjBhB,KAAK;MACLK,SAAS;MACTG;IACF,CAAC;IACD,QAAQE,WAAW;MACjB,KAAKnB,UAAU;QACb,OAAO,aAAaL,KAAK,CAAC+B,aAAa,CAACtB,aAAa,EAAEuB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;MACvF,KAAKxB,UAAU;QACb,OAAO,aAAaN,KAAK,CAAC+B,aAAa,CAACrB,aAAa,EAAEsB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;MACvF;MACA;QACE,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAACvB,aAAa,EAAEwB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;IACzF;EACF,CAAC,EAAE,CAACN,WAAW,EAAEL,SAAS,EAAEL,KAAK,EAAEQ,QAAQ,CAAC,CAAC;EAC7C,OAAO,aAAatB,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC7CG,SAAS,EAAE,GAAGR,mBAAmB;EACnC,CAAC,EAAE,CAACH,cAAc,KAAK,aAAavB,KAAK,CAAC+B,aAAa,CAAC5B,MAAM,EAAE;IAC9DW,KAAK,EAAEU,WAAW;IAClBW,OAAO,EAAE,YAAY;IACrBC,iBAAiB,EAAEC,OAAO,IAAIA,OAAO;IACrCC,qBAAqB,EAAE,EAAE;IACzBC,SAAS,EAAE,aAAa;IACxBjB,QAAQ,EAAEK,kBAAkB;IAC5BO,SAAS,EAAE,GAAGf,SAAS,gBAAgB;IACvCqB,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE9B;EACX,CAAC,CAAC,CAAC,EAAE,aAAaX,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC3CG,SAAS,EAAER;EACb,CAAC,EAAEG,YAAY,CAAC,EAAE,CAACT,aAAa,KAAK,aAAapB,KAAK,CAAC+B,aAAa,CAACxB,eAAe,EAAE;IACrFY,SAAS,EAAEA,SAAS;IACpBL,KAAK,EAAEA,KAAK;IACZQ,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
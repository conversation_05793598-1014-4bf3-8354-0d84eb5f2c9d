{"ast": null, "code": "/**\n * Fallback of IE.\n * Safe to remove.\n */\n// Style as inline component\nimport { prepareToken } from '.';\nimport { genSubStyleComponent } from '../../theme/internal';\n// ============================= Fallback =============================\nconst genFallbackStyle = token => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    '@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)': {\n      // Fallback for IE, safe to remove we not support it anymore\n      [`${formItemCls}-control`]: {\n        display: 'flex'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Form', 'item-item'], (token, {\n  rootPrefixCls\n}) => {\n  const formToken = prepareToken(token, rootPrefixCls);\n  return [genFallbackStyle(formToken)];\n});", "map": {"version": 3, "names": ["prepareToken", "genSubStyleComponent", "genFallbackStyle", "token", "formItemCls", "display", "rootPrefixCls", "formToken"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/form/style/fallbackCmp.js"], "sourcesContent": ["/**\n * Fallback of IE.\n * Safe to remove.\n */\n// Style as inline component\nimport { prepareToken } from '.';\nimport { genSubStyleComponent } from '../../theme/internal';\n// ============================= Fallback =============================\nconst genFallbackStyle = token => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    '@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)': {\n      // Fallback for IE, safe to remove we not support it anymore\n      [`${formItemCls}-control`]: {\n        display: 'flex'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Form', 'item-item'], (token, {\n  rootPrefixCls\n}) => {\n  const formToken = prepareToken(token, rootPrefixCls);\n  return [genFallbackStyle(formToken)];\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,GAAG;AAChC,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D;AACA,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,0EAA0E,EAAE;MAC1E;MACA,CAAC,GAAGC,WAAW,UAAU,GAAG;QAC1BC,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAeJ,oBAAoB,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAACE,KAAK,EAAE;EACjEG;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAGP,YAAY,CAACG,KAAK,EAAEG,aAAa,CAAC;EACpD,OAAO,CAACJ,gBAAgB,CAACK,SAAS,CAAC,CAAC;AACtC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
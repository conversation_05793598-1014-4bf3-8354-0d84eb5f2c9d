{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef(null);\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    var _a;\n    if ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = ({\n    target\n  }) => {\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = ({\n    keyCode\n  }) => {\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = ({\n    keyCode,\n    ctrlKey,\n    altKey,\n    metaKey,\n    shiftKey\n  }) => {\n    // Check if it's a real key\n    if (lastKeyCode.current !== keyCode || inComposition.current || ctrlKey || altKey || metaKey || shiftKey) {\n      return;\n    }\n    if (keyCode === KeyCode.ENTER) {\n      confirmChange();\n      onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n    } else if (keyCode === KeyCode.ESC) {\n      onCancel();\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${component}`]: !!component\n  }, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "map": {"version": 3, "names": ["React", "EnterOutlined", "classNames", "KeyCode", "cloneElement", "TextArea", "useStyle", "Editable", "props", "prefixCls", "aria<PERSON><PERSON><PERSON>", "className", "style", "direction", "max<PERSON><PERSON><PERSON>", "autoSize", "value", "onSave", "onCancel", "onEnd", "component", "enterIcon", "createElement", "ref", "useRef", "inComposition", "lastKeyCode", "current", "setCurrent", "useState", "useEffect", "_a", "resizableTextArea", "textArea", "focus", "length", "setSelectionRange", "onChange", "target", "replace", "onCompositionStart", "onCompositionEnd", "onKeyDown", "keyCode", "confirmChange", "trim", "onKeyUp", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "ENTER", "ESC", "onBlur", "wrapCSSVar", "hashId", "cssVarCls", "textAreaClassName", "rows"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/Editable.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef(null);\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    var _a;\n    if ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = ({\n    target\n  }) => {\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = ({\n    keyCode\n  }) => {\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = ({\n    keyCode,\n    ctrlKey,\n    altKey,\n    metaKey,\n    shiftKey\n  }) => {\n    // Check if it's a real key\n    if (lastKeyCode.current !== keyCode || inComposition.current || ctrlKey || altKey || metaKey || shiftKey) {\n      return;\n    }\n    if (keyCode === KeyCode.ENTER) {\n      confirmChange();\n      onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n    } else if (keyCode === KeyCode.ESC) {\n      onCancel();\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${component}`]: !!component\n  }, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS;IACT,YAAY,EAAEC,SAAS;IACvBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,QAAQ,GAAG,IAAI;IACfC,KAAK;IACLC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,SAAS;IACTC,SAAS,GAAG,aAAarB,KAAK,CAACsB,aAAa,CAACrB,aAAa,EAAE,IAAI;EAClE,CAAC,GAAGO,KAAK;EACT,MAAMe,GAAG,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,aAAa,GAAGzB,KAAK,CAACwB,MAAM,CAAC,KAAK,CAAC;EACzC,MAAME,WAAW,GAAG1B,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG5B,KAAK,CAAC6B,QAAQ,CAACb,KAAK,CAAC;EACnDhB,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpBF,UAAU,CAACZ,KAAK,CAAC;EACnB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXhB,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB,IAAIC,EAAE;IACN,IAAI,CAACA,EAAE,GAAGR,GAAG,CAACI,OAAO,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,iBAAiB,EAAE;MAChF,MAAM;QACJC;MACF,CAAC,GAAGV,GAAG,CAACI,OAAO,CAACK,iBAAiB;MACjCC,QAAQ,CAACC,KAAK,CAAC,CAAC;MAChB,MAAM;QACJC;MACF,CAAC,GAAGF,QAAQ,CAACjB,KAAK;MAClBiB,QAAQ,CAACG,iBAAiB,CAACD,MAAM,EAAEA,MAAM,CAAC;IAC5C;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,QAAQ,GAAGA,CAAC;IAChBC;EACF,CAAC,KAAK;IACJV,UAAU,CAACU,MAAM,CAACtB,KAAK,CAACuB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;EACjD,CAAC;EACD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bf,aAAa,CAACE,OAAO,GAAG,IAAI;EAC9B,CAAC;EACD,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,aAAa,CAACE,OAAO,GAAG,KAAK;EAC/B,CAAC;EACD,MAAMe,SAAS,GAAGA,CAAC;IACjBC;EACF,CAAC,KAAK;IACJ;IACA,IAAIlB,aAAa,CAACE,OAAO,EAAE;IAC3BD,WAAW,CAACC,OAAO,GAAGgB,OAAO;EAC/B,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B3B,MAAM,CAACU,OAAO,CAACkB,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,MAAMC,OAAO,GAAGA,CAAC;IACfH,OAAO;IACPI,OAAO;IACPC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,KAAK;IACJ;IACA,IAAIxB,WAAW,CAACC,OAAO,KAAKgB,OAAO,IAAIlB,aAAa,CAACE,OAAO,IAAIoB,OAAO,IAAIC,MAAM,IAAIC,OAAO,IAAIC,QAAQ,EAAE;MACxG;IACF;IACA,IAAIP,OAAO,KAAKxC,OAAO,CAACgD,KAAK,EAAE;MAC7BP,aAAa,CAAC,CAAC;MACfzB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;IACvD,CAAC,MAAM,IAAIwB,OAAO,KAAKxC,OAAO,CAACiD,GAAG,EAAE;MAClClC,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACD,MAAMmC,MAAM,GAAGA,CAAA,KAAM;IACnBT,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,MAAM,CAACU,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAACG,SAAS,CAAC;EAC3D,MAAMgD,iBAAiB,GAAGvD,UAAU,CAACO,SAAS,EAAE,GAAGA,SAAS,eAAe,EAAE;IAC3E,CAAC,GAAGA,SAAS,MAAM,GAAGI,SAAS,KAAK,KAAK;IACzC,CAAC,GAAGJ,SAAS,IAAIW,SAAS,EAAE,GAAG,CAAC,CAACA;EACnC,CAAC,EAAET,SAAS,EAAE4C,MAAM,EAAEC,SAAS,CAAC;EAChC,OAAOF,UAAU,CAAC,aAAatD,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IACxDX,SAAS,EAAE8C,iBAAiB;IAC5B7C,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaZ,KAAK,CAACsB,aAAa,CAACjB,QAAQ,EAAE;IAC5CkB,GAAG,EAAEA,GAAG;IACRT,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAEW,OAAO;IACdU,QAAQ,EAAEA,QAAQ;IAClBK,SAAS,EAAEA,SAAS;IACpBI,OAAO,EAAEA,OAAO;IAChBN,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAClCY,MAAM,EAAEA,MAAM;IACd,YAAY,EAAE3C,SAAS;IACvBgD,IAAI,EAAE,CAAC;IACP3C,QAAQ,EAAEA;EACZ,CAAC,CAAC,EAAEM,SAAS,KAAK,IAAI,GAAGjB,YAAY,CAACiB,SAAS,EAAE;IAC/CV,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACb,CAAC;AACD,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
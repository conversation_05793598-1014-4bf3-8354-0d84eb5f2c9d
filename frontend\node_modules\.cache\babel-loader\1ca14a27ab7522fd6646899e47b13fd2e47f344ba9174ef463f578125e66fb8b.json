{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport Arrow from \"./Arrow\";\nimport Mask from \"./Mask\";\nimport PopupContent from \"./PopupContent\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var popup = props.popup,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    style = props.style,\n    target = props.target,\n    _onVisibleChanged = props.onVisibleChanged,\n    open = props.open,\n    keepDom = props.keepDom,\n    fresh = props.fresh,\n    onClick = props.onClick,\n    mask = props.mask,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos,\n    align = props.align,\n    motion = props.motion,\n    maskMotion = props.maskMotion,\n    forceRender = props.forceRender,\n    getPopupContainer = props.getPopupContainer,\n    autoDestroy = props.autoDestroy,\n    Portal = props.portal,\n    zIndex = props.zIndex,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onPointerEnter = props.onPointerEnter,\n    onPointerDownCapture = props.onPointerDownCapture,\n    ready = props.ready,\n    offsetX = props.offsetX,\n    offsetY = props.offsetY,\n    offsetR = props.offsetR,\n    offsetB = props.offsetB,\n    onAlign = props.onAlign,\n    onPrepare = props.onPrepare,\n    stretch = props.stretch,\n    targetWidth = props.targetWidth,\n    targetHeight = props.targetHeight;\n  var childNode = typeof popup === 'function' ? popup() : popup;\n\n  // We can not remove holder only when motion finished.\n  var isNodeVisible = open || keepDom;\n\n  // ======================= Container ========================\n  var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;\n  var _React$useState = React.useState(!getPopupContainer || !getPopupContainerNeedParams),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    show = _React$useState2[0],\n    setShow = _React$useState2[1];\n\n  // Delay to show since `getPopupContainer` need target element\n  useLayoutEffect(function () {\n    if (!show && getPopupContainerNeedParams && target) {\n      setShow(true);\n    }\n  }, [show, getPopupContainerNeedParams, target]);\n\n  // ========================= Render =========================\n  if (!show) {\n    return null;\n  }\n\n  // >>>>> Offset\n  var AUTO = 'auto';\n  var offsetStyle = {\n    left: '-1000vw',\n    top: '-1000vh',\n    right: AUTO,\n    bottom: AUTO\n  };\n\n  // Set align style\n  if (ready || !open) {\n    var _experimental;\n    var points = align.points;\n    var dynamicInset = align.dynamicInset || ((_experimental = align._experimental) === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset);\n    var alignRight = dynamicInset && points[0][1] === 'r';\n    var alignBottom = dynamicInset && points[0][0] === 'b';\n    if (alignRight) {\n      offsetStyle.right = offsetR;\n      offsetStyle.left = AUTO;\n    } else {\n      offsetStyle.left = offsetX;\n      offsetStyle.right = AUTO;\n    }\n    if (alignBottom) {\n      offsetStyle.bottom = offsetB;\n      offsetStyle.top = AUTO;\n    } else {\n      offsetStyle.top = offsetY;\n      offsetStyle.bottom = AUTO;\n    }\n  }\n\n  // >>>>> Misc\n  var miscStyle = {};\n  if (stretch) {\n    if (stretch.includes('height') && targetHeight) {\n      miscStyle.height = targetHeight;\n    } else if (stretch.includes('minHeight') && targetHeight) {\n      miscStyle.minHeight = targetHeight;\n    }\n    if (stretch.includes('width') && targetWidth) {\n      miscStyle.width = targetWidth;\n    } else if (stretch.includes('minWidth') && targetWidth) {\n      miscStyle.minWidth = targetWidth;\n    }\n  }\n  if (!open) {\n    miscStyle.pointerEvents = 'none';\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: forceRender || isNodeVisible,\n    getContainer: getPopupContainer && function () {\n      return getPopupContainer(target);\n    },\n    autoDestroy: autoDestroy\n  }, /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    open: open,\n    zIndex: zIndex,\n    mask: mask,\n    motion: maskMotion\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onAlign,\n    disabled: !open\n  }, function (resizeObserverRef) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      motionAppear: true,\n      motionEnter: true,\n      motionLeave: true,\n      removeOnLeave: false,\n      forceRender: forceRender,\n      leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n    }, motion, {\n      onAppearPrepare: onPrepare,\n      onEnterPrepare: onPrepare,\n      visible: open,\n      onVisibleChanged: function onVisibleChanged(nextVisible) {\n        var _motion$onVisibleChan;\n        motion === null || motion === void 0 || (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 || _motion$onVisibleChan.call(motion, nextVisible);\n        _onVisibleChanged(nextVisible);\n      }\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      var cls = classNames(prefixCls, motionClassName, className);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: composeRef(resizeObserverRef, ref, motionRef),\n        className: cls,\n        style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          '--arrow-x': \"\".concat(arrowPos.x || 0, \"px\"),\n          '--arrow-y': \"\".concat(arrowPos.y || 0, \"px\")\n        }, offsetStyle), miscStyle), motionStyle), {}, {\n          boxSizing: 'border-box',\n          zIndex: zIndex\n        }, style),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onClick: onClick,\n        onPointerDownCapture: onPointerDownCapture\n      }, arrow && /*#__PURE__*/React.createElement(Arrow, {\n        prefixCls: prefixCls,\n        arrow: arrow,\n        arrowPos: arrowPos,\n        align: align\n      }), /*#__PURE__*/React.createElement(PopupContent, {\n        cache: !open && !fresh\n      }, childNode));\n    });\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Popup.displayName = 'Popup';\n}\nexport default Popup;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "classNames", "CSSMotion", "ResizeObserver", "useLayoutEffect", "composeRef", "React", "Arrow", "Mask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Popup", "forwardRef", "props", "ref", "popup", "className", "prefixCls", "style", "target", "_onVisibleChanged", "onVisibleChanged", "open", "keepDom", "fresh", "onClick", "mask", "arrow", "arrowPos", "align", "motion", "maskMotion", "forceRender", "getPopupContainer", "autoDestroy", "Portal", "portal", "zIndex", "onMouseEnter", "onMouseLeave", "onPointerEnter", "onPointerDownCapture", "ready", "offsetX", "offsetY", "offsetR", "offsetB", "onAlign", "onPrepare", "stretch", "targetWidth", "targetHeight", "childNode", "isNodeVisible", "getPopupContainerNeedParams", "length", "_React$useState", "useState", "_React$useState2", "show", "setShow", "AUTO", "offsetStyle", "left", "top", "right", "bottom", "_experimental", "points", "dynamicInset", "alignRight", "alignBottom", "miscStyle", "includes", "height", "minHeight", "width", "min<PERSON><PERSON><PERSON>", "pointerEvents", "createElement", "getContainer", "onResize", "disabled", "resizeObserverRef", "motionAppear", "motionEnter", "motionLeave", "removeOnLeave", "leavedClassName", "concat", "onAppearPrepare", "onEnterPrepare", "visible", "nextVisible", "_motion$onVisibleChan", "call", "_ref", "motionRef", "motionClassName", "motionStyle", "cls", "x", "y", "boxSizing", "cache", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+trigger@2.2.7_947a2b7433feaf867fb1763028e83efc/node_modules/@rc-component/trigger/es/Popup/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport Arrow from \"./Arrow\";\nimport Mask from \"./Mask\";\nimport PopupContent from \"./PopupContent\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var popup = props.popup,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    style = props.style,\n    target = props.target,\n    _onVisibleChanged = props.onVisibleChanged,\n    open = props.open,\n    keepDom = props.keepDom,\n    fresh = props.fresh,\n    onClick = props.onClick,\n    mask = props.mask,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos,\n    align = props.align,\n    motion = props.motion,\n    maskMotion = props.maskMotion,\n    forceRender = props.forceRender,\n    getPopupContainer = props.getPopupContainer,\n    autoDestroy = props.autoDestroy,\n    Portal = props.portal,\n    zIndex = props.zIndex,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onPointerEnter = props.onPointerEnter,\n    onPointerDownCapture = props.onPointerDownCapture,\n    ready = props.ready,\n    offsetX = props.offsetX,\n    offsetY = props.offsetY,\n    offsetR = props.offsetR,\n    offsetB = props.offsetB,\n    onAlign = props.onAlign,\n    onPrepare = props.onPrepare,\n    stretch = props.stretch,\n    targetWidth = props.targetWidth,\n    targetHeight = props.targetHeight;\n  var childNode = typeof popup === 'function' ? popup() : popup;\n\n  // We can not remove holder only when motion finished.\n  var isNodeVisible = open || keepDom;\n\n  // ======================= Container ========================\n  var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;\n  var _React$useState = React.useState(!getPopupContainer || !getPopupContainerNeedParams),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    show = _React$useState2[0],\n    setShow = _React$useState2[1];\n\n  // Delay to show since `getPopupContainer` need target element\n  useLayoutEffect(function () {\n    if (!show && getPopupContainerNeedParams && target) {\n      setShow(true);\n    }\n  }, [show, getPopupContainerNeedParams, target]);\n\n  // ========================= Render =========================\n  if (!show) {\n    return null;\n  }\n\n  // >>>>> Offset\n  var AUTO = 'auto';\n  var offsetStyle = {\n    left: '-1000vw',\n    top: '-1000vh',\n    right: AUTO,\n    bottom: AUTO\n  };\n\n  // Set align style\n  if (ready || !open) {\n    var _experimental;\n    var points = align.points;\n    var dynamicInset = align.dynamicInset || ((_experimental = align._experimental) === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset);\n    var alignRight = dynamicInset && points[0][1] === 'r';\n    var alignBottom = dynamicInset && points[0][0] === 'b';\n    if (alignRight) {\n      offsetStyle.right = offsetR;\n      offsetStyle.left = AUTO;\n    } else {\n      offsetStyle.left = offsetX;\n      offsetStyle.right = AUTO;\n    }\n    if (alignBottom) {\n      offsetStyle.bottom = offsetB;\n      offsetStyle.top = AUTO;\n    } else {\n      offsetStyle.top = offsetY;\n      offsetStyle.bottom = AUTO;\n    }\n  }\n\n  // >>>>> Misc\n  var miscStyle = {};\n  if (stretch) {\n    if (stretch.includes('height') && targetHeight) {\n      miscStyle.height = targetHeight;\n    } else if (stretch.includes('minHeight') && targetHeight) {\n      miscStyle.minHeight = targetHeight;\n    }\n    if (stretch.includes('width') && targetWidth) {\n      miscStyle.width = targetWidth;\n    } else if (stretch.includes('minWidth') && targetWidth) {\n      miscStyle.minWidth = targetWidth;\n    }\n  }\n  if (!open) {\n    miscStyle.pointerEvents = 'none';\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: forceRender || isNodeVisible,\n    getContainer: getPopupContainer && function () {\n      return getPopupContainer(target);\n    },\n    autoDestroy: autoDestroy\n  }, /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    open: open,\n    zIndex: zIndex,\n    mask: mask,\n    motion: maskMotion\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onAlign,\n    disabled: !open\n  }, function (resizeObserverRef) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      motionAppear: true,\n      motionEnter: true,\n      motionLeave: true,\n      removeOnLeave: false,\n      forceRender: forceRender,\n      leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n    }, motion, {\n      onAppearPrepare: onPrepare,\n      onEnterPrepare: onPrepare,\n      visible: open,\n      onVisibleChanged: function onVisibleChanged(nextVisible) {\n        var _motion$onVisibleChan;\n        motion === null || motion === void 0 || (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 || _motion$onVisibleChan.call(motion, nextVisible);\n        _onVisibleChanged(nextVisible);\n      }\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      var cls = classNames(prefixCls, motionClassName, className);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: composeRef(resizeObserverRef, ref, motionRef),\n        className: cls,\n        style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          '--arrow-x': \"\".concat(arrowPos.x || 0, \"px\"),\n          '--arrow-y': \"\".concat(arrowPos.y || 0, \"px\")\n        }, offsetStyle), miscStyle), motionStyle), {}, {\n          boxSizing: 'border-box',\n          zIndex: zIndex\n        }, style),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onClick: onClick,\n        onPointerDownCapture: onPointerDownCapture\n      }, arrow && /*#__PURE__*/React.createElement(Arrow, {\n        prefixCls: prefixCls,\n        arrow: arrow,\n        arrowPos: arrowPos,\n        align: align\n      }), /*#__PURE__*/React.createElement(PopupContent, {\n        cache: !open && !fresh\n      }, childNode));\n    });\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Popup.displayName = 'Popup';\n}\nexport default Popup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,iBAAiB,GAAGP,KAAK,CAACQ,gBAAgB;IAC1CC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;IACvBC,IAAI,GAAGb,KAAK,CAACa,IAAI;IACjBC,KAAK,GAAGd,KAAK,CAACc,KAAK;IACnBC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,WAAW,GAAGnB,KAAK,CAACmB,WAAW;IAC/BC,iBAAiB,GAAGpB,KAAK,CAACoB,iBAAiB;IAC3CC,WAAW,GAAGrB,KAAK,CAACqB,WAAW;IAC/BC,MAAM,GAAGtB,KAAK,CAACuB,MAAM;IACrBC,MAAM,GAAGxB,KAAK,CAACwB,MAAM;IACrBC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,YAAY,GAAG1B,KAAK,CAAC0B,YAAY;IACjCC,cAAc,GAAG3B,KAAK,CAAC2B,cAAc;IACrCC,oBAAoB,GAAG5B,KAAK,CAAC4B,oBAAoB;IACjDC,KAAK,GAAG7B,KAAK,CAAC6B,KAAK;IACnBC,OAAO,GAAG9B,KAAK,CAAC8B,OAAO;IACvBC,OAAO,GAAG/B,KAAK,CAAC+B,OAAO;IACvBC,OAAO,GAAGhC,KAAK,CAACgC,OAAO;IACvBC,OAAO,GAAGjC,KAAK,CAACiC,OAAO;IACvBC,OAAO,GAAGlC,KAAK,CAACkC,OAAO;IACvBC,SAAS,GAAGnC,KAAK,CAACmC,SAAS;IAC3BC,OAAO,GAAGpC,KAAK,CAACoC,OAAO;IACvBC,WAAW,GAAGrC,KAAK,CAACqC,WAAW;IAC/BC,YAAY,GAAGtC,KAAK,CAACsC,YAAY;EACnC,IAAIC,SAAS,GAAG,OAAOrC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;;EAE7D;EACA,IAAIsC,aAAa,GAAG/B,IAAI,IAAIC,OAAO;;EAEnC;EACA,IAAI+B,2BAA2B,GAAG,CAACrB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACsB,MAAM,IAAI,CAAC;EACtI,IAAIC,eAAe,GAAGjD,KAAK,CAACkD,QAAQ,CAAC,CAACxB,iBAAiB,IAAI,CAACqB,2BAA2B,CAAC;IACtFI,gBAAgB,GAAGzD,cAAc,CAACuD,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAE/B;EACArD,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACsD,IAAI,IAAIL,2BAA2B,IAAInC,MAAM,EAAE;MAClDyC,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC,EAAE,CAACD,IAAI,EAAEL,2BAA2B,EAAEnC,MAAM,CAAC,CAAC;;EAE/C;EACA,IAAI,CAACwC,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAIE,IAAI,GAAG,MAAM;EACjB,IAAIC,WAAW,GAAG;IAChBC,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAEJ,IAAI;IACXK,MAAM,EAAEL;EACV,CAAC;;EAED;EACA,IAAInB,KAAK,IAAI,CAACpB,IAAI,EAAE;IAClB,IAAI6C,aAAa;IACjB,IAAIC,MAAM,GAAGvC,KAAK,CAACuC,MAAM;IACzB,IAAIC,YAAY,GAAGxC,KAAK,CAACwC,YAAY,KAAK,CAACF,aAAa,GAAGtC,KAAK,CAACsC,aAAa,MAAM,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACE,YAAY,CAAC;IAC3J,IAAIC,UAAU,GAAGD,YAAY,IAAID,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IACrD,IAAIG,WAAW,GAAGF,YAAY,IAAID,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IACtD,IAAIE,UAAU,EAAE;MACdR,WAAW,CAACG,KAAK,GAAGpB,OAAO;MAC3BiB,WAAW,CAACC,IAAI,GAAGF,IAAI;IACzB,CAAC,MAAM;MACLC,WAAW,CAACC,IAAI,GAAGpB,OAAO;MAC1BmB,WAAW,CAACG,KAAK,GAAGJ,IAAI;IAC1B;IACA,IAAIU,WAAW,EAAE;MACfT,WAAW,CAACI,MAAM,GAAGpB,OAAO;MAC5BgB,WAAW,CAACE,GAAG,GAAGH,IAAI;IACxB,CAAC,MAAM;MACLC,WAAW,CAACE,GAAG,GAAGpB,OAAO;MACzBkB,WAAW,CAACI,MAAM,GAAGL,IAAI;IAC3B;EACF;;EAEA;EACA,IAAIW,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIvB,OAAO,EAAE;IACX,IAAIA,OAAO,CAACwB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,EAAE;MAC9CqB,SAAS,CAACE,MAAM,GAAGvB,YAAY;IACjC,CAAC,MAAM,IAAIF,OAAO,CAACwB,QAAQ,CAAC,WAAW,CAAC,IAAItB,YAAY,EAAE;MACxDqB,SAAS,CAACG,SAAS,GAAGxB,YAAY;IACpC;IACA,IAAIF,OAAO,CAACwB,QAAQ,CAAC,OAAO,CAAC,IAAIvB,WAAW,EAAE;MAC5CsB,SAAS,CAACI,KAAK,GAAG1B,WAAW;IAC/B,CAAC,MAAM,IAAID,OAAO,CAACwB,QAAQ,CAAC,UAAU,CAAC,IAAIvB,WAAW,EAAE;MACtDsB,SAAS,CAACK,QAAQ,GAAG3B,WAAW;IAClC;EACF;EACA,IAAI,CAAC5B,IAAI,EAAE;IACTkD,SAAS,CAACM,aAAa,GAAG,MAAM;EAClC;EACA,OAAO,aAAavE,KAAK,CAACwE,aAAa,CAAC5C,MAAM,EAAE;IAC9Cb,IAAI,EAAEU,WAAW,IAAIqB,aAAa;IAClC2B,YAAY,EAAE/C,iBAAiB,IAAI,YAAY;MAC7C,OAAOA,iBAAiB,CAACd,MAAM,CAAC;IAClC,CAAC;IACDe,WAAW,EAAEA;EACf,CAAC,EAAE,aAAa3B,KAAK,CAACwE,aAAa,CAACtE,IAAI,EAAE;IACxCQ,SAAS,EAAEA,SAAS;IACpBK,IAAI,EAAEA,IAAI;IACVe,MAAM,EAAEA,MAAM;IACdX,IAAI,EAAEA,IAAI;IACVI,MAAM,EAAEC;EACV,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAACwE,aAAa,CAAC3E,cAAc,EAAE;IACnD6E,QAAQ,EAAElC,OAAO;IACjBmC,QAAQ,EAAE,CAAC5D;EACb,CAAC,EAAE,UAAU6D,iBAAiB,EAAE;IAC9B,OAAO,aAAa5E,KAAK,CAACwE,aAAa,CAAC5E,SAAS,EAAEJ,QAAQ,CAAC;MAC1DqF,YAAY,EAAE,IAAI;MAClBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,KAAK;MACpBvD,WAAW,EAAEA,WAAW;MACxBwD,eAAe,EAAE,EAAE,CAACC,MAAM,CAACxE,SAAS,EAAE,SAAS;IACjD,CAAC,EAAEa,MAAM,EAAE;MACT4D,eAAe,EAAE1C,SAAS;MAC1B2C,cAAc,EAAE3C,SAAS;MACzB4C,OAAO,EAAEtE,IAAI;MACbD,gBAAgB,EAAE,SAASA,gBAAgBA,CAACwE,WAAW,EAAE;QACvD,IAAIC,qBAAqB;QACzBhE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAI,CAACgE,qBAAqB,GAAGhE,MAAM,CAACT,gBAAgB,MAAM,IAAI,IAAIyE,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,IAAI,CAACjE,MAAM,EAAE+D,WAAW,CAAC;QACzLzE,iBAAiB,CAACyE,WAAW,CAAC;MAChC;IACF,CAAC,CAAC,EAAE,UAAUG,IAAI,EAAEC,SAAS,EAAE;MAC7B,IAAIC,eAAe,GAAGF,IAAI,CAAChF,SAAS;QAClCmF,WAAW,GAAGH,IAAI,CAAC9E,KAAK;MAC1B,IAAIkF,GAAG,GAAGlG,UAAU,CAACe,SAAS,EAAEiF,eAAe,EAAElF,SAAS,CAAC;MAC3D,OAAO,aAAaT,KAAK,CAACwE,aAAa,CAAC,KAAK,EAAE;QAC7CjE,GAAG,EAAER,UAAU,CAAC6E,iBAAiB,EAAErE,GAAG,EAAEmF,SAAS,CAAC;QAClDjF,SAAS,EAAEoF,GAAG;QACdlF,KAAK,EAAElB,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UAC7D,WAAW,EAAE,EAAE,CAACyF,MAAM,CAAC7D,QAAQ,CAACyE,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;UAC7C,WAAW,EAAE,EAAE,CAACZ,MAAM,CAAC7D,QAAQ,CAAC0E,CAAC,IAAI,CAAC,EAAE,IAAI;QAC9C,CAAC,EAAExC,WAAW,CAAC,EAAEU,SAAS,CAAC,EAAE2B,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7CI,SAAS,EAAE,YAAY;UACvBlE,MAAM,EAAEA;QACV,CAAC,EAAEnB,KAAK,CAAC;QACToB,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA,YAAY;QAC1BC,cAAc,EAAEA,cAAc;QAC9Bf,OAAO,EAAEA,OAAO;QAChBgB,oBAAoB,EAAEA;MACxB,CAAC,EAAEd,KAAK,IAAI,aAAapB,KAAK,CAACwE,aAAa,CAACvE,KAAK,EAAE;QAClDS,SAAS,EAAEA,SAAS;QACpBU,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA,QAAQ;QAClBC,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACwE,aAAa,CAACrE,YAAY,EAAE;QACjD8F,KAAK,EAAE,CAAClF,IAAI,IAAI,CAACE;MACnB,CAAC,EAAE4B,SAAS,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChG,KAAK,CAACiG,WAAW,GAAG,OAAO;AAC7B;AACA,eAAejG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nvar Notify = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    _props$duration = props.duration,\n    duration = _props$duration === void 0 ? 4.5 : _props$duration,\n    showProgress = props.showProgress,\n    _props$pauseOnHover = props.pauseOnHover,\n    pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover,\n    eventKey = props.eventKey,\n    content = props.content,\n    closable = props.closable,\n    _props$closeIcon = props.closeIcon,\n    closeIcon = _props$closeIcon === void 0 ? 'x' : _props$closeIcon,\n    divProps = props.props,\n    onClick = props.onClick,\n    onNoticeClose = props.onNoticeClose,\n    times = props.times,\n    forcedHovering = props.hovering;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hovering = _React$useState2[0],\n    setHovering = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    percent = _React$useState4[0],\n    setPercent = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    spentTime = _React$useState6[0],\n    setSpentTime = _React$useState6[1];\n  var mergedHovering = forcedHovering || hovering;\n  var mergedShowProgress = duration > 0 && showProgress;\n\n  // ======================== Close =========================\n  var onInternalClose = function onInternalClose() {\n    onNoticeClose(eventKey);\n  };\n  var onCloseKeyDown = function onCloseKeyDown(e) {\n    if (e.key === 'Enter' || e.code === 'Enter' || e.keyCode === KeyCode.ENTER) {\n      onInternalClose();\n    }\n  };\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    if (!mergedHovering && duration > 0) {\n      var start = Date.now() - spentTime;\n      var timeout = setTimeout(function () {\n        onInternalClose();\n      }, duration * 1000 - spentTime);\n      return function () {\n        if (pauseOnHover) {\n          clearTimeout(timeout);\n        }\n        setSpentTime(Date.now() - start);\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, mergedHovering, times]);\n  React.useEffect(function () {\n    if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n      var start = performance.now();\n      var animationFrame;\n      var calculate = function calculate() {\n        cancelAnimationFrame(animationFrame);\n        animationFrame = requestAnimationFrame(function (timestamp) {\n          var runtime = timestamp + spentTime - start;\n          var progress = Math.min(runtime / (duration * 1000), 1);\n          setPercent(progress * 100);\n          if (progress < 1) {\n            calculate();\n          }\n        });\n      };\n      calculate();\n      return function () {\n        if (pauseOnHover) {\n          cancelAnimationFrame(animationFrame);\n        }\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, spentTime, mergedHovering, mergedShowProgress, times]);\n\n  // ======================== Closable ========================\n  var closableObj = React.useMemo(function () {\n    if (_typeof(closable) === 'object' && closable !== null) {\n      return closable;\n    }\n    if (closable) {\n      return {\n        closeIcon: closeIcon\n      };\n    }\n    return {};\n  }, [closable, closeIcon]);\n  var ariaProps = pickAttrs(closableObj, true);\n\n  // ======================== Progress ========================\n  var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n\n  // ======================== Render ========================\n  var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    ref: ref,\n    className: classNames(noticePrefixCls, className, _defineProperty({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n    style: style,\n    onMouseEnter: function onMouseEnter(e) {\n      var _divProps$onMouseEnte;\n      setHovering(true);\n      divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n    },\n    onMouseLeave: function onMouseLeave(e) {\n      var _divProps$onMouseLeav;\n      setHovering(false);\n      divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n    },\n    onClick: onClick\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(noticePrefixCls, \"-content\")\n  }, content), closable && /*#__PURE__*/React.createElement(\"a\", _extends({\n    tabIndex: 0,\n    className: \"\".concat(noticePrefixCls, \"-close\"),\n    onKeyDown: onCloseKeyDown,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    onClick: function onClick(e) {\n      e.preventDefault();\n      e.stopPropagation();\n      onInternalClose();\n    }\n  }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/React.createElement(\"progress\", {\n    className: \"\".concat(noticePrefixCls, \"-progress\"),\n    max: \"100\",\n    value: validPercent\n  }, validPercent + '%'));\n});\nexport default Notify;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "classNames", "KeyCode", "React", "pickAttrs", "Notify", "forwardRef", "props", "ref", "prefixCls", "style", "className", "_props$duration", "duration", "showProgress", "_props$pauseOnHover", "pauseOnHover", "eventKey", "content", "closable", "_props$closeIcon", "closeIcon", "divProps", "onClick", "onNoticeClose", "times", "forcedHovering", "hovering", "_React$useState", "useState", "_React$useState2", "setHovering", "_React$useState3", "_React$useState4", "percent", "setPercent", "_React$useState5", "_React$useState6", "spentTime", "setSpentTime", "mergedHovering", "mergedShowProgress", "onInternalClose", "onCloseKeyDown", "e", "key", "code", "keyCode", "ENTER", "useEffect", "start", "Date", "now", "timeout", "setTimeout", "clearTimeout", "performance", "animationFrame", "calculate", "cancelAnimationFrame", "requestAnimationFrame", "timestamp", "runtime", "progress", "Math", "min", "closableObj", "useMemo", "ariaProps", "validPercent", "noticePrefixCls", "concat", "createElement", "onMouseEnter", "_divProps$onMouseEnte", "call", "onMouseLeave", "_divProps$onMouseLeav", "tabIndex", "onKeyDown", "preventDefault", "stopPropagation", "max", "value"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-notification@5.6.4_react_4c4d643d02667c590803e33b81c2ec6f/node_modules/rc-notification/es/Notice.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nvar Notify = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    _props$duration = props.duration,\n    duration = _props$duration === void 0 ? 4.5 : _props$duration,\n    showProgress = props.showProgress,\n    _props$pauseOnHover = props.pauseOnHover,\n    pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover,\n    eventKey = props.eventKey,\n    content = props.content,\n    closable = props.closable,\n    _props$closeIcon = props.closeIcon,\n    closeIcon = _props$closeIcon === void 0 ? 'x' : _props$closeIcon,\n    divProps = props.props,\n    onClick = props.onClick,\n    onNoticeClose = props.onNoticeClose,\n    times = props.times,\n    forcedHovering = props.hovering;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hovering = _React$useState2[0],\n    setHovering = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    percent = _React$useState4[0],\n    setPercent = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    spentTime = _React$useState6[0],\n    setSpentTime = _React$useState6[1];\n  var mergedHovering = forcedHovering || hovering;\n  var mergedShowProgress = duration > 0 && showProgress;\n\n  // ======================== Close =========================\n  var onInternalClose = function onInternalClose() {\n    onNoticeClose(eventKey);\n  };\n  var onCloseKeyDown = function onCloseKeyDown(e) {\n    if (e.key === 'Enter' || e.code === 'Enter' || e.keyCode === KeyCode.ENTER) {\n      onInternalClose();\n    }\n  };\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    if (!mergedHovering && duration > 0) {\n      var start = Date.now() - spentTime;\n      var timeout = setTimeout(function () {\n        onInternalClose();\n      }, duration * 1000 - spentTime);\n      return function () {\n        if (pauseOnHover) {\n          clearTimeout(timeout);\n        }\n        setSpentTime(Date.now() - start);\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, mergedHovering, times]);\n  React.useEffect(function () {\n    if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n      var start = performance.now();\n      var animationFrame;\n      var calculate = function calculate() {\n        cancelAnimationFrame(animationFrame);\n        animationFrame = requestAnimationFrame(function (timestamp) {\n          var runtime = timestamp + spentTime - start;\n          var progress = Math.min(runtime / (duration * 1000), 1);\n          setPercent(progress * 100);\n          if (progress < 1) {\n            calculate();\n          }\n        });\n      };\n      calculate();\n      return function () {\n        if (pauseOnHover) {\n          cancelAnimationFrame(animationFrame);\n        }\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, spentTime, mergedHovering, mergedShowProgress, times]);\n\n  // ======================== Closable ========================\n  var closableObj = React.useMemo(function () {\n    if (_typeof(closable) === 'object' && closable !== null) {\n      return closable;\n    }\n    if (closable) {\n      return {\n        closeIcon: closeIcon\n      };\n    }\n    return {};\n  }, [closable, closeIcon]);\n  var ariaProps = pickAttrs(closableObj, true);\n\n  // ======================== Progress ========================\n  var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n\n  // ======================== Render ========================\n  var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    ref: ref,\n    className: classNames(noticePrefixCls, className, _defineProperty({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n    style: style,\n    onMouseEnter: function onMouseEnter(e) {\n      var _divProps$onMouseEnte;\n      setHovering(true);\n      divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n    },\n    onMouseLeave: function onMouseLeave(e) {\n      var _divProps$onMouseLeav;\n      setHovering(false);\n      divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n    },\n    onClick: onClick\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(noticePrefixCls, \"-content\")\n  }, content), closable && /*#__PURE__*/React.createElement(\"a\", _extends({\n    tabIndex: 0,\n    className: \"\".concat(noticePrefixCls, \"-close\"),\n    onKeyDown: onCloseKeyDown,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    onClick: function onClick(e) {\n      e.preventDefault();\n      e.stopPropagation();\n      onInternalClose();\n    }\n  }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/React.createElement(\"progress\", {\n    className: \"\".concat(noticePrefixCls, \"-progress\"),\n    max: \"100\",\n    value: validPercent\n  }, validPercent + '%'));\n});\nexport default Notify;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,IAAIC,MAAM,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,eAAe,GAAGL,KAAK,CAACM,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,eAAe;IAC7DE,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,mBAAmB,GAAGR,KAAK,CAACS,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;IAC1EE,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,gBAAgB,GAAGb,KAAK,CAACc,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,gBAAgB;IAChEE,QAAQ,GAAGf,KAAK,CAACA,KAAK;IACtBgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,aAAa,GAAGjB,KAAK,CAACiB,aAAa;IACnCC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,cAAc,GAAGnB,KAAK,CAACoB,QAAQ;EACjC,IAAIC,eAAe,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAG9B,cAAc,CAAC4B,eAAe,EAAE,CAAC,CAAC;IACrDD,QAAQ,GAAGG,gBAAgB,CAAC,CAAC,CAAC;IAC9BC,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIE,gBAAgB,GAAG7B,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;IACtCI,gBAAgB,GAAGjC,cAAc,CAACgC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAGjC,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;IACtCQ,gBAAgB,GAAGrC,cAAc,CAACoC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,cAAc,GAAGd,cAAc,IAAIC,QAAQ;EAC/C,IAAIc,kBAAkB,GAAG5B,QAAQ,GAAG,CAAC,IAAIC,YAAY;;EAErD;EACA,IAAI4B,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/ClB,aAAa,CAACP,QAAQ,CAAC;EACzB,CAAC;EACD,IAAI0B,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;IAC9C,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACE,IAAI,KAAK,OAAO,IAAIF,CAAC,CAACG,OAAO,KAAK7C,OAAO,CAAC8C,KAAK,EAAE;MAC1EN,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,KAAK,CAAC8C,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACT,cAAc,IAAI3B,QAAQ,GAAG,CAAC,EAAE;MACnC,IAAIqC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGd,SAAS;MAClC,IAAIe,OAAO,GAAGC,UAAU,CAAC,YAAY;QACnCZ,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE7B,QAAQ,GAAG,IAAI,GAAGyB,SAAS,CAAC;MAC/B,OAAO,YAAY;QACjB,IAAItB,YAAY,EAAE;UAChBuC,YAAY,CAACF,OAAO,CAAC;QACvB;QACAd,YAAY,CAACY,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,CAAC;MAClC,CAAC;IACH;IACA;EACF,CAAC,EAAE,CAACrC,QAAQ,EAAE2B,cAAc,EAAEf,KAAK,CAAC,CAAC;EACrCtB,KAAK,CAAC8C,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACT,cAAc,IAAIC,kBAAkB,KAAKzB,YAAY,IAAIsB,SAAS,KAAK,CAAC,CAAC,EAAE;MAC9E,IAAIY,KAAK,GAAGM,WAAW,CAACJ,GAAG,CAAC,CAAC;MAC7B,IAAIK,cAAc;MAClB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;QACnCC,oBAAoB,CAACF,cAAc,CAAC;QACpCA,cAAc,GAAGG,qBAAqB,CAAC,UAAUC,SAAS,EAAE;UAC1D,IAAIC,OAAO,GAAGD,SAAS,GAAGvB,SAAS,GAAGY,KAAK;UAC3C,IAAIa,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,IAAIjD,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;UACvDsB,UAAU,CAAC4B,QAAQ,GAAG,GAAG,CAAC;UAC1B,IAAIA,QAAQ,GAAG,CAAC,EAAE;YAChBL,SAAS,CAAC,CAAC;UACb;QACF,CAAC,CAAC;MACJ,CAAC;MACDA,SAAS,CAAC,CAAC;MACX,OAAO,YAAY;QACjB,IAAI1C,YAAY,EAAE;UAChB2C,oBAAoB,CAACF,cAAc,CAAC;QACtC;MACF,CAAC;IACH;IACA;EACF,CAAC,EAAE,CAAC5C,QAAQ,EAAEyB,SAAS,EAAEE,cAAc,EAAEC,kBAAkB,EAAEhB,KAAK,CAAC,CAAC;;EAEpE;EACA,IAAIyC,WAAW,GAAG/D,KAAK,CAACgE,OAAO,CAAC,YAAY;IAC1C,IAAIpE,OAAO,CAACoB,QAAQ,CAAC,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACvD,OAAOA,QAAQ;IACjB;IACA,IAAIA,QAAQ,EAAE;MACZ,OAAO;QACLE,SAAS,EAAEA;MACb,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACF,QAAQ,EAAEE,SAAS,CAAC,CAAC;EACzB,IAAI+C,SAAS,GAAGhE,SAAS,CAAC8D,WAAW,EAAE,IAAI,CAAC;;EAE5C;EACA,IAAIG,YAAY,GAAG,GAAG,IAAI,CAACnC,OAAO,IAAIA,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGA,OAAO,GAAG,GAAG,GAAG,GAAG,GAAGA,OAAO,CAAC;;EAEtF;EACA,IAAIoC,eAAe,GAAG,EAAE,CAACC,MAAM,CAAC9D,SAAS,EAAE,SAAS,CAAC;EACrD,OAAO,aAAaN,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE3E,QAAQ,CAAC,CAAC,CAAC,EAAEyB,QAAQ,EAAE;IACpEd,GAAG,EAAEA,GAAG;IACRG,SAAS,EAAEV,UAAU,CAACqE,eAAe,EAAE3D,SAAS,EAAEb,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyE,MAAM,CAACD,eAAe,EAAE,WAAW,CAAC,EAAEnD,QAAQ,CAAC,CAAC;IACzHT,KAAK,EAAEA,KAAK;IACZ+D,YAAY,EAAE,SAASA,YAAYA,CAAC7B,CAAC,EAAE;MACrC,IAAI8B,qBAAqB;MACzB3C,WAAW,CAAC,IAAI,CAAC;MACjBT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAI,CAACoD,qBAAqB,GAAGpD,QAAQ,CAACmD,YAAY,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,IAAI,CAACrD,QAAQ,EAAEsB,CAAC,CAAC;IACrL,CAAC;IACDgC,YAAY,EAAE,SAASA,YAAYA,CAAChC,CAAC,EAAE;MACrC,IAAIiC,qBAAqB;MACzB9C,WAAW,CAAC,KAAK,CAAC;MAClBT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAI,CAACuD,qBAAqB,GAAGvD,QAAQ,CAACsD,YAAY,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACF,IAAI,CAACrD,QAAQ,EAAEsB,CAAC,CAAC;IACrL,CAAC;IACDrB,OAAO,EAAEA;EACX,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE;IAC1C7D,SAAS,EAAE,EAAE,CAAC4D,MAAM,CAACD,eAAe,EAAE,UAAU;EAClD,CAAC,EAAEpD,OAAO,CAAC,EAAEC,QAAQ,IAAI,aAAahB,KAAK,CAACqE,aAAa,CAAC,GAAG,EAAE3E,QAAQ,CAAC;IACtEiF,QAAQ,EAAE,CAAC;IACXnE,SAAS,EAAE,EAAE,CAAC4D,MAAM,CAACD,eAAe,EAAE,QAAQ,CAAC;IAC/CS,SAAS,EAAEpC,cAAc;IACzB,YAAY,EAAE;EAChB,CAAC,EAAEyB,SAAS,EAAE;IACZ7C,OAAO,EAAE,SAASA,OAAOA,CAACqB,CAAC,EAAE;MAC3BA,CAAC,CAACoC,cAAc,CAAC,CAAC;MAClBpC,CAAC,CAACqC,eAAe,CAAC,CAAC;MACnBvC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,CAAC,EAAEwB,WAAW,CAAC7C,SAAS,CAAC,EAAEoB,kBAAkB,IAAI,aAAatC,KAAK,CAACqE,aAAa,CAAC,UAAU,EAAE;IAC7F7D,SAAS,EAAE,EAAE,CAAC4D,MAAM,CAACD,eAAe,EAAE,WAAW,CAAC;IAClDY,GAAG,EAAE,KAAK;IACVC,KAAK,EAAEd;EACT,CAAC,EAAEA,YAAY,GAAG,GAAG,CAAC,CAAC;AACzB,CAAC,CAAC;AACF,eAAehE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
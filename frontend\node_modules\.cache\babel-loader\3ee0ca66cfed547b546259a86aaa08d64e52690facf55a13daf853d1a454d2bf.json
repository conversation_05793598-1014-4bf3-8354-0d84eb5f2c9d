{"ast": null, "code": "import * as React from 'react';\nvar TreeSelectContext = /*#__PURE__*/React.createContext(null);\nexport default TreeSelectContext;", "map": {"version": 3, "names": ["React", "TreeSelectContext", "createContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree-select@5.27.0_react_436633a6a2f00ab1ebd4c6e59066bdb2/node_modules/rc-tree-select/es/TreeSelectContext.js"], "sourcesContent": ["import * as React from 'react';\nvar TreeSelectContext = /*#__PURE__*/React.createContext(null);\nexport default TreeSelectContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
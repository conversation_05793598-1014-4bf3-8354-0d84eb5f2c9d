{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { pathKey } from \"../Cache\";\nimport StyleContext from \"../StyleContext\";\nimport useCompatibleInsertionEffect from \"./useCompatibleInsertionEffect\";\nimport useEffectCleanupRegister from \"./useEffectCleanupRegister\";\nimport useHMR from \"./useHMR\";\nexport default function useGlobalCache(prefix, keyPath, cacheFn, onCacheRemove,\n// Add additional effect trigger by `useInsertionEffect`\nonCacheEffect) {\n  var _React$useContext = React.useContext(StyleContext),\n    globalCache = _React$useContext.cache;\n  var fullPath = [prefix].concat(_toConsumableArray(keyPath));\n  var fullPathStr = pathKey(fullPath);\n  var register = useEffectCleanupRegister([fullPathStr]);\n  var HMRUpdate = useHMR();\n  var buildCache = function buildCache(updater) {\n    globalCache.opUpdate(fullPathStr, function (prevCache) {\n      var _ref = prevCache || [undefined, undefined],\n        _ref2 = _slicedToArray(_ref, 2),\n        _ref2$ = _ref2[0],\n        times = _ref2$ === void 0 ? 0 : _ref2$,\n        cache = _ref2[1];\n\n      // HMR should always ignore cache since developer may change it\n      var tmpCache = cache;\n      if (process.env.NODE_ENV !== 'production' && cache && HMRUpdate) {\n        onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(tmpCache, HMRUpdate);\n        tmpCache = null;\n      }\n      var mergedCache = tmpCache || cacheFn();\n      var data = [times, mergedCache];\n\n      // Call updater if need additional logic\n      return updater ? updater(data) : data;\n    });\n  };\n\n  // Create cache\n  React.useMemo(function () {\n    buildCache();\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [fullPathStr]\n  /* eslint-enable */);\n  var cacheEntity = globalCache.opGet(fullPathStr);\n\n  // HMR clean the cache but not trigger `useMemo` again\n  // Let's fallback of this\n  // ref https://github.com/ant-design/cssinjs/issues/127\n  if (process.env.NODE_ENV !== 'production' && !cacheEntity) {\n    buildCache();\n    cacheEntity = globalCache.opGet(fullPathStr);\n  }\n  var cacheContent = cacheEntity[1];\n\n  // Remove if no need anymore\n  useCompatibleInsertionEffect(function () {\n    onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n  }, function (polyfill) {\n    // It's bad to call build again in effect.\n    // But we have to do this since StrictMode will call effect twice\n    // which will clear cache on the first time.\n    buildCache(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        times = _ref4[0],\n        cache = _ref4[1];\n      if (polyfill && times === 0) {\n        onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n      }\n      return [times + 1, cache];\n    });\n    return function () {\n      globalCache.opUpdate(fullPathStr, function (prevCache) {\n        var _ref5 = prevCache || [],\n          _ref6 = _slicedToArray(_ref5, 2),\n          _ref6$ = _ref6[0],\n          times = _ref6$ === void 0 ? 0 : _ref6$,\n          cache = _ref6[1];\n        var nextCount = times - 1;\n        if (nextCount === 0) {\n          // Always remove styles in useEffect callback\n          register(function () {\n            // With polyfill, registered callback will always be called synchronously\n            // But without polyfill, it will be called in effect clean up,\n            // And by that time this cache is cleaned up.\n            if (polyfill || !globalCache.opGet(fullPathStr)) {\n              onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(cache, false);\n            }\n          });\n          return null;\n        }\n        return [times - 1, cache];\n      });\n    };\n  }, [fullPathStr]);\n  return cacheContent;\n}", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "React", "path<PERSON><PERSON>", "StyleContext", "useCompatibleInsertionEffect", "useEffectCleanupRegister", "useHMR", "useGlobalCache", "prefix", "keyP<PERSON>", "cacheFn", "onCacheRemove", "onCacheEffect", "_React$useContext", "useContext", "globalCache", "cache", "fullPath", "concat", "fullPathStr", "register", "HMRUpdate", "buildCache", "updater", "opUpdate", "prevCache", "_ref", "undefined", "_ref2", "_ref2$", "times", "tmpCache", "process", "env", "NODE_ENV", "mergedCache", "data", "useMemo", "cacheEntity", "opGet", "cacheContent", "polyfill", "_ref3", "_ref4", "_ref5", "_ref6", "_ref6$", "nextCount"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/hooks/useGlobalCache.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { pathKey } from \"../Cache\";\nimport StyleContext from \"../StyleContext\";\nimport useCompatibleInsertionEffect from \"./useCompatibleInsertionEffect\";\nimport useEffectCleanupRegister from \"./useEffectCleanupRegister\";\nimport useHMR from \"./useHMR\";\nexport default function useGlobalCache(prefix, keyPath, cacheFn, onCacheRemove,\n// Add additional effect trigger by `useInsertionEffect`\nonCacheEffect) {\n  var _React$useContext = React.useContext(StyleContext),\n    globalCache = _React$useContext.cache;\n  var fullPath = [prefix].concat(_toConsumableArray(keyPath));\n  var fullPathStr = pathKey(fullPath);\n  var register = useEffectCleanupRegister([fullPathStr]);\n  var HMRUpdate = useHMR();\n  var buildCache = function buildCache(updater) {\n    globalCache.opUpdate(fullPathStr, function (prevCache) {\n      var _ref = prevCache || [undefined, undefined],\n        _ref2 = _slicedToArray(_ref, 2),\n        _ref2$ = _ref2[0],\n        times = _ref2$ === void 0 ? 0 : _ref2$,\n        cache = _ref2[1];\n\n      // HMR should always ignore cache since developer may change it\n      var tmpCache = cache;\n      if (process.env.NODE_ENV !== 'production' && cache && HMRUpdate) {\n        onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(tmpCache, HMRUpdate);\n        tmpCache = null;\n      }\n      var mergedCache = tmpCache || cacheFn();\n      var data = [times, mergedCache];\n\n      // Call updater if need additional logic\n      return updater ? updater(data) : data;\n    });\n  };\n\n  // Create cache\n  React.useMemo(function () {\n    buildCache();\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [fullPathStr]\n  /* eslint-enable */);\n\n  var cacheEntity = globalCache.opGet(fullPathStr);\n\n  // HMR clean the cache but not trigger `useMemo` again\n  // Let's fallback of this\n  // ref https://github.com/ant-design/cssinjs/issues/127\n  if (process.env.NODE_ENV !== 'production' && !cacheEntity) {\n    buildCache();\n    cacheEntity = globalCache.opGet(fullPathStr);\n  }\n  var cacheContent = cacheEntity[1];\n\n  // Remove if no need anymore\n  useCompatibleInsertionEffect(function () {\n    onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n  }, function (polyfill) {\n    // It's bad to call build again in effect.\n    // But we have to do this since StrictMode will call effect twice\n    // which will clear cache on the first time.\n    buildCache(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        times = _ref4[0],\n        cache = _ref4[1];\n      if (polyfill && times === 0) {\n        onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n      }\n      return [times + 1, cache];\n    });\n    return function () {\n      globalCache.opUpdate(fullPathStr, function (prevCache) {\n        var _ref5 = prevCache || [],\n          _ref6 = _slicedToArray(_ref5, 2),\n          _ref6$ = _ref6[0],\n          times = _ref6$ === void 0 ? 0 : _ref6$,\n          cache = _ref6[1];\n        var nextCount = times - 1;\n        if (nextCount === 0) {\n          // Always remove styles in useEffect callback\n          register(function () {\n            // With polyfill, registered callback will always be called synchronously\n            // But without polyfill, it will be called in effect clean up,\n            // And by that time this cache is cleaned up.\n            if (polyfill || !globalCache.opGet(fullPathStr)) {\n              onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(cache, false);\n            }\n          });\n          return null;\n        }\n        return [times - 1, cache];\n      });\n    };\n  }, [fullPathStr]);\n  return cacheContent;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,UAAU;AAClC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,MAAM,MAAM,UAAU;AAC7B,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa;AAC9E;AACAC,aAAa,EAAE;EACb,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACX,YAAY,CAAC;IACpDY,WAAW,GAAGF,iBAAiB,CAACG,KAAK;EACvC,IAAIC,QAAQ,GAAG,CAACT,MAAM,CAAC,CAACU,MAAM,CAAClB,kBAAkB,CAACS,OAAO,CAAC,CAAC;EAC3D,IAAIU,WAAW,GAAGjB,OAAO,CAACe,QAAQ,CAAC;EACnC,IAAIG,QAAQ,GAAGf,wBAAwB,CAAC,CAACc,WAAW,CAAC,CAAC;EACtD,IAAIE,SAAS,GAAGf,MAAM,CAAC,CAAC;EACxB,IAAIgB,UAAU,GAAG,SAASA,UAAUA,CAACC,OAAO,EAAE;IAC5CR,WAAW,CAACS,QAAQ,CAACL,WAAW,EAAE,UAAUM,SAAS,EAAE;MACrD,IAAIC,IAAI,GAAGD,SAAS,IAAI,CAACE,SAAS,EAAEA,SAAS,CAAC;QAC5CC,KAAK,GAAG7B,cAAc,CAAC2B,IAAI,EAAE,CAAC,CAAC;QAC/BG,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;QACjBE,KAAK,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;QACtCb,KAAK,GAAGY,KAAK,CAAC,CAAC,CAAC;;MAElB;MACA,IAAIG,QAAQ,GAAGf,KAAK;MACpB,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIlB,KAAK,IAAIK,SAAS,EAAE;QAC/DV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACoB,QAAQ,EAAEV,SAAS,CAAC;QACxFU,QAAQ,GAAG,IAAI;MACjB;MACA,IAAII,WAAW,GAAGJ,QAAQ,IAAIrB,OAAO,CAAC,CAAC;MACvC,IAAI0B,IAAI,GAAG,CAACN,KAAK,EAAEK,WAAW,CAAC;;MAE/B;MACA,OAAOZ,OAAO,GAAGA,OAAO,CAACa,IAAI,CAAC,GAAGA,IAAI;IACvC,CAAC,CAAC;EACJ,CAAC;;EAED;EACAnC,KAAK,CAACoC,OAAO,CAAC,YAAY;IACxBf,UAAU,CAAC,CAAC;EACd,CAAC,EAAE;EACH,CAACH,WAAW;EACZ,mBAAmB,CAAC;EAEpB,IAAImB,WAAW,GAAGvB,WAAW,CAACwB,KAAK,CAACpB,WAAW,CAAC;;EAEhD;EACA;EACA;EACA,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACI,WAAW,EAAE;IACzDhB,UAAU,CAAC,CAAC;IACZgB,WAAW,GAAGvB,WAAW,CAACwB,KAAK,CAACpB,WAAW,CAAC;EAC9C;EACA,IAAIqB,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;;EAEjC;EACAlC,4BAA4B,CAAC,YAAY;IACvCQ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAAC4B,YAAY,CAAC;EACnF,CAAC,EAAE,UAAUC,QAAQ,EAAE;IACrB;IACA;IACA;IACAnB,UAAU,CAAC,UAAUoB,KAAK,EAAE;MAC1B,IAAIC,KAAK,GAAG5C,cAAc,CAAC2C,KAAK,EAAE,CAAC,CAAC;QAClCZ,KAAK,GAAGa,KAAK,CAAC,CAAC,CAAC;QAChB3B,KAAK,GAAG2B,KAAK,CAAC,CAAC,CAAC;MAClB,IAAIF,QAAQ,IAAIX,KAAK,KAAK,CAAC,EAAE;QAC3BlB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAAC4B,YAAY,CAAC;MACnF;MACA,OAAO,CAACV,KAAK,GAAG,CAAC,EAAEd,KAAK,CAAC;IAC3B,CAAC,CAAC;IACF,OAAO,YAAY;MACjBD,WAAW,CAACS,QAAQ,CAACL,WAAW,EAAE,UAAUM,SAAS,EAAE;QACrD,IAAImB,KAAK,GAAGnB,SAAS,IAAI,EAAE;UACzBoB,KAAK,GAAG9C,cAAc,CAAC6C,KAAK,EAAE,CAAC,CAAC;UAChCE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;UACjBf,KAAK,GAAGgB,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;UACtC9B,KAAK,GAAG6B,KAAK,CAAC,CAAC,CAAC;QAClB,IAAIE,SAAS,GAAGjB,KAAK,GAAG,CAAC;QACzB,IAAIiB,SAAS,KAAK,CAAC,EAAE;UACnB;UACA3B,QAAQ,CAAC,YAAY;YACnB;YACA;YACA;YACA,IAAIqB,QAAQ,IAAI,CAAC1B,WAAW,CAACwB,KAAK,CAACpB,WAAW,CAAC,EAAE;cAC/CR,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACK,KAAK,EAAE,KAAK,CAAC;YACnF;UACF,CAAC,CAAC;UACF,OAAO,IAAI;QACb;QACA,OAAO,CAACc,KAAK,GAAG,CAAC,EAAEd,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACG,WAAW,CAAC,CAAC;EACjB,OAAOqB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
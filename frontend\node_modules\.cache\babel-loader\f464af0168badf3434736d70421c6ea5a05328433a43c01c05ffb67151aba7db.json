{"ast": null, "code": "import { createContext } from '@rc-component/context';\nexport var StaticContext = createContext(null);\nexport var GridContext = createContext(null);", "map": {"version": 3, "names": ["createContext", "StaticContext", "GridContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/VirtualTable/context.js"], "sourcesContent": ["import { createContext } from '@rc-component/context';\nexport var StaticContext = createContext(null);\nexport var GridContext = createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,aAAa,GAAGD,aAAa,CAAC,IAAI,CAAC;AAC9C,OAAO,IAAIE,WAAW,GAAGF,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
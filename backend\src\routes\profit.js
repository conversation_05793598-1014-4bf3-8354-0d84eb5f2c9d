const express = require('express');
const router = express.Router();
const { query } = require('express-validator');
const profitController = require('../controllers/profitController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const dateRangeValidation = [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式不正确'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式不正确'),
];

const groupByValidation = [
  query('group_by')
    .optional()
    .isIn(['day', 'week', 'month', 'year'])
    .withMessage('分组方式必须是: day, week, month, year'),
];

const limitValidation = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('限制数量必须在1-100之间'),
];

// 获取销售利润分析
router.get('/sales-analysis', 
  auth, 
  authorize('admin', 'manager'),
  [...dateRangeValidation, ...groupByValidation],
  profitController.getSalesProfitAnalysis
);

// 获取产品利润分析
router.get('/product-analysis',
  auth,
  authorize('admin', 'manager'),
  [...dateRangeValidation, ...limitValidation],
  profitController.getProductProfitAnalysis
);

// 获取客户利润分析
router.get('/customer-analysis',
  auth,
  authorize('admin', 'manager'),
  [...dateRangeValidation, ...limitValidation],
  profitController.getCustomerProfitAnalysis
);

// 获取成本分析
router.get('/cost-analysis',
  auth,
  authorize('admin', 'manager'),
  dateRangeValidation,
  profitController.getCostAnalysis
);

module.exports = router;

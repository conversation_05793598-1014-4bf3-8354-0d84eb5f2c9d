{"ast": null, "code": "import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext({});\nexport default CascaderContext;", "map": {"version": 3, "names": ["React", "CascaderContext", "createContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext({});\nexport default CascaderContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
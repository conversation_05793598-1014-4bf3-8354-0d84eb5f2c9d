{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"item\"];\nimport warning from \"rc-util/es/warning\";\n\n/**\n * `onClick` event return `info.item` which point to react node directly.\n * We should warning this since it will not work on FC.\n */\nexport function warnItemProp(_ref) {\n  var item = _ref.item,\n    restInfo = _objectWithoutProperties(_ref, _excluded);\n  Object.defineProperty(restInfo, 'item', {\n    get: function get() {\n      warning(false, '`info.item` is deprecated since we will move to function component that not provides React Node instance in future.');\n      return item;\n    }\n  });\n  return restInfo;\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_excluded", "warning", "warnItemProp", "_ref", "item", "restInfo", "Object", "defineProperty", "get"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-menu@9.16.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-menu/es/utils/warnUtil.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"item\"];\nimport warning from \"rc-util/es/warning\";\n\n/**\n * `onClick` event return `info.item` which point to react node directly.\n * We should warning this since it will not work on FC.\n */\nexport function warnItemProp(_ref) {\n  var item = _ref.item,\n    restInfo = _objectWithoutProperties(_ref, _excluded);\n  Object.defineProperty(restInfo, 'item', {\n    get: function get() {\n      warning(false, '`info.item` is deprecated since we will move to function component that not provides React Node instance in future.');\n      return item;\n    }\n  });\n  return restInfo;\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAOC,OAAO,MAAM,oBAAoB;;AAExC;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,QAAQ,GAAGN,wBAAwB,CAACI,IAAI,EAAEH,SAAS,CAAC;EACtDM,MAAM,CAACC,cAAc,CAACF,QAAQ,EAAE,MAAM,EAAE;IACtCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClBP,OAAO,CAAC,KAAK,EAAE,qHAAqH,CAAC;MACrI,OAAOG,IAAI;IACb;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
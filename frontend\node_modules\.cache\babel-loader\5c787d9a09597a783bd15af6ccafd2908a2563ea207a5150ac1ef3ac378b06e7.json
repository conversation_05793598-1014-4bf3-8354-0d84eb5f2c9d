{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genPresetsStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    colorTextQuaternary,\n    paddingXXS,\n    colorPickerPresetColorSize,\n    fontSizeSM,\n    colorText,\n    lineHeightSM,\n    lineWidth,\n    borderRadius,\n    colorFill,\n    colorWhite,\n    marginXXS,\n    paddingXS,\n    fontHeightSM\n  } = token;\n  return {\n    [`${componentCls}-presets`]: {\n      [`${antCls}-collapse-item > ${antCls}-collapse-header`]: {\n        padding: 0,\n        [`${antCls}-collapse-expand-icon`]: {\n          height: fontHeightSM,\n          color: colorTextQuaternary,\n          paddingInlineEnd: paddingXXS\n        }\n      },\n      [`${antCls}-collapse`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: marginXXS\n      },\n      [`${antCls}-collapse-item > ${antCls}-collapse-content > ${antCls}-collapse-content-box`]: {\n        padding: `${unit(paddingXS)} 0`\n      },\n      '&-label': {\n        fontSize: fontSizeSM,\n        color: colorText,\n        lineHeight: lineHeightSM\n      },\n      '&-items': {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: token.calc(marginXXS).mul(1.5).equal(),\n        [`${componentCls}-presets-color`]: {\n          position: 'relative',\n          cursor: 'pointer',\n          width: colorPickerPresetColorSize,\n          height: colorPickerPresetColorSize,\n          '&::before': {\n            content: '\"\"',\n            pointerEvents: 'none',\n            width: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            height: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            position: 'absolute',\n            top: token.calc(lineWidth).mul(-2).equal(),\n            insetInlineStart: token.calc(lineWidth).mul(-2).equal(),\n            borderRadius,\n            border: `${unit(lineWidth)} solid transparent`,\n            transition: `border-color ${token.motionDurationMid} ${token.motionEaseInBack}`\n          },\n          '&:hover::before': {\n            borderColor: colorFill\n          },\n          '&::after': {\n            boxSizing: 'border-box',\n            position: 'absolute',\n            top: '50%',\n            insetInlineStart: '21.5%',\n            display: 'table',\n            width: token.calc(colorPickerPresetColorSize).div(13).mul(5).equal(),\n            height: token.calc(colorPickerPresetColorSize).div(13).mul(8).equal(),\n            border: `${unit(token.lineWidthBold)} solid ${token.colorWhite}`,\n            borderTop: 0,\n            borderInlineStart: 0,\n            transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',\n            opacity: 0,\n            content: '\"\"',\n            transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`\n          },\n          [`&${componentCls}-presets-color-checked`]: {\n            '&::after': {\n              opacity: 1,\n              borderColor: colorWhite,\n              transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',\n              transition: `transform ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`\n            },\n            [`&${componentCls}-presets-color-bright`]: {\n              '&::after': {\n                borderColor: 'rgba(0, 0, 0, 0.45)'\n              }\n            }\n          }\n        }\n      },\n      '&-empty': {\n        fontSize: fontSizeSM,\n        color: colorTextQuaternary\n      }\n    }\n  };\n};\nexport default genPresetsStyle;", "map": {"version": 3, "names": ["unit", "genPresetsStyle", "token", "componentCls", "antCls", "colorTextQuaternary", "paddingXXS", "colorPickerPresetColorSize", "fontSizeSM", "colorText", "lineHeightSM", "lineWidth", "borderRadius", "colorFill", "colorWhite", "marginXXS", "paddingXS", "fontHeightSM", "padding", "height", "color", "paddingInlineEnd", "display", "flexDirection", "gap", "fontSize", "lineHeight", "flexWrap", "calc", "mul", "equal", "position", "cursor", "width", "content", "pointerEvents", "add", "top", "insetInlineStart", "border", "transition", "motionDurationMid", "motionEaseInBack", "borderColor", "boxSizing", "div", "lineWidthBold", "borderTop", "borderInlineStart", "transform", "opacity", "motionDurationFast", "motionEaseOutBack"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/style/presets.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPresetsStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    colorTextQuaternary,\n    paddingXXS,\n    colorPickerPresetColorSize,\n    fontSizeSM,\n    colorText,\n    lineHeightSM,\n    lineWidth,\n    borderRadius,\n    colorFill,\n    colorWhite,\n    marginXXS,\n    paddingXS,\n    fontHeightSM\n  } = token;\n  return {\n    [`${componentCls}-presets`]: {\n      [`${antCls}-collapse-item > ${antCls}-collapse-header`]: {\n        padding: 0,\n        [`${antCls}-collapse-expand-icon`]: {\n          height: fontHeightSM,\n          color: colorTextQuaternary,\n          paddingInlineEnd: paddingXXS\n        }\n      },\n      [`${antCls}-collapse`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: marginXXS\n      },\n      [`${antCls}-collapse-item > ${antCls}-collapse-content > ${antCls}-collapse-content-box`]: {\n        padding: `${unit(paddingXS)} 0`\n      },\n      '&-label': {\n        fontSize: fontSizeSM,\n        color: colorText,\n        lineHeight: lineHeightSM\n      },\n      '&-items': {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: token.calc(marginXXS).mul(1.5).equal(),\n        [`${componentCls}-presets-color`]: {\n          position: 'relative',\n          cursor: 'pointer',\n          width: colorPickerPresetColorSize,\n          height: colorPickerPresetColorSize,\n          '&::before': {\n            content: '\"\"',\n            pointerEvents: 'none',\n            width: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            height: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            position: 'absolute',\n            top: token.calc(lineWidth).mul(-2).equal(),\n            insetInlineStart: token.calc(lineWidth).mul(-2).equal(),\n            borderRadius,\n            border: `${unit(lineWidth)} solid transparent`,\n            transition: `border-color ${token.motionDurationMid} ${token.motionEaseInBack}`\n          },\n          '&:hover::before': {\n            borderColor: colorFill\n          },\n          '&::after': {\n            boxSizing: 'border-box',\n            position: 'absolute',\n            top: '50%',\n            insetInlineStart: '21.5%',\n            display: 'table',\n            width: token.calc(colorPickerPresetColorSize).div(13).mul(5).equal(),\n            height: token.calc(colorPickerPresetColorSize).div(13).mul(8).equal(),\n            border: `${unit(token.lineWidthBold)} solid ${token.colorWhite}`,\n            borderTop: 0,\n            borderInlineStart: 0,\n            transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',\n            opacity: 0,\n            content: '\"\"',\n            transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`\n          },\n          [`&${componentCls}-presets-color-checked`]: {\n            '&::after': {\n              opacity: 1,\n              borderColor: colorWhite,\n              transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',\n              transition: `transform ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`\n            },\n            [`&${componentCls}-presets-color-bright`]: {\n              '&::after': {\n                borderColor: 'rgba(0, 0, 0, 0.45)'\n              }\n            }\n          }\n        }\n      },\n      '&-empty': {\n        fontSize: fontSizeSM,\n        color: colorTextQuaternary\n      }\n    }\n  };\n};\nexport default genPresetsStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,mBAAmB;IACnBC,UAAU;IACVC,0BAA0B;IAC1BC,UAAU;IACVC,SAAS;IACTC,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGf,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGC,MAAM,oBAAoBA,MAAM,kBAAkB,GAAG;QACvDc,OAAO,EAAE,CAAC;QACV,CAAC,GAAGd,MAAM,uBAAuB,GAAG;UAClCe,MAAM,EAAEF,YAAY;UACpBG,KAAK,EAAEf,mBAAmB;UAC1BgB,gBAAgB,EAAEf;QACpB;MACF,CAAC;MACD,CAAC,GAAGF,MAAM,WAAW,GAAG;QACtBkB,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAET;MACP,CAAC;MACD,CAAC,GAAGX,MAAM,oBAAoBA,MAAM,uBAAuBA,MAAM,uBAAuB,GAAG;QACzFc,OAAO,EAAE,GAAGlB,IAAI,CAACgB,SAAS,CAAC;MAC7B,CAAC;MACD,SAAS,EAAE;QACTS,QAAQ,EAAEjB,UAAU;QACpBY,KAAK,EAAEX,SAAS;QAChBiB,UAAU,EAAEhB;MACd,CAAC;MACD,SAAS,EAAE;QACTY,OAAO,EAAE,MAAM;QACfK,QAAQ,EAAE,MAAM;QAChBH,GAAG,EAAEtB,KAAK,CAAC0B,IAAI,CAACb,SAAS,CAAC,CAACc,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;QAC3C,CAAC,GAAG3B,YAAY,gBAAgB,GAAG;UACjC4B,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE1B,0BAA0B;UACjCY,MAAM,EAAEZ,0BAA0B;UAClC,WAAW,EAAE;YACX2B,OAAO,EAAE,IAAI;YACbC,aAAa,EAAE,MAAM;YACrBF,KAAK,EAAE/B,KAAK,CAAC0B,IAAI,CAACrB,0BAA0B,CAAC,CAAC6B,GAAG,CAAClC,KAAK,CAAC0B,IAAI,CAACjB,SAAS,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACvFX,MAAM,EAAEjB,KAAK,CAAC0B,IAAI,CAACrB,0BAA0B,CAAC,CAAC6B,GAAG,CAAClC,KAAK,CAAC0B,IAAI,CAACjB,SAAS,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACxFC,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAEnC,KAAK,CAAC0B,IAAI,CAACjB,SAAS,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAC1CQ,gBAAgB,EAAEpC,KAAK,CAAC0B,IAAI,CAACjB,SAAS,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACvDlB,YAAY;YACZ2B,MAAM,EAAE,GAAGvC,IAAI,CAACW,SAAS,CAAC,oBAAoB;YAC9C6B,UAAU,EAAE,gBAAgBtC,KAAK,CAACuC,iBAAiB,IAAIvC,KAAK,CAACwC,gBAAgB;UAC/E,CAAC;UACD,iBAAiB,EAAE;YACjBC,WAAW,EAAE9B;UACf,CAAC;UACD,UAAU,EAAE;YACV+B,SAAS,EAAE,YAAY;YACvBb,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAE,KAAK;YACVC,gBAAgB,EAAE,OAAO;YACzBhB,OAAO,EAAE,OAAO;YAChBW,KAAK,EAAE/B,KAAK,CAAC0B,IAAI,CAACrB,0BAA0B,CAAC,CAACsC,GAAG,CAAC,EAAE,CAAC,CAAChB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACpEX,MAAM,EAAEjB,KAAK,CAAC0B,IAAI,CAACrB,0BAA0B,CAAC,CAACsC,GAAG,CAAC,EAAE,CAAC,CAAChB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACrES,MAAM,EAAE,GAAGvC,IAAI,CAACE,KAAK,CAAC4C,aAAa,CAAC,UAAU5C,KAAK,CAACY,UAAU,EAAE;YAChEiC,SAAS,EAAE,CAAC;YACZC,iBAAiB,EAAE,CAAC;YACpBC,SAAS,EAAE,6CAA6C;YACxDC,OAAO,EAAE,CAAC;YACVhB,OAAO,EAAE,IAAI;YACbM,UAAU,EAAE,OAAOtC,KAAK,CAACiD,kBAAkB,IAAIjD,KAAK,CAACwC,gBAAgB,aAAaxC,KAAK,CAACiD,kBAAkB;UAC5G,CAAC;UACD,CAAC,IAAIhD,YAAY,wBAAwB,GAAG;YAC1C,UAAU,EAAE;cACV+C,OAAO,EAAE,CAAC;cACVP,WAAW,EAAE7B,UAAU;cACvBmC,SAAS,EAAE,6CAA6C;cACxDT,UAAU,EAAE,aAAatC,KAAK,CAACuC,iBAAiB,IAAIvC,KAAK,CAACkD,iBAAiB,IAAIlD,KAAK,CAACiD,kBAAkB;YACzG,CAAC;YACD,CAAC,IAAIhD,YAAY,uBAAuB,GAAG;cACzC,UAAU,EAAE;gBACVwC,WAAW,EAAE;cACf;YACF;UACF;QACF;MACF,CAAC;MACD,SAAS,EAAE;QACTlB,QAAQ,EAAEjB,UAAU;QACpBY,KAAK,EAAEf;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
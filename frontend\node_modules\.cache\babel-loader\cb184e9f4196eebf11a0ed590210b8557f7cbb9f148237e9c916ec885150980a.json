{"ast": null, "code": "// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;", "map": {"version": 3, "names": ["genRtlStyle", "token", "componentCls", "direction"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/upload/style/rtl.js"], "sourcesContent": ["// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;"], "mappings": "AAAA;AACA,MAAMA,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,MAAM,GAAG;MACvBC,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC;AACD,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
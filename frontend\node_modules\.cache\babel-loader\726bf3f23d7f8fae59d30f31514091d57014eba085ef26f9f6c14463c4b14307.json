{"ast": null, "code": "import React from 'react';\nexport var NotificationContext = /*#__PURE__*/React.createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n  var children = _ref.children,\n    classNames = _ref.classNames;\n  return /*#__PURE__*/React.createElement(NotificationContext.Provider, {\n    value: {\n      classNames: classNames\n    }\n  }, children);\n};\nexport default NotificationProvider;", "map": {"version": 3, "names": ["React", "NotificationContext", "createContext", "NotificationProvider", "_ref", "children", "classNames", "createElement", "Provider", "value"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-notification@5.6.4_react_4c4d643d02667c590803e33b81c2ec6f/node_modules/rc-notification/es/NotificationProvider.js"], "sourcesContent": ["import React from 'react';\nexport var NotificationContext = /*#__PURE__*/React.createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n  var children = _ref.children,\n    classNames = _ref.classNames;\n  return /*#__PURE__*/React.createElement(NotificationContext.Provider, {\n    value: {\n      classNames: classNames\n    }\n  }, children);\n};\nexport default NotificationProvider;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,mBAAmB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACrE,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,IAAI,EAAE;EAC7D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,UAAU,GAAGF,IAAI,CAACE,UAAU;EAC9B,OAAO,aAAaN,KAAK,CAACO,aAAa,CAACN,mBAAmB,CAACO,QAAQ,EAAE;IACpEC,KAAK,EAAE;MACLH,UAAU,EAAEA;IACd;EACF,CAAC,EAAED,QAAQ,CAAC;AACd,CAAC;AACD,eAAeF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
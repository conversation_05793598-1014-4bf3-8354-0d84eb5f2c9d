{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// [times, realValue]\n\nvar SPLIT = '%';\n\n/** Connect key with `SPLIT` */\nexport function pathKey(keys) {\n  return keys.join(SPLIT);\n}\nvar Entity = /*#__PURE__*/function () {\n  function Entity(instanceId) {\n    _classCallCheck(this, Entity);\n    _defineProperty(this, \"instanceId\", void 0);\n    /** @private Internal cache map. Do not access this directly */\n    _defineProperty(this, \"cache\", new Map());\n    this.instanceId = instanceId;\n  }\n  _createClass(Entity, [{\n    key: \"get\",\n    value: function get(keys) {\n      return this.opGet(pathKey(keys));\n    }\n\n    /** A fast get cache with `get` concat. */\n  }, {\n    key: \"opGet\",\n    value: function opGet(keyPathStr) {\n      return this.cache.get(keyPathStr) || null;\n    }\n  }, {\n    key: \"update\",\n    value: function update(keys, valueFn) {\n      return this.opUpdate(pathKey(keys), valueFn);\n    }\n\n    /** A fast get cache with `get` concat. */\n  }, {\n    key: \"opUpdate\",\n    value: function opUpdate(keyPathStr, valueFn) {\n      var prevValue = this.cache.get(keyPathStr);\n      var nextValue = valueFn(prevValue);\n      if (nextValue === null) {\n        this.cache.delete(keyPathStr);\n      } else {\n        this.cache.set(keyPathStr, nextValue);\n      }\n    }\n  }]);\n  return Entity;\n}();\nexport default Entity;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "SPLIT", "path<PERSON><PERSON>", "keys", "join", "Entity", "instanceId", "Map", "key", "value", "get", "opGet", "keyPathStr", "cache", "update", "valueFn", "opUpdate", "prevValue", "nextValue", "delete", "set"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/Cache.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// [times, realValue]\n\nvar SPLIT = '%';\n\n/** Connect key with `SPLIT` */\nexport function pathKey(keys) {\n  return keys.join(SPLIT);\n}\nvar Entity = /*#__PURE__*/function () {\n  function Entity(instanceId) {\n    _classCallCheck(this, Entity);\n    _defineProperty(this, \"instanceId\", void 0);\n    /** @private Internal cache map. Do not access this directly */\n    _defineProperty(this, \"cache\", new Map());\n    this.instanceId = instanceId;\n  }\n  _createClass(Entity, [{\n    key: \"get\",\n    value: function get(keys) {\n      return this.opGet(pathKey(keys));\n    }\n\n    /** A fast get cache with `get` concat. */\n  }, {\n    key: \"opGet\",\n    value: function opGet(keyPathStr) {\n      return this.cache.get(keyPathStr) || null;\n    }\n  }, {\n    key: \"update\",\n    value: function update(keys, valueFn) {\n      return this.opUpdate(pathKey(keys), valueFn);\n    }\n\n    /** A fast get cache with `get` concat. */\n  }, {\n    key: \"opUpdate\",\n    value: function opUpdate(keyPathStr, valueFn) {\n      var prevValue = this.cache.get(keyPathStr);\n      var nextValue = valueFn(prevValue);\n      if (nextValue === null) {\n        this.cache.delete(keyPathStr);\n      } else {\n        this.cache.set(keyPathStr, nextValue);\n      }\n    }\n  }]);\n  return Entity;\n}();\nexport default Entity;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;;AAEA,IAAIC,KAAK,GAAG,GAAG;;AAEf;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;AACzB;AACA,IAAII,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACC,UAAU,EAAE;IAC1BR,eAAe,CAAC,IAAI,EAAEO,MAAM,CAAC;IAC7BL,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3C;IACAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAIO,GAAG,CAAC,CAAC,CAAC;IACzC,IAAI,CAACD,UAAU,GAAGA,UAAU;EAC9B;EACAP,YAAY,CAACM,MAAM,EAAE,CAAC;IACpBG,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACP,IAAI,EAAE;MACxB,OAAO,IAAI,CAACQ,KAAK,CAACT,OAAO,CAACC,IAAI,CAAC,CAAC;IAClC;;IAEA;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASE,KAAKA,CAACC,UAAU,EAAE;MAChC,OAAO,IAAI,CAACC,KAAK,CAACH,GAAG,CAACE,UAAU,CAAC,IAAI,IAAI;IAC3C;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASK,MAAMA,CAACX,IAAI,EAAEY,OAAO,EAAE;MACpC,OAAO,IAAI,CAACC,QAAQ,CAACd,OAAO,CAACC,IAAI,CAAC,EAAEY,OAAO,CAAC;IAC9C;;IAEA;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASO,QAAQA,CAACJ,UAAU,EAAEG,OAAO,EAAE;MAC5C,IAAIE,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACH,GAAG,CAACE,UAAU,CAAC;MAC1C,IAAIM,SAAS,GAAGH,OAAO,CAACE,SAAS,CAAC;MAClC,IAAIC,SAAS,KAAK,IAAI,EAAE;QACtB,IAAI,CAACL,KAAK,CAACM,MAAM,CAACP,UAAU,CAAC;MAC/B,CAAC,MAAM;QACL,IAAI,CAACC,KAAK,CAACO,GAAG,CAACR,UAAU,EAAEM,SAAS,CAAC;MACvC;IACF;EACF,CAAC,CAAC,CAAC;EACH,OAAOb,MAAM;AACf,CAAC,CAAC,CAAC;AACH,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
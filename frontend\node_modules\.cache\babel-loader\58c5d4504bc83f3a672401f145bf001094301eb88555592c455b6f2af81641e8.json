{"ast": null, "code": "import { FastColor } from '@ant-design/fast-color';\nexport const getAlphaColor = (baseColor, alpha) => new FastColor(baseColor).setA(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new FastColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};", "map": {"version": 3, "names": ["FastColor", "getAlphaColor", "baseColor", "alpha", "setA", "toRgbString", "getSolidColor", "brightness", "instance", "darken", "toHexString"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/theme/themes/default/colorAlgorithm.js"], "sourcesContent": ["import { FastColor } from '@ant-design/fast-color';\nexport const getAlphaColor = (baseColor, alpha) => new FastColor(baseColor).setA(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new FastColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK,IAAIH,SAAS,CAACE,SAAS,CAAC,CAACE,IAAI,CAACD,KAAK,CAAC,CAACE,WAAW,CAAC,CAAC;AACrG,OAAO,MAAMC,aAAa,GAAGA,CAACJ,SAAS,EAAEK,UAAU,KAAK;EACtD,MAAMC,QAAQ,GAAG,IAAIR,SAAS,CAACE,SAAS,CAAC;EACzC,OAAOM,QAAQ,CAACC,MAAM,CAACF,UAAU,CAAC,CAACG,WAAW,CAAC,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nfunction warningProps(props) {\n  var onPopupVisibleChange = props.onPopupVisibleChange,\n    popupVisible = props.popupVisible,\n    popupClassName = props.popupClassName,\n    popupPlacement = props.popupPlacement,\n    onDropdownVisibleChange = props.onDropdownVisibleChange;\n  warning(!onPopupVisibleChange, '`onPopupVisibleChange` is deprecated. Please use `onOpenChange` instead.');\n  warning(!onDropdownVisibleChange, '`onDropdownVisibleChange` is deprecated. Please use `onOpenChange` instead.');\n  warning(popupVisible === undefined, '`popupVisible` is deprecated. Please use `open` instead.');\n  warning(popupClassName === undefined, '`popupClassName` is deprecated. Please use `dropdownClassName` instead.');\n  warning(popupPlacement === undefined, '`popupPlacement` is deprecated. Please use `placement` instead.');\n}\n\n// value in Cascader options should not be null\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Cascader options should not be `null`.');\n          return true;\n        }\n        if (Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children])) {\n          return true;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;", "map": {"version": 3, "names": ["warning", "warningProps", "props", "onPopupVisibleChange", "popupVisible", "popupClassName", "popupPlacement", "onDropdownVisibleChange", "undefined", "warningNullOptions", "options", "fieldNames", "recursiveOptions", "optionsList", "i", "length", "option", "value", "Array", "isArray", "children"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/utils/warningPropsUtil.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nfunction warningProps(props) {\n  var onPopupVisibleChange = props.onPopupVisibleChange,\n    popupVisible = props.popupVisible,\n    popupClassName = props.popupClassName,\n    popupPlacement = props.popupPlacement,\n    onDropdownVisibleChange = props.onDropdownVisibleChange;\n  warning(!onPopupVisibleChange, '`onPopupVisibleChange` is deprecated. Please use `onOpenChange` instead.');\n  warning(!onDropdownVisibleChange, '`onDropdownVisibleChange` is deprecated. Please use `onOpenChange` instead.');\n  warning(popupVisible === undefined, '`popupVisible` is deprecated. Please use `open` instead.');\n  warning(popupClassName === undefined, '`popupClassName` is deprecated. Please use `dropdownClassName` instead.');\n  warning(popupPlacement === undefined, '`popupPlacement` is deprecated. Please use `placement` instead.');\n}\n\n// value in Cascader options should not be null\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Cascader options should not be `null`.');\n          return true;\n        }\n        if (Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children])) {\n          return true;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,oBAAoB,GAAGD,KAAK,CAACC,oBAAoB;IACnDC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,uBAAuB,GAAGL,KAAK,CAACK,uBAAuB;EACzDP,OAAO,CAAC,CAACG,oBAAoB,EAAE,0EAA0E,CAAC;EAC1GH,OAAO,CAAC,CAACO,uBAAuB,EAAE,6EAA6E,CAAC;EAChHP,OAAO,CAACI,YAAY,KAAKI,SAAS,EAAE,0DAA0D,CAAC;EAC/FR,OAAO,CAACK,cAAc,KAAKG,SAAS,EAAE,yEAAyE,CAAC;EAChHR,OAAO,CAACM,cAAc,KAAKE,SAAS,EAAE,iEAAiE,CAAC;AAC1G;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACtD,IAAID,OAAO,EAAE;IACX,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAE;MAC5D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,IAAIE,MAAM,GAAGH,WAAW,CAACC,CAAC,CAAC;QAC3B,IAAIE,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACM,KAAK,CAAC,KAAK,IAAI,EAAE;UAC7FjB,OAAO,CAAC,KAAK,EAAE,mDAAmD,CAAC;UACnE,OAAO,IAAI;QACb;QACA,IAAIkB,KAAK,CAACC,OAAO,CAACH,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,QAAQ,CAAC,CAAC,IAAIR,gBAAgB,CAACI,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,QAAQ,CAAC,CAAC,EAAE;UAC/M,OAAO,IAAI;QACb;MACF;IACF,CAAC;IACDR,gBAAgB,CAACF,OAAO,CAAC;EAC3B;AACF;AACA,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
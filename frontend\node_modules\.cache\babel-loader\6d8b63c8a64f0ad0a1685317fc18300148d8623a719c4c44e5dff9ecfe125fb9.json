{"ast": null, "code": "\"use client\";\n\nimport { genVirtualTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nconst RcVirtualTable = genVirtualTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});\nexport default RcVirtualTable;", "map": {"version": 3, "names": ["genVirtualTable", "RcVirtualTable", "prev", "next", "_renderTimes", "prevRenderTimes", "nextRenderTimes"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/RcTable/VirtualTable.js"], "sourcesContent": ["\"use client\";\n\nimport { genVirtualTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nconst RcVirtualTable = genVirtualTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});\nexport default RcVirtualTable;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,UAAU;AAC1C;AACA;AACA;AACA,MAAMC,cAAc,GAAGD,eAAe,CAAC,CAACE,IAAI,EAAEC,IAAI,KAAK;EACrD,MAAM;IACJC,YAAY,EAAEC;EAChB,CAAC,GAAGH,IAAI;EACR,MAAM;IACJE,YAAY,EAAEE;EAChB,CAAC,GAAGH,IAAI;EACR,OAAOE,eAAe,KAAKC,eAAe;AAC5C,CAAC,CAAC;AACF,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
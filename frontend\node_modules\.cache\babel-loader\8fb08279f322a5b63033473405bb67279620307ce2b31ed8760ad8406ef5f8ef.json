{"ast": null, "code": "// ============================ Directory =============================\nexport const genDirectoryStyle = ({\n  treeCls,\n  treeNodeCls,\n  directoryNodeSelectedBg,\n  directoryNodeSelectedColor,\n  motionDurationMid,\n  borderRadius,\n  controlItemBgHover\n}) => ({\n  [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {\n    // >>> Title\n    [`${treeCls}-node-content-wrapper`]: {\n      position: 'static',\n      [`> *:not(${treeCls}-drop-indicator)`]: {\n        position: 'relative'\n      },\n      '&:hover': {\n        background: 'transparent'\n      },\n      // Expand interactive area to whole line\n      '&:before': {\n        position: 'absolute',\n        inset: 0,\n        transition: `background-color ${motionDurationMid}`,\n        content: '\"\"',\n        borderRadius\n      },\n      '&:hover:before': {\n        background: controlItemBgHover\n      }\n    },\n    [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {\n      zIndex: 1\n    },\n    // ============= Selected =============\n    '&-selected': {\n      [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {\n        color: directoryNodeSelectedColor\n      },\n      // >>> Title\n      [`${treeCls}-node-content-wrapper`]: {\n        color: directoryNodeSelectedColor,\n        background: 'transparent',\n        '&:before, &:hover:before': {\n          background: directoryNodeSelectedBg\n        }\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["genDirectoryStyle", "treeCls", "treeNodeCls", "directoryNodeSelectedBg", "directoryNodeSelectedColor", "motionDurationMid", "borderRadius", "controlItemBgHover", "position", "background", "inset", "transition", "content", "zIndex", "color"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/tree/style/directory.js"], "sourcesContent": ["// ============================ Directory =============================\nexport const genDirectoryStyle = ({\n  treeCls,\n  treeNodeCls,\n  directoryNodeSelectedBg,\n  directoryNodeSelectedColor,\n  motionDurationMid,\n  borderRadius,\n  controlItemBgHover\n}) => ({\n  [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {\n    // >>> Title\n    [`${treeCls}-node-content-wrapper`]: {\n      position: 'static',\n      [`> *:not(${treeCls}-drop-indicator)`]: {\n        position: 'relative'\n      },\n      '&:hover': {\n        background: 'transparent'\n      },\n      // Expand interactive area to whole line\n      '&:before': {\n        position: 'absolute',\n        inset: 0,\n        transition: `background-color ${motionDurationMid}`,\n        content: '\"\"',\n        borderRadius\n      },\n      '&:hover:before': {\n        background: controlItemBgHover\n      }\n    },\n    [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {\n      zIndex: 1\n    },\n    // ============= Selected =============\n    '&-selected': {\n      [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {\n        color: directoryNodeSelectedColor\n      },\n      // >>> Title\n      [`${treeCls}-node-content-wrapper`]: {\n        color: directoryNodeSelectedColor,\n        background: 'transparent',\n        '&:before, &:hover:before': {\n          background: directoryNodeSelectedBg\n        }\n      }\n    }\n  }\n});"], "mappings": "AAAA;AACA,OAAO,MAAMA,iBAAiB,GAAGA,CAAC;EAChCC,OAAO;EACPC,WAAW;EACXC,uBAAuB;EACvBC,0BAA0B;EAC1BC,iBAAiB;EACjBC,YAAY;EACZC;AACF,CAAC,MAAM;EACL,CAAC,GAAGN,OAAO,GAAGA,OAAO,cAAcC,WAAW,EAAE,GAAG;IACjD;IACA,CAAC,GAAGD,OAAO,uBAAuB,GAAG;MACnCO,QAAQ,EAAE,QAAQ;MAClB,CAAC,WAAWP,OAAO,kBAAkB,GAAG;QACtCO,QAAQ,EAAE;MACZ,CAAC;MACD,SAAS,EAAE;QACTC,UAAU,EAAE;MACd,CAAC;MACD;MACA,UAAU,EAAE;QACVD,QAAQ,EAAE,UAAU;QACpBE,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,oBAAoBN,iBAAiB,EAAE;QACnDO,OAAO,EAAE,IAAI;QACbN;MACF,CAAC;MACD,gBAAgB,EAAE;QAChBG,UAAU,EAAEF;MACd;IACF,CAAC;IACD,CAAC,GAAGN,OAAO,cAAcA,OAAO,cAAcA,OAAO,iBAAiB,GAAG;MACvEY,MAAM,EAAE;IACV,CAAC;IACD;IACA,YAAY,EAAE;MACZ,CAAC,GAAGZ,OAAO,cAAcA,OAAO,iBAAiB,GAAG;QAClDa,KAAK,EAAEV;MACT,CAAC;MACD;MACA,CAAC,GAAGH,OAAO,uBAAuB,GAAG;QACnCa,KAAK,EAAEV,0BAA0B;QACjCK,UAAU,EAAE,aAAa;QACzB,0BAA0B,EAAE;UAC1BA,UAAU,EAAEN;QACd;MACF;IACF;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
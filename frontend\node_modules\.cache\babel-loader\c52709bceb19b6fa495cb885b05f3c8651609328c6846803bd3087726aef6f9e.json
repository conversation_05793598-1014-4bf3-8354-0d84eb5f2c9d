{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { useEffect, useRef, useState } from 'react';\nimport getFixScaleEleTransPosition from \"../getFixScaleEleTransPosition\";\nfunction getDistance(a, b) {\n  var x = a.x - b.x;\n  var y = a.y - b.y;\n  return Math.hypot(x, y);\n}\nfunction getCenter(oldPoint1, oldPoint2, newPoint1, newPoint2) {\n  // Calculate the distance each point has moved\n  var distance1 = getDistance(oldPoint1, newPoint1);\n  var distance2 = getDistance(oldPoint2, newPoint2);\n\n  // If both distances are 0, return the original points\n  if (distance1 === 0 && distance2 === 0) {\n    return [oldPoint1.x, oldPoint1.y];\n  }\n\n  // Calculate the ratio of the distances\n  var ratio = distance1 / (distance1 + distance2);\n\n  // Calculate the new center point based on the ratio\n  var x = oldPoint1.x + ratio * (oldPoint2.x - oldPoint1.x);\n  var y = oldPoint1.y + ratio * (oldPoint2.y - oldPoint1.y);\n  return [x, y];\n}\nexport default function useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange) {\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isTouching = _useState2[0],\n    setIsTouching = _useState2[1];\n  var touchPointInfo = useRef({\n    point1: {\n      x: 0,\n      y: 0\n    },\n    point2: {\n      x: 0,\n      y: 0\n    },\n    eventType: 'none'\n  });\n  var updateTouchPointInfo = function updateTouchPointInfo(values) {\n    touchPointInfo.current = _objectSpread(_objectSpread({}, touchPointInfo.current), values);\n  };\n  var onTouchStart = function onTouchStart(event) {\n    if (!movable) return;\n    event.stopPropagation();\n    setIsTouching(true);\n    var _event$touches = event.touches,\n      touches = _event$touches === void 0 ? [] : _event$touches;\n    if (touches.length > 1) {\n      // touch zoom\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX,\n          y: touches[0].clientY\n        },\n        point2: {\n          x: touches[1].clientX,\n          y: touches[1].clientY\n        },\n        eventType: 'touchZoom'\n      });\n    } else {\n      // touch move\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX - x,\n          y: touches[0].clientY - y\n        },\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchMove = function onTouchMove(event) {\n    var _event$touches2 = event.touches,\n      touches = _event$touches2 === void 0 ? [] : _event$touches2;\n    var _touchPointInfo$curre = touchPointInfo.current,\n      point1 = _touchPointInfo$curre.point1,\n      point2 = _touchPointInfo$curre.point2,\n      eventType = _touchPointInfo$curre.eventType;\n    if (touches.length > 1 && eventType === 'touchZoom') {\n      // touch zoom\n      var newPoint1 = {\n        x: touches[0].clientX,\n        y: touches[0].clientY\n      };\n      var newPoint2 = {\n        x: touches[1].clientX,\n        y: touches[1].clientY\n      };\n      var _getCenter = getCenter(point1, point2, newPoint1, newPoint2),\n        _getCenter2 = _slicedToArray(_getCenter, 2),\n        centerX = _getCenter2[0],\n        centerY = _getCenter2[1];\n      var ratio = getDistance(newPoint1, newPoint2) / getDistance(point1, point2);\n      dispatchZoomChange(ratio, 'touchZoom', centerX, centerY, true);\n      updateTouchPointInfo({\n        point1: newPoint1,\n        point2: newPoint2,\n        eventType: 'touchZoom'\n      });\n    } else if (eventType === 'move') {\n      // touch move\n      updateTransform({\n        x: touches[0].clientX - point1.x,\n        y: touches[0].clientY - point1.y\n      }, 'move');\n      updateTouchPointInfo({\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    if (!visible) return;\n    if (isTouching) {\n      setIsTouching(false);\n    }\n    updateTouchPointInfo({\n      eventType: 'none'\n    });\n    if (minScale > scale) {\n      /** When the scaling ratio is less than the minimum scaling ratio, reset the scaling ratio */\n      return updateTransform({\n        x: 0,\n        y: 0,\n        scale: minScale\n      }, 'touchZoom');\n    }\n    var width = imgRef.current.offsetWidth * scale;\n    var height = imgRef.current.offsetHeight * scale;\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n      left = _imgRef$current$getBo.left,\n      top = _imgRef$current$getBo.top;\n    var isRotate = rotate % 180 !== 0;\n    var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n    if (fixState) {\n      updateTransform(_objectSpread({}, fixState), 'dragRebound');\n    }\n  };\n  useEffect(function () {\n    var onTouchMoveListener;\n    if (visible && movable) {\n      onTouchMoveListener = addEventListener(window, 'touchmove', function (e) {\n        return e.preventDefault();\n      }, {\n        passive: false\n      });\n    }\n    return function () {\n      var _onTouchMoveListener;\n      (_onTouchMoveListener = onTouchMoveListener) === null || _onTouchMoveListener === void 0 || _onTouchMoveListener.remove();\n    };\n  }, [visible, movable]);\n  return {\n    isTouching: isTouching,\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd\n  };\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "addEventListener", "useEffect", "useRef", "useState", "getFixScaleEleTransPosition", "getDistance", "a", "b", "x", "y", "Math", "hypot", "getCenter", "oldPoint1", "oldPoint2", "newPoint1", "newPoint2", "distance1", "distance2", "ratio", "useTouchEvent", "imgRef", "movable", "visible", "minScale", "transform", "updateTransform", "dispatchZoomChange", "rotate", "scale", "_useState", "_useState2", "isTouching", "setIsTouching", "touchPointInfo", "point1", "point2", "eventType", "updateTouchPointInfo", "values", "current", "onTouchStart", "event", "stopPropagation", "_event$touches", "touches", "length", "clientX", "clientY", "onTouchMove", "_event$touches2", "_touchPointInfo$curre", "_getCenter", "_getCenter2", "centerX", "centerY", "onTouchEnd", "width", "offsetWidth", "height", "offsetHeight", "_imgRef$current$getBo", "getBoundingClientRect", "left", "top", "isRotate", "fixState", "onTouchMoveListener", "window", "e", "preventDefault", "passive", "_onTouchMoveListener", "remove"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-image@7.12.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-image/es/hooks/useTouchEvent.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { useEffect, useRef, useState } from 'react';\nimport getFixScaleEleTransPosition from \"../getFixScaleEleTransPosition\";\nfunction getDistance(a, b) {\n  var x = a.x - b.x;\n  var y = a.y - b.y;\n  return Math.hypot(x, y);\n}\nfunction getCenter(oldPoint1, oldPoint2, newPoint1, newPoint2) {\n  // Calculate the distance each point has moved\n  var distance1 = getDistance(oldPoint1, newPoint1);\n  var distance2 = getDistance(oldPoint2, newPoint2);\n\n  // If both distances are 0, return the original points\n  if (distance1 === 0 && distance2 === 0) {\n    return [oldPoint1.x, oldPoint1.y];\n  }\n\n  // Calculate the ratio of the distances\n  var ratio = distance1 / (distance1 + distance2);\n\n  // Calculate the new center point based on the ratio\n  var x = oldPoint1.x + ratio * (oldPoint2.x - oldPoint1.x);\n  var y = oldPoint1.y + ratio * (oldPoint2.y - oldPoint1.y);\n  return [x, y];\n}\nexport default function useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange) {\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isTouching = _useState2[0],\n    setIsTouching = _useState2[1];\n  var touchPointInfo = useRef({\n    point1: {\n      x: 0,\n      y: 0\n    },\n    point2: {\n      x: 0,\n      y: 0\n    },\n    eventType: 'none'\n  });\n  var updateTouchPointInfo = function updateTouchPointInfo(values) {\n    touchPointInfo.current = _objectSpread(_objectSpread({}, touchPointInfo.current), values);\n  };\n  var onTouchStart = function onTouchStart(event) {\n    if (!movable) return;\n    event.stopPropagation();\n    setIsTouching(true);\n    var _event$touches = event.touches,\n      touches = _event$touches === void 0 ? [] : _event$touches;\n    if (touches.length > 1) {\n      // touch zoom\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX,\n          y: touches[0].clientY\n        },\n        point2: {\n          x: touches[1].clientX,\n          y: touches[1].clientY\n        },\n        eventType: 'touchZoom'\n      });\n    } else {\n      // touch move\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX - x,\n          y: touches[0].clientY - y\n        },\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchMove = function onTouchMove(event) {\n    var _event$touches2 = event.touches,\n      touches = _event$touches2 === void 0 ? [] : _event$touches2;\n    var _touchPointInfo$curre = touchPointInfo.current,\n      point1 = _touchPointInfo$curre.point1,\n      point2 = _touchPointInfo$curre.point2,\n      eventType = _touchPointInfo$curre.eventType;\n    if (touches.length > 1 && eventType === 'touchZoom') {\n      // touch zoom\n      var newPoint1 = {\n        x: touches[0].clientX,\n        y: touches[0].clientY\n      };\n      var newPoint2 = {\n        x: touches[1].clientX,\n        y: touches[1].clientY\n      };\n      var _getCenter = getCenter(point1, point2, newPoint1, newPoint2),\n        _getCenter2 = _slicedToArray(_getCenter, 2),\n        centerX = _getCenter2[0],\n        centerY = _getCenter2[1];\n      var ratio = getDistance(newPoint1, newPoint2) / getDistance(point1, point2);\n      dispatchZoomChange(ratio, 'touchZoom', centerX, centerY, true);\n      updateTouchPointInfo({\n        point1: newPoint1,\n        point2: newPoint2,\n        eventType: 'touchZoom'\n      });\n    } else if (eventType === 'move') {\n      // touch move\n      updateTransform({\n        x: touches[0].clientX - point1.x,\n        y: touches[0].clientY - point1.y\n      }, 'move');\n      updateTouchPointInfo({\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    if (!visible) return;\n    if (isTouching) {\n      setIsTouching(false);\n    }\n    updateTouchPointInfo({\n      eventType: 'none'\n    });\n    if (minScale > scale) {\n      /** When the scaling ratio is less than the minimum scaling ratio, reset the scaling ratio */\n      return updateTransform({\n        x: 0,\n        y: 0,\n        scale: minScale\n      }, 'touchZoom');\n    }\n    var width = imgRef.current.offsetWidth * scale;\n    var height = imgRef.current.offsetHeight * scale;\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n      left = _imgRef$current$getBo.left,\n      top = _imgRef$current$getBo.top;\n    var isRotate = rotate % 180 !== 0;\n    var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n    if (fixState) {\n      updateTransform(_objectSpread({}, fixState), 'dragRebound');\n    }\n  };\n  useEffect(function () {\n    var onTouchMoveListener;\n    if (visible && movable) {\n      onTouchMoveListener = addEventListener(window, 'touchmove', function (e) {\n        return e.preventDefault();\n      }, {\n        passive: false\n      });\n    }\n    return function () {\n      var _onTouchMoveListener;\n      (_onTouchMoveListener = onTouchMoveListener) === null || _onTouchMoveListener === void 0 || _onTouchMoveListener.remove();\n    };\n  }, [visible, movable]);\n  return {\n    isTouching: isTouching,\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,2BAA2B,MAAM,gCAAgC;AACxE,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAIC,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAGD,CAAC,CAACC,CAAC;EACjB,IAAIC,CAAC,GAAGH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC;EACjB,OAAOC,IAAI,CAACC,KAAK,CAACH,CAAC,EAAEC,CAAC,CAAC;AACzB;AACA,SAASG,SAASA,CAACC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC7D;EACA,IAAIC,SAAS,GAAGZ,WAAW,CAACQ,SAAS,EAAEE,SAAS,CAAC;EACjD,IAAIG,SAAS,GAAGb,WAAW,CAACS,SAAS,EAAEE,SAAS,CAAC;;EAEjD;EACA,IAAIC,SAAS,KAAK,CAAC,IAAIC,SAAS,KAAK,CAAC,EAAE;IACtC,OAAO,CAACL,SAAS,CAACL,CAAC,EAAEK,SAAS,CAACJ,CAAC,CAAC;EACnC;;EAEA;EACA,IAAIU,KAAK,GAAGF,SAAS,IAAIA,SAAS,GAAGC,SAAS,CAAC;;EAE/C;EACA,IAAIV,CAAC,GAAGK,SAAS,CAACL,CAAC,GAAGW,KAAK,IAAIL,SAAS,CAACN,CAAC,GAAGK,SAAS,CAACL,CAAC,CAAC;EACzD,IAAIC,CAAC,GAAGI,SAAS,CAACJ,CAAC,GAAGU,KAAK,IAAIL,SAAS,CAACL,CAAC,GAAGI,SAAS,CAACJ,CAAC,CAAC;EACzD,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf;AACA,eAAe,SAASW,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,kBAAkB,EAAE;EACxH,IAAIC,MAAM,GAAGH,SAAS,CAACG,MAAM;IAC3BC,KAAK,GAAGJ,SAAS,CAACI,KAAK;IACvBrB,CAAC,GAAGiB,SAAS,CAACjB,CAAC;IACfC,CAAC,GAAGgB,SAAS,CAAChB,CAAC;EACjB,IAAIqB,SAAS,GAAG3B,QAAQ,CAAC,KAAK,CAAC;IAC7B4B,UAAU,GAAGhC,cAAc,CAAC+B,SAAS,EAAE,CAAC,CAAC;IACzCE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIG,cAAc,GAAGhC,MAAM,CAAC;IAC1BiC,MAAM,EAAE;MACN3B,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD2B,MAAM,EAAE;MACN5B,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD4B,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,MAAM,EAAE;IAC/DL,cAAc,CAACM,OAAO,GAAG1C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,cAAc,CAACM,OAAO,CAAC,EAAED,MAAM,CAAC;EAC3F,CAAC;EACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9C,IAAI,CAACpB,OAAO,EAAE;IACdoB,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBV,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIW,cAAc,GAAGF,KAAK,CAACG,OAAO;MAChCA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;IAC3D,IAAIC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB;MACAR,oBAAoB,CAAC;QACnBH,MAAM,EAAE;UACN3B,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;UACrBtC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG;QAChB,CAAC;QACDZ,MAAM,EAAE;UACN5B,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;UACrBtC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG;QAChB,CAAC;QACDX,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAC,oBAAoB,CAAC;QACnBH,MAAM,EAAE;UACN3B,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO,GAAGvC,CAAC;UACzBC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGvC;QAC1B,CAAC;QACD4B,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIY,WAAW,GAAG,SAASA,WAAWA,CAACP,KAAK,EAAE;IAC5C,IAAIQ,eAAe,GAAGR,KAAK,CAACG,OAAO;MACjCA,OAAO,GAAGK,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;IAC7D,IAAIC,qBAAqB,GAAGjB,cAAc,CAACM,OAAO;MAChDL,MAAM,GAAGgB,qBAAqB,CAAChB,MAAM;MACrCC,MAAM,GAAGe,qBAAqB,CAACf,MAAM;MACrCC,SAAS,GAAGc,qBAAqB,CAACd,SAAS;IAC7C,IAAIQ,OAAO,CAACC,MAAM,GAAG,CAAC,IAAIT,SAAS,KAAK,WAAW,EAAE;MACnD;MACA,IAAItB,SAAS,GAAG;QACdP,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;QACrBtC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG;MAChB,CAAC;MACD,IAAIhC,SAAS,GAAG;QACdR,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;QACrBtC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG;MAChB,CAAC;MACD,IAAII,UAAU,GAAGxC,SAAS,CAACuB,MAAM,EAAEC,MAAM,EAAErB,SAAS,EAAEC,SAAS,CAAC;QAC9DqC,WAAW,GAAGtD,cAAc,CAACqD,UAAU,EAAE,CAAC,CAAC;QAC3CE,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;QACxBE,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC1B,IAAIlC,KAAK,GAAGd,WAAW,CAACU,SAAS,EAAEC,SAAS,CAAC,GAAGX,WAAW,CAAC8B,MAAM,EAAEC,MAAM,CAAC;MAC3ET,kBAAkB,CAACR,KAAK,EAAE,WAAW,EAAEmC,OAAO,EAAEC,OAAO,EAAE,IAAI,CAAC;MAC9DjB,oBAAoB,CAAC;QACnBH,MAAM,EAAEpB,SAAS;QACjBqB,MAAM,EAAEpB,SAAS;QACjBqB,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/B;MACAX,eAAe,CAAC;QACdlB,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO,GAAGZ,MAAM,CAAC3B,CAAC;QAChCC,CAAC,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGb,MAAM,CAAC1B;MACjC,CAAC,EAAE,MAAM,CAAC;MACV6B,oBAAoB,CAAC;QACnBD,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAImB,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAI,CAACjC,OAAO,EAAE;IACd,IAAIS,UAAU,EAAE;MACdC,aAAa,CAAC,KAAK,CAAC;IACtB;IACAK,oBAAoB,CAAC;MACnBD,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAIb,QAAQ,GAAGK,KAAK,EAAE;MACpB;MACA,OAAOH,eAAe,CAAC;QACrBlB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJoB,KAAK,EAAEL;MACT,CAAC,EAAE,WAAW,CAAC;IACjB;IACA,IAAIiC,KAAK,GAAGpC,MAAM,CAACmB,OAAO,CAACkB,WAAW,GAAG7B,KAAK;IAC9C,IAAI8B,MAAM,GAAGtC,MAAM,CAACmB,OAAO,CAACoB,YAAY,GAAG/B,KAAK;IAChD;IACA,IAAIgC,qBAAqB,GAAGxC,MAAM,CAACmB,OAAO,CAACsB,qBAAqB,CAAC,CAAC;MAChEC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;MACjCC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;IACjC,IAAIC,QAAQ,GAAGrC,MAAM,GAAG,GAAG,KAAK,CAAC;IACjC,IAAIsC,QAAQ,GAAG9D,2BAA2B,CAAC6D,QAAQ,GAAGN,MAAM,GAAGF,KAAK,EAAEQ,QAAQ,GAAGR,KAAK,GAAGE,MAAM,EAAEI,IAAI,EAAEC,GAAG,CAAC;IAC3G,IAAIE,QAAQ,EAAE;MACZxC,eAAe,CAAC5B,aAAa,CAAC,CAAC,CAAC,EAAEoE,QAAQ,CAAC,EAAE,aAAa,CAAC;IAC7D;EACF,CAAC;EACDjE,SAAS,CAAC,YAAY;IACpB,IAAIkE,mBAAmB;IACvB,IAAI5C,OAAO,IAAID,OAAO,EAAE;MACtB6C,mBAAmB,GAAGnE,gBAAgB,CAACoE,MAAM,EAAE,WAAW,EAAE,UAAUC,CAAC,EAAE;QACvE,OAAOA,CAAC,CAACC,cAAc,CAAC,CAAC;MAC3B,CAAC,EAAE;QACDC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACA,OAAO,YAAY;MACjB,IAAIC,oBAAoB;MACxB,CAACA,oBAAoB,GAAGL,mBAAmB,MAAM,IAAI,IAAIK,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAACC,MAAM,CAAC,CAAC;IAC3H,CAAC;EACH,CAAC,EAAE,CAAClD,OAAO,EAAED,OAAO,CAAC,CAAC;EACtB,OAAO;IACLU,UAAU,EAAEA,UAAU;IACtBS,YAAY,EAAEA,YAAY;IAC1BQ,WAAW,EAAEA,WAAW;IACxBO,UAAU,EAAEA;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
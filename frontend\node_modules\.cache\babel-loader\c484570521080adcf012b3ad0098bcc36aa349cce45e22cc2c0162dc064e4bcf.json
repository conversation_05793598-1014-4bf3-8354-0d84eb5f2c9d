{"ast": null, "code": "import { useMemo } from 'react';\nimport PickerButton from '../PickerButton';\nexport default function useComponents(components) {\n  return useMemo(() => Object.assign({\n    button: PickerButton\n  }, components), [components]);\n}", "map": {"version": 3, "names": ["useMemo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useComponents", "components", "Object", "assign", "button"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/date-picker/generatePicker/useComponents.js"], "sourcesContent": ["import { useMemo } from 'react';\nimport PickerButton from '../PickerButton';\nexport default function useComponents(components) {\n  return useMemo(() => Object.assign({\n    button: PickerButton\n  }, components), [components]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,eAAe,SAASC,aAAaA,CAACC,UAAU,EAAE;EAChD,OAAOH,OAAO,CAAC,MAAMI,MAAM,CAACC,MAAM,CAAC;IACjCC,MAAM,EAAEL;EACV,CAAC,EAAEE,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
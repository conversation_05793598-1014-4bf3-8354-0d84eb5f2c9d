{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nfunction isConfigObj(closable) {\n  return closable !== null && _typeof(closable) === 'object';\n}\n/**\n * Convert `closable` to ClosableConfig.\n * When `preset` is true, will auto fill ClosableConfig with default value.\n */\nfunction getClosableConfig(closable, closeIcon, preset) {\n  if (closable === false || closeIcon === false && (!isConfigObj(closable) || !closable.closeIcon)) {\n    return null;\n  }\n  var mergedCloseIcon = typeof closeIcon !== 'boolean' ? closeIcon : undefined;\n  if (isConfigObj(closable)) {\n    var _closable$closeIcon;\n    return _objectSpread(_objectSpread({}, closable), {}, {\n      closeIcon: (_closable$closeIcon = closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : mergedCloseIcon\n    });\n  }\n\n  // When StepClosable no need auto fill, but RootClosable need this.\n  return preset || closable || closeIcon ? {\n    closeIcon: mergedCloseIcon\n  } : 'empty';\n}\nexport function useClosable(stepClosable, stepCloseIcon, closable, closeIcon) {\n  return React.useMemo(function () {\n    var stepClosableConfig = getClosableConfig(stepClosable, stepCloseIcon, false);\n    var rootClosableConfig = getClosableConfig(closable, closeIcon, true);\n    if (stepClosableConfig !== 'empty') {\n      return stepClosableConfig;\n    }\n    return rootClosableConfig;\n  }, [closable, closeIcon, stepClosable, stepCloseIcon]);\n}", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "React", "isConfigObj", "closable", "getClosableConfig", "closeIcon", "preset", "mergedCloseIcon", "undefined", "_closable$closeIcon", "useClosable", "stepClosable", "stepCloseIcon", "useMemo", "stepClosableConfig", "rootClosableConfig"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+tour@1.15.1_r_14d992225ad622c42cfc1bfce16fe219/node_modules/@rc-component/tour/es/hooks/useClosable.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nfunction isConfigObj(closable) {\n  return closable !== null && _typeof(closable) === 'object';\n}\n/**\n * Convert `closable` to ClosableConfig.\n * When `preset` is true, will auto fill ClosableConfig with default value.\n */\nfunction getClosableConfig(closable, closeIcon, preset) {\n  if (closable === false || closeIcon === false && (!isConfigObj(closable) || !closable.closeIcon)) {\n    return null;\n  }\n  var mergedCloseIcon = typeof closeIcon !== 'boolean' ? closeIcon : undefined;\n  if (isConfigObj(closable)) {\n    var _closable$closeIcon;\n    return _objectSpread(_objectSpread({}, closable), {}, {\n      closeIcon: (_closable$closeIcon = closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : mergedCloseIcon\n    });\n  }\n\n  // When StepClosable no need auto fill, but RootClosable need this.\n  return preset || closable || closeIcon ? {\n    closeIcon: mergedCloseIcon\n  } : 'empty';\n}\nexport function useClosable(stepClosable, stepCloseIcon, closable, closeIcon) {\n  return React.useMemo(function () {\n    var stepClosableConfig = getClosableConfig(stepClosable, stepCloseIcon, false);\n    var rootClosableConfig = getClosableConfig(closable, closeIcon, true);\n    if (stepClosableConfig !== 'empty') {\n      return stepClosableConfig;\n    }\n    return rootClosableConfig;\n  }, [closable, closeIcon, stepClosable, stepCloseIcon]);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,OAAOA,QAAQ,KAAK,IAAI,IAAIH,OAAO,CAACG,QAAQ,CAAC,KAAK,QAAQ;AAC5D;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACD,QAAQ,EAAEE,SAAS,EAAEC,MAAM,EAAE;EACtD,IAAIH,QAAQ,KAAK,KAAK,IAAIE,SAAS,KAAK,KAAK,KAAK,CAACH,WAAW,CAACC,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACE,SAAS,CAAC,EAAE;IAChG,OAAO,IAAI;EACb;EACA,IAAIE,eAAe,GAAG,OAAOF,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAGG,SAAS;EAC5E,IAAIN,WAAW,CAACC,QAAQ,CAAC,EAAE;IACzB,IAAIM,mBAAmB;IACvB,OAAOV,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEI,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;MACpDE,SAAS,EAAE,CAACI,mBAAmB,GAAGN,QAAQ,CAACE,SAAS,MAAM,IAAI,IAAII,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGF;IAC3H,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOD,MAAM,IAAIH,QAAQ,IAAIE,SAAS,GAAG;IACvCA,SAAS,EAAEE;EACb,CAAC,GAAG,OAAO;AACb;AACA,OAAO,SAASG,WAAWA,CAACC,YAAY,EAAEC,aAAa,EAAET,QAAQ,EAAEE,SAAS,EAAE;EAC5E,OAAOJ,KAAK,CAACY,OAAO,CAAC,YAAY;IAC/B,IAAIC,kBAAkB,GAAGV,iBAAiB,CAACO,YAAY,EAAEC,aAAa,EAAE,KAAK,CAAC;IAC9E,IAAIG,kBAAkB,GAAGX,iBAAiB,CAACD,QAAQ,EAAEE,SAAS,EAAE,IAAI,CAAC;IACrE,IAAIS,kBAAkB,KAAK,OAAO,EAAE;MAClC,OAAOA,kBAAkB;IAC3B;IACA,OAAOC,kBAAkB;EAC3B,CAAC,EAAE,CAACZ,QAAQ,EAAEE,SAAS,EAAEM,YAAY,EAAEC,aAAa,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import getMiniDecimal from \"./MiniDecimal\";\nexport * from \"./MiniDecimal\";\nimport { trimNumber, getNumberPrecision, num2str, validateNumber } from \"./numberUtil\";\nexport { trimNumber, getNumberPrecision, num2str, validateNumber };\nexport default getMiniDecimal;", "map": {"version": 3, "names": ["getMiniDecimal", "trimNumber", "getNumberPrecision", "num2str", "validateNumber"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/index.js"], "sourcesContent": ["import getMiniDecimal from \"./MiniDecimal\";\nexport * from \"./MiniDecimal\";\nimport { trimNumber, getNumberPrecision, num2str, validateNumber } from \"./numberUtil\";\nexport { trimNumber, getNumberPrecision, num2str, validateNumber };\nexport default getMiniDecimal;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,eAAe;AAC1C,cAAc,eAAe;AAC7B,SAASC,UAAU,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,cAAc,QAAQ,cAAc;AACtF,SAASH,UAAU,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,cAAc;AAChE,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
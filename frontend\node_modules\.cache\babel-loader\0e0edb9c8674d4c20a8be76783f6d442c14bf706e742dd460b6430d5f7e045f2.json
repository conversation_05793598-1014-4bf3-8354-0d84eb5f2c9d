{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport ActionButton from '../../_util/ActionButton';\nimport { ModalContext } from '../context';\nconst ConfirmOkBtn = () => {\n  const {\n    autoFocusButton,\n    close,\n    isSilent,\n    okButtonProps,\n    rootPrefixCls,\n    okTextLocale,\n    okType,\n    onConfirm,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(ActionButton, {\n    isSilent: isSilent,\n    type: okType || 'primary',\n    actionFn: onOk,\n    close: (...args) => {\n      close === null || close === void 0 ? void 0 : close.apply(void 0, args);\n      onConfirm === null || onConfirm === void 0 ? void 0 : onConfirm(true);\n    },\n    autoFocus: autoFocusButton === 'ok',\n    buttonProps: okButtonProps,\n    prefixCls: `${rootPrefixCls}-btn`\n  }, okTextLocale);\n};\nexport default ConfirmOkBtn;", "map": {"version": 3, "names": ["React", "useContext", "ActionButton", "ModalContext", "ConfirmOkBtn", "autoFocusButton", "close", "isSilent", "okButtonProps", "rootPrefixCls", "okTextLocale", "okType", "onConfirm", "onOk", "createElement", "type", "actionFn", "args", "apply", "autoFocus", "buttonProps", "prefixCls"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/modal/components/ConfirmOkBtn.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport ActionButton from '../../_util/ActionButton';\nimport { ModalContext } from '../context';\nconst ConfirmOkBtn = () => {\n  const {\n    autoFocusButton,\n    close,\n    isSilent,\n    okButtonProps,\n    rootPrefixCls,\n    okTextLocale,\n    okType,\n    onConfirm,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(ActionButton, {\n    isSilent: isSilent,\n    type: okType || 'primary',\n    actionFn: onOk,\n    close: (...args) => {\n      close === null || close === void 0 ? void 0 : close.apply(void 0, args);\n      onConfirm === null || onConfirm === void 0 ? void 0 : onConfirm(true);\n    },\n    autoFocus: autoFocusButton === 'ok',\n    buttonProps: okButtonProps,\n    prefixCls: `${rootPrefixCls}-btn`\n  }, okTextLocale);\n};\nexport default ConfirmOkBtn;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,SAASC,YAAY,QAAQ,YAAY;AACzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAM;IACJC,eAAe;IACfC,KAAK;IACLC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,YAAY;IACZC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGZ,UAAU,CAACE,YAAY,CAAC;EAC5B,OAAO,aAAaH,KAAK,CAACc,aAAa,CAACZ,YAAY,EAAE;IACpDK,QAAQ,EAAEA,QAAQ;IAClBQ,IAAI,EAAEJ,MAAM,IAAI,SAAS;IACzBK,QAAQ,EAAEH,IAAI;IACdP,KAAK,EAAEA,CAAC,GAAGW,IAAI,KAAK;MAClBX,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,KAAK,CAAC,KAAK,CAAC,EAAED,IAAI,CAAC;MACvEL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,IAAI,CAAC;IACvE,CAAC;IACDO,SAAS,EAAEd,eAAe,KAAK,IAAI;IACnCe,WAAW,EAAEZ,aAAa;IAC1Ba,SAAS,EAAE,GAAGZ,aAAa;EAC7B,CAAC,EAAEC,YAAY,CAAC;AAClB,CAAC;AACD,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { useEffect, useRef } from 'react';\nconst usePrevious = value => {\n  const ref = useRef(undefined);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePrevious;", "map": {"version": 3, "names": ["useEffect", "useRef", "usePrevious", "value", "ref", "undefined", "current"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/hooks/usePrevious.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nconst usePrevious = value => {\n  const ref = useRef(undefined);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePrevious;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAMC,GAAG,GAAGH,MAAM,CAACI,SAAS,CAAC;EAC7BL,SAAS,CAAC,MAAM;IACdI,GAAG,CAACE,OAAO,GAAGH,KAAK;EACrB,CAAC,CAAC;EACF,OAAOC,GAAG,CAACE,OAAO;AACpB,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nexport default function Polite(props) {\n  var visible = props.visible,\n    values = props.values;\n  if (!visible) {\n    return null;\n  }\n\n  // Only cut part of values since it's a screen reader\n  var MAX_COUNT = 50;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"\".concat(values.slice(0, MAX_COUNT).map(function (_ref) {\n    var label = _ref.label,\n      value = _ref.value;\n    return ['number', 'string'].includes(_typeof(label)) ? label : value;\n  }).join(', ')), values.length > MAX_COUNT ? ', ...' : null);\n}", "map": {"version": 3, "names": ["_typeof", "React", "Polite", "props", "visible", "values", "MAX_COUNT", "createElement", "style", "width", "height", "position", "overflow", "opacity", "concat", "slice", "map", "_ref", "label", "value", "includes", "join", "length"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/BaseSelect/Polite.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nexport default function Polite(props) {\n  var visible = props.visible,\n    values = props.values;\n  if (!visible) {\n    return null;\n  }\n\n  // Only cut part of values since it's a screen reader\n  var MAX_COUNT = 50;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"\".concat(values.slice(0, MAX_COUNT).map(function (_ref) {\n    var label = _ref.label,\n      value = _ref.value;\n    return ['number', 'string'].includes(_typeof(label)) ? label : value;\n  }).join(', ')), values.length > MAX_COUNT ? ', ...' : null);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACzBC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACvB,IAAI,CAACD,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;;EAEA;EACA,IAAIE,SAAS,GAAG,EAAE;EAClB,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IAC9C,WAAW,EAAE,QAAQ;IACrBC,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;IACX;EACF,CAAC,EAAE,EAAE,CAACC,MAAM,CAACT,MAAM,CAACU,KAAK,CAAC,CAAC,EAAET,SAAS,CAAC,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC1D,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpB,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACpB,OAAO,CAACkB,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAGC,KAAK;EACtE,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEhB,MAAM,CAACiB,MAAM,GAAGhB,SAAS,GAAG,OAAO,GAAG,IAAI,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
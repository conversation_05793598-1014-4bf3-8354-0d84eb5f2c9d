{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;", "map": {"version": 3, "names": ["classNames", "React", "TabPane", "forwardRef", "props", "ref", "prefixCls", "className", "style", "id", "active", "tabKey", "children", "createElement", "concat", "role", "tabIndex", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tabs@15.6.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tabs/es/TabPanelList/TabPane.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,OAAO,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAChE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,EAAE,GAAGL,KAAK,CAACK,EAAE;IACbC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EAC3B,OAAO,aAAaX,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC7CJ,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACK,MAAM,CAACL,EAAE,EAAE,SAAS,CAAC,CAACK,MAAM,CAACH,MAAM,CAAC;IACjDI,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAEN,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,iBAAiB,EAAED,EAAE,IAAI,EAAE,CAACK,MAAM,CAACL,EAAE,EAAE,OAAO,CAAC,CAACK,MAAM,CAACH,MAAM,CAAC;IAC9D,aAAa,EAAE,CAACD,MAAM;IACtBF,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEP,UAAU,CAACM,SAAS,EAAEI,MAAM,IAAI,EAAE,CAACI,MAAM,CAACR,SAAS,EAAE,SAAS,CAAC,EAAEC,SAAS,CAAC;IACtFF,GAAG,EAAEA;EACP,CAAC,EAAEO,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,OAAO,CAACkB,WAAW,GAAG,SAAS;AACjC;AACA,eAAelB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
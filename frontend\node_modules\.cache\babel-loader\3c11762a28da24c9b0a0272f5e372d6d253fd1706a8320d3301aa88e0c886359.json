{"ast": null, "code": "import { pickProps } from \"../utils/miscUtil\";\nexport function pickTriggerProps(props) {\n  return pickProps(props, ['placement', 'builtinPlacements', 'popupAlign', 'getPopupContainer', 'transitionName', 'direction']);\n}", "map": {"version": 3, "names": ["pickProps", "pickTriggerProps", "props"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerTrigger/util.js"], "sourcesContent": ["import { pickProps } from \"../utils/miscUtil\";\nexport function pickTriggerProps(props) {\n  return pickProps(props, ['placement', 'builtinPlacements', 'popupAlign', 'getPopupContainer', 'transitionName', 'direction']);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAOF,SAAS,CAACE,KAAK,EAAE,CAAC,WAAW,EAAE,mBAAmB,EAAE,YAAY,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;AAC/H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
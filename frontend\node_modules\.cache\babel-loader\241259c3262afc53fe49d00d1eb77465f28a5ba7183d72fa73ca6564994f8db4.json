{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\n/**\n * Execute code before next frame but async\n */\nexport function useLayoutState(defaultState) {\n  var stateRef = useRef(defaultState);\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    forceUpdate = _useState2[1];\n  var lastPromiseRef = useRef(null);\n  var updateBatchRef = useRef([]);\n  function setFrameState(updater) {\n    updateBatchRef.current.push(updater);\n    var promise = Promise.resolve();\n    lastPromiseRef.current = promise;\n    promise.then(function () {\n      if (lastPromiseRef.current === promise) {\n        var prevBatch = updateBatchRef.current;\n        var prevState = stateRef.current;\n        updateBatchRef.current = [];\n        prevBatch.forEach(function (batchUpdater) {\n          stateRef.current = batchUpdater(stateRef.current);\n        });\n        lastPromiseRef.current = null;\n        if (prevState !== stateRef.current) {\n          forceUpdate({});\n        }\n      }\n    });\n  }\n  useEffect(function () {\n    return function () {\n      lastPromiseRef.current = null;\n    };\n  }, []);\n  return [stateRef.current, setFrameState];\n}\n\n/** Lock frame, when frame pass reset the lock. */\nexport function useTimeoutLock(defaultState) {\n  var frameRef = useRef(defaultState || null);\n  var timeoutRef = useRef();\n  function cleanUp() {\n    window.clearTimeout(timeoutRef.current);\n  }\n  function setState(newState) {\n    frameRef.current = newState;\n    cleanUp();\n    timeoutRef.current = window.setTimeout(function () {\n      frameRef.current = null;\n      timeoutRef.current = undefined;\n    }, 100);\n  }\n  function getState() {\n    return frameRef.current;\n  }\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return [setState, getState];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useRef", "useState", "useEffect", "useLayoutState", "defaultState", "stateRef", "_useState", "_useState2", "forceUpdate", "lastPromiseRef", "updateBatchRef", "setFrameState", "updater", "current", "push", "promise", "Promise", "resolve", "then", "prevBatch", "prevState", "for<PERSON>ach", "batchUpdater", "useTimeoutLock", "frameRef", "timeoutRef", "cleanUp", "window", "clearTimeout", "setState", "newState", "setTimeout", "undefined", "getState"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/hooks/useFrame.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\n/**\n * Execute code before next frame but async\n */\nexport function useLayoutState(defaultState) {\n  var stateRef = useRef(defaultState);\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    forceUpdate = _useState2[1];\n  var lastPromiseRef = useRef(null);\n  var updateBatchRef = useRef([]);\n  function setFrameState(updater) {\n    updateBatchRef.current.push(updater);\n    var promise = Promise.resolve();\n    lastPromiseRef.current = promise;\n    promise.then(function () {\n      if (lastPromiseRef.current === promise) {\n        var prevBatch = updateBatchRef.current;\n        var prevState = stateRef.current;\n        updateBatchRef.current = [];\n        prevBatch.forEach(function (batchUpdater) {\n          stateRef.current = batchUpdater(stateRef.current);\n        });\n        lastPromiseRef.current = null;\n        if (prevState !== stateRef.current) {\n          forceUpdate({});\n        }\n      }\n    });\n  }\n  useEffect(function () {\n    return function () {\n      lastPromiseRef.current = null;\n    };\n  }, []);\n  return [stateRef.current, setFrameState];\n}\n\n/** Lock frame, when frame pass reset the lock. */\nexport function useTimeoutLock(defaultState) {\n  var frameRef = useRef(defaultState || null);\n  var timeoutRef = useRef();\n  function cleanUp() {\n    window.clearTimeout(timeoutRef.current);\n  }\n  function setState(newState) {\n    frameRef.current = newState;\n    cleanUp();\n    timeoutRef.current = window.setTimeout(function () {\n      frameRef.current = null;\n      timeoutRef.current = undefined;\n    }, 100);\n  }\n  function getState() {\n    return frameRef.current;\n  }\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return [setState, getState];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACnD;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,YAAY,EAAE;EAC3C,IAAIC,QAAQ,GAAGL,MAAM,CAACI,YAAY,CAAC;EACnC,IAAIE,SAAS,GAAGL,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1BM,UAAU,GAAGR,cAAc,CAACO,SAAS,EAAE,CAAC,CAAC;IACzCE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;EAC7B,IAAIE,cAAc,GAAGT,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIU,cAAc,GAAGV,MAAM,CAAC,EAAE,CAAC;EAC/B,SAASW,aAAaA,CAACC,OAAO,EAAE;IAC9BF,cAAc,CAACG,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACpC,IAAIG,OAAO,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC/BR,cAAc,CAACI,OAAO,GAAGE,OAAO;IAChCA,OAAO,CAACG,IAAI,CAAC,YAAY;MACvB,IAAIT,cAAc,CAACI,OAAO,KAAKE,OAAO,EAAE;QACtC,IAAII,SAAS,GAAGT,cAAc,CAACG,OAAO;QACtC,IAAIO,SAAS,GAAGf,QAAQ,CAACQ,OAAO;QAChCH,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3BM,SAAS,CAACE,OAAO,CAAC,UAAUC,YAAY,EAAE;UACxCjB,QAAQ,CAACQ,OAAO,GAAGS,YAAY,CAACjB,QAAQ,CAACQ,OAAO,CAAC;QACnD,CAAC,CAAC;QACFJ,cAAc,CAACI,OAAO,GAAG,IAAI;QAC7B,IAAIO,SAAS,KAAKf,QAAQ,CAACQ,OAAO,EAAE;UAClCL,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;IACF,CAAC,CAAC;EACJ;EACAN,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBO,cAAc,CAACI,OAAO,GAAG,IAAI;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACR,QAAQ,CAACQ,OAAO,EAAEF,aAAa,CAAC;AAC1C;;AAEA;AACA,OAAO,SAASY,cAAcA,CAACnB,YAAY,EAAE;EAC3C,IAAIoB,QAAQ,GAAGxB,MAAM,CAACI,YAAY,IAAI,IAAI,CAAC;EAC3C,IAAIqB,UAAU,GAAGzB,MAAM,CAAC,CAAC;EACzB,SAAS0B,OAAOA,CAAA,EAAG;IACjBC,MAAM,CAACC,YAAY,CAACH,UAAU,CAACZ,OAAO,CAAC;EACzC;EACA,SAASgB,QAAQA,CAACC,QAAQ,EAAE;IAC1BN,QAAQ,CAACX,OAAO,GAAGiB,QAAQ;IAC3BJ,OAAO,CAAC,CAAC;IACTD,UAAU,CAACZ,OAAO,GAAGc,MAAM,CAACI,UAAU,CAAC,YAAY;MACjDP,QAAQ,CAACX,OAAO,GAAG,IAAI;MACvBY,UAAU,CAACZ,OAAO,GAAGmB,SAAS;IAChC,CAAC,EAAE,GAAG,CAAC;EACT;EACA,SAASC,QAAQA,CAAA,EAAG;IAClB,OAAOT,QAAQ,CAACX,OAAO;EACzB;EACAX,SAAS,CAAC,YAAY;IACpB,OAAOwB,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACG,QAAQ,EAAEI,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
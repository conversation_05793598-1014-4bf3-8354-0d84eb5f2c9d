{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useState } from 'react';\nimport extendsObject from '../../_util/extendsObject';\nexport const DEFAULT_PAGE_SIZE = 10;\nexport function getPaginationParam(mergedPagination, pagination) {\n  const param = {\n    current: mergedPagination.current,\n    pageSize: mergedPagination.pageSize\n  };\n  const paginationObj = pagination && typeof pagination === 'object' ? pagination : {};\n  Object.keys(paginationObj).forEach(pageProp => {\n    const value = mergedPagination[pageProp];\n    if (typeof value !== 'function') {\n      param[pageProp] = value;\n    }\n  });\n  return param;\n}\nfunction usePagination(total, onChange, pagination) {\n  const _a = pagination && typeof pagination === 'object' ? pagination : {},\n    {\n      total: paginationTotal = 0\n    } = _a,\n    paginationObj = __rest(_a, [\"total\"]);\n  const [innerPagination, setInnerPagination] = useState(() => ({\n    current: 'defaultCurrent' in paginationObj ? paginationObj.defaultCurrent : 1,\n    pageSize: 'defaultPageSize' in paginationObj ? paginationObj.defaultPageSize : DEFAULT_PAGE_SIZE\n  }));\n  // ============ Basic Pagination Config ============\n  const mergedPagination = extendsObject(innerPagination, paginationObj, {\n    total: paginationTotal > 0 ? paginationTotal : total\n  });\n  // Reset `current` if data length or pageSize changed\n  const maxPage = Math.ceil((paginationTotal || total) / mergedPagination.pageSize);\n  if (mergedPagination.current > maxPage) {\n    // Prevent a maximum page count of 0\n    mergedPagination.current = maxPage || 1;\n  }\n  const refreshPagination = (current, pageSize) => {\n    setInnerPagination({\n      current: current !== null && current !== void 0 ? current : 1,\n      pageSize: pageSize || mergedPagination.pageSize\n    });\n  };\n  const onInternalChange = (current, pageSize) => {\n    var _a;\n    if (pagination) {\n      (_a = pagination.onChange) === null || _a === void 0 ? void 0 : _a.call(pagination, current, pageSize);\n    }\n    refreshPagination(current, pageSize);\n    onChange(current, pageSize || (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize));\n  };\n  if (pagination === false) {\n    return [{}, () => {}];\n  }\n  return [Object.assign(Object.assign({}, mergedPagination), {\n    onChange: onInternalChange\n  }), refreshPagination];\n}\nexport default usePagination;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "useState", "extendsObject", "DEFAULT_PAGE_SIZE", "getPaginationParam", "mergedPagination", "pagination", "param", "current", "pageSize", "paginationObj", "keys", "for<PERSON>ach", "pageProp", "value", "usePagination", "total", "onChange", "_a", "paginationTotal", "innerPagination", "setInnerPagination", "defaultCurrent", "defaultPageSize", "maxPage", "Math", "ceil", "refreshPagination", "onInternalChange", "assign"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/hooks/usePagination.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useState } from 'react';\nimport extendsObject from '../../_util/extendsObject';\nexport const DEFAULT_PAGE_SIZE = 10;\nexport function getPaginationParam(mergedPagination, pagination) {\n  const param = {\n    current: mergedPagination.current,\n    pageSize: mergedPagination.pageSize\n  };\n  const paginationObj = pagination && typeof pagination === 'object' ? pagination : {};\n  Object.keys(paginationObj).forEach(pageProp => {\n    const value = mergedPagination[pageProp];\n    if (typeof value !== 'function') {\n      param[pageProp] = value;\n    }\n  });\n  return param;\n}\nfunction usePagination(total, onChange, pagination) {\n  const _a = pagination && typeof pagination === 'object' ? pagination : {},\n    {\n      total: paginationTotal = 0\n    } = _a,\n    paginationObj = __rest(_a, [\"total\"]);\n  const [innerPagination, setInnerPagination] = useState(() => ({\n    current: 'defaultCurrent' in paginationObj ? paginationObj.defaultCurrent : 1,\n    pageSize: 'defaultPageSize' in paginationObj ? paginationObj.defaultPageSize : DEFAULT_PAGE_SIZE\n  }));\n  // ============ Basic Pagination Config ============\n  const mergedPagination = extendsObject(innerPagination, paginationObj, {\n    total: paginationTotal > 0 ? paginationTotal : total\n  });\n  // Reset `current` if data length or pageSize changed\n  const maxPage = Math.ceil((paginationTotal || total) / mergedPagination.pageSize);\n  if (mergedPagination.current > maxPage) {\n    // Prevent a maximum page count of 0\n    mergedPagination.current = maxPage || 1;\n  }\n  const refreshPagination = (current, pageSize) => {\n    setInnerPagination({\n      current: current !== null && current !== void 0 ? current : 1,\n      pageSize: pageSize || mergedPagination.pageSize\n    });\n  };\n  const onInternalChange = (current, pageSize) => {\n    var _a;\n    if (pagination) {\n      (_a = pagination.onChange) === null || _a === void 0 ? void 0 : _a.call(pagination, current, pageSize);\n    }\n    refreshPagination(current, pageSize);\n    onChange(current, pageSize || (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize));\n  };\n  if (pagination === false) {\n    return [{}, () => {}];\n  }\n  return [Object.assign(Object.assign({}, mergedPagination), {\n    onChange: onInternalChange\n  }), refreshPagination];\n}\nexport default usePagination;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,SAASW,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAO,MAAMC,iBAAiB,GAAG,EAAE;AACnC,OAAO,SAASC,kBAAkBA,CAACC,gBAAgB,EAAEC,UAAU,EAAE;EAC/D,MAAMC,KAAK,GAAG;IACZC,OAAO,EAAEH,gBAAgB,CAACG,OAAO;IACjCC,QAAQ,EAAEJ,gBAAgB,CAACI;EAC7B,CAAC;EACD,MAAMC,aAAa,GAAGJ,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;EACpFd,MAAM,CAACmB,IAAI,CAACD,aAAa,CAAC,CAACE,OAAO,CAACC,QAAQ,IAAI;IAC7C,MAAMC,KAAK,GAAGT,gBAAgB,CAACQ,QAAQ,CAAC;IACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE;MAC/BP,KAAK,CAACM,QAAQ,CAAC,GAAGC,KAAK;IACzB;EACF,CAAC,CAAC;EACF,OAAOP,KAAK;AACd;AACA,SAASQ,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEX,UAAU,EAAE;EAClD,MAAMY,EAAE,GAAGZ,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;IACvE;MACEU,KAAK,EAAEG,eAAe,GAAG;IAC3B,CAAC,GAAGD,EAAE;IACNR,aAAa,GAAGvB,MAAM,CAAC+B,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;EACvC,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,OAAO;IAC5DO,OAAO,EAAE,gBAAgB,IAAIE,aAAa,GAAGA,aAAa,CAACY,cAAc,GAAG,CAAC;IAC7Eb,QAAQ,EAAE,iBAAiB,IAAIC,aAAa,GAAGA,aAAa,CAACa,eAAe,GAAGpB;EACjF,CAAC,CAAC,CAAC;EACH;EACA,MAAME,gBAAgB,GAAGH,aAAa,CAACkB,eAAe,EAAEV,aAAa,EAAE;IACrEM,KAAK,EAAEG,eAAe,GAAG,CAAC,GAAGA,eAAe,GAAGH;EACjD,CAAC,CAAC;EACF;EACA,MAAMQ,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACP,eAAe,IAAIH,KAAK,IAAIX,gBAAgB,CAACI,QAAQ,CAAC;EACjF,IAAIJ,gBAAgB,CAACG,OAAO,GAAGgB,OAAO,EAAE;IACtC;IACAnB,gBAAgB,CAACG,OAAO,GAAGgB,OAAO,IAAI,CAAC;EACzC;EACA,MAAMG,iBAAiB,GAAGA,CAACnB,OAAO,EAAEC,QAAQ,KAAK;IAC/CY,kBAAkB,CAAC;MACjBb,OAAO,EAAEA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC;MAC7DC,QAAQ,EAAEA,QAAQ,IAAIJ,gBAAgB,CAACI;IACzC,CAAC,CAAC;EACJ,CAAC;EACD,MAAMmB,gBAAgB,GAAGA,CAACpB,OAAO,EAAEC,QAAQ,KAAK;IAC9C,IAAIS,EAAE;IACN,IAAIZ,UAAU,EAAE;MACd,CAACY,EAAE,GAAGZ,UAAU,CAACW,QAAQ,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvB,IAAI,CAACW,UAAU,EAAEE,OAAO,EAAEC,QAAQ,CAAC;IACxG;IACAkB,iBAAiB,CAACnB,OAAO,EAAEC,QAAQ,CAAC;IACpCQ,QAAQ,CAACT,OAAO,EAAEC,QAAQ,KAAKJ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACI,QAAQ,CAAC,CAAC;EAChI,CAAC;EACD,IAAIH,UAAU,KAAK,KAAK,EAAE;IACxB,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;EACvB;EACA,OAAO,CAACd,MAAM,CAACqC,MAAM,CAACrC,MAAM,CAACqC,MAAM,CAAC,CAAC,CAAC,EAAExB,gBAAgB,CAAC,EAAE;IACzDY,QAAQ,EAAEW;EACZ,CAAC,CAAC,EAAED,iBAAiB,CAAC;AACxB;AACA,eAAeZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nvar TransBtn = function TransBtn(props) {\n  var className = props.className,\n    customizeIcon = props.customizeIcon,\n    customizeIconProps = props.customizeIconProps,\n    children = props.children,\n    _onMouseDown = props.onMouseDown,\n    onClick = props.onClick;\n  var icon = typeof customizeIcon === 'function' ? customizeIcon(customizeIconProps) : customizeIcon;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    onMouseDown: function onMouseDown(event) {\n      event.preventDefault();\n      _onMouseDown === null || _onMouseDown === void 0 || _onMouseDown(event);\n    },\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none'\n    },\n    unselectable: \"on\",\n    onClick: onClick,\n    \"aria-hidden\": true\n  }, icon !== undefined ? icon : /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(className.split(/\\s+/).map(function (cls) {\n      return \"\".concat(cls, \"-icon\");\n    }))\n  }, children));\n};\nexport default TransBtn;", "map": {"version": 3, "names": ["React", "classNames", "TransBtn", "props", "className", "customizeIcon", "customizeIconProps", "children", "_onMouseDown", "onMouseDown", "onClick", "icon", "createElement", "event", "preventDefault", "style", "userSelect", "WebkitUserSelect", "unselectable", "undefined", "split", "map", "cls", "concat"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/TransBtn.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nvar TransBtn = function TransBtn(props) {\n  var className = props.className,\n    customizeIcon = props.customizeIcon,\n    customizeIconProps = props.customizeIconProps,\n    children = props.children,\n    _onMouseDown = props.onMouseDown,\n    onClick = props.onClick;\n  var icon = typeof customizeIcon === 'function' ? customizeIcon(customizeIconProps) : customizeIcon;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    onMouseDown: function onMouseDown(event) {\n      event.preventDefault();\n      _onMouseDown === null || _onMouseDown === void 0 || _onMouseDown(event);\n    },\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none'\n    },\n    unselectable: \"on\",\n    onClick: onClick,\n    \"aria-hidden\": true\n  }, icon !== undefined ? icon : /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(className.split(/\\s+/).map(function (cls) {\n      return \"\".concat(cls, \"-icon\");\n    }))\n  }, children));\n};\nexport default TransBtn;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,kBAAkB,GAAGH,KAAK,CAACG,kBAAkB;IAC7CC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,YAAY,GAAGL,KAAK,CAACM,WAAW;IAChCC,OAAO,GAAGP,KAAK,CAACO,OAAO;EACzB,IAAIC,IAAI,GAAG,OAAON,aAAa,KAAK,UAAU,GAAGA,aAAa,CAACC,kBAAkB,CAAC,GAAGD,aAAa;EAClG,OAAO,aAAaL,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IAC9CR,SAAS,EAAEA,SAAS;IACpBK,WAAW,EAAE,SAASA,WAAWA,CAACI,KAAK,EAAE;MACvCA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACK,KAAK,CAAC;IACzE,CAAC;IACDE,KAAK,EAAE;MACLC,UAAU,EAAE,MAAM;MAClBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE,IAAI;IAClBR,OAAO,EAAEA,OAAO;IAChB,aAAa,EAAE;EACjB,CAAC,EAAEC,IAAI,KAAKQ,SAAS,GAAGR,IAAI,GAAG,aAAaX,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IACtER,SAAS,EAAEH,UAAU,CAACG,SAAS,CAACgB,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC9D,OAAO,EAAE,CAACC,MAAM,CAACD,GAAG,EAAE,OAAO,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,EAAEf,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontSize,\n    lineHeight,\n    calc\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: calc(token.lineHeight).mul(fontSize).equal(),\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          borderRadius: token.borderRadiusSM,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${unit(token.paddingXS)}`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            whiteSpace: 'nowrap',\n            [actionCls]: {\n              opacity: 0\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`\n              ${actionCls}:focus-visible,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorIcon,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: token.calc(token.uploadProgressOffset).mul(-1).equal(),\n            width: '100%',\n            paddingInlineStart: calc(fontSize).add(token.paddingXS).equal(),\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;", "map": {"version": 3, "names": ["unit", "clearFix", "textEllipsis", "genListStyle", "token", "componentCls", "iconCls", "fontSize", "lineHeight", "calc", "itemCls", "actionsCls", "actionCls", "Object", "assign", "position", "height", "mul", "equal", "marginTop", "marginXS", "display", "alignItems", "transition", "motionDurationSlow", "borderRadius", "borderRadiusSM", "backgroundColor", "controlItemBgHover", "padding", "paddingXS", "flex", "whiteSpace", "opacity", "color", "actionsColor", "colorIcon", "bottom", "uploadProgressOffset", "width", "paddingInlineStart", "add", "pointerEvents", "margin", "colorError", "content"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/upload/style/list.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontSize,\n    lineHeight,\n    calc\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: calc(token.lineHeight).mul(fontSize).equal(),\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          borderRadius: token.borderRadiusSM,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${unit(token.paddingXS)}`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            whiteSpace: 'nowrap',\n            [actionCls]: {\n              opacity: 0\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`\n              ${actionCls}:focus-visible,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorIcon,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: token.calc(token.uploadProgressOffset).mul(-1).equal(),\n            width: '100%',\n            paddingInlineStart: calc(fontSize).add(token.paddingXS).equal(),\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,QAAQ,EAAEC,YAAY,QAAQ,aAAa;AACpD,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,GAAG,GAAGL,YAAY,YAAY;EAC3C,MAAMM,UAAU,GAAG,GAAGD,OAAO,UAAU;EACvC,MAAME,SAAS,GAAG,GAAGF,OAAO,SAAS;EACrC,OAAO;IACL,CAAC,GAAGL,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,OAAO,GAAGQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAE;QACrEO,UAAU,EAAEJ,KAAK,CAACI,UAAU;QAC5B,CAACE,OAAO,GAAG;UACTK,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAEP,IAAI,CAACL,KAAK,CAACI,UAAU,CAAC,CAACS,GAAG,CAACV,QAAQ,CAAC,CAACW,KAAK,CAAC,CAAC;UACpDC,SAAS,EAAEf,KAAK,CAACgB,QAAQ;UACzBb,QAAQ;UACRc,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAE,oBAAoBnB,KAAK,CAACoB,kBAAkB,EAAE;UAC1DC,YAAY,EAAErB,KAAK,CAACsB,cAAc;UAClC,SAAS,EAAE;YACTC,eAAe,EAAEvB,KAAK,CAACwB;UACzB,CAAC;UACD,CAAC,GAAGlB,OAAO,OAAO,GAAGG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,YAAY,CAAC,EAAE;YAClE2B,OAAO,EAAE,KAAK7B,IAAI,CAACI,KAAK,CAAC0B,SAAS,CAAC,EAAE;YACrCtB,UAAU;YACVuB,IAAI,EAAE,MAAM;YACZR,UAAU,EAAE,OAAOnB,KAAK,CAACoB,kBAAkB;UAC7C,CAAC,CAAC;UACF,CAACb,UAAU,GAAG;YACZqB,UAAU,EAAE,QAAQ;YACpB,CAACpB,SAAS,GAAG;cACXqB,OAAO,EAAE;YACX,CAAC;YACD,CAAC3B,OAAO,GAAG;cACT4B,KAAK,EAAE9B,KAAK,CAAC+B,YAAY;cACzBZ,UAAU,EAAE,OAAOnB,KAAK,CAACoB,kBAAkB;YAC7C,CAAC;YACD,CAAC;AACb,gBAAgBZ,SAAS;AACzB,0BAA0BA,SAAS;AACnC,aAAa,GAAG;cACFqB,OAAO,EAAE;YACX;UACF,CAAC;UACD,CAAC,GAAG5B,YAAY,SAASC,OAAO,EAAE,GAAG;YACnC4B,KAAK,EAAE9B,KAAK,CAACgC,SAAS;YACtB7B;UACF,CAAC;UACD,CAAC,GAAGG,OAAO,WAAW,GAAG;YACvBK,QAAQ,EAAE,UAAU;YACpBsB,MAAM,EAAEjC,KAAK,CAACK,IAAI,CAACL,KAAK,CAACkC,oBAAoB,CAAC,CAACrB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAC9DqB,KAAK,EAAE,MAAM;YACbC,kBAAkB,EAAE/B,IAAI,CAACF,QAAQ,CAAC,CAACkC,GAAG,CAACrC,KAAK,CAAC0B,SAAS,CAAC,CAACZ,KAAK,CAAC,CAAC;YAC/DX,QAAQ;YACRC,UAAU,EAAE,CAAC;YACbkC,aAAa,EAAE,MAAM;YACrB,OAAO,EAAE;cACPC,MAAM,EAAE;YACV;UACF;QACF,CAAC;QACD,CAAC,GAAGjC,OAAO,UAAUE,SAAS,EAAE,GAAG;UACjCqB,OAAO,EAAE;QACX,CAAC;QACD,CAAC,GAAGvB,OAAO,QAAQ,GAAG;UACpBwB,KAAK,EAAE9B,KAAK,CAACwC,UAAU;UACvB,CAAC,GAAGlC,OAAO,UAAUL,YAAY,SAASC,OAAO,EAAE,GAAG;YACpD4B,KAAK,EAAE9B,KAAK,CAACwC;UACf,CAAC;UACD,CAACjC,UAAU,GAAG;YACZ,CAAC,GAAGL,OAAO,KAAKA,OAAO,QAAQ,GAAG;cAChC4B,KAAK,EAAE9B,KAAK,CAACwC;YACf,CAAC;YACD,CAAChC,SAAS,GAAG;cACXqB,OAAO,EAAE;YACX;UACF;QACF,CAAC;QACD,CAAC,GAAG5B,YAAY,sBAAsB,GAAG;UACvCkB,UAAU,EAAE,WAAWnB,KAAK,CAACoB,kBAAkB,YAAYpB,KAAK,CAACoB,kBAAkB,EAAE;UACrF;UACA,WAAW,EAAE;YACXH,OAAO,EAAE,OAAO;YAChBkB,KAAK,EAAE,CAAC;YACRvB,MAAM,EAAE,CAAC;YACT6B,OAAO,EAAE;UACX;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAe1C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
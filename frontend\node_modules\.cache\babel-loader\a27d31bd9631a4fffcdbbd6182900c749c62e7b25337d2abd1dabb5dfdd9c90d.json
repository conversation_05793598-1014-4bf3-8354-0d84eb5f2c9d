{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getMenuId } from \"../context/IdContext\";\n// destruct to reduce minify size\nvar LEFT = KeyCode.LEFT,\n  RIGHT = KeyCode.RIGHT,\n  UP = KeyCode.UP,\n  DOWN = KeyCode.DOWN,\n  ENTER = KeyCode.ENTER,\n  ESC = KeyCode.ESC,\n  HOME = KeyCode.HOME,\n  END = KeyCode.END;\nvar ArrowKeys = [UP, DOWN, LEFT, RIGHT];\nfunction getOffset(mode, isRootLevel, isRtl, which) {\n  var _offsets;\n  var prev = 'prev';\n  var next = 'next';\n  var children = 'children';\n  var parent = 'parent';\n\n  // Inline enter is special that we use unique operation\n  if (mode === 'inline' && which === ENTER) {\n    return {\n      inlineTrigger: true\n    };\n  }\n  var inline = _defineProperty(_defineProperty({}, UP, prev), DOWN, next);\n  var horizontal = _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, LEFT, isRtl ? next : prev), RIGHT, isRtl ? prev : next), DOWN, children), ENTER, children);\n  var vertical = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, UP, prev), DOWN, next), ENTER, children), ESC, parent), LEFT, isRtl ? children : parent), RIGHT, isRtl ? parent : children);\n  var offsets = {\n    inline: inline,\n    horizontal: horizontal,\n    vertical: vertical,\n    inlineSub: inline,\n    horizontalSub: vertical,\n    verticalSub: vertical\n  };\n  var type = (_offsets = offsets[\"\".concat(mode).concat(isRootLevel ? '' : 'Sub')]) === null || _offsets === void 0 ? void 0 : _offsets[which];\n  switch (type) {\n    case prev:\n      return {\n        offset: -1,\n        sibling: true\n      };\n    case next:\n      return {\n        offset: 1,\n        sibling: true\n      };\n    case parent:\n      return {\n        offset: -1,\n        sibling: false\n      };\n    case children:\n      return {\n        offset: 1,\n        sibling: false\n      };\n    default:\n      return null;\n  }\n}\nfunction findContainerUL(element) {\n  var current = element;\n  while (current) {\n    if (current.getAttribute('data-menu-list')) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n\n  // Normally should not reach this line\n  /* istanbul ignore next */\n  return null;\n}\n\n/**\n * Find focused element within element set provided\n */\nfunction getFocusElement(activeElement, elements) {\n  var current = activeElement || document.activeElement;\n  while (current) {\n    if (elements.has(current)) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n\n/**\n * Get focusable elements from the element set under provided container\n */\nexport function getFocusableElements(container, elements) {\n  var list = getFocusNodeList(container, true);\n  return list.filter(function (ele) {\n    return elements.has(ele);\n  });\n}\nfunction getNextFocusElement(parentQueryContainer, elements, focusMenuElement) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  // Key on the menu item will not get validate parent container\n  if (!parentQueryContainer) {\n    return null;\n  }\n\n  // List current level menu item elements\n  var sameLevelFocusableMenuElementList = getFocusableElements(parentQueryContainer, elements);\n\n  // Find next focus index\n  var count = sameLevelFocusableMenuElementList.length;\n  var focusIndex = sameLevelFocusableMenuElementList.findIndex(function (ele) {\n    return focusMenuElement === ele;\n  });\n  if (offset < 0) {\n    if (focusIndex === -1) {\n      focusIndex = count - 1;\n    } else {\n      focusIndex -= 1;\n    }\n  } else if (offset > 0) {\n    focusIndex += 1;\n  }\n  focusIndex = (focusIndex + count) % count;\n\n  // Focus menu item\n  return sameLevelFocusableMenuElementList[focusIndex];\n}\nexport var refreshElements = function refreshElements(keys, id) {\n  var elements = new Set();\n  var key2element = new Map();\n  var element2key = new Map();\n  keys.forEach(function (key) {\n    var element = document.querySelector(\"[data-menu-id='\".concat(getMenuId(id, key), \"']\"));\n    if (element) {\n      elements.add(element);\n      element2key.set(element, key);\n      key2element.set(key, element);\n    }\n  });\n  return {\n    elements: elements,\n    key2element: key2element,\n    element2key: element2key\n  };\n};\nexport function useAccessibility(mode, activeKey, isRtl, id, containerRef, getKeys, getKeyPath, triggerActiveKey, triggerAccessibilityOpen, originOnKeyDown) {\n  var rafRef = React.useRef();\n  var activeRef = React.useRef();\n  activeRef.current = activeKey;\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(rafRef.current);\n  };\n  React.useEffect(function () {\n    return function () {\n      cleanRaf();\n    };\n  }, []);\n  return function (e) {\n    var which = e.which;\n    if ([].concat(ArrowKeys, [ENTER, ESC, HOME, END]).includes(which)) {\n      var keys = getKeys();\n      var refreshedElements = refreshElements(keys, id);\n      var _refreshedElements = refreshedElements,\n        elements = _refreshedElements.elements,\n        key2element = _refreshedElements.key2element,\n        element2key = _refreshedElements.element2key;\n\n      // First we should find current focused MenuItem/SubMenu element\n      var activeElement = key2element.get(activeKey);\n      var focusMenuElement = getFocusElement(activeElement, elements);\n      var focusMenuKey = element2key.get(focusMenuElement);\n      var offsetObj = getOffset(mode, getKeyPath(focusMenuKey, true).length === 1, isRtl, which);\n\n      // Some mode do not have fully arrow operation like inline\n      if (!offsetObj && which !== HOME && which !== END) {\n        return;\n      }\n\n      // Arrow prevent default to avoid page scroll\n      if (ArrowKeys.includes(which) || [HOME, END].includes(which)) {\n        e.preventDefault();\n      }\n      var tryFocus = function tryFocus(menuElement) {\n        if (menuElement) {\n          var focusTargetElement = menuElement;\n\n          // Focus to link instead of menu item if possible\n          var link = menuElement.querySelector('a');\n          if (link !== null && link !== void 0 && link.getAttribute('href')) {\n            focusTargetElement = link;\n          }\n          var targetKey = element2key.get(menuElement);\n          triggerActiveKey(targetKey);\n\n          /**\n           * Do not `useEffect` here since `tryFocus` may trigger async\n           * which makes React sync update the `activeKey`\n           * that force render before `useRef` set the next activeKey\n           */\n          cleanRaf();\n          rafRef.current = raf(function () {\n            if (activeRef.current === targetKey) {\n              focusTargetElement.focus();\n            }\n          });\n        }\n      };\n      if ([HOME, END].includes(which) || offsetObj.sibling || !focusMenuElement) {\n        // ========================== Sibling ==========================\n        // Find walkable focus menu element container\n        var parentQueryContainer;\n        if (!focusMenuElement || mode === 'inline') {\n          parentQueryContainer = containerRef.current;\n        } else {\n          parentQueryContainer = findContainerUL(focusMenuElement);\n        }\n\n        // Get next focus element\n        var targetElement;\n        var focusableElements = getFocusableElements(parentQueryContainer, elements);\n        if (which === HOME) {\n          targetElement = focusableElements[0];\n        } else if (which === END) {\n          targetElement = focusableElements[focusableElements.length - 1];\n        } else {\n          targetElement = getNextFocusElement(parentQueryContainer, elements, focusMenuElement, offsetObj.offset);\n        }\n        // Focus menu item\n        tryFocus(targetElement);\n\n        // ======================= InlineTrigger =======================\n      } else if (offsetObj.inlineTrigger) {\n        // Inline trigger no need switch to sub menu item\n        triggerAccessibilityOpen(focusMenuKey);\n        // =========================== Level ===========================\n      } else if (offsetObj.offset > 0) {\n        triggerAccessibilityOpen(focusMenuKey, true);\n        cleanRaf();\n        rafRef.current = raf(function () {\n          // Async should resync elements\n          refreshedElements = refreshElements(keys, id);\n          var controlId = focusMenuElement.getAttribute('aria-controls');\n          var subQueryContainer = document.getElementById(controlId);\n\n          // Get sub focusable menu item\n          var targetElement = getNextFocusElement(subQueryContainer, refreshedElements.elements);\n\n          // Focus menu item\n          tryFocus(targetElement);\n        }, 5);\n      } else if (offsetObj.offset < 0) {\n        var keyPath = getKeyPath(focusMenuKey, true);\n        var parentKey = keyPath[keyPath.length - 2];\n        var parentMenuElement = key2element.get(parentKey);\n\n        // Focus menu item\n        triggerAccessibilityOpen(parentKey, false);\n        tryFocus(parentMenuElement);\n      }\n    }\n\n    // Pass origin key down event\n    originOnKeyDown === null || originOnKeyDown === void 0 || originOnKeyDown(e);\n  };\n}", "map": {"version": 3, "names": ["_defineProperty", "getFocusNodeList", "KeyCode", "raf", "React", "getMenuId", "LEFT", "RIGHT", "UP", "DOWN", "ENTER", "ESC", "HOME", "END", "ArrowKeys", "getOffset", "mode", "isRootLevel", "isRtl", "which", "_offsets", "prev", "next", "children", "parent", "inlineTrigger", "inline", "horizontal", "vertical", "offsets", "inlineSub", "horizontalSub", "verticalSub", "type", "concat", "offset", "sibling", "findContainerUL", "element", "current", "getAttribute", "parentElement", "getFocusElement", "activeElement", "elements", "document", "has", "getFocusableElements", "container", "list", "filter", "ele", "getNextFocusElement", "parentQueryContainer", "focusMenuElement", "arguments", "length", "undefined", "sameLevelFocusableMenuElementList", "count", "focusIndex", "findIndex", "refreshElements", "keys", "id", "Set", "key2element", "Map", "element2key", "for<PERSON>ach", "key", "querySelector", "add", "set", "useAccessibility", "active<PERSON><PERSON>", "containerRef", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerActiveKey", "triggerAccessibilityOpen", "originOnKeyDown", "rafRef", "useRef", "activeRef", "cleanRaf", "cancel", "useEffect", "e", "includes", "refreshedElements", "_refreshedElements", "get", "focusMenuKey", "offsetObj", "preventDefault", "tryFocus", "menuElement", "focusTargetElement", "link", "<PERSON><PERSON><PERSON>", "focus", "targetElement", "focusableElements", "controlId", "subQueryContainer", "getElementById", "keyP<PERSON>", "parent<PERSON><PERSON>", "parentMenuElement"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-menu@9.16.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-menu/es/hooks/useAccessibility.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getMenuId } from \"../context/IdContext\";\n// destruct to reduce minify size\nvar LEFT = KeyCode.LEFT,\n  RIGHT = KeyCode.RIGHT,\n  UP = KeyCode.UP,\n  DOWN = KeyCode.DOWN,\n  ENTER = KeyCode.ENTER,\n  ESC = KeyCode.ESC,\n  HOME = KeyCode.HOME,\n  END = KeyCode.END;\nvar ArrowKeys = [UP, DOWN, LEFT, RIGHT];\nfunction getOffset(mode, isRootLevel, isRtl, which) {\n  var _offsets;\n  var prev = 'prev';\n  var next = 'next';\n  var children = 'children';\n  var parent = 'parent';\n\n  // Inline enter is special that we use unique operation\n  if (mode === 'inline' && which === ENTER) {\n    return {\n      inlineTrigger: true\n    };\n  }\n  var inline = _defineProperty(_defineProperty({}, UP, prev), DOWN, next);\n  var horizontal = _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, LEFT, isRtl ? next : prev), RIGHT, isRtl ? prev : next), DOWN, children), ENTER, children);\n  var vertical = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, UP, prev), DOWN, next), ENTER, children), ESC, parent), LEFT, isRtl ? children : parent), RIGHT, isRtl ? parent : children);\n  var offsets = {\n    inline: inline,\n    horizontal: horizontal,\n    vertical: vertical,\n    inlineSub: inline,\n    horizontalSub: vertical,\n    verticalSub: vertical\n  };\n  var type = (_offsets = offsets[\"\".concat(mode).concat(isRootLevel ? '' : 'Sub')]) === null || _offsets === void 0 ? void 0 : _offsets[which];\n  switch (type) {\n    case prev:\n      return {\n        offset: -1,\n        sibling: true\n      };\n    case next:\n      return {\n        offset: 1,\n        sibling: true\n      };\n    case parent:\n      return {\n        offset: -1,\n        sibling: false\n      };\n    case children:\n      return {\n        offset: 1,\n        sibling: false\n      };\n    default:\n      return null;\n  }\n}\nfunction findContainerUL(element) {\n  var current = element;\n  while (current) {\n    if (current.getAttribute('data-menu-list')) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n\n  // Normally should not reach this line\n  /* istanbul ignore next */\n  return null;\n}\n\n/**\n * Find focused element within element set provided\n */\nfunction getFocusElement(activeElement, elements) {\n  var current = activeElement || document.activeElement;\n  while (current) {\n    if (elements.has(current)) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n\n/**\n * Get focusable elements from the element set under provided container\n */\nexport function getFocusableElements(container, elements) {\n  var list = getFocusNodeList(container, true);\n  return list.filter(function (ele) {\n    return elements.has(ele);\n  });\n}\nfunction getNextFocusElement(parentQueryContainer, elements, focusMenuElement) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  // Key on the menu item will not get validate parent container\n  if (!parentQueryContainer) {\n    return null;\n  }\n\n  // List current level menu item elements\n  var sameLevelFocusableMenuElementList = getFocusableElements(parentQueryContainer, elements);\n\n  // Find next focus index\n  var count = sameLevelFocusableMenuElementList.length;\n  var focusIndex = sameLevelFocusableMenuElementList.findIndex(function (ele) {\n    return focusMenuElement === ele;\n  });\n  if (offset < 0) {\n    if (focusIndex === -1) {\n      focusIndex = count - 1;\n    } else {\n      focusIndex -= 1;\n    }\n  } else if (offset > 0) {\n    focusIndex += 1;\n  }\n  focusIndex = (focusIndex + count) % count;\n\n  // Focus menu item\n  return sameLevelFocusableMenuElementList[focusIndex];\n}\nexport var refreshElements = function refreshElements(keys, id) {\n  var elements = new Set();\n  var key2element = new Map();\n  var element2key = new Map();\n  keys.forEach(function (key) {\n    var element = document.querySelector(\"[data-menu-id='\".concat(getMenuId(id, key), \"']\"));\n    if (element) {\n      elements.add(element);\n      element2key.set(element, key);\n      key2element.set(key, element);\n    }\n  });\n  return {\n    elements: elements,\n    key2element: key2element,\n    element2key: element2key\n  };\n};\nexport function useAccessibility(mode, activeKey, isRtl, id, containerRef, getKeys, getKeyPath, triggerActiveKey, triggerAccessibilityOpen, originOnKeyDown) {\n  var rafRef = React.useRef();\n  var activeRef = React.useRef();\n  activeRef.current = activeKey;\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(rafRef.current);\n  };\n  React.useEffect(function () {\n    return function () {\n      cleanRaf();\n    };\n  }, []);\n  return function (e) {\n    var which = e.which;\n    if ([].concat(ArrowKeys, [ENTER, ESC, HOME, END]).includes(which)) {\n      var keys = getKeys();\n      var refreshedElements = refreshElements(keys, id);\n      var _refreshedElements = refreshedElements,\n        elements = _refreshedElements.elements,\n        key2element = _refreshedElements.key2element,\n        element2key = _refreshedElements.element2key;\n\n      // First we should find current focused MenuItem/SubMenu element\n      var activeElement = key2element.get(activeKey);\n      var focusMenuElement = getFocusElement(activeElement, elements);\n      var focusMenuKey = element2key.get(focusMenuElement);\n      var offsetObj = getOffset(mode, getKeyPath(focusMenuKey, true).length === 1, isRtl, which);\n\n      // Some mode do not have fully arrow operation like inline\n      if (!offsetObj && which !== HOME && which !== END) {\n        return;\n      }\n\n      // Arrow prevent default to avoid page scroll\n      if (ArrowKeys.includes(which) || [HOME, END].includes(which)) {\n        e.preventDefault();\n      }\n      var tryFocus = function tryFocus(menuElement) {\n        if (menuElement) {\n          var focusTargetElement = menuElement;\n\n          // Focus to link instead of menu item if possible\n          var link = menuElement.querySelector('a');\n          if (link !== null && link !== void 0 && link.getAttribute('href')) {\n            focusTargetElement = link;\n          }\n          var targetKey = element2key.get(menuElement);\n          triggerActiveKey(targetKey);\n\n          /**\n           * Do not `useEffect` here since `tryFocus` may trigger async\n           * which makes React sync update the `activeKey`\n           * that force render before `useRef` set the next activeKey\n           */\n          cleanRaf();\n          rafRef.current = raf(function () {\n            if (activeRef.current === targetKey) {\n              focusTargetElement.focus();\n            }\n          });\n        }\n      };\n      if ([HOME, END].includes(which) || offsetObj.sibling || !focusMenuElement) {\n        // ========================== Sibling ==========================\n        // Find walkable focus menu element container\n        var parentQueryContainer;\n        if (!focusMenuElement || mode === 'inline') {\n          parentQueryContainer = containerRef.current;\n        } else {\n          parentQueryContainer = findContainerUL(focusMenuElement);\n        }\n\n        // Get next focus element\n        var targetElement;\n        var focusableElements = getFocusableElements(parentQueryContainer, elements);\n        if (which === HOME) {\n          targetElement = focusableElements[0];\n        } else if (which === END) {\n          targetElement = focusableElements[focusableElements.length - 1];\n        } else {\n          targetElement = getNextFocusElement(parentQueryContainer, elements, focusMenuElement, offsetObj.offset);\n        }\n        // Focus menu item\n        tryFocus(targetElement);\n\n        // ======================= InlineTrigger =======================\n      } else if (offsetObj.inlineTrigger) {\n        // Inline trigger no need switch to sub menu item\n        triggerAccessibilityOpen(focusMenuKey);\n        // =========================== Level ===========================\n      } else if (offsetObj.offset > 0) {\n        triggerAccessibilityOpen(focusMenuKey, true);\n        cleanRaf();\n        rafRef.current = raf(function () {\n          // Async should resync elements\n          refreshedElements = refreshElements(keys, id);\n          var controlId = focusMenuElement.getAttribute('aria-controls');\n          var subQueryContainer = document.getElementById(controlId);\n\n          // Get sub focusable menu item\n          var targetElement = getNextFocusElement(subQueryContainer, refreshedElements.elements);\n\n          // Focus menu item\n          tryFocus(targetElement);\n        }, 5);\n      } else if (offsetObj.offset < 0) {\n        var keyPath = getKeyPath(focusMenuKey, true);\n        var parentKey = keyPath[keyPath.length - 2];\n        var parentMenuElement = key2element.get(parentKey);\n\n        // Focus menu item\n        triggerAccessibilityOpen(parentKey, false);\n        tryFocus(parentMenuElement);\n      }\n    }\n\n    // Pass origin key down event\n    originOnKeyDown === null || originOnKeyDown === void 0 || originOnKeyDown(e);\n  };\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,sBAAsB;AAChD;AACA,IAAIC,IAAI,GAAGJ,OAAO,CAACI,IAAI;EACrBC,KAAK,GAAGL,OAAO,CAACK,KAAK;EACrBC,EAAE,GAAGN,OAAO,CAACM,EAAE;EACfC,IAAI,GAAGP,OAAO,CAACO,IAAI;EACnBC,KAAK,GAAGR,OAAO,CAACQ,KAAK;EACrBC,GAAG,GAAGT,OAAO,CAACS,GAAG;EACjBC,IAAI,GAAGV,OAAO,CAACU,IAAI;EACnBC,GAAG,GAAGX,OAAO,CAACW,GAAG;AACnB,IAAIC,SAAS,GAAG,CAACN,EAAE,EAAEC,IAAI,EAAEH,IAAI,EAAEC,KAAK,CAAC;AACvC,SAASQ,SAASA,CAACC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAClD,IAAIC,QAAQ;EACZ,IAAIC,IAAI,GAAG,MAAM;EACjB,IAAIC,IAAI,GAAG,MAAM;EACjB,IAAIC,QAAQ,GAAG,UAAU;EACzB,IAAIC,MAAM,GAAG,QAAQ;;EAErB;EACA,IAAIR,IAAI,KAAK,QAAQ,IAAIG,KAAK,KAAKT,KAAK,EAAE;IACxC,OAAO;MACLe,aAAa,EAAE;IACjB,CAAC;EACH;EACA,IAAIC,MAAM,GAAG1B,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEa,IAAI,CAAC,EAAEZ,IAAI,EAAEa,IAAI,CAAC;EACvE,IAAIK,UAAU,GAAG3B,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEM,IAAI,EAAEY,KAAK,GAAGI,IAAI,GAAGD,IAAI,CAAC,EAAEd,KAAK,EAAEW,KAAK,GAAGG,IAAI,GAAGC,IAAI,CAAC,EAAEb,IAAI,EAAEc,QAAQ,CAAC,EAAEb,KAAK,EAAEa,QAAQ,CAAC;EAC/K,IAAIK,QAAQ,GAAG5B,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEa,IAAI,CAAC,EAAEZ,IAAI,EAAEa,IAAI,CAAC,EAAEZ,KAAK,EAAEa,QAAQ,CAAC,EAAEZ,GAAG,EAAEa,MAAM,CAAC,EAAElB,IAAI,EAAEY,KAAK,GAAGK,QAAQ,GAAGC,MAAM,CAAC,EAAEjB,KAAK,EAAEW,KAAK,GAAGM,MAAM,GAAGD,QAAQ,CAAC;EAC9O,IAAIM,OAAO,GAAG;IACZH,MAAM,EAAEA,MAAM;IACdC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA,QAAQ;IAClBE,SAAS,EAAEJ,MAAM;IACjBK,aAAa,EAAEH,QAAQ;IACvBI,WAAW,EAAEJ;EACf,CAAC;EACD,IAAIK,IAAI,GAAG,CAACb,QAAQ,GAAGS,OAAO,CAAC,EAAE,CAACK,MAAM,CAAClB,IAAI,CAAC,CAACkB,MAAM,CAACjB,WAAW,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,MAAM,IAAI,IAAIG,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,KAAK,CAAC;EAC5I,QAAQc,IAAI;IACV,KAAKZ,IAAI;MACP,OAAO;QACLc,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,KAAKd,IAAI;MACP,OAAO;QACLa,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;IACH,KAAKZ,MAAM;MACT,OAAO;QACLW,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,KAAKb,QAAQ;MACX,OAAO;QACLY,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;IACH;MACE,OAAO,IAAI;EACf;AACF;AACA,SAASC,eAAeA,CAACC,OAAO,EAAE;EAChC,IAAIC,OAAO,GAAGD,OAAO;EACrB,OAAOC,OAAO,EAAE;IACd,IAAIA,OAAO,CAACC,YAAY,CAAC,gBAAgB,CAAC,EAAE;MAC1C,OAAOD,OAAO;IAChB;IACAA,OAAO,GAAGA,OAAO,CAACE,aAAa;EACjC;;EAEA;EACA;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAE;EAChD,IAAIL,OAAO,GAAGI,aAAa,IAAIE,QAAQ,CAACF,aAAa;EACrD,OAAOJ,OAAO,EAAE;IACd,IAAIK,QAAQ,CAACE,GAAG,CAACP,OAAO,CAAC,EAAE;MACzB,OAAOA,OAAO;IAChB;IACAA,OAAO,GAAGA,OAAO,CAACE,aAAa;EACjC;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,OAAO,SAASM,oBAAoBA,CAACC,SAAS,EAAEJ,QAAQ,EAAE;EACxD,IAAIK,IAAI,GAAGhD,gBAAgB,CAAC+C,SAAS,EAAE,IAAI,CAAC;EAC5C,OAAOC,IAAI,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;IAChC,OAAOP,QAAQ,CAACE,GAAG,CAACK,GAAG,CAAC;EAC1B,CAAC,CAAC;AACJ;AACA,SAASC,mBAAmBA,CAACC,oBAAoB,EAAET,QAAQ,EAAEU,gBAAgB,EAAE;EAC7E,IAAInB,MAAM,GAAGoB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF;EACA,IAAI,CAACF,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb;;EAEA;EACA,IAAIK,iCAAiC,GAAGX,oBAAoB,CAACM,oBAAoB,EAAET,QAAQ,CAAC;;EAE5F;EACA,IAAIe,KAAK,GAAGD,iCAAiC,CAACF,MAAM;EACpD,IAAII,UAAU,GAAGF,iCAAiC,CAACG,SAAS,CAAC,UAAUV,GAAG,EAAE;IAC1E,OAAOG,gBAAgB,KAAKH,GAAG;EACjC,CAAC,CAAC;EACF,IAAIhB,MAAM,GAAG,CAAC,EAAE;IACd,IAAIyB,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBA,UAAU,GAAGD,KAAK,GAAG,CAAC;IACxB,CAAC,MAAM;MACLC,UAAU,IAAI,CAAC;IACjB;EACF,CAAC,MAAM,IAAIzB,MAAM,GAAG,CAAC,EAAE;IACrByB,UAAU,IAAI,CAAC;EACjB;EACAA,UAAU,GAAG,CAACA,UAAU,GAAGD,KAAK,IAAIA,KAAK;;EAEzC;EACA,OAAOD,iCAAiC,CAACE,UAAU,CAAC;AACtD;AACA,OAAO,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,EAAE,EAAE;EAC9D,IAAIpB,QAAQ,GAAG,IAAIqB,GAAG,CAAC,CAAC;EACxB,IAAIC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B,IAAIC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3BJ,IAAI,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC1B,IAAIhC,OAAO,GAAGO,QAAQ,CAAC0B,aAAa,CAAC,iBAAiB,CAACrC,MAAM,CAAC7B,SAAS,CAAC2D,EAAE,EAAEM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IACxF,IAAIhC,OAAO,EAAE;MACXM,QAAQ,CAAC4B,GAAG,CAAClC,OAAO,CAAC;MACrB8B,WAAW,CAACK,GAAG,CAACnC,OAAO,EAAEgC,GAAG,CAAC;MAC7BJ,WAAW,CAACO,GAAG,CAACH,GAAG,EAAEhC,OAAO,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO;IACLM,QAAQ,EAAEA,QAAQ;IAClBsB,WAAW,EAAEA,WAAW;IACxBE,WAAW,EAAEA;EACf,CAAC;AACH,CAAC;AACD,OAAO,SAASM,gBAAgBA,CAAC1D,IAAI,EAAE2D,SAAS,EAAEzD,KAAK,EAAE8C,EAAE,EAAEY,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,eAAe,EAAE;EAC3J,IAAIC,MAAM,GAAG9E,KAAK,CAAC+E,MAAM,CAAC,CAAC;EAC3B,IAAIC,SAAS,GAAGhF,KAAK,CAAC+E,MAAM,CAAC,CAAC;EAC9BC,SAAS,CAAC7C,OAAO,GAAGoC,SAAS;EAC7B,IAAIU,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjClF,GAAG,CAACmF,MAAM,CAACJ,MAAM,CAAC3C,OAAO,CAAC;EAC5B,CAAC;EACDnC,KAAK,CAACmF,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBF,QAAQ,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,UAAUG,CAAC,EAAE;IAClB,IAAIrE,KAAK,GAAGqE,CAAC,CAACrE,KAAK;IACnB,IAAI,EAAE,CAACe,MAAM,CAACpB,SAAS,EAAE,CAACJ,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,CAAC,CAAC,CAAC4E,QAAQ,CAACtE,KAAK,CAAC,EAAE;MACjE,IAAI4C,IAAI,GAAGc,OAAO,CAAC,CAAC;MACpB,IAAIa,iBAAiB,GAAG5B,eAAe,CAACC,IAAI,EAAEC,EAAE,CAAC;MACjD,IAAI2B,kBAAkB,GAAGD,iBAAiB;QACxC9C,QAAQ,GAAG+C,kBAAkB,CAAC/C,QAAQ;QACtCsB,WAAW,GAAGyB,kBAAkB,CAACzB,WAAW;QAC5CE,WAAW,GAAGuB,kBAAkB,CAACvB,WAAW;;MAE9C;MACA,IAAIzB,aAAa,GAAGuB,WAAW,CAAC0B,GAAG,CAACjB,SAAS,CAAC;MAC9C,IAAIrB,gBAAgB,GAAGZ,eAAe,CAACC,aAAa,EAAEC,QAAQ,CAAC;MAC/D,IAAIiD,YAAY,GAAGzB,WAAW,CAACwB,GAAG,CAACtC,gBAAgB,CAAC;MACpD,IAAIwC,SAAS,GAAG/E,SAAS,CAACC,IAAI,EAAE8D,UAAU,CAACe,YAAY,EAAE,IAAI,CAAC,CAACrC,MAAM,KAAK,CAAC,EAAEtC,KAAK,EAAEC,KAAK,CAAC;;MAE1F;MACA,IAAI,CAAC2E,SAAS,IAAI3E,KAAK,KAAKP,IAAI,IAAIO,KAAK,KAAKN,GAAG,EAAE;QACjD;MACF;;MAEA;MACA,IAAIC,SAAS,CAAC2E,QAAQ,CAACtE,KAAK,CAAC,IAAI,CAACP,IAAI,EAAEC,GAAG,CAAC,CAAC4E,QAAQ,CAACtE,KAAK,CAAC,EAAE;QAC5DqE,CAAC,CAACO,cAAc,CAAC,CAAC;MACpB;MACA,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,WAAW,EAAE;QAC5C,IAAIA,WAAW,EAAE;UACf,IAAIC,kBAAkB,GAAGD,WAAW;;UAEpC;UACA,IAAIE,IAAI,GAAGF,WAAW,CAAC1B,aAAa,CAAC,GAAG,CAAC;UACzC,IAAI4B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,CAAC3D,YAAY,CAAC,MAAM,CAAC,EAAE;YACjE0D,kBAAkB,GAAGC,IAAI;UAC3B;UACA,IAAIC,SAAS,GAAGhC,WAAW,CAACwB,GAAG,CAACK,WAAW,CAAC;UAC5ClB,gBAAgB,CAACqB,SAAS,CAAC;;UAE3B;AACV;AACA;AACA;AACA;UACUf,QAAQ,CAAC,CAAC;UACVH,MAAM,CAAC3C,OAAO,GAAGpC,GAAG,CAAC,YAAY;YAC/B,IAAIiF,SAAS,CAAC7C,OAAO,KAAK6D,SAAS,EAAE;cACnCF,kBAAkB,CAACG,KAAK,CAAC,CAAC;YAC5B;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MACD,IAAI,CAACzF,IAAI,EAAEC,GAAG,CAAC,CAAC4E,QAAQ,CAACtE,KAAK,CAAC,IAAI2E,SAAS,CAAC1D,OAAO,IAAI,CAACkB,gBAAgB,EAAE;QACzE;QACA;QACA,IAAID,oBAAoB;QACxB,IAAI,CAACC,gBAAgB,IAAItC,IAAI,KAAK,QAAQ,EAAE;UAC1CqC,oBAAoB,GAAGuB,YAAY,CAACrC,OAAO;QAC7C,CAAC,MAAM;UACLc,oBAAoB,GAAGhB,eAAe,CAACiB,gBAAgB,CAAC;QAC1D;;QAEA;QACA,IAAIgD,aAAa;QACjB,IAAIC,iBAAiB,GAAGxD,oBAAoB,CAACM,oBAAoB,EAAET,QAAQ,CAAC;QAC5E,IAAIzB,KAAK,KAAKP,IAAI,EAAE;UAClB0F,aAAa,GAAGC,iBAAiB,CAAC,CAAC,CAAC;QACtC,CAAC,MAAM,IAAIpF,KAAK,KAAKN,GAAG,EAAE;UACxByF,aAAa,GAAGC,iBAAiB,CAACA,iBAAiB,CAAC/C,MAAM,GAAG,CAAC,CAAC;QACjE,CAAC,MAAM;UACL8C,aAAa,GAAGlD,mBAAmB,CAACC,oBAAoB,EAAET,QAAQ,EAAEU,gBAAgB,EAAEwC,SAAS,CAAC3D,MAAM,CAAC;QACzG;QACA;QACA6D,QAAQ,CAACM,aAAa,CAAC;;QAEvB;MACF,CAAC,MAAM,IAAIR,SAAS,CAACrE,aAAa,EAAE;QAClC;QACAuD,wBAAwB,CAACa,YAAY,CAAC;QACtC;MACF,CAAC,MAAM,IAAIC,SAAS,CAAC3D,MAAM,GAAG,CAAC,EAAE;QAC/B6C,wBAAwB,CAACa,YAAY,EAAE,IAAI,CAAC;QAC5CR,QAAQ,CAAC,CAAC;QACVH,MAAM,CAAC3C,OAAO,GAAGpC,GAAG,CAAC,YAAY;UAC/B;UACAuF,iBAAiB,GAAG5B,eAAe,CAACC,IAAI,EAAEC,EAAE,CAAC;UAC7C,IAAIwC,SAAS,GAAGlD,gBAAgB,CAACd,YAAY,CAAC,eAAe,CAAC;UAC9D,IAAIiE,iBAAiB,GAAG5D,QAAQ,CAAC6D,cAAc,CAACF,SAAS,CAAC;;UAE1D;UACA,IAAIF,aAAa,GAAGlD,mBAAmB,CAACqD,iBAAiB,EAAEf,iBAAiB,CAAC9C,QAAQ,CAAC;;UAEtF;UACAoD,QAAQ,CAACM,aAAa,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM,IAAIR,SAAS,CAAC3D,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAIwE,OAAO,GAAG7B,UAAU,CAACe,YAAY,EAAE,IAAI,CAAC;QAC5C,IAAIe,SAAS,GAAGD,OAAO,CAACA,OAAO,CAACnD,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAIqD,iBAAiB,GAAG3C,WAAW,CAAC0B,GAAG,CAACgB,SAAS,CAAC;;QAElD;QACA5B,wBAAwB,CAAC4B,SAAS,EAAE,KAAK,CAAC;QAC1CZ,QAAQ,CAACa,iBAAiB,CAAC;MAC7B;IACF;;IAEA;IACA5B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACO,CAAC,CAAC;EAC9E,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
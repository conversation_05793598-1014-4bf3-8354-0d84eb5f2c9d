{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = useState(),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = useRef();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = useRef();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useRef", "useState", "MIN_SWIPE_DISTANCE", "STOP_SWIPE_DISTANCE", "REFRESH_INTERVAL", "SPEED_OFF_MULTIPLE", "Math", "pow", "useTouchMove", "ref", "onOffset", "_useState", "_useState2", "touchPosition", "setTouchPosition", "_useState3", "_useState4", "lastTimestamp", "setLastTimestamp", "_useState5", "_useState6", "lastTimeDiff", "setLastTimeDiff", "_useState7", "_useState8", "lastOffset", "setLastOffset", "motionRef", "onTouchStart", "e", "_e$touches$", "touches", "screenX", "screenY", "x", "y", "window", "clearInterval", "current", "onTouchMove", "_e$touches$2", "offsetX", "offsetY", "now", "Date", "onTouchEnd", "distanceX", "distanceY", "absX", "abs", "absY", "max", "currentX", "currentY", "setInterval", "lastWheelDirectionRef", "onWheel", "deltaX", "deltaY", "mixed", "preventDefault", "touchEventsRef", "useEffect", "onProxyTouchStart", "onProxyTouchMove", "onProxyTouchEnd", "onProxyWheel", "document", "addEventListener", "passive", "removeEventListener"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tabs@15.6.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tabs/es/hooks/useTouchMove.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = useState(),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = useRef();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = useRef();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,IAAIC,kBAAkB,GAAG,GAAG;AAC5B,IAAIC,mBAAmB,GAAG,IAAI;AAC9B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,KAAK,EAAEH,gBAAgB,CAAC;;AAE1D;AACA,eAAe,SAASI,YAAYA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAClD,IAAIC,SAAS,GAAGV,QAAQ,CAAC,CAAC;IACxBW,UAAU,GAAGd,cAAc,CAACa,SAAS,EAAE,CAAC,CAAC;IACzCE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC;IAC1Be,UAAU,GAAGlB,cAAc,CAACiB,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,UAAU,GAAGlB,QAAQ,CAAC,CAAC,CAAC;IAC1BmB,UAAU,GAAGtB,cAAc,CAACqB,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,UAAU,GAAGtB,QAAQ,CAAC,CAAC;IACzBuB,UAAU,GAAG1B,cAAc,CAACyB,UAAU,EAAE,CAAC,CAAC;IAC1CE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIG,SAAS,GAAG3B,MAAM,CAAC,CAAC;;EAExB;EACA;EACA,SAAS4B,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIC,WAAW,GAAGD,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;MAC5BC,OAAO,GAAGF,WAAW,CAACE,OAAO;MAC7BC,OAAO,GAAGH,WAAW,CAACG,OAAO;IAC/BnB,gBAAgB,CAAC;MACfoB,CAAC,EAAEF,OAAO;MACVG,CAAC,EAAEF;IACL,CAAC,CAAC;IACFG,MAAM,CAACC,aAAa,CAACV,SAAS,CAACW,OAAO,CAAC;EACzC;EACA,SAASC,WAAWA,CAACV,CAAC,EAAE;IACtB,IAAI,CAAChB,aAAa,EAAE;;IAEpB;IACA,IAAI2B,YAAY,GAAGX,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;MAC7BC,OAAO,GAAGQ,YAAY,CAACR,OAAO;MAC9BC,OAAO,GAAGO,YAAY,CAACP,OAAO;IAChCnB,gBAAgB,CAAC;MACfoB,CAAC,EAAEF,OAAO;MACVG,CAAC,EAAEF;IACL,CAAC,CAAC;IACF,IAAIQ,OAAO,GAAGT,OAAO,GAAGnB,aAAa,CAACqB,CAAC;IACvC,IAAIQ,OAAO,GAAGT,OAAO,GAAGpB,aAAa,CAACsB,CAAC;IACvCzB,QAAQ,CAAC+B,OAAO,EAAEC,OAAO,CAAC;IAC1B,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACpBzB,gBAAgB,CAACyB,GAAG,CAAC;IACrBrB,eAAe,CAACqB,GAAG,GAAG1B,aAAa,CAAC;IACpCS,aAAa,CAAC;MACZQ,CAAC,EAAEO,OAAO;MACVN,CAAC,EAAEO;IACL,CAAC,CAAC;EACJ;EACA,SAASG,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAChC,aAAa,EAAE;IACpBC,gBAAgB,CAAC,IAAI,CAAC;IACtBY,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAID,UAAU,EAAE;MACd,IAAIqB,SAAS,GAAGrB,UAAU,CAACS,CAAC,GAAGb,YAAY;MAC3C,IAAI0B,SAAS,GAAGtB,UAAU,CAACU,CAAC,GAAGd,YAAY;MAC3C,IAAI2B,IAAI,GAAG1C,IAAI,CAAC2C,GAAG,CAACH,SAAS,CAAC;MAC9B,IAAII,IAAI,GAAG5C,IAAI,CAAC2C,GAAG,CAACF,SAAS,CAAC;;MAE9B;MACA,IAAIzC,IAAI,CAAC6C,GAAG,CAACH,IAAI,EAAEE,IAAI,CAAC,GAAGhD,kBAAkB,EAAE;MAC/C,IAAIkD,QAAQ,GAAGN,SAAS;MACxB,IAAIO,QAAQ,GAAGN,SAAS;MACxBpB,SAAS,CAACW,OAAO,GAAGF,MAAM,CAACkB,WAAW,CAAC,YAAY;QACjD,IAAIhD,IAAI,CAAC2C,GAAG,CAACG,QAAQ,CAAC,GAAGjD,mBAAmB,IAAIG,IAAI,CAAC2C,GAAG,CAACI,QAAQ,CAAC,GAAGlD,mBAAmB,EAAE;UACxFiC,MAAM,CAACC,aAAa,CAACV,SAAS,CAACW,OAAO,CAAC;UACvC;QACF;QACAc,QAAQ,IAAI/C,kBAAkB;QAC9BgD,QAAQ,IAAIhD,kBAAkB;QAC9BK,QAAQ,CAAC0C,QAAQ,GAAGhD,gBAAgB,EAAEiD,QAAQ,GAAGjD,gBAAgB,CAAC;MACpE,CAAC,EAAEA,gBAAgB,CAAC;IACtB;EACF;;EAEA;EACA,IAAImD,qBAAqB,GAAGvD,MAAM,CAAC,CAAC;EACpC,SAASwD,OAAOA,CAAC3B,CAAC,EAAE;IAClB,IAAI4B,MAAM,GAAG5B,CAAC,CAAC4B,MAAM;MACnBC,MAAM,GAAG7B,CAAC,CAAC6B,MAAM;;IAEnB;IACA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIX,IAAI,GAAG1C,IAAI,CAAC2C,GAAG,CAACQ,MAAM,CAAC;IAC3B,IAAIP,IAAI,GAAG5C,IAAI,CAAC2C,GAAG,CAACS,MAAM,CAAC;IAC3B,IAAIV,IAAI,KAAKE,IAAI,EAAE;MACjBS,KAAK,GAAGJ,qBAAqB,CAACjB,OAAO,KAAK,GAAG,GAAGmB,MAAM,GAAGC,MAAM;IACjE,CAAC,MAAM,IAAIV,IAAI,GAAGE,IAAI,EAAE;MACtBS,KAAK,GAAGF,MAAM;MACdF,qBAAqB,CAACjB,OAAO,GAAG,GAAG;IACrC,CAAC,MAAM;MACLqB,KAAK,GAAGD,MAAM;MACdH,qBAAqB,CAACjB,OAAO,GAAG,GAAG;IACrC;IACA,IAAI5B,QAAQ,CAAC,CAACiD,KAAK,EAAE,CAACA,KAAK,CAAC,EAAE;MAC5B9B,CAAC,CAAC+B,cAAc,CAAC,CAAC;IACpB;EACF;;EAEA;EACA,IAAIC,cAAc,GAAG7D,MAAM,CAAC,IAAI,CAAC;EACjC6D,cAAc,CAACvB,OAAO,GAAG;IACvBV,YAAY,EAAEA,YAAY;IAC1BW,WAAW,EAAEA,WAAW;IACxBM,UAAU,EAAEA,UAAU;IACtBW,OAAO,EAAEA;EACX,CAAC;EACDzD,KAAK,CAAC+D,SAAS,CAAC,YAAY;IAC1B,SAASC,iBAAiBA,CAAClC,CAAC,EAAE;MAC5BgC,cAAc,CAACvB,OAAO,CAACV,YAAY,CAACC,CAAC,CAAC;IACxC;IACA,SAASmC,gBAAgBA,CAACnC,CAAC,EAAE;MAC3BgC,cAAc,CAACvB,OAAO,CAACC,WAAW,CAACV,CAAC,CAAC;IACvC;IACA,SAASoC,eAAeA,CAACpC,CAAC,EAAE;MAC1BgC,cAAc,CAACvB,OAAO,CAACO,UAAU,CAAChB,CAAC,CAAC;IACtC;IACA,SAASqC,YAAYA,CAACrC,CAAC,EAAE;MACvBgC,cAAc,CAACvB,OAAO,CAACkB,OAAO,CAAC3B,CAAC,CAAC;IACnC;IACAsC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,gBAAgB,EAAE;MACvDK,OAAO,EAAE;IACX,CAAC,CAAC;IACFF,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEH,eAAe,EAAE;MACrDI,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACA5D,GAAG,CAAC6B,OAAO,CAAC8B,gBAAgB,CAAC,YAAY,EAAEL,iBAAiB,EAAE;MAC5DM,OAAO,EAAE;IACX,CAAC,CAAC;IACF5D,GAAG,CAAC6B,OAAO,CAAC8B,gBAAgB,CAAC,OAAO,EAAEF,YAAY,EAAE;MAClDG,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,YAAY;MACjBF,QAAQ,CAACG,mBAAmB,CAAC,WAAW,EAAEN,gBAAgB,CAAC;MAC3DG,QAAQ,CAACG,mBAAmB,CAAC,UAAU,EAAEL,eAAe,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
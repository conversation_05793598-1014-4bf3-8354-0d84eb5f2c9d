{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { createPortal } from 'react-dom';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport warning from \"rc-util/es/warning\";\nimport { supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport OrderContext from \"./Context\";\nimport useDom from \"./useDom\";\nimport useScrollLocker from \"./useScrollLocker\";\nimport { inlineMock } from \"./mock\";\nvar getPortalContainer = function getPortalContainer(getContainer) {\n  if (getContainer === false) {\n    return false;\n  }\n  if (!canUseDom() || !getContainer) {\n    return null;\n  }\n  if (typeof getContainer === 'string') {\n    return document.querySelector(getContainer);\n  }\n  if (typeof getContainer === 'function') {\n    return getContainer();\n  }\n  return getContainer;\n};\nvar Portal = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var open = props.open,\n    autoLock = props.autoLock,\n    getContainer = props.getContainer,\n    debug = props.debug,\n    _props$autoDestroy = props.autoDestroy,\n    autoDestroy = _props$autoDestroy === void 0 ? true : _props$autoDestroy,\n    children = props.children;\n  var _React$useState = React.useState(open),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    shouldRender = _React$useState2[0],\n    setShouldRender = _React$useState2[1];\n  var mergedRender = shouldRender || open;\n\n  // ========================= Warning =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(canUseDom() || !open, \"Portal only work in client side. Please call 'useEffect' to show Portal instead default render in SSR.\");\n  }\n\n  // ====================== Should Render ======================\n  React.useEffect(function () {\n    if (autoDestroy || open) {\n      setShouldRender(open);\n    }\n  }, [open, autoDestroy]);\n\n  // ======================== Container ========================\n  var _React$useState3 = React.useState(function () {\n      return getPortalContainer(getContainer);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    innerContainer = _React$useState4[0],\n    setInnerContainer = _React$useState4[1];\n  React.useEffect(function () {\n    var customizeContainer = getPortalContainer(getContainer);\n\n    // Tell component that we check this in effect which is safe to be `null`\n    setInnerContainer(customizeContainer !== null && customizeContainer !== void 0 ? customizeContainer : null);\n  });\n  var _useDom = useDom(mergedRender && !innerContainer, debug),\n    _useDom2 = _slicedToArray(_useDom, 2),\n    defaultContainer = _useDom2[0],\n    queueCreate = _useDom2[1];\n  var mergedContainer = innerContainer !== null && innerContainer !== void 0 ? innerContainer : defaultContainer;\n\n  // ========================= Locker ==========================\n  useScrollLocker(autoLock && open && canUseDom() && (mergedContainer === defaultContainer || mergedContainer === document.body));\n\n  // =========================== Ref ===========================\n  var childRef = null;\n  if (children && supportRef(children) && ref) {\n    var _ref = children;\n    childRef = _ref.ref;\n  }\n  var mergedRef = useComposeRef(childRef, ref);\n\n  // ========================= Render ==========================\n  // Do not render when nothing need render\n  // When innerContainer is `undefined`, it may not ready since user use ref in the same render\n  if (!mergedRender || !canUseDom() || innerContainer === undefined) {\n    return null;\n  }\n\n  // Render inline\n  var renderInline = mergedContainer === false || inlineMock();\n  var reffedChildren = children;\n  if (ref) {\n    reffedChildren = /*#__PURE__*/React.cloneElement(children, {\n      ref: mergedRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(OrderContext.Provider, {\n    value: queueCreate\n  }, renderInline ? reffedChildren : /*#__PURE__*/createPortal(reffedChildren, mergedContainer));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Portal.displayName = 'Portal';\n}\nexport default Portal;", "map": {"version": 3, "names": ["_slicedToArray", "React", "createPortal", "canUseDom", "warning", "supportRef", "useComposeRef", "OrderContext", "useDom", "useScrollLocker", "inlineMock", "getPortalContainer", "getContainer", "document", "querySelector", "Portal", "forwardRef", "props", "ref", "open", "autoLock", "debug", "_props$autoDestroy", "autoDestroy", "children", "_React$useState", "useState", "_React$useState2", "shouldRender", "setShouldRender", "mergedRender", "process", "env", "NODE_ENV", "useEffect", "_React$useState3", "_React$useState4", "innerContainer", "setInnerContainer", "customizeContainer", "_useDom", "_useDom2", "defaultContainer", "queueCreate", "mergedContainer", "body", "childRef", "_ref", "mergedRef", "undefined", "renderInline", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneElement", "createElement", "Provider", "value", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+portal@1.1.2__121a1c2d50078df157871654a9c7e2b4/node_modules/@rc-component/portal/es/Portal.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { createPortal } from 'react-dom';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport warning from \"rc-util/es/warning\";\nimport { supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport OrderContext from \"./Context\";\nimport useDom from \"./useDom\";\nimport useScrollLocker from \"./useScrollLocker\";\nimport { inlineMock } from \"./mock\";\nvar getPortalContainer = function getPortalContainer(getContainer) {\n  if (getContainer === false) {\n    return false;\n  }\n  if (!canUseDom() || !getContainer) {\n    return null;\n  }\n  if (typeof getContainer === 'string') {\n    return document.querySelector(getContainer);\n  }\n  if (typeof getContainer === 'function') {\n    return getContainer();\n  }\n  return getContainer;\n};\nvar Portal = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var open = props.open,\n    autoLock = props.autoLock,\n    getContainer = props.getContainer,\n    debug = props.debug,\n    _props$autoDestroy = props.autoDestroy,\n    autoDestroy = _props$autoDestroy === void 0 ? true : _props$autoDestroy,\n    children = props.children;\n  var _React$useState = React.useState(open),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    shouldRender = _React$useState2[0],\n    setShouldRender = _React$useState2[1];\n  var mergedRender = shouldRender || open;\n\n  // ========================= Warning =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(canUseDom() || !open, \"Portal only work in client side. Please call 'useEffect' to show Portal instead default render in SSR.\");\n  }\n\n  // ====================== Should Render ======================\n  React.useEffect(function () {\n    if (autoDestroy || open) {\n      setShouldRender(open);\n    }\n  }, [open, autoDestroy]);\n\n  // ======================== Container ========================\n  var _React$useState3 = React.useState(function () {\n      return getPortalContainer(getContainer);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    innerContainer = _React$useState4[0],\n    setInnerContainer = _React$useState4[1];\n  React.useEffect(function () {\n    var customizeContainer = getPortalContainer(getContainer);\n\n    // Tell component that we check this in effect which is safe to be `null`\n    setInnerContainer(customizeContainer !== null && customizeContainer !== void 0 ? customizeContainer : null);\n  });\n  var _useDom = useDom(mergedRender && !innerContainer, debug),\n    _useDom2 = _slicedToArray(_useDom, 2),\n    defaultContainer = _useDom2[0],\n    queueCreate = _useDom2[1];\n  var mergedContainer = innerContainer !== null && innerContainer !== void 0 ? innerContainer : defaultContainer;\n\n  // ========================= Locker ==========================\n  useScrollLocker(autoLock && open && canUseDom() && (mergedContainer === defaultContainer || mergedContainer === document.body));\n\n  // =========================== Ref ===========================\n  var childRef = null;\n  if (children && supportRef(children) && ref) {\n    var _ref = children;\n    childRef = _ref.ref;\n  }\n  var mergedRef = useComposeRef(childRef, ref);\n\n  // ========================= Render ==========================\n  // Do not render when nothing need render\n  // When innerContainer is `undefined`, it may not ready since user use ref in the same render\n  if (!mergedRender || !canUseDom() || innerContainer === undefined) {\n    return null;\n  }\n\n  // Render inline\n  var renderInline = mergedContainer === false || inlineMock();\n  var reffedChildren = children;\n  if (ref) {\n    reffedChildren = /*#__PURE__*/React.cloneElement(children, {\n      ref: mergedRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(OrderContext.Provider, {\n    value: queueCreate\n  }, renderInline ? reffedChildren : /*#__PURE__*/createPortal(reffedChildren, mergedContainer));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Portal.displayName = 'Portal';\n}\nexport default Portal;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAC1D,OAAOC,YAAY,MAAM,WAAW;AACpC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,UAAU,QAAQ,QAAQ;AACnC,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,YAAY,EAAE;EACjE,IAAIA,YAAY,KAAK,KAAK,EAAE;IAC1B,OAAO,KAAK;EACd;EACA,IAAI,CAACT,SAAS,CAAC,CAAC,IAAI,CAACS,YAAY,EAAE;IACjC,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACpC,OAAOC,QAAQ,CAACC,aAAa,CAACF,YAAY,CAAC;EAC7C;EACA,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;IACtC,OAAOA,YAAY,CAAC,CAAC;EACvB;EACA,OAAOA,YAAY;AACrB,CAAC;AACD,IAAIG,MAAM,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBR,YAAY,GAAGK,KAAK,CAACL,YAAY;IACjCS,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,kBAAkB,GAAGL,KAAK,CAACM,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,kBAAkB;IACvEE,QAAQ,GAAGP,KAAK,CAACO,QAAQ;EAC3B,IAAIC,eAAe,GAAGxB,KAAK,CAACyB,QAAQ,CAACP,IAAI,CAAC;IACxCQ,gBAAgB,GAAG3B,cAAc,CAACyB,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,YAAY,GAAGF,YAAY,IAAIT,IAAI;;EAEvC;EACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC7B,OAAO,CAACD,SAAS,CAAC,CAAC,IAAI,CAACgB,IAAI,EAAE,wGAAwG,CAAC;EACzI;;EAEA;EACAlB,KAAK,CAACiC,SAAS,CAAC,YAAY;IAC1B,IAAIX,WAAW,IAAIJ,IAAI,EAAE;MACvBU,eAAe,CAACV,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACA,IAAI,EAAEI,WAAW,CAAC,CAAC;;EAEvB;EACA,IAAIY,gBAAgB,GAAGlC,KAAK,CAACyB,QAAQ,CAAC,YAAY;MAC9C,OAAOf,kBAAkB,CAACC,YAAY,CAAC;IACzC,CAAC,CAAC;IACFwB,gBAAgB,GAAGpC,cAAc,CAACmC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzCnC,KAAK,CAACiC,SAAS,CAAC,YAAY;IAC1B,IAAIK,kBAAkB,GAAG5B,kBAAkB,CAACC,YAAY,CAAC;;IAEzD;IACA0B,iBAAiB,CAACC,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,IAAI,CAAC;EAC7G,CAAC,CAAC;EACF,IAAIC,OAAO,GAAGhC,MAAM,CAACsB,YAAY,IAAI,CAACO,cAAc,EAAEhB,KAAK,CAAC;IAC1DoB,QAAQ,GAAGzC,cAAc,CAACwC,OAAO,EAAE,CAAC,CAAC;IACrCE,gBAAgB,GAAGD,QAAQ,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,QAAQ,CAAC,CAAC,CAAC;EAC3B,IAAIG,eAAe,GAAGP,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGK,gBAAgB;;EAE9G;EACAjC,eAAe,CAACW,QAAQ,IAAID,IAAI,IAAIhB,SAAS,CAAC,CAAC,KAAKyC,eAAe,KAAKF,gBAAgB,IAAIE,eAAe,KAAK/B,QAAQ,CAACgC,IAAI,CAAC,CAAC;;EAE/H;EACA,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAItB,QAAQ,IAAInB,UAAU,CAACmB,QAAQ,CAAC,IAAIN,GAAG,EAAE;IAC3C,IAAI6B,IAAI,GAAGvB,QAAQ;IACnBsB,QAAQ,GAAGC,IAAI,CAAC7B,GAAG;EACrB;EACA,IAAI8B,SAAS,GAAG1C,aAAa,CAACwC,QAAQ,EAAE5B,GAAG,CAAC;;EAE5C;EACA;EACA;EACA,IAAI,CAACY,YAAY,IAAI,CAAC3B,SAAS,CAAC,CAAC,IAAIkC,cAAc,KAAKY,SAAS,EAAE;IACjE,OAAO,IAAI;EACb;;EAEA;EACA,IAAIC,YAAY,GAAGN,eAAe,KAAK,KAAK,IAAIlC,UAAU,CAAC,CAAC;EAC5D,IAAIyC,cAAc,GAAG3B,QAAQ;EAC7B,IAAIN,GAAG,EAAE;IACPiC,cAAc,GAAG,aAAalD,KAAK,CAACmD,YAAY,CAAC5B,QAAQ,EAAE;MACzDN,GAAG,EAAE8B;IACP,CAAC,CAAC;EACJ;EACA,OAAO,aAAa/C,KAAK,CAACoD,aAAa,CAAC9C,YAAY,CAAC+C,QAAQ,EAAE;IAC7DC,KAAK,EAAEZ;EACT,CAAC,EAAEO,YAAY,GAAGC,cAAc,GAAG,aAAajD,YAAY,CAACiD,cAAc,EAAEP,eAAe,CAAC,CAAC;AAChG,CAAC,CAAC;AACF,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClB,MAAM,CAACyC,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAezC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
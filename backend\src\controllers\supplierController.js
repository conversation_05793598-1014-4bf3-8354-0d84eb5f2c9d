const { Supplier, PurchaseOrder } = require('../models');
const { Op } = require('sequelize');
const { validationResult } = require('express-validator');

// 生成供应商编码
const generateSupplierCode = async () => {
  const prefix = 'SUP';
  
  const lastSupplier = await Supplier.findOne({
    where: {
      supplier_code: {
        [Op.like]: `${prefix}%`
      }
    },
    order: [['supplier_code', 'DESC']]
  });
  
  let sequence = 1;
  if (lastSupplier) {
    const lastSequence = parseInt(lastSupplier.supplier_code.slice(3));
    sequence = lastSequence + 1;
  }
  
  return `${prefix}${sequence.toString().padStart(6, '0')}`;
};

// 获取所有供应商
exports.getSuppliers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      supplier_type,
      search
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    // 状态筛选
    if (status) {
      where.status = status;
    }
    
    // 供应商类型筛选
    if (supplier_type) {
      where.supplier_type = supplier_type;
    }
    
    // 搜索供应商名称或编码
    if (search) {
      where[Op.or] = [
        { company_name: { [Op.like]: `%${search}%` } },
        { supplier_code: { [Op.like]: `%${search}%` } }
      ];
    }
    
    const { count, rows } = await Supplier.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        suppliers: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取供应商列表失败',
      error: error.message
    });
  }
};

// 获取单个供应商详情
exports.getSupplierById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const supplier = await Supplier.findByPk(id, {
      include: [
        {
          model: PurchaseOrder,
          as: 'purchaseOrders',
          attributes: ['id', 'po_number', 'order_date', 'status', 'total_amount'],
          limit: 10,
          order: [['created_at', 'DESC']]
        }
      ]
    });
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
    }
    
    res.json({
      success: true,
      data: supplier
    });
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取供应商详情失败',
      error: error.message
    });
  }
};

// 创建供应商
exports.createSupplier = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }
    
    const {
      company_name,
      contact_person,
      email,
      phone,
      mobile,
      address,
      city,
      province,
      postal_code,
      tax_number,
      bank_name,
      bank_account,
      payment_terms,
      supplier_type,
      quality_rating,
      delivery_rating,
      notes
    } = req.body;
    
    // 生成供应商编码
    const supplierCode = await generateSupplierCode();
    
    // 创建供应商
    const supplier = await Supplier.create({
      supplier_code: supplierCode,
      company_name,
      contact_person,
      email,
      phone,
      mobile,
      address,
      city,
      province,
      postal_code,
      tax_number,
      bank_name,
      bank_account,
      payment_terms,
      supplier_type,
      quality_rating,
      delivery_rating,
      notes
    });
    
    res.status(201).json({
      success: true,
      message: '供应商创建成功',
      data: supplier
    });
  } catch (error) {
    console.error('创建供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '创建供应商失败',
      error: error.message
    });
  }
};

// 更新供应商
exports.updateSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
    }
    
    // 不允许修改供应商编码
    delete updateData.supplier_code;
    
    await supplier.update(updateData);
    
    res.json({
      success: true,
      message: '供应商更新成功',
      data: supplier
    });
  } catch (error) {
    console.error('更新供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '更新供应商失败',
      error: error.message
    });
  }
};

// 删除供应商
exports.deleteSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    
    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
    }
    
    // 检查是否有关联的采购订单
    const purchaseOrderCount = await PurchaseOrder.count({
      where: { supplier_id: id }
    });
    
    if (purchaseOrderCount > 0) {
      return res.status(400).json({
        success: false,
        message: '该供应商有关联的采购订单，不能删除'
      });
    }
    
    await supplier.destroy();
    
    res.json({
      success: true,
      message: '供应商删除成功'
    });
  } catch (error) {
    console.error('删除供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '删除供应商失败',
      error: error.message
    });
  }
};

// 更新供应商状态
exports.updateSupplierStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const validStatuses = ['active', 'inactive', 'suspended'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的供应商状态'
      });
    }
    
    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
    }
    
    await supplier.update({ status });
    
    res.json({
      success: true,
      message: '供应商状态更新成功',
      data: supplier
    });
  } catch (error) {
    console.error('更新供应商状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新供应商状态失败',
      error: error.message
    });
  }
};

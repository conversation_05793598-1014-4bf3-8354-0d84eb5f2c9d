@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules\jest-cli\bin\node_modules;D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules\jest-cli\node_modules;D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules\jest-cli\bin\node_modules;D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules\jest-cli\node_modules;D:\code\erp1\backend\node_modules\.pnpm\jest-cli@29.7.0_@types+node@24.0.4\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\jest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\jest.js" %*
)

{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from 'react';\nimport { token2CSSVar, useCSSVarRegister, useStyleRegister } from '@ant-design/cssinjs';\nimport genCalc from \"./calc\";\nimport getCompVarPrefix from \"./getCompVarPrefix\";\nimport getComponentToken from \"./getComponentToken\";\nimport getDefaultComponentToken from \"./getDefaultComponentToken\";\nimport genMaxMin from \"./maxmin\";\nimport statisticToken, { merge as mergeToken } from \"./statistic\";\nimport useUniqueMemo from \"../_util/hooks/useUniqueMemo\";\nimport useDefaultCSP from \"../hooks/useCSP\";\nfunction genStyleUtils(config) {\n  // Dependency inversion for preparing basic config.\n  var _config$useCSP = config.useCSP,\n    useCSP = _config$useCSP === void 0 ? useDefaultCSP : _config$useCSP,\n    useToken = config.useToken,\n    usePrefix = config.usePrefix,\n    getResetStyles = config.getResetStyles,\n    getCommonStyle = config.getCommonStyle,\n    getCompUnitless = config.getCompUnitless;\n  function genStyleHooks(component, styleFn, getDefaultToken, options) {\n    var componentName = Array.isArray(component) ? component[0] : component;\n    function prefixToken(key) {\n      return \"\".concat(String(componentName)).concat(key.slice(0, 1).toUpperCase()).concat(key.slice(1));\n    }\n\n    // Fill unitless\n    var originUnitless = (options === null || options === void 0 ? void 0 : options.unitless) || {};\n    var originCompUnitless = typeof getCompUnitless === 'function' ? getCompUnitless(component) : {};\n    var compUnitless = _objectSpread(_objectSpread({}, originCompUnitless), {}, _defineProperty({}, prefixToken('zIndexPopup'), true));\n    Object.keys(originUnitless).forEach(function (key) {\n      compUnitless[prefixToken(key)] = originUnitless[key];\n    });\n\n    // Options\n    var mergedOptions = _objectSpread(_objectSpread({}, options), {}, {\n      unitless: compUnitless,\n      prefixToken: prefixToken\n    });\n\n    // Hooks\n    var useStyle = genComponentStyleHook(component, styleFn, getDefaultToken, mergedOptions);\n    var useCSSVar = genCSSVarRegister(componentName, getDefaultToken, mergedOptions);\n    return function (prefixCls) {\n      var rootCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : prefixCls;\n      var _useStyle = useStyle(prefixCls, rootCls),\n        _useStyle2 = _slicedToArray(_useStyle, 2),\n        hashId = _useStyle2[1];\n      var _useCSSVar = useCSSVar(rootCls),\n        _useCSSVar2 = _slicedToArray(_useCSSVar, 2),\n        wrapCSSVar = _useCSSVar2[0],\n        cssVarCls = _useCSSVar2[1];\n      return [wrapCSSVar, hashId, cssVarCls];\n    };\n  }\n  function genCSSVarRegister(component, getDefaultToken, options) {\n    var compUnitless = options.unitless,\n      _options$injectStyle = options.injectStyle,\n      injectStyle = _options$injectStyle === void 0 ? true : _options$injectStyle,\n      prefixToken = options.prefixToken,\n      ignore = options.ignore;\n    var CSSVarRegister = function CSSVarRegister(_ref) {\n      var rootCls = _ref.rootCls,\n        _ref$cssVar = _ref.cssVar,\n        cssVar = _ref$cssVar === void 0 ? {} : _ref$cssVar;\n      var _useToken = useToken(),\n        realToken = _useToken.realToken;\n      useCSSVarRegister({\n        path: [component],\n        prefix: cssVar.prefix,\n        key: cssVar.key,\n        unitless: compUnitless,\n        ignore: ignore,\n        token: realToken,\n        scope: rootCls\n      }, function () {\n        var defaultToken = getDefaultComponentToken(component, realToken, getDefaultToken);\n        var componentToken = getComponentToken(component, realToken, defaultToken, {\n          deprecatedTokens: options === null || options === void 0 ? void 0 : options.deprecatedTokens\n        });\n        Object.keys(defaultToken).forEach(function (key) {\n          componentToken[prefixToken(key)] = componentToken[key];\n          delete componentToken[key];\n        });\n        return componentToken;\n      });\n      return null;\n    };\n    var useCSSVar = function useCSSVar(rootCls) {\n      var _useToken2 = useToken(),\n        cssVar = _useToken2.cssVar;\n      return [function (node) {\n        return injectStyle && cssVar ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(CSSVarRegister, {\n          rootCls: rootCls,\n          cssVar: cssVar,\n          component: component\n        }), node) : node;\n      }, cssVar === null || cssVar === void 0 ? void 0 : cssVar.key];\n    };\n    return useCSSVar;\n  }\n  function genComponentStyleHook(componentName, styleFn, getDefaultToken) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var cells = Array.isArray(componentName) ? componentName : [componentName, componentName];\n    var _cells = _slicedToArray(cells, 1),\n      component = _cells[0];\n    var concatComponent = cells.join('-');\n    var mergedLayer = config.layer || {\n      name: 'antd'\n    };\n\n    // Return new style hook\n    return function (prefixCls) {\n      var rootCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : prefixCls;\n      var _useToken3 = useToken(),\n        theme = _useToken3.theme,\n        realToken = _useToken3.realToken,\n        hashId = _useToken3.hashId,\n        token = _useToken3.token,\n        cssVar = _useToken3.cssVar;\n      var _usePrefix = usePrefix(),\n        rootPrefixCls = _usePrefix.rootPrefixCls,\n        iconPrefixCls = _usePrefix.iconPrefixCls;\n      var csp = useCSP();\n      var type = cssVar ? 'css' : 'js';\n\n      // Use unique memo to share the result across all instances\n      var calc = useUniqueMemo(function () {\n        var unitlessCssVar = new Set();\n        if (cssVar) {\n          Object.keys(options.unitless || {}).forEach(function (key) {\n            // Some component proxy the AliasToken (e.g. Image) and some not (e.g. Modal)\n            // We should both pass in `unitlessCssVar` to make sure the CSSVar can be unitless.\n            unitlessCssVar.add(token2CSSVar(key, cssVar.prefix));\n            unitlessCssVar.add(token2CSSVar(key, getCompVarPrefix(component, cssVar.prefix)));\n          });\n        }\n        return genCalc(type, unitlessCssVar);\n      }, [type, component, cssVar === null || cssVar === void 0 ? void 0 : cssVar.prefix]);\n      var _genMaxMin = genMaxMin(type),\n        max = _genMaxMin.max,\n        min = _genMaxMin.min;\n\n      // Shared config\n      var sharedConfig = {\n        theme: theme,\n        token: token,\n        hashId: hashId,\n        nonce: function nonce() {\n          return csp.nonce;\n        },\n        clientOnly: options.clientOnly,\n        layer: mergedLayer,\n        // antd is always at top of styles\n        order: options.order || -999\n      };\n\n      // This if statement is safe, as it will only be used if the generator has the function. It's not dynamic.\n      if (typeof getResetStyles === 'function') {\n        // Generate style for all need reset tags.\n        useStyleRegister(_objectSpread(_objectSpread({}, sharedConfig), {}, {\n          clientOnly: false,\n          path: ['Shared', rootPrefixCls]\n        }), function () {\n          return getResetStyles(token, {\n            prefix: {\n              rootPrefixCls: rootPrefixCls,\n              iconPrefixCls: iconPrefixCls\n            },\n            csp: csp\n          });\n        });\n      }\n      var wrapSSR = useStyleRegister(_objectSpread(_objectSpread({}, sharedConfig), {}, {\n        path: [concatComponent, prefixCls, iconPrefixCls]\n      }), function () {\n        if (options.injectStyle === false) {\n          return [];\n        }\n        var _statisticToken = statisticToken(token),\n          proxyToken = _statisticToken.token,\n          flush = _statisticToken.flush;\n        var defaultComponentToken = getDefaultComponentToken(component, realToken, getDefaultToken);\n        var componentCls = \".\".concat(prefixCls);\n        var componentToken = getComponentToken(component, realToken, defaultComponentToken, {\n          deprecatedTokens: options.deprecatedTokens\n        });\n        if (cssVar && defaultComponentToken && _typeof(defaultComponentToken) === 'object') {\n          Object.keys(defaultComponentToken).forEach(function (key) {\n            defaultComponentToken[key] = \"var(\".concat(token2CSSVar(key, getCompVarPrefix(component, cssVar.prefix)), \")\");\n          });\n        }\n        var mergedToken = mergeToken(proxyToken, {\n          componentCls: componentCls,\n          prefixCls: prefixCls,\n          iconCls: \".\".concat(iconPrefixCls),\n          antCls: \".\".concat(rootPrefixCls),\n          calc: calc,\n          // @ts-ignore\n          max: max,\n          // @ts-ignore\n          min: min\n        }, cssVar ? defaultComponentToken : componentToken);\n        var styleInterpolation = styleFn(mergedToken, {\n          hashId: hashId,\n          prefixCls: prefixCls,\n          rootPrefixCls: rootPrefixCls,\n          iconPrefixCls: iconPrefixCls\n        });\n        flush(component, componentToken);\n        var commonStyle = typeof getCommonStyle === 'function' ? getCommonStyle(mergedToken, prefixCls, rootCls, options.resetFont) : null;\n        return [options.resetStyle === false ? null : commonStyle, styleInterpolation];\n      });\n      return [wrapSSR, hashId];\n    };\n  }\n  function genSubStyleComponent(componentName, styleFn, getDefaultToken) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var useStyle = genComponentStyleHook(componentName, styleFn, getDefaultToken, _objectSpread({\n      resetStyle: false,\n      // Sub Style should default after root one\n      order: -998\n    }, options));\n    var StyledComponent = function StyledComponent(_ref2) {\n      var prefixCls = _ref2.prefixCls,\n        _ref2$rootCls = _ref2.rootCls,\n        rootCls = _ref2$rootCls === void 0 ? prefixCls : _ref2$rootCls;\n      useStyle(prefixCls, rootCls);\n      return null;\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      StyledComponent.displayName = \"SubStyle_\".concat(String(Array.isArray(componentName) ? componentName.join('.') : componentName));\n    }\n    return StyledComponent;\n  }\n  return {\n    genStyleHooks: genStyleHooks,\n    genSubStyleComponent: genSubStyleComponent,\n    genComponentStyleHook: genComponentStyleHook\n  };\n}\nexport default genStyleUtils;", "map": {"version": 3, "names": ["_typeof", "_slicedToArray", "_defineProperty", "_objectSpread", "React", "token2CSSVar", "useCSSVarRegister", "useStyleRegister", "genCalc", "getCompVarPrefix", "getComponentToken", "getDefaultComponentToken", "genMaxMin", "statisticToken", "merge", "mergeToken", "useUniqueMemo", "useDefaultCSP", "genStyleUtils", "config", "_config$useCSP", "useCSP", "useToken", "usePrefix", "getResetStyles", "getCommonStyle", "getCompUnitless", "genStyleHooks", "component", "styleFn", "getDefaultToken", "options", "componentName", "Array", "isArray", "prefixToken", "key", "concat", "String", "slice", "toUpperCase", "originUnitless", "unitless", "originCompUnitless", "compUnitless", "Object", "keys", "for<PERSON>ach", "mergedOptions", "useStyle", "genComponentStyleHook", "useCSSVar", "genCSSVarRegister", "prefixCls", "rootCls", "arguments", "length", "undefined", "_useStyle", "_useStyle2", "hashId", "_useCSSVar", "_useCSSVar2", "wrapCSSVar", "cssVarCls", "_options$injectStyle", "injectStyle", "ignore", "CSSVarRegister", "_ref", "_ref$cssVar", "cssVar", "_useToken", "realToken", "path", "prefix", "token", "scope", "defaultToken", "componentToken", "deprecatedTokens", "_useToken2", "node", "createElement", "Fragment", "cells", "_cells", "concatComponent", "join", "mergedLayer", "layer", "name", "_useToken3", "theme", "_usePrefix", "rootPrefixCls", "iconPrefixCls", "csp", "type", "calc", "unitlessCssVar", "Set", "add", "_genMaxMin", "max", "min", "sharedConfig", "nonce", "clientOnly", "order", "wrapSSR", "_statisticToken", "proxyToken", "flush", "defaultComponentToken", "componentCls", "mergedToken", "iconCls", "antCls", "styleInterpolation", "commonStyle", "resetFont", "resetStyle", "genSubStyleComponent", "StyledComponent", "_ref2", "_ref2$rootCls", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs-utils@1_3af55f7738cc1fa06ee42984195bbb7b/node_modules/@ant-design/cssinjs-utils/es/util/genStyleUtils.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from 'react';\nimport { token2CSSVar, useCSSVarRegister, useStyleRegister } from '@ant-design/cssinjs';\nimport genCalc from \"./calc\";\nimport getCompVarPrefix from \"./getCompVarPrefix\";\nimport getComponentToken from \"./getComponentToken\";\nimport getDefaultComponentToken from \"./getDefaultComponentToken\";\nimport genMaxMin from \"./maxmin\";\nimport statisticToken, { merge as mergeToken } from \"./statistic\";\nimport useUniqueMemo from \"../_util/hooks/useUniqueMemo\";\nimport useDefaultCSP from \"../hooks/useCSP\";\nfunction genStyleUtils(config) {\n  // Dependency inversion for preparing basic config.\n  var _config$useCSP = config.useCSP,\n    useCSP = _config$useCSP === void 0 ? useDefaultCSP : _config$useCSP,\n    useToken = config.useToken,\n    usePrefix = config.usePrefix,\n    getResetStyles = config.getResetStyles,\n    getCommonStyle = config.getCommonStyle,\n    getCompUnitless = config.getCompUnitless;\n  function genStyleHooks(component, styleFn, getDefaultToken, options) {\n    var componentName = Array.isArray(component) ? component[0] : component;\n    function prefixToken(key) {\n      return \"\".concat(String(componentName)).concat(key.slice(0, 1).toUpperCase()).concat(key.slice(1));\n    }\n\n    // Fill unitless\n    var originUnitless = (options === null || options === void 0 ? void 0 : options.unitless) || {};\n    var originCompUnitless = typeof getCompUnitless === 'function' ? getCompUnitless(component) : {};\n    var compUnitless = _objectSpread(_objectSpread({}, originCompUnitless), {}, _defineProperty({}, prefixToken('zIndexPopup'), true));\n    Object.keys(originUnitless).forEach(function (key) {\n      compUnitless[prefixToken(key)] = originUnitless[key];\n    });\n\n    // Options\n    var mergedOptions = _objectSpread(_objectSpread({}, options), {}, {\n      unitless: compUnitless,\n      prefixToken: prefixToken\n    });\n\n    // Hooks\n    var useStyle = genComponentStyleHook(component, styleFn, getDefaultToken, mergedOptions);\n    var useCSSVar = genCSSVarRegister(componentName, getDefaultToken, mergedOptions);\n    return function (prefixCls) {\n      var rootCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : prefixCls;\n      var _useStyle = useStyle(prefixCls, rootCls),\n        _useStyle2 = _slicedToArray(_useStyle, 2),\n        hashId = _useStyle2[1];\n      var _useCSSVar = useCSSVar(rootCls),\n        _useCSSVar2 = _slicedToArray(_useCSSVar, 2),\n        wrapCSSVar = _useCSSVar2[0],\n        cssVarCls = _useCSSVar2[1];\n      return [wrapCSSVar, hashId, cssVarCls];\n    };\n  }\n  function genCSSVarRegister(component, getDefaultToken, options) {\n    var compUnitless = options.unitless,\n      _options$injectStyle = options.injectStyle,\n      injectStyle = _options$injectStyle === void 0 ? true : _options$injectStyle,\n      prefixToken = options.prefixToken,\n      ignore = options.ignore;\n    var CSSVarRegister = function CSSVarRegister(_ref) {\n      var rootCls = _ref.rootCls,\n        _ref$cssVar = _ref.cssVar,\n        cssVar = _ref$cssVar === void 0 ? {} : _ref$cssVar;\n      var _useToken = useToken(),\n        realToken = _useToken.realToken;\n      useCSSVarRegister({\n        path: [component],\n        prefix: cssVar.prefix,\n        key: cssVar.key,\n        unitless: compUnitless,\n        ignore: ignore,\n        token: realToken,\n        scope: rootCls\n      }, function () {\n        var defaultToken = getDefaultComponentToken(component, realToken, getDefaultToken);\n        var componentToken = getComponentToken(component, realToken, defaultToken, {\n          deprecatedTokens: options === null || options === void 0 ? void 0 : options.deprecatedTokens\n        });\n        Object.keys(defaultToken).forEach(function (key) {\n          componentToken[prefixToken(key)] = componentToken[key];\n          delete componentToken[key];\n        });\n        return componentToken;\n      });\n      return null;\n    };\n    var useCSSVar = function useCSSVar(rootCls) {\n      var _useToken2 = useToken(),\n        cssVar = _useToken2.cssVar;\n      return [function (node) {\n        return injectStyle && cssVar ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(CSSVarRegister, {\n          rootCls: rootCls,\n          cssVar: cssVar,\n          component: component\n        }), node) : node;\n      }, cssVar === null || cssVar === void 0 ? void 0 : cssVar.key];\n    };\n    return useCSSVar;\n  }\n  function genComponentStyleHook(componentName, styleFn, getDefaultToken) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var cells = Array.isArray(componentName) ? componentName : [componentName, componentName];\n    var _cells = _slicedToArray(cells, 1),\n      component = _cells[0];\n    var concatComponent = cells.join('-');\n    var mergedLayer = config.layer || {\n      name: 'antd'\n    };\n\n    // Return new style hook\n    return function (prefixCls) {\n      var rootCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : prefixCls;\n      var _useToken3 = useToken(),\n        theme = _useToken3.theme,\n        realToken = _useToken3.realToken,\n        hashId = _useToken3.hashId,\n        token = _useToken3.token,\n        cssVar = _useToken3.cssVar;\n      var _usePrefix = usePrefix(),\n        rootPrefixCls = _usePrefix.rootPrefixCls,\n        iconPrefixCls = _usePrefix.iconPrefixCls;\n      var csp = useCSP();\n      var type = cssVar ? 'css' : 'js';\n\n      // Use unique memo to share the result across all instances\n      var calc = useUniqueMemo(function () {\n        var unitlessCssVar = new Set();\n        if (cssVar) {\n          Object.keys(options.unitless || {}).forEach(function (key) {\n            // Some component proxy the AliasToken (e.g. Image) and some not (e.g. Modal)\n            // We should both pass in `unitlessCssVar` to make sure the CSSVar can be unitless.\n            unitlessCssVar.add(token2CSSVar(key, cssVar.prefix));\n            unitlessCssVar.add(token2CSSVar(key, getCompVarPrefix(component, cssVar.prefix)));\n          });\n        }\n        return genCalc(type, unitlessCssVar);\n      }, [type, component, cssVar === null || cssVar === void 0 ? void 0 : cssVar.prefix]);\n      var _genMaxMin = genMaxMin(type),\n        max = _genMaxMin.max,\n        min = _genMaxMin.min;\n\n      // Shared config\n      var sharedConfig = {\n        theme: theme,\n        token: token,\n        hashId: hashId,\n        nonce: function nonce() {\n          return csp.nonce;\n        },\n        clientOnly: options.clientOnly,\n        layer: mergedLayer,\n        // antd is always at top of styles\n        order: options.order || -999\n      };\n\n      // This if statement is safe, as it will only be used if the generator has the function. It's not dynamic.\n      if (typeof getResetStyles === 'function') {\n        // Generate style for all need reset tags.\n        useStyleRegister(_objectSpread(_objectSpread({}, sharedConfig), {}, {\n          clientOnly: false,\n          path: ['Shared', rootPrefixCls]\n        }), function () {\n          return getResetStyles(token, {\n            prefix: {\n              rootPrefixCls: rootPrefixCls,\n              iconPrefixCls: iconPrefixCls\n            },\n            csp: csp\n          });\n        });\n      }\n      var wrapSSR = useStyleRegister(_objectSpread(_objectSpread({}, sharedConfig), {}, {\n        path: [concatComponent, prefixCls, iconPrefixCls]\n      }), function () {\n        if (options.injectStyle === false) {\n          return [];\n        }\n        var _statisticToken = statisticToken(token),\n          proxyToken = _statisticToken.token,\n          flush = _statisticToken.flush;\n        var defaultComponentToken = getDefaultComponentToken(component, realToken, getDefaultToken);\n        var componentCls = \".\".concat(prefixCls);\n        var componentToken = getComponentToken(component, realToken, defaultComponentToken, {\n          deprecatedTokens: options.deprecatedTokens\n        });\n        if (cssVar && defaultComponentToken && _typeof(defaultComponentToken) === 'object') {\n          Object.keys(defaultComponentToken).forEach(function (key) {\n            defaultComponentToken[key] = \"var(\".concat(token2CSSVar(key, getCompVarPrefix(component, cssVar.prefix)), \")\");\n          });\n        }\n        var mergedToken = mergeToken(proxyToken, {\n          componentCls: componentCls,\n          prefixCls: prefixCls,\n          iconCls: \".\".concat(iconPrefixCls),\n          antCls: \".\".concat(rootPrefixCls),\n          calc: calc,\n          // @ts-ignore\n          max: max,\n          // @ts-ignore\n          min: min\n        }, cssVar ? defaultComponentToken : componentToken);\n        var styleInterpolation = styleFn(mergedToken, {\n          hashId: hashId,\n          prefixCls: prefixCls,\n          rootPrefixCls: rootPrefixCls,\n          iconPrefixCls: iconPrefixCls\n        });\n        flush(component, componentToken);\n        var commonStyle = typeof getCommonStyle === 'function' ? getCommonStyle(mergedToken, prefixCls, rootCls, options.resetFont) : null;\n        return [options.resetStyle === false ? null : commonStyle, styleInterpolation];\n      });\n      return [wrapSSR, hashId];\n    };\n  }\n  function genSubStyleComponent(componentName, styleFn, getDefaultToken) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var useStyle = genComponentStyleHook(componentName, styleFn, getDefaultToken, _objectSpread({\n      resetStyle: false,\n      // Sub Style should default after root one\n      order: -998\n    }, options));\n    var StyledComponent = function StyledComponent(_ref2) {\n      var prefixCls = _ref2.prefixCls,\n        _ref2$rootCls = _ref2.rootCls,\n        rootCls = _ref2$rootCls === void 0 ? prefixCls : _ref2$rootCls;\n      useStyle(prefixCls, rootCls);\n      return null;\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      StyledComponent.displayName = \"SubStyle_\".concat(String(Array.isArray(componentName) ? componentName.join('.') : componentName));\n    }\n    return StyledComponent;\n  }\n  return {\n    genStyleHooks: genStyleHooks,\n    genSubStyleComponent: genSubStyleComponent,\n    genComponentStyleHook: genComponentStyleHook\n  };\n}\nexport default genStyleUtils;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,qBAAqB;AACvF,OAAOC,OAAO,MAAM,QAAQ;AAC5B,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,SAAS,MAAM,UAAU;AAChC,OAAOC,cAAc,IAAIC,KAAK,IAAIC,UAAU,QAAQ,aAAa;AACjE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC7B;EACA,IAAIC,cAAc,GAAGD,MAAM,CAACE,MAAM;IAChCA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGH,aAAa,GAAGG,cAAc;IACnEE,QAAQ,GAAGH,MAAM,CAACG,QAAQ;IAC1BC,SAAS,GAAGJ,MAAM,CAACI,SAAS;IAC5BC,cAAc,GAAGL,MAAM,CAACK,cAAc;IACtCC,cAAc,GAAGN,MAAM,CAACM,cAAc;IACtCC,eAAe,GAAGP,MAAM,CAACO,eAAe;EAC1C,SAASC,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAEC,eAAe,EAAEC,OAAO,EAAE;IACnE,IAAIC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;IACvE,SAASO,WAAWA,CAACC,GAAG,EAAE;MACxB,OAAO,EAAE,CAACC,MAAM,CAACC,MAAM,CAACN,aAAa,CAAC,CAAC,CAACK,MAAM,CAACD,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACH,MAAM,CAACD,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpG;;IAEA;IACA,IAAIE,cAAc,GAAG,CAACV,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,QAAQ,KAAK,CAAC,CAAC;IAC/F,IAAIC,kBAAkB,GAAG,OAAOjB,eAAe,KAAK,UAAU,GAAGA,eAAe,CAACE,SAAS,CAAC,GAAG,CAAC,CAAC;IAChG,IAAIgB,YAAY,GAAGzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwC,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAEzC,eAAe,CAAC,CAAC,CAAC,EAAEiC,WAAW,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;IAClIU,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,OAAO,CAAC,UAAUX,GAAG,EAAE;MACjDQ,YAAY,CAACT,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGK,cAAc,CAACL,GAAG,CAAC;IACtD,CAAC,CAAC;;IAEF;IACA,IAAIY,aAAa,GAAG7C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MAChEW,QAAQ,EAAEE,YAAY;MACtBT,WAAW,EAAEA;IACf,CAAC,CAAC;;IAEF;IACA,IAAIc,QAAQ,GAAGC,qBAAqB,CAACtB,SAAS,EAAEC,OAAO,EAAEC,eAAe,EAAEkB,aAAa,CAAC;IACxF,IAAIG,SAAS,GAAGC,iBAAiB,CAACpB,aAAa,EAAEF,eAAe,EAAEkB,aAAa,CAAC;IAChF,OAAO,UAAUK,SAAS,EAAE;MAC1B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGF,SAAS;MAC3F,IAAIK,SAAS,GAAGT,QAAQ,CAACI,SAAS,EAAEC,OAAO,CAAC;QAC1CK,UAAU,GAAG1D,cAAc,CAACyD,SAAS,EAAE,CAAC,CAAC;QACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;MACxB,IAAIE,UAAU,GAAGV,SAAS,CAACG,OAAO,CAAC;QACjCQ,WAAW,GAAG7D,cAAc,CAAC4D,UAAU,EAAE,CAAC,CAAC;QAC3CE,UAAU,GAAGD,WAAW,CAAC,CAAC,CAAC;QAC3BE,SAAS,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC5B,OAAO,CAACC,UAAU,EAAEH,MAAM,EAAEI,SAAS,CAAC;IACxC,CAAC;EACH;EACA,SAASZ,iBAAiBA,CAACxB,SAAS,EAAEE,eAAe,EAAEC,OAAO,EAAE;IAC9D,IAAIa,YAAY,GAAGb,OAAO,CAACW,QAAQ;MACjCuB,oBAAoB,GAAGlC,OAAO,CAACmC,WAAW;MAC1CA,WAAW,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;MAC3E9B,WAAW,GAAGJ,OAAO,CAACI,WAAW;MACjCgC,MAAM,GAAGpC,OAAO,CAACoC,MAAM;IACzB,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;MACjD,IAAIf,OAAO,GAAGe,IAAI,CAACf,OAAO;QACxBgB,WAAW,GAAGD,IAAI,CAACE,MAAM;QACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,WAAW;MACpD,IAAIE,SAAS,GAAGlD,QAAQ,CAAC,CAAC;QACxBmD,SAAS,GAAGD,SAAS,CAACC,SAAS;MACjCnE,iBAAiB,CAAC;QAChBoE,IAAI,EAAE,CAAC9C,SAAS,CAAC;QACjB+C,MAAM,EAAEJ,MAAM,CAACI,MAAM;QACrBvC,GAAG,EAAEmC,MAAM,CAACnC,GAAG;QACfM,QAAQ,EAAEE,YAAY;QACtBuB,MAAM,EAAEA,MAAM;QACdS,KAAK,EAAEH,SAAS;QAChBI,KAAK,EAAEvB;MACT,CAAC,EAAE,YAAY;QACb,IAAIwB,YAAY,GAAGnE,wBAAwB,CAACiB,SAAS,EAAE6C,SAAS,EAAE3C,eAAe,CAAC;QAClF,IAAIiD,cAAc,GAAGrE,iBAAiB,CAACkB,SAAS,EAAE6C,SAAS,EAAEK,YAAY,EAAE;UACzEE,gBAAgB,EAAEjD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiD;QAC9E,CAAC,CAAC;QACFnC,MAAM,CAACC,IAAI,CAACgC,YAAY,CAAC,CAAC/B,OAAO,CAAC,UAAUX,GAAG,EAAE;UAC/C2C,cAAc,CAAC5C,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG2C,cAAc,CAAC3C,GAAG,CAAC;UACtD,OAAO2C,cAAc,CAAC3C,GAAG,CAAC;QAC5B,CAAC,CAAC;QACF,OAAO2C,cAAc;MACvB,CAAC,CAAC;MACF,OAAO,IAAI;IACb,CAAC;IACD,IAAI5B,SAAS,GAAG,SAASA,SAASA,CAACG,OAAO,EAAE;MAC1C,IAAI2B,UAAU,GAAG3D,QAAQ,CAAC,CAAC;QACzBiD,MAAM,GAAGU,UAAU,CAACV,MAAM;MAC5B,OAAO,CAAC,UAAUW,IAAI,EAAE;QACtB,OAAOhB,WAAW,IAAIK,MAAM,GAAG,aAAanE,KAAK,CAAC+E,aAAa,CAAC/E,KAAK,CAACgF,QAAQ,EAAE,IAAI,EAAE,aAAahF,KAAK,CAAC+E,aAAa,CAACf,cAAc,EAAE;UACrId,OAAO,EAAEA,OAAO;UAChBiB,MAAM,EAAEA,MAAM;UACd3C,SAAS,EAAEA;QACb,CAAC,CAAC,EAAEsD,IAAI,CAAC,GAAGA,IAAI;MAClB,CAAC,EAAEX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnC,GAAG,CAAC;IAChE,CAAC;IACD,OAAOe,SAAS;EAClB;EACA,SAASD,qBAAqBA,CAAClB,aAAa,EAAEH,OAAO,EAAEC,eAAe,EAAE;IACtE,IAAIC,OAAO,GAAGwB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI8B,KAAK,GAAGpD,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,EAAEA,aAAa,CAAC;IACzF,IAAIsD,MAAM,GAAGrF,cAAc,CAACoF,KAAK,EAAE,CAAC,CAAC;MACnCzD,SAAS,GAAG0D,MAAM,CAAC,CAAC,CAAC;IACvB,IAAIC,eAAe,GAAGF,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;IACrC,IAAIC,WAAW,GAAGtE,MAAM,CAACuE,KAAK,IAAI;MAChCC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,OAAO,UAAUtC,SAAS,EAAE;MAC1B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGF,SAAS;MAC3F,IAAIuC,UAAU,GAAGtE,QAAQ,CAAC,CAAC;QACzBuE,KAAK,GAAGD,UAAU,CAACC,KAAK;QACxBpB,SAAS,GAAGmB,UAAU,CAACnB,SAAS;QAChCb,MAAM,GAAGgC,UAAU,CAAChC,MAAM;QAC1BgB,KAAK,GAAGgB,UAAU,CAAChB,KAAK;QACxBL,MAAM,GAAGqB,UAAU,CAACrB,MAAM;MAC5B,IAAIuB,UAAU,GAAGvE,SAAS,CAAC,CAAC;QAC1BwE,aAAa,GAAGD,UAAU,CAACC,aAAa;QACxCC,aAAa,GAAGF,UAAU,CAACE,aAAa;MAC1C,IAAIC,GAAG,GAAG5E,MAAM,CAAC,CAAC;MAClB,IAAI6E,IAAI,GAAG3B,MAAM,GAAG,KAAK,GAAG,IAAI;;MAEhC;MACA,IAAI4B,IAAI,GAAGnF,aAAa,CAAC,YAAY;QACnC,IAAIoF,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC9B,IAAI9B,MAAM,EAAE;UACV1B,MAAM,CAACC,IAAI,CAACf,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACK,OAAO,CAAC,UAAUX,GAAG,EAAE;YACzD;YACA;YACAgE,cAAc,CAACE,GAAG,CAACjG,YAAY,CAAC+B,GAAG,EAAEmC,MAAM,CAACI,MAAM,CAAC,CAAC;YACpDyB,cAAc,CAACE,GAAG,CAACjG,YAAY,CAAC+B,GAAG,EAAE3B,gBAAgB,CAACmB,SAAS,EAAE2C,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC;QACJ;QACA,OAAOnE,OAAO,CAAC0F,IAAI,EAAEE,cAAc,CAAC;MACtC,CAAC,EAAE,CAACF,IAAI,EAAEtE,SAAS,EAAE2C,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,MAAM,CAAC,CAAC;MACpF,IAAI4B,UAAU,GAAG3F,SAAS,CAACsF,IAAI,CAAC;QAC9BM,GAAG,GAAGD,UAAU,CAACC,GAAG;QACpBC,GAAG,GAAGF,UAAU,CAACE,GAAG;;MAEtB;MACA,IAAIC,YAAY,GAAG;QACjBb,KAAK,EAAEA,KAAK;QACZjB,KAAK,EAAEA,KAAK;QACZhB,MAAM,EAAEA,MAAM;QACd+C,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;UACtB,OAAOV,GAAG,CAACU,KAAK;QAClB,CAAC;QACDC,UAAU,EAAE7E,OAAO,CAAC6E,UAAU;QAC9BlB,KAAK,EAAED,WAAW;QAClB;QACAoB,KAAK,EAAE9E,OAAO,CAAC8E,KAAK,IAAI,CAAC;MAC3B,CAAC;;MAED;MACA,IAAI,OAAOrF,cAAc,KAAK,UAAU,EAAE;QACxC;QACAjB,gBAAgB,CAACJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuG,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UAClEE,UAAU,EAAE,KAAK;UACjBlC,IAAI,EAAE,CAAC,QAAQ,EAAEqB,aAAa;QAChC,CAAC,CAAC,EAAE,YAAY;UACd,OAAOvE,cAAc,CAACoD,KAAK,EAAE;YAC3BD,MAAM,EAAE;cACNoB,aAAa,EAAEA,aAAa;cAC5BC,aAAa,EAAEA;YACjB,CAAC;YACDC,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,IAAIa,OAAO,GAAGvG,gBAAgB,CAACJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuG,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QAChFhC,IAAI,EAAE,CAACa,eAAe,EAAElC,SAAS,EAAE2C,aAAa;MAClD,CAAC,CAAC,EAAE,YAAY;QACd,IAAIjE,OAAO,CAACmC,WAAW,KAAK,KAAK,EAAE;UACjC,OAAO,EAAE;QACX;QACA,IAAI6C,eAAe,GAAGlG,cAAc,CAAC+D,KAAK,CAAC;UACzCoC,UAAU,GAAGD,eAAe,CAACnC,KAAK;UAClCqC,KAAK,GAAGF,eAAe,CAACE,KAAK;QAC/B,IAAIC,qBAAqB,GAAGvG,wBAAwB,CAACiB,SAAS,EAAE6C,SAAS,EAAE3C,eAAe,CAAC;QAC3F,IAAIqF,YAAY,GAAG,GAAG,CAAC9E,MAAM,CAACgB,SAAS,CAAC;QACxC,IAAI0B,cAAc,GAAGrE,iBAAiB,CAACkB,SAAS,EAAE6C,SAAS,EAAEyC,qBAAqB,EAAE;UAClFlC,gBAAgB,EAAEjD,OAAO,CAACiD;QAC5B,CAAC,CAAC;QACF,IAAIT,MAAM,IAAI2C,qBAAqB,IAAIlH,OAAO,CAACkH,qBAAqB,CAAC,KAAK,QAAQ,EAAE;UAClFrE,MAAM,CAACC,IAAI,CAACoE,qBAAqB,CAAC,CAACnE,OAAO,CAAC,UAAUX,GAAG,EAAE;YACxD8E,qBAAqB,CAAC9E,GAAG,CAAC,GAAG,MAAM,CAACC,MAAM,CAAChC,YAAY,CAAC+B,GAAG,EAAE3B,gBAAgB,CAACmB,SAAS,EAAE2C,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC;UAChH,CAAC,CAAC;QACJ;QACA,IAAIyC,WAAW,GAAGrG,UAAU,CAACiG,UAAU,EAAE;UACvCG,YAAY,EAAEA,YAAY;UAC1B9D,SAAS,EAAEA,SAAS;UACpBgE,OAAO,EAAE,GAAG,CAAChF,MAAM,CAAC2D,aAAa,CAAC;UAClCsB,MAAM,EAAE,GAAG,CAACjF,MAAM,CAAC0D,aAAa,CAAC;UACjCI,IAAI,EAAEA,IAAI;UACV;UACAK,GAAG,EAAEA,GAAG;UACR;UACAC,GAAG,EAAEA;QACP,CAAC,EAAElC,MAAM,GAAG2C,qBAAqB,GAAGnC,cAAc,CAAC;QACnD,IAAIwC,kBAAkB,GAAG1F,OAAO,CAACuF,WAAW,EAAE;UAC5CxD,MAAM,EAAEA,MAAM;UACdP,SAAS,EAAEA,SAAS;UACpB0C,aAAa,EAAEA,aAAa;UAC5BC,aAAa,EAAEA;QACjB,CAAC,CAAC;QACFiB,KAAK,CAACrF,SAAS,EAAEmD,cAAc,CAAC;QAChC,IAAIyC,WAAW,GAAG,OAAO/F,cAAc,KAAK,UAAU,GAAGA,cAAc,CAAC2F,WAAW,EAAE/D,SAAS,EAAEC,OAAO,EAAEvB,OAAO,CAAC0F,SAAS,CAAC,GAAG,IAAI;QAClI,OAAO,CAAC1F,OAAO,CAAC2F,UAAU,KAAK,KAAK,GAAG,IAAI,GAAGF,WAAW,EAAED,kBAAkB,CAAC;MAChF,CAAC,CAAC;MACF,OAAO,CAACT,OAAO,EAAElD,MAAM,CAAC;IAC1B,CAAC;EACH;EACA,SAAS+D,oBAAoBA,CAAC3F,aAAa,EAAEH,OAAO,EAAEC,eAAe,EAAE;IACrE,IAAIC,OAAO,GAAGwB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIN,QAAQ,GAAGC,qBAAqB,CAAClB,aAAa,EAAEH,OAAO,EAAEC,eAAe,EAAE3B,aAAa,CAAC;MAC1FuH,UAAU,EAAE,KAAK;MACjB;MACAb,KAAK,EAAE,CAAC;IACV,CAAC,EAAE9E,OAAO,CAAC,CAAC;IACZ,IAAI6F,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;MACpD,IAAIxE,SAAS,GAAGwE,KAAK,CAACxE,SAAS;QAC7ByE,aAAa,GAAGD,KAAK,CAACvE,OAAO;QAC7BA,OAAO,GAAGwE,aAAa,KAAK,KAAK,CAAC,GAAGzE,SAAS,GAAGyE,aAAa;MAChE7E,QAAQ,CAACI,SAAS,EAAEC,OAAO,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC;IACD,IAAIyE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCL,eAAe,CAACM,WAAW,GAAG,WAAW,CAAC7F,MAAM,CAACC,MAAM,CAACL,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,GAAGA,aAAa,CAACwD,IAAI,CAAC,GAAG,CAAC,GAAGxD,aAAa,CAAC,CAAC;IAClI;IACA,OAAO4F,eAAe;EACxB;EACA,OAAO;IACLjG,aAAa,EAAEA,aAAa;IAC5BgG,oBAAoB,EAAEA,oBAAoB;IAC1CzE,qBAAqB,EAAEA;EACzB,CAAC;AACH;AACA,eAAehC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
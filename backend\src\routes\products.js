const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const productController = require('../controllers/productController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createProductValidation = [
  body('name')
    .isLength({ min: 2, max: 200 })
    .withMessage('产品名称长度必须在2-200个字符之间')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('产品描述长度不能超过1000个字符')
    .trim(),
  body('category')
    .isLength({ min: 2, max: 100 })
    .withMessage('产品分类长度必须在2-100个字符之间')
    .trim(),
  body('unit')
    .isLength({ min: 1, max: 20 })
    .withMessage('计量单位长度必须在1-20个字符之间')
    .trim(),
  body('cost_price')
    .isFloat({ min: 0 })
    .withMessage('成本价格必须是非负数'),
  body('selling_price')
    .isFloat({ min: 0 })
    .withMessage('销售价格必须是非负数'),
  body('min_stock_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最低库存必须是非负整数'),
  body('max_stock_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最高库存必须是非负整数'),
  body('current_stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('当前库存必须是非负整数')
];

const updateProductValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('产品名称长度必须在2-200个字符之间')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('产品描述长度不能超过1000个字符')
    .trim(),
  body('category')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('产品分类长度必须在2-100个字符之间')
    .trim(),
  body('unit')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('计量单位长度必须在1-20个字符之间')
    .trim(),
  body('cost_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('成本价格必须是非负数'),
  body('selling_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('销售价格必须是非负数'),
  body('min_stock_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最低库存必须是非负整数'),
  body('max_stock_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最高库存必须是非负整数'),
  body('current_stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('当前库存必须是非负整数')
];

const stockUpdateValidation = [
  body('quantity')
    .isInt()
    .withMessage('数量必须是整数'),
  body('type')
    .isIn(['in', 'out'])
    .withMessage('类型必须是: in, out'),
  body('reason')
    .optional()
    .isLength({ max: 200 })
    .withMessage('原因长度不能超过200个字符')
    .trim()
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取产品列表
router.get('/', 
  auth,
  paginationValidation,
  productController.getProducts
);

// 获取产品详情
router.get('/:id', 
  auth,
  productController.getProductById
);

// 创建产品
router.post('/', 
  auth,
  authorize('admin', 'manager', 'employee'),
  createProductValidation,
  productController.createProduct
);

// 更新产品
router.put('/:id', 
  auth,
  authorize('admin', 'manager', 'employee'),
  updateProductValidation,
  productController.updateProduct
);

// 删除产品
router.delete('/:id', 
  auth,
  authorize('admin', 'manager'),
  productController.deleteProduct
);

// 更新产品状态
router.patch('/:id/status', 
  auth,
  authorize('admin', 'manager'),
  body('status').isIn(['active', 'inactive']).withMessage('状态必须是: active, inactive'),
  productController.updateProductStatus
);

// 更新库存
router.patch('/:id/stock', 
  auth,
  authorize('admin', 'manager', 'employee'),
  stockUpdateValidation,
  productController.updateStock
);

module.exports = router;

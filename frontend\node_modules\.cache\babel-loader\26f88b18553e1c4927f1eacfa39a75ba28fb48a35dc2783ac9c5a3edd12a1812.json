{"ast": null, "code": "import * as React from 'react';\nvar DrawerContext = /*#__PURE__*/React.createContext(null);\nexport var RefContext = /*#__PURE__*/React.createContext({});\nexport default DrawerContext;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createContext", "RefContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-drawer@7.3.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-drawer/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar DrawerContext = /*#__PURE__*/React.createContext(null);\nexport var RefContext = /*#__PURE__*/React.createContext({});\nexport default DrawerContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,OAAO,IAAIC,UAAU,GAAG,aAAaH,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
const { PurchaseOrder, PurchaseOrderItem, Supplier, Product, User } = require('../models');
const { Op } = require('sequelize');
const { validationResult } = require('express-validator');

// 生成采购订单号
const generatePONumber = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  const prefix = `PO${dateStr}`;
  
  const lastPO = await PurchaseOrder.findOne({
    where: {
      po_number: {
        [Op.like]: `${prefix}%`
      }
    },
    order: [['po_number', 'DESC']]
  });
  
  let sequence = 1;
  if (lastPO) {
    const lastSequence = parseInt(lastPO.po_number.slice(-4));
    sequence = lastSequence + 1;
  }
  
  return `${prefix}${sequence.toString().padStart(4, '0')}`;
};

// 计算采购订单总额
const calculatePOTotals = (items) => {
  let subtotal = 0;
  let taxAmount = 0;
  
  items.forEach(item => {
    const lineTotal = item.quantity * item.unit_cost;
    const discountAmount = lineTotal * (item.discount_rate / 100);
    const netAmount = lineTotal - discountAmount;
    const itemTax = netAmount * (item.tax_rate / 100);
    
    subtotal += netAmount;
    taxAmount += itemTax;
  });
  
  return { subtotal, taxAmount };
};

// 获取所有采购订单
exports.getPurchaseOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      supplier_id,
      start_date,
      end_date,
      search
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    // 状态筛选
    if (status) {
      where.status = status;
    }
    
    // 供应商筛选
    if (supplier_id) {
      where.supplier_id = supplier_id;
    }
    
    // 日期范围筛选
    if (start_date && end_date) {
      where.order_date = {
        [Op.between]: [start_date, end_date]
      };
    }
    
    // 搜索采购订单号
    if (search) {
      where.po_number = {
        [Op.like]: `%${search}%`
      };
    }
    
    const { count, rows } = await PurchaseOrder.findAndCountAll({
      where,
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'company_name', 'contact_person']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'full_name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        purchaseOrders: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取采购订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采购订单列表失败',
      error: error.message
    });
  }
};

// 获取单个采购订单详情
exports.getPurchaseOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const purchaseOrder = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_code', 'name', 'unit']
            }
          ]
        }
      ]
    });
    
    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
    }
    
    res.json({
      success: true,
      data: purchaseOrder
    });
  } catch (error) {
    console.error('获取采购订单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采购订单详情失败',
      error: error.message
    });
  }
};

// 创建采购订单
exports.createPurchaseOrder = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }
    
    const {
      supplier_id,
      expected_delivery_date,
      priority = 'normal',
      discount_amount = 0,
      shipping_cost = 0,
      payment_method,
      payment_terms,
      delivery_address,
      notes,
      items
    } = req.body;
    
    // 验证供应商是否存在
    const supplier = await Supplier.findByPk(supplier_id);
    if (!supplier) {
      return res.status(400).json({
        success: false,
        message: '供应商不存在'
      });
    }
    
    // 验证采购订单明细
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '采购订单明细不能为空'
      });
    }
    
    // 生成采购订单号
    const poNumber = await generatePONumber();
    
    // 计算采购订单总额
    const { subtotal, taxAmount } = calculatePOTotals(items);
    const totalAmount = subtotal + taxAmount + shipping_cost - discount_amount;
    
    // 创建采购订单
    const purchaseOrder = await PurchaseOrder.create({
      po_number: poNumber,
      supplier_id,
      user_id: req.user.id,
      expected_delivery_date,
      priority,
      subtotal,
      tax_amount: taxAmount,
      discount_amount,
      shipping_cost,
      total_amount: totalAmount,
      payment_method,
      payment_terms,
      delivery_address,
      notes
    });
    
    // 创建采购订单明细
    const purchaseOrderItems = items.map(item => ({
      purchase_order_id: purchaseOrder.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_cost: item.unit_cost,
      discount_rate: item.discount_rate || 0,
      discount_amount: (item.quantity * item.unit_cost) * ((item.discount_rate || 0) / 100),
      tax_rate: item.tax_rate || 0,
      tax_amount: ((item.quantity * item.unit_cost) - ((item.quantity * item.unit_cost) * ((item.discount_rate || 0) / 100))) * ((item.tax_rate || 0) / 100),
      line_total: item.quantity * item.unit_cost,
      notes: item.notes
    }));
    
    await PurchaseOrderItem.bulkCreate(purchaseOrderItems);
    
    // 返回完整的采购订单信息
    const createdPurchaseOrder = await PurchaseOrder.findByPk(purchaseOrder.id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_code', 'name', 'unit']
            }
          ]
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      message: '采购订单创建成功',
      data: createdPurchaseOrder
    });
  } catch (error) {
    console.error('创建采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建采购订单失败',
      error: error.message
    });
  }
};

// 更新采购订单
exports.updatePurchaseOrder = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const purchaseOrder = await PurchaseOrder.findByPk(id);
    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
    }

    // 只有草稿状态的订单可以修改
    if (purchaseOrder.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: '只有草稿状态的订单可以修改'
      });
    }

    // 更新采购订单
    await purchaseOrder.update(updateData);

    res.json({
      success: true,
      message: '采购订单更新成功',
      data: { purchaseOrder }
    });
  } catch (error) {
    console.error('更新采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '更新采购订单失败',
      error: error.message
    });
  }
};

// 更新采购订单状态
exports.updatePurchaseOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['draft', 'sent', 'confirmed', 'partial_received', 'received', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的采购订单状态'
      });
    }

    const purchaseOrder = await PurchaseOrder.findByPk(id);
    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
    }

    await purchaseOrder.update({ status });

    res.json({
      success: true,
      message: '采购订单状态更新成功',
      data: purchaseOrder
    });
  } catch (error) {
    console.error('更新采购订单状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新采购订单状态失败',
      error: error.message
    });
  }
};

// 收货处理
exports.receiveItems = async (req, res) => {
  try {
    const { id } = req.params;
    const { items } = req.body; // [{ item_id, received_quantity, quality_status }]

    const purchaseOrder = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: PurchaseOrderItem,
          as: 'items'
        }
      ]
    });

    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
    }

    // 更新收货数量和质检状态
    for (const item of items) {
      const poItem = await PurchaseOrderItem.findByPk(item.item_id);
      if (poItem && poItem.purchase_order_id === parseInt(id)) {
        const newReceivedQty = poItem.received_quantity + item.received_quantity;

        await poItem.update({
          received_quantity: newReceivedQty,
          quality_status: item.quality_status || 'pending'
        });

        // 更新产品库存
        const product = await Product.findByPk(poItem.product_id);
        if (product && item.quality_status === 'passed') {
          await product.update({
            current_stock: product.current_stock + item.received_quantity
          });
        }
      }
    }

    // 检查是否全部收货完成
    const updatedPO = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: PurchaseOrderItem,
          as: 'items'
        }
      ]
    });

    const allReceived = updatedPO.items.every(item =>
      item.received_quantity >= item.quantity
    );

    const partialReceived = updatedPO.items.some(item =>
      item.received_quantity > 0 && item.received_quantity < item.quantity
    );

    let newStatus = updatedPO.status;
    if (allReceived) {
      newStatus = 'received';
    } else if (partialReceived) {
      newStatus = 'partial_received';
    }

    if (newStatus !== updatedPO.status) {
      await updatedPO.update({
        status: newStatus,
        actual_delivery_date: allReceived ? new Date() : null
      });
    }

    res.json({
      success: true,
      message: '收货处理成功',
      data: updatedPO
    });
  } catch (error) {
    console.error('收货处理失败:', error);
    res.status(500).json({
      success: false,
      message: '收货处理失败',
      error: error.message
    });
  }
};

// 删除采购订单
exports.deletePurchaseOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const purchaseOrder = await PurchaseOrder.findByPk(id);
    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
    }

    // 只有草稿状态的采购订单可以删除
    if (purchaseOrder.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: '只有草稿状态的采购订单可以删除'
      });
    }

    // 删除采购订单明细
    await PurchaseOrderItem.destroy({ where: { purchase_order_id: id } });

    // 删除采购订单
    await purchaseOrder.destroy();

    res.json({
      success: true,
      message: '采购订单删除成功'
    });
  } catch (error) {
    console.error('删除采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '删除采购订单失败',
      error: error.message
    });
  }
};

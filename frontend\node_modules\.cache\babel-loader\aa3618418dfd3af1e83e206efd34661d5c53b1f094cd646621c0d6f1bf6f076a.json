{"ast": null, "code": "/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react';\n// We will never use default, here only to fix TypeScript warning\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;", "map": {"version": 3, "names": ["React", "MentionsContext", "createContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-mentions@2.20.0_react-do_10ce709700cdbca87e7a23d808de1d8b/node_modules/rc-mentions/es/MentionsContext.js"], "sourcesContent": ["/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react';\n// We will never use default, here only to fix TypeScript warning\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
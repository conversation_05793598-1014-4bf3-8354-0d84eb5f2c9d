{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;", "map": {"version": 3, "names": ["React", "generateColor", "ColorClear", "prefixCls", "value", "onChange", "handleClick", "cleared", "hsba", "toHsb", "a", "genColor", "createElement", "className", "onClick"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/components/ColorClear.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,UAAU,GAAGA,CAAC;EAClBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAID,QAAQ,IAAID,KAAK,IAAI,CAACA,KAAK,CAACG,OAAO,EAAE;MACvC,MAAMC,IAAI,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC;MAC1BD,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,MAAMC,QAAQ,GAAGV,aAAa,CAACO,IAAI,CAAC;MACpCG,QAAQ,CAACJ,OAAO,GAAG,IAAI;MACvBF,QAAQ,CAACM,QAAQ,CAAC;IACpB;EACF,CAAC;EACD,OAAO,aAAaX,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,GAAGV,SAAS,QAAQ;IAC/BW,OAAO,EAAER;EACX,CAAC,CAAC;AACJ,CAAC;AACD,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
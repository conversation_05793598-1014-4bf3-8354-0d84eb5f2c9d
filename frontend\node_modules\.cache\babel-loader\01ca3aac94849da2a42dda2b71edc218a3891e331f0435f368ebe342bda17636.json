{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\n\n/**\n * Trigger only when component unmount\n */\nfunction useUnmount(triggerStart, triggerEnd) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstMount = _React$useState2[0],\n    setFirstMount = _React$useState2[1];\n  useLayoutEffect(function () {\n    if (firstMount) {\n      triggerStart();\n      return function () {\n        triggerEnd();\n      };\n    }\n  }, [firstMount]);\n  useLayoutEffect(function () {\n    setFirstMount(true);\n    return function () {\n      setFirstMount(false);\n    };\n  }, []);\n}\nexport default useUnmount;", "map": {"version": 3, "names": ["_slicedToArray", "React", "useLayoutEffect", "useUnmount", "triggerStart", "triggerEnd", "_React$useState", "useState", "_React$useState2", "firstMount", "set<PERSON><PERSON><PERSON>Mount"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree@5.13.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tree/es/useUnmount.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\n\n/**\n * Trigger only when component unmount\n */\nfunction useUnmount(triggerStart, triggerEnd) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstMount = _React$useState2[0],\n    setFirstMount = _React$useState2[1];\n  useLayoutEffect(function () {\n    if (firstMount) {\n      triggerStart();\n      return function () {\n        triggerEnd();\n      };\n    }\n  }, [firstMount]);\n  useLayoutEffect(function () {\n    setFirstMount(true);\n    return function () {\n      setFirstMount(false);\n    };\n  }, []);\n}\nexport default useUnmount;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,kCAAkC;;AAE9D;AACA;AACA;AACA,SAASC,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;EAC5C,IAAIC,eAAe,GAAGL,KAAK,CAACM,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGR,cAAc,CAACM,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrCN,eAAe,CAAC,YAAY;IAC1B,IAAIO,UAAU,EAAE;MACdL,YAAY,CAAC,CAAC;MACd,OAAO,YAAY;QACjBC,UAAU,CAAC,CAAC;MACd,CAAC;IACH;EACF,CAAC,EAAE,CAACI,UAAU,CAAC,CAAC;EAChBP,eAAe,CAAC,YAAY;IAC1BQ,aAAa,CAAC,IAAI,CAAC;IACnB,OAAO,YAAY;MACjBA,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR;AACA,eAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport Summary from \"./Summary\";\nimport SummaryContext from \"./SummaryContext\";\nfunction Footer(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var children = props.children,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default responseImmutable(Footer);\nexport var FooterComponents = Summary;", "map": {"version": 3, "names": ["useContext", "React", "TableContext", "responseImmutable", "devRenderTimes", "Summary", "SummaryContext", "Footer", "props", "process", "env", "NODE_ENV", "children", "stickyOffsets", "flattenColumns", "prefixCls", "lastColumnIndex", "length", "scrollColumn", "summaryContext", "useMemo", "scrollColumnIndex", "scrollbar", "createElement", "Provider", "value", "className", "concat", "FooterComponents"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/Footer/index.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport Summary from \"./Summary\";\nimport SummaryContext from \"./SummaryContext\";\nfunction Footer(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var children = props.children,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default responseImmutable(Footer);\nexport var FooterComponents = Summary;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCP,cAAc,CAACI,KAAK,CAAC;EACvB;EACA,IAAII,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,cAAc,GAAGN,KAAK,CAACM,cAAc;EACvC,IAAIC,SAAS,GAAGf,UAAU,CAACE,YAAY,EAAE,WAAW,CAAC;EACrD,IAAIc,eAAe,GAAGF,cAAc,CAACG,MAAM,GAAG,CAAC;EAC/C,IAAIC,YAAY,GAAGJ,cAAc,CAACE,eAAe,CAAC;EAClD,IAAIG,cAAc,GAAGlB,KAAK,CAACmB,OAAO,CAAC,YAAY;IAC7C,OAAO;MACLP,aAAa,EAAEA,aAAa;MAC5BC,cAAc,EAAEA,cAAc;MAC9BO,iBAAiB,EAAEH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACI,SAAS,GAAGN,eAAe,GAAG;IACpH,CAAC;EACH,CAAC,EAAE,CAACE,YAAY,EAAEJ,cAAc,EAAEE,eAAe,EAAEH,aAAa,CAAC,CAAC;EAClE,OAAO,aAAaZ,KAAK,CAACsB,aAAa,CAACjB,cAAc,CAACkB,QAAQ,EAAE;IAC/DC,KAAK,EAAEN;EACT,CAAC,EAAE,aAAalB,KAAK,CAACsB,aAAa,CAAC,OAAO,EAAE;IAC3CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEH,QAAQ,CAAC,CAAC;AACf;AACA,eAAeT,iBAAiB,CAACI,MAAM,CAAC;AACxC,OAAO,IAAIqB,gBAAgB,GAAGvB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nimport * as React from 'react';\nexport default function useCellRender(cellRender, dateRender, monthCellRender, range) {\n  // ========================= Warn =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!dateRender, \"'dateRender' is deprecated. Please use 'cellRender' instead.\");\n    warning(!monthCellRender, \"'monthCellRender' is deprecated. Please use 'cellRender' instead.\");\n  }\n\n  // ======================== Render ========================\n  // Merged render\n  var mergedCellRender = React.useMemo(function () {\n    if (cellRender) {\n      return cellRender;\n    }\n    return function (current, info) {\n      var date = current;\n      if (dateRender && info.type === 'date') {\n        return dateRender(date, info.today);\n      }\n      if (monthCellRender && info.type === 'month') {\n        return monthCellRender(date, info.locale);\n      }\n      return info.originNode;\n    };\n  }, [cellRender, monthCellRender, dateRender]);\n\n  // Cell render\n  var onInternalCellRender = React.useCallback(function (date, info) {\n    return mergedCellRender(date, _objectSpread(_objectSpread({}, info), {}, {\n      range: range\n    }));\n  }, [mergedCellRender, range]);\n  return onInternalCellRender;\n}", "map": {"version": 3, "names": ["_objectSpread", "warning", "React", "useCellRender", "cellRender", "dateRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range", "process", "env", "NODE_ENV", "mergedCellRender", "useMemo", "current", "info", "date", "type", "today", "locale", "originNode", "onInternalCellRender", "useCallback"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useCellRender.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nimport * as React from 'react';\nexport default function useCellRender(cellRender, dateRender, monthCellRender, range) {\n  // ========================= Warn =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!dateRender, \"'dateRender' is deprecated. Please use 'cellRender' instead.\");\n    warning(!monthCellRender, \"'monthCellRender' is deprecated. Please use 'cellRender' instead.\");\n  }\n\n  // ======================== Render ========================\n  // Merged render\n  var mergedCellRender = React.useMemo(function () {\n    if (cellRender) {\n      return cellRender;\n    }\n    return function (current, info) {\n      var date = current;\n      if (dateRender && info.type === 'date') {\n        return dateRender(date, info.today);\n      }\n      if (monthCellRender && info.type === 'month') {\n        return monthCellRender(date, info.locale);\n      }\n      return info.originNode;\n    };\n  }, [cellRender, monthCellRender, dateRender]);\n\n  // Cell render\n  var onInternalCellRender = React.useCallback(function (date, info) {\n    return mergedCellRender(date, _objectSpread(_objectSpread({}, info), {}, {\n      range: range\n    }));\n  }, [mergedCellRender, range]);\n  return onInternalCellRender;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAE;EACpF;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCT,OAAO,CAAC,CAACI,UAAU,EAAE,8DAA8D,CAAC;IACpFJ,OAAO,CAAC,CAACK,eAAe,EAAE,mEAAmE,CAAC;EAChG;;EAEA;EACA;EACA,IAAIK,gBAAgB,GAAGT,KAAK,CAACU,OAAO,CAAC,YAAY;IAC/C,IAAIR,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;IACA,OAAO,UAAUS,OAAO,EAAEC,IAAI,EAAE;MAC9B,IAAIC,IAAI,GAAGF,OAAO;MAClB,IAAIR,UAAU,IAAIS,IAAI,CAACE,IAAI,KAAK,MAAM,EAAE;QACtC,OAAOX,UAAU,CAACU,IAAI,EAAED,IAAI,CAACG,KAAK,CAAC;MACrC;MACA,IAAIX,eAAe,IAAIQ,IAAI,CAACE,IAAI,KAAK,OAAO,EAAE;QAC5C,OAAOV,eAAe,CAACS,IAAI,EAAED,IAAI,CAACI,MAAM,CAAC;MAC3C;MACA,OAAOJ,IAAI,CAACK,UAAU;IACxB,CAAC;EACH,CAAC,EAAE,CAACf,UAAU,EAAEE,eAAe,EAAED,UAAU,CAAC,CAAC;;EAE7C;EACA,IAAIe,oBAAoB,GAAGlB,KAAK,CAACmB,WAAW,CAAC,UAAUN,IAAI,EAAED,IAAI,EAAE;IACjE,OAAOH,gBAAgB,CAACI,IAAI,EAAEf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEc,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACvEP,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACI,gBAAgB,EAAEJ,KAAK,CAAC,CAAC;EAC7B,OAAOa,oBAAoB;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\", \"overlay\", \"children\", \"onVisibleChange\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React from 'react';\nimport useAccessibility from \"./hooks/useAccessibility\";\nimport Overlay from \"./Overlay\";\nimport Placements from \"./placements\";\nfunction Dropdown(props, ref) {\n  var _children$props;\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? Placements : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = React.useRef(null);\n  var overlayRef = React.useRef(null);\n  var childRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var handleVisibleChange = function handleVisibleChange(newVisible) {\n    setTriggerVisible(newVisible);\n    onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(newVisible);\n  };\n  useAccessibility({\n    visible: mergedVisible,\n    triggerRef: childRef,\n    onVisibleChange: handleVisibleChange,\n    autoFocus: autoFocus,\n    overlayRef: overlayRef\n  });\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    return /*#__PURE__*/React.createElement(Overlay, {\n      ref: overlayRef,\n      overlay: overlay,\n      prefixCls: prefixCls,\n      arrow: arrow\n    });\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var childrenNode = /*#__PURE__*/React.cloneElement(children, {\n    className: classNames((_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.className, mergedVisible && getOpenClassName()),\n    ref: supportRef(children) ? composeRef(childRef, getNodeRef(children)) : undefined\n  });\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    builtinPlacements: placements\n  }, otherProps, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction,\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: handleVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), childrenNode);\n}\nexport default /*#__PURE__*/React.forwardRef(Dropdown);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON>", "classNames", "composeRef", "getNodeRef", "supportRef", "React", "useAccessibility", "Overlay", "Placements", "Dropdown", "props", "ref", "_children$props", "_props$arrow", "arrow", "_props$prefixCls", "prefixCls", "transitionName", "animation", "align", "_props$placement", "placement", "_props$placements", "placements", "getPopupContainer", "showAction", "hideAction", "overlayClassName", "overlayStyle", "visible", "_props$trigger", "trigger", "autoFocus", "overlay", "children", "onVisibleChange", "otherProps", "_React$useState", "useState", "_React$useState2", "triggerVisible", "setTriggerVisible", "mergedVisible", "triggerRef", "useRef", "overlayRef", "childRef", "useImperativeHandle", "current", "handleVisibleChange", "newVisible", "onClick", "e", "onOverlayClick", "getMenuElement", "createElement", "getMenuElementOrLambda", "getMinOverlayWidthMatchTrigger", "minOverlayWidthMatchTrigger", "alignPoint", "getOpenClassName", "openClassName", "undefined", "concat", "childrenNode", "cloneElement", "className", "triggerHideAction", "indexOf", "builtinPlacements", "popupClassName", "popupStyle", "action", "popupPlacement", "popupAlign", "popupTransitionName", "popupAnimation", "popupVisible", "stretch", "popup", "onPopupVisibleChange", "onPopupClick", "forwardRef"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-dropdown@4.2.1_react-dom_e5c4f19f381956aa8dbdca68b61f3cdd/node_modules/rc-dropdown/es/Dropdown.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\", \"overlay\", \"children\", \"onVisibleChange\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React from 'react';\nimport useAccessibility from \"./hooks/useAccessibility\";\nimport Overlay from \"./Overlay\";\nimport Placements from \"./placements\";\nfunction Dropdown(props, ref) {\n  var _children$props;\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? Placements : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = React.useRef(null);\n  var overlayRef = React.useRef(null);\n  var childRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var handleVisibleChange = function handleVisibleChange(newVisible) {\n    setTriggerVisible(newVisible);\n    onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(newVisible);\n  };\n  useAccessibility({\n    visible: mergedVisible,\n    triggerRef: childRef,\n    onVisibleChange: handleVisibleChange,\n    autoFocus: autoFocus,\n    overlayRef: overlayRef\n  });\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    return /*#__PURE__*/React.createElement(Overlay, {\n      ref: overlayRef,\n      overlay: overlay,\n      prefixCls: prefixCls,\n      arrow: arrow\n    });\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var childrenNode = /*#__PURE__*/React.cloneElement(children, {\n    className: classNames((_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.className, mergedVisible && getOpenClassName()),\n    ref: supportRef(children) ? composeRef(childRef, getNodeRef(children)) : undefined\n  });\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    builtinPlacements: placements\n  }, otherProps, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction,\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: handleVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), childrenNode);\n}\nexport default /*#__PURE__*/React.forwardRef(Dropdown);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,kBAAkB,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC;AAC3Q,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACnE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5B,IAAIC,eAAe;EACnB,IAAIC,YAAY,GAAGH,KAAK,CAACI,KAAK;IAC5BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IACtDE,gBAAgB,GAAGL,KAAK,CAACM,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,gBAAgB,GAAGV,KAAK,CAACW,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEE,iBAAiB,GAAGZ,KAAK,CAACa,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGd,UAAU,GAAGc,iBAAiB;IAC1EE,iBAAiB,GAAGd,KAAK,CAACc,iBAAiB;IAC3CC,UAAU,GAAGf,KAAK,CAACe,UAAU;IAC7BC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,gBAAgB,GAAGjB,KAAK,CAACiB,gBAAgB;IACzCC,YAAY,GAAGlB,KAAK,CAACkB,YAAY;IACjCC,OAAO,GAAGnB,KAAK,CAACmB,OAAO;IACvBC,cAAc,GAAGpB,KAAK,CAACqB,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,cAAc;IAChEE,SAAS,GAAGtB,KAAK,CAACsB,SAAS;IAC3BC,OAAO,GAAGvB,KAAK,CAACuB,OAAO;IACvBC,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ;IACzBC,eAAe,GAAGzB,KAAK,CAACyB,eAAe;IACvCC,UAAU,GAAGtC,wBAAwB,CAACY,KAAK,EAAEX,SAAS,CAAC;EACzD,IAAIsC,eAAe,GAAGhC,KAAK,CAACiC,QAAQ,CAAC,CAAC;IACpCC,gBAAgB,GAAG1C,cAAc,CAACwC,eAAe,EAAE,CAAC,CAAC;IACrDG,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,aAAa,GAAG,SAAS,IAAIhC,KAAK,GAAGmB,OAAO,GAAGW,cAAc;EACjE,IAAIG,UAAU,GAAGtC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAGxC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,QAAQ,GAAGzC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACjCvC,KAAK,CAAC0C,mBAAmB,CAACpC,GAAG,EAAE,YAAY;IACzC,OAAOgC,UAAU,CAACK,OAAO;EAC3B,CAAC,CAAC;EACF,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,UAAU,EAAE;IACjET,iBAAiB,CAACS,UAAU,CAAC;IAC7Bf,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACe,UAAU,CAAC;EACvF,CAAC;EACD5C,gBAAgB,CAAC;IACfuB,OAAO,EAAEa,aAAa;IACtBC,UAAU,EAAEG,QAAQ;IACpBX,eAAe,EAAEc,mBAAmB;IACpCjB,SAAS,EAAEA,SAAS;IACpBa,UAAU,EAAEA;EACd,CAAC,CAAC;EACF,IAAIM,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAE;IAChC,IAAIC,cAAc,GAAG3C,KAAK,CAAC2C,cAAc;IACzCZ,iBAAiB,CAAC,KAAK,CAAC;IACxB,IAAIY,cAAc,EAAE;MAClBA,cAAc,CAACD,CAAC,CAAC;IACnB;EACF,CAAC;EACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAO,aAAajD,KAAK,CAACkD,aAAa,CAAChD,OAAO,EAAE;MAC/CI,GAAG,EAAEkC,UAAU;MACfZ,OAAO,EAAEA,OAAO;MAChBjB,SAAS,EAAEA,SAAS;MACpBF,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACD,IAAI0C,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;IAC7D,IAAI,OAAOvB,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOqB,cAAc;IACvB;IACA,OAAOA,cAAc,CAAC,CAAC;EACzB,CAAC;EACD,IAAIG,8BAA8B,GAAG,SAASA,8BAA8BA,CAAA,EAAG;IAC7E,IAAIC,2BAA2B,GAAGhD,KAAK,CAACgD,2BAA2B;MACjEC,UAAU,GAAGjD,KAAK,CAACiD,UAAU;IAC/B,IAAI,6BAA6B,IAAIjD,KAAK,EAAE;MAC1C,OAAOgD,2BAA2B;IACpC;IACA,OAAO,CAACC,UAAU;EACpB,CAAC;EACD,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,aAAa,GAAGnD,KAAK,CAACmD,aAAa;IACvC,IAAIA,aAAa,KAAKC,SAAS,EAAE;MAC/B,OAAOD,aAAa;IACtB;IACA,OAAO,EAAE,CAACE,MAAM,CAAC/C,SAAS,EAAE,OAAO,CAAC;EACtC,CAAC;EACD,IAAIgD,YAAY,GAAG,aAAa3D,KAAK,CAAC4D,YAAY,CAAC/B,QAAQ,EAAE;IAC3DgC,SAAS,EAAEjE,UAAU,CAAC,CAACW,eAAe,GAAGsB,QAAQ,CAACxB,KAAK,MAAM,IAAI,IAAIE,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACsD,SAAS,EAAExB,aAAa,IAAIkB,gBAAgB,CAAC,CAAC,CAAC;IAC1KjD,GAAG,EAAEP,UAAU,CAAC8B,QAAQ,CAAC,GAAGhC,UAAU,CAAC4C,QAAQ,EAAE3C,UAAU,CAAC+B,QAAQ,CAAC,CAAC,GAAG4B;EAC3E,CAAC,CAAC;EACF,IAAIK,iBAAiB,GAAGzC,UAAU;EAClC,IAAI,CAACyC,iBAAiB,IAAIpC,OAAO,CAACqC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;IAC/DD,iBAAiB,GAAG,CAAC,OAAO,CAAC;EAC/B;EACA,OAAO,aAAa9D,KAAK,CAACkD,aAAa,CAACvD,OAAO,EAAEL,QAAQ,CAAC;IACxD0E,iBAAiB,EAAE9C;EACrB,CAAC,EAAEa,UAAU,EAAE;IACbpB,SAAS,EAAEA,SAAS;IACpBL,GAAG,EAAEgC,UAAU;IACf2B,cAAc,EAAErE,UAAU,CAAC0B,gBAAgB,EAAE/B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmE,MAAM,CAAC/C,SAAS,EAAE,aAAa,CAAC,EAAEF,KAAK,CAAC,CAAC;IAC7GyD,UAAU,EAAE3C,YAAY;IACxB4C,MAAM,EAAEzC,OAAO;IACfN,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEyC,iBAAiB;IAC7BM,cAAc,EAAEpD,SAAS;IACzBqD,UAAU,EAAEvD,KAAK;IACjBwD,mBAAmB,EAAE1D,cAAc;IACnC2D,cAAc,EAAE1D,SAAS;IACzB2D,YAAY,EAAEnC,aAAa;IAC3BoC,OAAO,EAAErB,8BAA8B,CAAC,CAAC,GAAG,UAAU,GAAG,EAAE;IAC3DsB,KAAK,EAAEvB,sBAAsB,CAAC,CAAC;IAC/BwB,oBAAoB,EAAE/B,mBAAmB;IACzCgC,YAAY,EAAE9B,OAAO;IACrB3B,iBAAiB,EAAEA;EACrB,CAAC,CAAC,EAAEwC,YAAY,CAAC;AACnB;AACA,eAAe,aAAa3D,KAAK,CAAC6E,UAAU,CAACzE,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { unstableSetRender } from '../../config-provider/UnstableContext';\nimport { TARGET_CLS } from './interface';\nimport { getTargetWaveColor } from './util';\nfunction validateNum(value) {\n  return Number.isNaN(value) ? 0 : value;\n}\nconst WaveEffect = props => {\n  const {\n    className,\n    target,\n    component,\n    registerUnmount\n  } = props;\n  const divRef = React.useRef(null);\n  // ====================== Refs ======================\n  const unmountRef = React.useRef(null);\n  React.useEffect(() => {\n    unmountRef.current = registerUnmount();\n  }, []);\n  // ===================== Effect =====================\n  const [color, setWaveColor] = React.useState(null);\n  const [borderRadius, setBorderRadius] = React.useState([]);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const [enabled, setEnabled] = React.useState(false);\n  const waveStyle = {\n    left,\n    top,\n    width,\n    height,\n    borderRadius: borderRadius.map(radius => `${radius}px`).join(' ')\n  };\n  if (color) {\n    waveStyle['--wave-color'] = color;\n  }\n  function syncPos() {\n    const nodeStyle = getComputedStyle(target);\n    // Get wave color from target\n    setWaveColor(getTargetWaveColor(target));\n    const isStatic = nodeStyle.position === 'static';\n    // Rect\n    const {\n      borderLeftWidth,\n      borderTopWidth\n    } = nodeStyle;\n    setLeft(isStatic ? target.offsetLeft : validateNum(-parseFloat(borderLeftWidth)));\n    setTop(isStatic ? target.offsetTop : validateNum(-parseFloat(borderTopWidth)));\n    setWidth(target.offsetWidth);\n    setHeight(target.offsetHeight);\n    // Get border radius\n    const {\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius\n    } = nodeStyle;\n    setBorderRadius([borderTopLeftRadius, borderTopRightRadius, borderBottomRightRadius, borderBottomLeftRadius].map(radius => validateNum(parseFloat(radius))));\n  }\n  React.useEffect(() => {\n    if (target) {\n      // We need delay to check position here\n      // since UI may change after click\n      const id = raf(() => {\n        syncPos();\n        setEnabled(true);\n      });\n      // Add resize observer to follow size\n      let resizeObserver;\n      if (typeof ResizeObserver !== 'undefined') {\n        resizeObserver = new ResizeObserver(syncPos);\n        resizeObserver.observe(target);\n      }\n      return () => {\n        raf.cancel(id);\n        resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.disconnect();\n      };\n    }\n  }, []);\n  if (!enabled) {\n    return null;\n  }\n  const isSmallComponent = (component === 'Checkbox' || component === 'Radio') && (target === null || target === void 0 ? void 0 : target.classList.contains(TARGET_CLS));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionAppear: true,\n    motionName: \"wave-motion\",\n    motionDeadline: 5000,\n    onAppearEnd: (_, event) => {\n      var _a, _b;\n      if (event.deadline || event.propertyName === 'opacity') {\n        const holder = (_a = divRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;\n        (_b = unmountRef.current) === null || _b === void 0 ? void 0 : _b.call(unmountRef).then(() => {\n          holder === null || holder === void 0 ? void 0 : holder.remove();\n        });\n      }\n      return false;\n    }\n  }, ({\n    className: motionClassName\n  }, ref) => (/*#__PURE__*/React.createElement(\"div\", {\n    ref: composeRef(divRef, ref),\n    className: classNames(className, motionClassName, {\n      'wave-quick': isSmallComponent\n    }),\n    style: waveStyle\n  })));\n};\nconst showWaveEffect = (target, info) => {\n  var _a;\n  const {\n    component\n  } = info;\n  // Skip for unchecked checkbox\n  if (component === 'Checkbox' && !((_a = target.querySelector('input')) === null || _a === void 0 ? void 0 : _a.checked)) {\n    return;\n  }\n  // Create holder\n  const holder = document.createElement('div');\n  holder.style.position = 'absolute';\n  holder.style.left = '0px';\n  holder.style.top = '0px';\n  target === null || target === void 0 ? void 0 : target.insertBefore(holder, target === null || target === void 0 ? void 0 : target.firstChild);\n  const reactRender = unstableSetRender();\n  let unmountCallback = null;\n  function registerUnmount() {\n    return unmountCallback;\n  }\n  unmountCallback = reactRender(/*#__PURE__*/React.createElement(WaveEffect, Object.assign({}, info, {\n    target: target,\n    registerUnmount: registerUnmount\n  })), holder);\n};\nexport default showWaveEffect;", "map": {"version": 3, "names": ["React", "classNames", "CSSMotion", "raf", "composeRef", "unstableSetRender", "TARGET_CLS", "getTargetWaveColor", "validateNum", "value", "Number", "isNaN", "WaveEffect", "props", "className", "target", "component", "registerUnmount", "divRef", "useRef", "unmountRef", "useEffect", "current", "color", "setWaveColor", "useState", "borderRadius", "setBorderRadius", "left", "setLeft", "top", "setTop", "width", "<PERSON><PERSON><PERSON><PERSON>", "height", "setHeight", "enabled", "setEnabled", "waveStyle", "map", "radius", "join", "syncPos", "nodeStyle", "getComputedStyle", "isStatic", "position", "borderLeftWidth", "borderTopWidth", "offsetLeft", "parseFloat", "offsetTop", "offsetWidth", "offsetHeight", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "id", "resizeObserver", "ResizeObserver", "observe", "cancel", "disconnect", "isSmallComponent", "classList", "contains", "createElement", "visible", "motionAppear", "motionName", "motionDeadline", "onAppearEnd", "_", "event", "_a", "_b", "deadline", "propertyName", "holder", "parentElement", "call", "then", "remove", "motionClassName", "ref", "style", "showWaveEffect", "info", "querySelector", "checked", "document", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "reactRender", "unmountCallback", "Object", "assign"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/_util/wave/WaveEffect.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { unstableSetRender } from '../../config-provider/UnstableContext';\nimport { TARGET_CLS } from './interface';\nimport { getTargetWaveColor } from './util';\nfunction validateNum(value) {\n  return Number.isNaN(value) ? 0 : value;\n}\nconst WaveEffect = props => {\n  const {\n    className,\n    target,\n    component,\n    registerUnmount\n  } = props;\n  const divRef = React.useRef(null);\n  // ====================== Refs ======================\n  const unmountRef = React.useRef(null);\n  React.useEffect(() => {\n    unmountRef.current = registerUnmount();\n  }, []);\n  // ===================== Effect =====================\n  const [color, setWaveColor] = React.useState(null);\n  const [borderRadius, setBorderRadius] = React.useState([]);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const [enabled, setEnabled] = React.useState(false);\n  const waveStyle = {\n    left,\n    top,\n    width,\n    height,\n    borderRadius: borderRadius.map(radius => `${radius}px`).join(' ')\n  };\n  if (color) {\n    waveStyle['--wave-color'] = color;\n  }\n  function syncPos() {\n    const nodeStyle = getComputedStyle(target);\n    // Get wave color from target\n    setWaveColor(getTargetWaveColor(target));\n    const isStatic = nodeStyle.position === 'static';\n    // Rect\n    const {\n      borderLeftWidth,\n      borderTopWidth\n    } = nodeStyle;\n    setLeft(isStatic ? target.offsetLeft : validateNum(-parseFloat(borderLeftWidth)));\n    setTop(isStatic ? target.offsetTop : validateNum(-parseFloat(borderTopWidth)));\n    setWidth(target.offsetWidth);\n    setHeight(target.offsetHeight);\n    // Get border radius\n    const {\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius\n    } = nodeStyle;\n    setBorderRadius([borderTopLeftRadius, borderTopRightRadius, borderBottomRightRadius, borderBottomLeftRadius].map(radius => validateNum(parseFloat(radius))));\n  }\n  React.useEffect(() => {\n    if (target) {\n      // We need delay to check position here\n      // since UI may change after click\n      const id = raf(() => {\n        syncPos();\n        setEnabled(true);\n      });\n      // Add resize observer to follow size\n      let resizeObserver;\n      if (typeof ResizeObserver !== 'undefined') {\n        resizeObserver = new ResizeObserver(syncPos);\n        resizeObserver.observe(target);\n      }\n      return () => {\n        raf.cancel(id);\n        resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.disconnect();\n      };\n    }\n  }, []);\n  if (!enabled) {\n    return null;\n  }\n  const isSmallComponent = (component === 'Checkbox' || component === 'Radio') && (target === null || target === void 0 ? void 0 : target.classList.contains(TARGET_CLS));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionAppear: true,\n    motionName: \"wave-motion\",\n    motionDeadline: 5000,\n    onAppearEnd: (_, event) => {\n      var _a, _b;\n      if (event.deadline || event.propertyName === 'opacity') {\n        const holder = (_a = divRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;\n        (_b = unmountRef.current) === null || _b === void 0 ? void 0 : _b.call(unmountRef).then(() => {\n          holder === null || holder === void 0 ? void 0 : holder.remove();\n        });\n      }\n      return false;\n    }\n  }, ({\n    className: motionClassName\n  }, ref) => (/*#__PURE__*/React.createElement(\"div\", {\n    ref: composeRef(divRef, ref),\n    className: classNames(className, motionClassName, {\n      'wave-quick': isSmallComponent\n    }),\n    style: waveStyle\n  })));\n};\nconst showWaveEffect = (target, info) => {\n  var _a;\n  const {\n    component\n  } = info;\n  // Skip for unchecked checkbox\n  if (component === 'Checkbox' && !((_a = target.querySelector('input')) === null || _a === void 0 ? void 0 : _a.checked)) {\n    return;\n  }\n  // Create holder\n  const holder = document.createElement('div');\n  holder.style.position = 'absolute';\n  holder.style.left = '0px';\n  holder.style.top = '0px';\n  target === null || target === void 0 ? void 0 : target.insertBefore(holder, target === null || target === void 0 ? void 0 : target.firstChild);\n  const reactRender = unstableSetRender();\n  let unmountCallback = null;\n  function registerUnmount() {\n    return unmountCallback;\n  }\n  unmountCallback = reactRender(/*#__PURE__*/React.createElement(WaveEffect, Object.assign({}, info, {\n    target: target,\n    registerUnmount: registerUnmount\n  })), holder);\n};\nexport default showWaveEffect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,kBAAkB,QAAQ,QAAQ;AAC3C,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAOC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;AACxC;AACA,MAAMG,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,MAAM,GAAGlB,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EACjC;EACA,MAAMC,UAAU,GAAGpB,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EACrCnB,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpBD,UAAU,CAACE,OAAO,GAAGL,eAAe,CAAC,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CAACM,KAAK,EAAEC,YAAY,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,KAAK,CAACyB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAG7B,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACK,GAAG,EAAEC,MAAM,CAAC,GAAG/B,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGnC,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGrC,KAAK,CAACyB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,SAAS,GAAG;IAChBV,IAAI;IACJE,GAAG;IACHE,KAAK;IACLE,MAAM;IACNR,YAAY,EAAEA,YAAY,CAACa,GAAG,CAACC,MAAM,IAAI,GAAGA,MAAM,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG;EAClE,CAAC;EACD,IAAIlB,KAAK,EAAE;IACTe,SAAS,CAAC,cAAc,CAAC,GAAGf,KAAK;EACnC;EACA,SAASmB,OAAOA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAGC,gBAAgB,CAAC7B,MAAM,CAAC;IAC1C;IACAS,YAAY,CAACjB,kBAAkB,CAACQ,MAAM,CAAC,CAAC;IACxC,MAAM8B,QAAQ,GAAGF,SAAS,CAACG,QAAQ,KAAK,QAAQ;IAChD;IACA,MAAM;MACJC,eAAe;MACfC;IACF,CAAC,GAAGL,SAAS;IACbd,OAAO,CAACgB,QAAQ,GAAG9B,MAAM,CAACkC,UAAU,GAAGzC,WAAW,CAAC,CAAC0C,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC;IACjFhB,MAAM,CAACc,QAAQ,GAAG9B,MAAM,CAACoC,SAAS,GAAG3C,WAAW,CAAC,CAAC0C,UAAU,CAACF,cAAc,CAAC,CAAC,CAAC;IAC9Ef,QAAQ,CAAClB,MAAM,CAACqC,WAAW,CAAC;IAC5BjB,SAAS,CAACpB,MAAM,CAACsC,YAAY,CAAC;IAC9B;IACA,MAAM;MACJC,mBAAmB;MACnBC,oBAAoB;MACpBC,sBAAsB;MACtBC;IACF,CAAC,GAAGd,SAAS;IACbhB,eAAe,CAAC,CAAC2B,mBAAmB,EAAEC,oBAAoB,EAAEE,uBAAuB,EAAED,sBAAsB,CAAC,CAACjB,GAAG,CAACC,MAAM,IAAIhC,WAAW,CAAC0C,UAAU,CAACV,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9J;EACAxC,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIN,MAAM,EAAE;MACV;MACA;MACA,MAAM2C,EAAE,GAAGvD,GAAG,CAAC,MAAM;QACnBuC,OAAO,CAAC,CAAC;QACTL,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC;MACF;MACA,IAAIsB,cAAc;MAClB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;QACzCD,cAAc,GAAG,IAAIC,cAAc,CAAClB,OAAO,CAAC;QAC5CiB,cAAc,CAACE,OAAO,CAAC9C,MAAM,CAAC;MAChC;MACA,OAAO,MAAM;QACXZ,GAAG,CAAC2D,MAAM,CAACJ,EAAE,CAAC;QACdC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,UAAU,CAAC,CAAC;MAC7F,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC3B,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,MAAM4B,gBAAgB,GAAG,CAAChD,SAAS,KAAK,UAAU,IAAIA,SAAS,KAAK,OAAO,MAAMD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACkD,SAAS,CAACC,QAAQ,CAAC5D,UAAU,CAAC,CAAC;EACvK,OAAO,aAAaN,KAAK,CAACmE,aAAa,CAACjE,SAAS,EAAE;IACjDkE,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,aAAa;IACzBC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAEA,CAACC,CAAC,EAAEC,KAAK,KAAK;MACzB,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIF,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACI,YAAY,KAAK,SAAS,EAAE;QACtD,MAAMC,MAAM,GAAG,CAACJ,EAAE,GAAGzD,MAAM,CAACI,OAAO,MAAM,IAAI,IAAIqD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,aAAa;QAC1F,CAACJ,EAAE,GAAGxD,UAAU,CAACE,OAAO,MAAM,IAAI,IAAIsD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC7D,UAAU,CAAC,CAAC8D,IAAI,CAAC,MAAM;UAC5FH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,MAAM,CAAC,CAAC;QACjE,CAAC,CAAC;MACJ;MACA,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAAC;IACFrE,SAAS,EAAEsE;EACb,CAAC,EAAEC,GAAG,MAAM,aAAarF,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE;IAClDkB,GAAG,EAAEjF,UAAU,CAACc,MAAM,EAAEmE,GAAG,CAAC;IAC5BvE,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAEsE,eAAe,EAAE;MAChD,YAAY,EAAEpB;IAChB,CAAC,CAAC;IACFsB,KAAK,EAAEhD;EACT,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,MAAMiD,cAAc,GAAGA,CAACxE,MAAM,EAAEyE,IAAI,KAAK;EACvC,IAAIb,EAAE;EACN,MAAM;IACJ3D;EACF,CAAC,GAAGwE,IAAI;EACR;EACA,IAAIxE,SAAS,KAAK,UAAU,IAAI,EAAE,CAAC2D,EAAE,GAAG5D,MAAM,CAAC0E,aAAa,CAAC,OAAO,CAAC,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,OAAO,CAAC,EAAE;IACvH;EACF;EACA;EACA,MAAMX,MAAM,GAAGY,QAAQ,CAACxB,aAAa,CAAC,KAAK,CAAC;EAC5CY,MAAM,CAACO,KAAK,CAACxC,QAAQ,GAAG,UAAU;EAClCiC,MAAM,CAACO,KAAK,CAAC1D,IAAI,GAAG,KAAK;EACzBmD,MAAM,CAACO,KAAK,CAACxD,GAAG,GAAG,KAAK;EACxBf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6E,YAAY,CAACb,MAAM,EAAEhE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8E,UAAU,CAAC;EAC9I,MAAMC,WAAW,GAAGzF,iBAAiB,CAAC,CAAC;EACvC,IAAI0F,eAAe,GAAG,IAAI;EAC1B,SAAS9E,eAAeA,CAAA,EAAG;IACzB,OAAO8E,eAAe;EACxB;EACAA,eAAe,GAAGD,WAAW,CAAC,aAAa9F,KAAK,CAACmE,aAAa,CAACvD,UAAU,EAAEoF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,IAAI,EAAE;IACjGzE,MAAM,EAAEA,MAAM;IACdE,eAAe,EAAEA;EACnB,CAAC,CAAC,CAAC,EAAE8D,MAAM,CAAC;AACd,CAAC;AACD,eAAeQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
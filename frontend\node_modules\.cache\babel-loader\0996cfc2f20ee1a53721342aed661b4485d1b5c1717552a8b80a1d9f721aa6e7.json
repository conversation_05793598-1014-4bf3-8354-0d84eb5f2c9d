{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { getRealPlacement } from \"../utils/uiUtil\";\nimport PickerContext from \"../PickerInput/context\";\nvar BUILT_IN_PLACEMENTS = {\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  }\n};\nfunction PickerTrigger(_ref) {\n  var popupElement = _ref.popupElement,\n    popupStyle = _ref.popupStyle,\n    popupClassName = _ref.popupClassName,\n    popupAlign = _ref.popupAlign,\n    transitionName = _ref.transitionName,\n    getPopupContainer = _ref.getPopupContainer,\n    children = _ref.children,\n    range = _ref.range,\n    placement = _ref.placement,\n    _ref$builtinPlacement = _ref.builtinPlacements,\n    builtinPlacements = _ref$builtinPlacement === void 0 ? BUILT_IN_PLACEMENTS : _ref$builtinPlacement,\n    direction = _ref.direction,\n    visible = _ref.visible,\n    onClose = _ref.onClose;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls;\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var realPlacement = getRealPlacement(placement, direction === 'rtl');\n  return /*#__PURE__*/React.createElement(Trigger, {\n    showAction: [],\n    hideAction: ['click'],\n    popupPlacement: realPlacement,\n    builtinPlacements: builtinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: transitionName,\n    popup: popupElement,\n    popupAlign: popupAlign,\n    popupVisible: visible,\n    popupClassName: classNames(popupClassName, _defineProperty(_defineProperty({}, \"\".concat(dropdownPrefixCls, \"-range\"), range), \"\".concat(dropdownPrefixCls, \"-rtl\"), direction === 'rtl')),\n    popupStyle: popupStyle,\n    stretch: \"minWidth\",\n    getPopupContainer: getPopupContainer,\n    onPopupVisibleChange: function onPopupVisibleChange(nextVisible) {\n      if (!nextVisible) {\n        onClose();\n      }\n    }\n  }, children);\n}\nexport default PickerTrigger;", "map": {"version": 3, "names": ["_defineProperty", "<PERSON><PERSON>", "classNames", "React", "getRealPlacement", "<PERSON>er<PERSON>ontext", "BUILT_IN_PLACEMENTS", "bottomLeft", "points", "offset", "overflow", "adjustX", "adjustY", "bottomRight", "topLeft", "topRight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "popupElement", "popupStyle", "popupClassName", "popupAlign", "transitionName", "getPopupContainer", "children", "range", "placement", "_ref$builtinPlacement", "builtinPlacements", "direction", "visible", "onClose", "_React$useContext", "useContext", "prefixCls", "dropdownPrefixCls", "concat", "realPlacement", "createElement", "showAction", "hideAction", "popupPlacement", "popupTransitionName", "popup", "popupVisible", "stretch", "onPopupVisibleChange", "nextVisible"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerTrigger/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { getRealPlacement } from \"../utils/uiUtil\";\nimport PickerContext from \"../PickerInput/context\";\nvar BUILT_IN_PLACEMENTS = {\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  }\n};\nfunction PickerTrigger(_ref) {\n  var popupElement = _ref.popupElement,\n    popupStyle = _ref.popupStyle,\n    popupClassName = _ref.popupClassName,\n    popupAlign = _ref.popupAlign,\n    transitionName = _ref.transitionName,\n    getPopupContainer = _ref.getPopupContainer,\n    children = _ref.children,\n    range = _ref.range,\n    placement = _ref.placement,\n    _ref$builtinPlacement = _ref.builtinPlacements,\n    builtinPlacements = _ref$builtinPlacement === void 0 ? BUILT_IN_PLACEMENTS : _ref$builtinPlacement,\n    direction = _ref.direction,\n    visible = _ref.visible,\n    onClose = _ref.onClose;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls;\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var realPlacement = getRealPlacement(placement, direction === 'rtl');\n  return /*#__PURE__*/React.createElement(Trigger, {\n    showAction: [],\n    hideAction: ['click'],\n    popupPlacement: realPlacement,\n    builtinPlacements: builtinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: transitionName,\n    popup: popupElement,\n    popupAlign: popupAlign,\n    popupVisible: visible,\n    popupClassName: classNames(popupClassName, _defineProperty(_defineProperty({}, \"\".concat(dropdownPrefixCls, \"-range\"), range), \"\".concat(dropdownPrefixCls, \"-rtl\"), direction === 'rtl')),\n    popupStyle: popupStyle,\n    stretch: \"minWidth\",\n    getPopupContainer: getPopupContainer,\n    onPopupVisibleChange: function onPopupVisibleChange(nextVisible) {\n      if (!nextVisible) {\n        onClose();\n      }\n    }\n  }, children);\n}\nexport default PickerTrigger;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,IAAIC,mBAAmB,GAAG;EACxBC,UAAU,EAAE;IACVC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,WAAW,EAAE;IACXL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDE,OAAO,EAAE;IACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC;AACD,SAASI,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IAClCC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,cAAc,GAAGL,IAAI,CAACK,cAAc;IACpCC,iBAAiB,GAAGN,IAAI,CAACM,iBAAiB;IAC1CC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,KAAK,GAAGR,IAAI,CAACQ,KAAK;IAClBC,SAAS,GAAGT,IAAI,CAACS,SAAS;IAC1BC,qBAAqB,GAAGV,IAAI,CAACW,iBAAiB;IAC9CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrB,mBAAmB,GAAGqB,qBAAqB;IAClGE,SAAS,GAAGZ,IAAI,CAACY,SAAS;IAC1BC,OAAO,GAAGb,IAAI,CAACa,OAAO;IACtBC,OAAO,GAAGd,IAAI,CAACc,OAAO;EACxB,IAAIC,iBAAiB,GAAG7B,KAAK,CAAC8B,UAAU,CAAC5B,aAAa,CAAC;IACrD6B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,IAAIC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACF,SAAS,EAAE,WAAW,CAAC;EACzD,IAAIG,aAAa,GAAGjC,gBAAgB,CAACsB,SAAS,EAAEG,SAAS,KAAK,KAAK,CAAC;EACpE,OAAO,aAAa1B,KAAK,CAACmC,aAAa,CAACrC,OAAO,EAAE;IAC/CsC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,CAAC,OAAO,CAAC;IACrBC,cAAc,EAAEJ,aAAa;IAC7BT,iBAAiB,EAAEA,iBAAiB;IACpCM,SAAS,EAAEC,iBAAiB;IAC5BO,mBAAmB,EAAEpB,cAAc;IACnCqB,KAAK,EAAEzB,YAAY;IACnBG,UAAU,EAAEA,UAAU;IACtBuB,YAAY,EAAEd,OAAO;IACrBV,cAAc,EAAElB,UAAU,CAACkB,cAAc,EAAEpB,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoC,MAAM,CAACD,iBAAiB,EAAE,QAAQ,CAAC,EAAEV,KAAK,CAAC,EAAE,EAAE,CAACW,MAAM,CAACD,iBAAiB,EAAE,MAAM,CAAC,EAAEN,SAAS,KAAK,KAAK,CAAC,CAAC;IAC1LV,UAAU,EAAEA,UAAU;IACtB0B,OAAO,EAAE,UAAU;IACnBtB,iBAAiB,EAAEA,iBAAiB;IACpCuB,oBAAoB,EAAE,SAASA,oBAAoBA,CAACC,WAAW,EAAE;MAC/D,IAAI,CAACA,WAAW,EAAE;QAChBhB,OAAO,CAAC,CAAC;MACX;IACF;EACF,CAAC,EAAEP,QAAQ,CAAC;AACd;AACA,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
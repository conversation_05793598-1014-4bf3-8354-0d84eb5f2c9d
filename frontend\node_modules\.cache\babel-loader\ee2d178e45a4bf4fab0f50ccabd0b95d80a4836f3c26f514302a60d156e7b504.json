{"ast": null, "code": "import { isValidElement, useMemo } from 'react';\nconst useTooltipProps = (tooltip, editConfigText, children) => useMemo(() => {\n  if (tooltip === true) {\n    return {\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    };\n  }\n  if (/*#__PURE__*/isValidElement(tooltip)) {\n    return {\n      title: tooltip\n    };\n  }\n  if (typeof tooltip === 'object') {\n    return Object.assign({\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    }, tooltip);\n  }\n  return {\n    title: tooltip\n  };\n}, [tooltip, editConfigText, children]);\nexport default useTooltipProps;", "map": {"version": 3, "names": ["isValidElement", "useMemo", "useTooltipProps", "tooltip", "editConfigText", "children", "title", "Object", "assign"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/hooks/useTooltipProps.js"], "sourcesContent": ["import { isValidElement, useMemo } from 'react';\nconst useTooltipProps = (tooltip, editConfigText, children) => useMemo(() => {\n  if (tooltip === true) {\n    return {\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    };\n  }\n  if (/*#__PURE__*/isValidElement(tooltip)) {\n    return {\n      title: tooltip\n    };\n  }\n  if (typeof tooltip === 'object') {\n    return Object.assign({\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    }, tooltip);\n  }\n  return {\n    title: tooltip\n  };\n}, [tooltip, editConfigText, children]);\nexport default useTooltipProps;"], "mappings": "AAAA,SAASA,cAAc,EAAEC,OAAO,QAAQ,OAAO;AAC/C,MAAMC,eAAe,GAAGA,CAACC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,KAAKJ,OAAO,CAAC,MAAM;EAC3E,IAAIE,OAAO,KAAK,IAAI,EAAE;IACpB,OAAO;MACLG,KAAK,EAAEF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC;IACjF,CAAC;EACH;EACA,IAAI,aAAaL,cAAc,CAACG,OAAO,CAAC,EAAE;IACxC,OAAO;MACLG,KAAK,EAAEH;IACT,CAAC;EACH;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOI,MAAM,CAACC,MAAM,CAAC;MACnBF,KAAK,EAAEF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC;IACjF,CAAC,EAAEF,OAAO,CAAC;EACb;EACA,OAAO;IACLG,KAAK,EAAEH;EACT,CAAC;AACH,CAAC,EAAE,CAACA,OAAO,EAAEC,cAAc,EAAEC,QAAQ,CAAC,CAAC;AACvC,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function findValidateTime(date, getHourUnits, getMinuteUnits, getSecondUnits, getMillisecondUnits, generateConfig) {\n  var nextDate = date;\n  function alignValidate(getUnitValue, setUnitValue, units) {\n    var nextValue = generateConfig[getUnitValue](nextDate);\n    var nextUnit = units.find(function (unit) {\n      return unit.value === nextValue;\n    });\n    if (!nextUnit || nextUnit.disabled) {\n      // Find most closest unit\n      var validateUnits = units.filter(function (unit) {\n        return !unit.disabled;\n      });\n      var reverseEnabledUnits = _toConsumableArray(validateUnits).reverse();\n      var validateUnit = reverseEnabledUnits.find(function (unit) {\n        return unit.value <= nextValue;\n      }) || validateUnits[0];\n      if (validateUnit) {\n        nextValue = validateUnit.value;\n        nextDate = generateConfig[setUnitValue](nextDate, nextValue);\n      }\n    }\n    return nextValue;\n  }\n\n  // Find validate hour\n  var nextHour = alignValidate('getHour', 'setHour', getHourUnits());\n\n  // Find validate minute\n  var nextMinute = alignValidate('getMinute', 'setMinute', getMinuteUnits(nextHour));\n\n  // Find validate second\n  var nextSecond = alignValidate('getSecond', 'setSecond', getSecondUnits(nextHour, nextMinute));\n\n  // Find validate millisecond\n  alignValidate('getMillisecond', 'setMillisecond', getMillisecondUnits(nextHour, nextMinute, nextSecond));\n  return nextDate;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "findValidateTime", "date", "getHourUnits", "getMinuteUnits", "getSecondUnits", "getMillisecondUnits", "generateConfig", "nextDate", "alignValidate", "getUnitValue", "setUnitValue", "units", "nextValue", "nextUnit", "find", "unit", "value", "disabled", "validateUnits", "filter", "reverseEnabledUnits", "reverse", "validateUnit", "nextHour", "nextMinute", "nextSecond"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerPanel/TimePanel/TimePanelBody/util.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function findValidateTime(date, getHourUnits, getMinuteUnits, getSecondUnits, getMillisecondUnits, generateConfig) {\n  var nextDate = date;\n  function alignValidate(getUnitValue, setUnitValue, units) {\n    var nextValue = generateConfig[getUnitValue](nextDate);\n    var nextUnit = units.find(function (unit) {\n      return unit.value === nextValue;\n    });\n    if (!nextUnit || nextUnit.disabled) {\n      // Find most closest unit\n      var validateUnits = units.filter(function (unit) {\n        return !unit.disabled;\n      });\n      var reverseEnabledUnits = _toConsumableArray(validateUnits).reverse();\n      var validateUnit = reverseEnabledUnits.find(function (unit) {\n        return unit.value <= nextValue;\n      }) || validateUnits[0];\n      if (validateUnit) {\n        nextValue = validateUnit.value;\n        nextDate = generateConfig[setUnitValue](nextDate, nextValue);\n      }\n    }\n    return nextValue;\n  }\n\n  // Find validate hour\n  var nextHour = alignValidate('getHour', 'setHour', getHourUnits());\n\n  // Find validate minute\n  var nextMinute = alignValidate('getMinute', 'setMinute', getMinuteUnits(nextHour));\n\n  // Find validate second\n  var nextSecond = alignValidate('getSecond', 'setSecond', getSecondUnits(nextHour, nextMinute));\n\n  // Find validate millisecond\n  alignValidate('getMillisecond', 'setMillisecond', getMillisecondUnits(nextHour, nextMinute, nextSecond));\n  return nextDate;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,cAAc,EAAE;EACxH,IAAIC,QAAQ,GAAGN,IAAI;EACnB,SAASO,aAAaA,CAACC,YAAY,EAAEC,YAAY,EAAEC,KAAK,EAAE;IACxD,IAAIC,SAAS,GAAGN,cAAc,CAACG,YAAY,CAAC,CAACF,QAAQ,CAAC;IACtD,IAAIM,QAAQ,GAAGF,KAAK,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;MACxC,OAAOA,IAAI,CAACC,KAAK,KAAKJ,SAAS;IACjC,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACI,QAAQ,EAAE;MAClC;MACA,IAAIC,aAAa,GAAGP,KAAK,CAACQ,MAAM,CAAC,UAAUJ,IAAI,EAAE;QAC/C,OAAO,CAACA,IAAI,CAACE,QAAQ;MACvB,CAAC,CAAC;MACF,IAAIG,mBAAmB,GAAGrB,kBAAkB,CAACmB,aAAa,CAAC,CAACG,OAAO,CAAC,CAAC;MACrE,IAAIC,YAAY,GAAGF,mBAAmB,CAACN,IAAI,CAAC,UAAUC,IAAI,EAAE;QAC1D,OAAOA,IAAI,CAACC,KAAK,IAAIJ,SAAS;MAChC,CAAC,CAAC,IAAIM,aAAa,CAAC,CAAC,CAAC;MACtB,IAAII,YAAY,EAAE;QAChBV,SAAS,GAAGU,YAAY,CAACN,KAAK;QAC9BT,QAAQ,GAAGD,cAAc,CAACI,YAAY,CAAC,CAACH,QAAQ,EAAEK,SAAS,CAAC;MAC9D;IACF;IACA,OAAOA,SAAS;EAClB;;EAEA;EACA,IAAIW,QAAQ,GAAGf,aAAa,CAAC,SAAS,EAAE,SAAS,EAAEN,YAAY,CAAC,CAAC,CAAC;;EAElE;EACA,IAAIsB,UAAU,GAAGhB,aAAa,CAAC,WAAW,EAAE,WAAW,EAAEL,cAAc,CAACoB,QAAQ,CAAC,CAAC;;EAElF;EACA,IAAIE,UAAU,GAAGjB,aAAa,CAAC,WAAW,EAAE,WAAW,EAAEJ,cAAc,CAACmB,QAAQ,EAAEC,UAAU,CAAC,CAAC;;EAE9F;EACAhB,aAAa,CAAC,gBAAgB,EAAE,gBAAgB,EAAEH,mBAAmB,CAACkB,QAAQ,EAAEC,UAAU,EAAEC,UAAU,CAAC,CAAC;EACxG,OAAOlB,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
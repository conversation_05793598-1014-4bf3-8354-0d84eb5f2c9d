{"ast": null, "code": "var _jsxFileName = \"D:\\\\code\\\\erp1\\\\frontend\\\\src\\\\components\\\\OrderForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Select, DatePicker, InputNumber, Button, Table, Row, Col, Card, Divider, message, Space, Tooltip } from 'antd';\nimport { PlusOutlined, DeleteOutlined, UserAddOutlined, EditOutlined } from '@ant-design/icons';\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { orderAPI, customerAPI, productAPI } from '../services/api';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst OrderForm = ({\n  visible,\n  order,\n  onCancel,\n  onSuccess\n}) => {\n  _s();\n  var _customersData$data, _customersData$data$c;\n  const [form] = Form.useForm();\n  const [customerForm] = Form.useForm();\n  const queryClient = useQueryClient();\n\n  // 状态管理\n  const [orderItems, setOrderItems] = useState([]);\n  const [totals, setTotals] = useState({\n    subtotal: 0,\n    taxAmount: 0,\n    totalAmount: 0\n  });\n  const [customerModalVisible, setCustomerModalVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // 获取客户列表\n  const {\n    data: customersData\n  } = useQuery({\n    queryKey: ['customers', {\n      limit: 1000\n    }],\n    queryFn: () => customerAPI.getCustomers({\n      limit: 1000\n    })\n  });\n\n  // 获取产品列表\n  const {\n    data: productsData\n  } = useQuery({\n    queryKey: ['products', {\n      limit: 1000\n    }],\n    queryFn: () => productAPI.getProducts({\n      limit: 1000\n    })\n  });\n\n  // 创建订单\n  const createOrderMutation = useMutation({\n    mutationFn: orderAPI.createOrder,\n    onSuccess: () => {\n      message.success('订单创建成功');\n      onSuccess();\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '创建失败');\n    }\n  });\n\n  // 更新订单\n  const updateOrderMutation = useMutation({\n    mutationFn: ({\n      id,\n      data\n    }) => orderAPI.updateOrder(id, data),\n    onSuccess: () => {\n      message.success('订单更新成功');\n      onSuccess();\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '更新失败');\n    }\n  });\n\n  // 创建客户\n  const createCustomerMutation = useMutation({\n    mutationFn: customerAPI.createCustomer,\n    onSuccess: response => {\n      message.success('客户创建成功');\n      setCustomerModalVisible(false);\n      customerForm.resetFields();\n      // 刷新客户列表\n      queryClient.invalidateQueries(['customers']);\n      // 自动选择新创建的客户\n      form.setFieldValue('customer_id', response.data.customer.id);\n    },\n    onError: error => {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '创建客户失败');\n    }\n  });\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (order) {\n      // 编辑模式\n      form.setFieldsValue({\n        customer_id: order.customer_id,\n        delivery_date: order.delivery_date ? dayjs(order.delivery_date) : null,\n        priority: order.priority,\n        discount_amount: order.discount_amount,\n        shipping_cost: order.shipping_cost,\n        payment_method: order.payment_method,\n        shipping_address: order.shipping_address,\n        tracking_number: order.tracking_number,\n        notes: order.notes\n      });\n      if (order.items) {\n        setOrderItems(order.items.map(item => {\n          var _item$product, _item$product2, _item$product3;\n          return {\n            key: item.id,\n            product_id: item.product_id,\n            product_name: (_item$product = item.product) === null || _item$product === void 0 ? void 0 : _item$product.name,\n            product_code: (_item$product2 = item.product) === null || _item$product2 === void 0 ? void 0 : _item$product2.product_code,\n            unit: (_item$product3 = item.product) === null || _item$product3 === void 0 ? void 0 : _item$product3.unit,\n            quantity: item.quantity,\n            unit_price: Number(item.unit_price),\n            unit_cost: Number(item.unit_cost),\n            discount_rate: Number(item.discount_rate),\n            discount_amount: Number(item.discount_amount),\n            tax_rate: Number(item.tax_rate),\n            tax_amount: Number(item.tax_amount),\n            line_total: Number(item.line_total),\n            notes: item.notes\n          };\n        }));\n      }\n    } else {\n      // 新建模式\n      form.resetFields();\n      setOrderItems([]);\n      form.setFieldsValue({\n        priority: 'normal',\n        discount_amount: 0,\n        shipping_cost: 0\n      });\n    }\n  }, [order, form]);\n\n  // 计算订单总额\n  const calculateTotals = (items, discountAmount = 0, shippingCost = 0) => {\n    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);\n    const taxAmount = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);\n    const totalAmount = subtotal + shippingCost - discountAmount;\n    setTotals({\n      subtotal,\n      taxAmount,\n      totalAmount\n    });\n    return {\n      subtotal,\n      taxAmount,\n      totalAmount\n    };\n  };\n\n  // 计算行总计\n  const calculateLineTotal = item => {\n    const {\n      quantity = 0,\n      unit_price = 0,\n      discount_rate = 0,\n      tax_rate = 0\n    } = item;\n    const subtotal = quantity * unit_price;\n    const discountAmount = subtotal * (discount_rate / 100);\n    const afterDiscount = subtotal - discountAmount;\n    const taxAmount = afterDiscount * (tax_rate / 100);\n    const lineTotal = afterDiscount + taxAmount;\n    return {\n      discount_amount: discountAmount,\n      tax_amount: taxAmount,\n      line_total: lineTotal\n    };\n  };\n\n  // 添加订单项\n  const addOrderItem = () => {\n    const newItem = {\n      key: Date.now(),\n      product_id: null,\n      product_name: '',\n      product_code: '',\n      unit: '',\n      quantity: 1,\n      unit_price: 0,\n      unit_cost: 0,\n      discount_rate: 0,\n      discount_amount: 0,\n      tax_rate: 0,\n      tax_amount: 0,\n      line_total: 0,\n      notes: ''\n    };\n    const newItems = [...orderItems, newItem];\n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 删除订单项\n  const removeOrderItem = key => {\n    const newItems = orderItems.filter(item => item.key !== key);\n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 更新订单项\n  const updateOrderItem = (key, field, value) => {\n    const newItems = orderItems.map(item => {\n      if (item.key === key) {\n        const updatedItem = {\n          ...item,\n          [field]: value\n        };\n\n        // 如果是产品选择，自动填充产品信息\n        if (field === 'product_id' && value) {\n          var _productsData$data, _productsData$data$pr;\n          const product = productsData === null || productsData === void 0 ? void 0 : (_productsData$data = productsData.data) === null || _productsData$data === void 0 ? void 0 : (_productsData$data$pr = _productsData$data.products) === null || _productsData$data$pr === void 0 ? void 0 : _productsData$data$pr.find(p => p.id === value);\n          if (product) {\n            updatedItem.product_name = product.name;\n            updatedItem.product_code = product.product_code;\n            updatedItem.unit = product.unit;\n            updatedItem.unit_price = Number(product.selling_price || 0);\n            updatedItem.unit_cost = Number(product.cost_price || 0);\n          }\n        }\n\n        // 重新计算行总计\n        if (['quantity', 'unit_price', 'discount_rate', 'tax_rate'].includes(field)) {\n          const calculated = calculateLineTotal(updatedItem);\n          Object.assign(updatedItem, calculated);\n        }\n        return updatedItem;\n      }\n      return item;\n    });\n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 表单提交\n  const handleSubmit = async () => {\n    try {\n      var _values$delivery_date;\n      const values = await form.validateFields();\n      if (orderItems.length === 0) {\n        message.error('请至少添加一个订单项');\n        return;\n      }\n\n      // 验证订单项\n      const invalidItems = orderItems.filter(item => !item.product_id && !item.product_name || item.quantity <= 0 || item.unit_price < 0);\n      if (invalidItems.length > 0) {\n        message.error('请检查订单项信息是否完整（产品名称、数量、单价）');\n        return;\n      }\n      const orderData = {\n        ...values,\n        delivery_date: (_values$delivery_date = values.delivery_date) === null || _values$delivery_date === void 0 ? void 0 : _values$delivery_date.format('YYYY-MM-DD'),\n        items: orderItems.map(item => ({\n          product_id: item.product_id,\n          product_name: item.product_name,\n          product_code: item.product_code,\n          unit: item.unit,\n          quantity: item.quantity,\n          unit_price: item.unit_price,\n          unit_cost: item.unit_cost,\n          discount_rate: item.discount_rate,\n          tax_rate: item.tax_rate,\n          notes: item.notes\n        }))\n      };\n      if (order) {\n        updateOrderMutation.mutate({\n          id: order.id,\n          data: orderData\n        });\n      } else {\n        createOrderMutation.mutate(orderData);\n      }\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  // 订单项表格列定义\n  const itemColumns = [{\n    title: '产品',\n    dataIndex: 'product_id',\n    width: 200,\n    render: (value, record) => {\n      var _productsData$data2, _productsData$data2$p;\n      return /*#__PURE__*/_jsxDEV(Space.Compact, {\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Select, {\n          value: value,\n          placeholder: \"\\u9009\\u62E9\\u4EA7\\u54C1\",\n          style: {\n            width: '85%'\n          },\n          showSearch: true,\n          optionFilterProp: \"children\",\n          allowClear: true,\n          onChange: val => updateOrderItem(record.key, 'product_id', val),\n          children: productsData === null || productsData === void 0 ? void 0 : (_productsData$data2 = productsData.data) === null || _productsData$data2 === void 0 ? void 0 : (_productsData$data2$p = _productsData$data2.products) === null || _productsData$data2$p === void 0 ? void 0 : _productsData$data2$p.map(product => /*#__PURE__*/_jsxDEV(Option, {\n            value: product.id,\n            children: [product.product_code, \" - \", product.name]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u624B\\u52A8\\u7F16\\u8F91\\u4EA7\\u54C1\\u4FE1\\u606F\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 21\n            }, this),\n            style: {\n              width: '15%'\n            },\n            onClick: () => setEditingProduct(record.key)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: '产品名称',\n    dataIndex: 'product_name',\n    width: 150,\n    render: (value, record) => editingProduct === record.key ? /*#__PURE__*/_jsxDEV(Input, {\n      value: value,\n      placeholder: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n      onChange: e => updateOrderItem(record.key, 'product_name', e.target.value),\n      onBlur: () => setEditingProduct(null),\n      autoFocus: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n      onClick: () => setEditingProduct(record.key),\n      style: {\n        cursor: 'pointer'\n      },\n      children: value || '点击编辑'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '产品编码',\n    dataIndex: 'product_code',\n    width: 120,\n    render: (value, record) => editingProduct === record.key ? /*#__PURE__*/_jsxDEV(Input, {\n      value: value,\n      placeholder: \"\\u4EA7\\u54C1\\u7F16\\u7801\",\n      onChange: e => updateOrderItem(record.key, 'product_code', e.target.value),\n      onBlur: () => setEditingProduct(null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n      onClick: () => setEditingProduct(record.key),\n      style: {\n        cursor: 'pointer'\n      },\n      children: value || '点击编辑'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '单位',\n    dataIndex: 'unit',\n    width: 80,\n    render: (value, record) => editingProduct === record.key ? /*#__PURE__*/_jsxDEV(Input, {\n      value: value,\n      placeholder: \"\\u5355\\u4F4D\",\n      onChange: e => updateOrderItem(record.key, 'unit', e.target.value),\n      onBlur: () => setEditingProduct(null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n      onClick: () => setEditingProduct(record.key),\n      style: {\n        cursor: 'pointer'\n      },\n      children: value || '件'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    width: 100,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: value,\n      min: 1,\n      style: {\n        width: '100%'\n      },\n      onChange: val => updateOrderItem(record.key, 'quantity', val || 1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '单价',\n    dataIndex: 'unit_price',\n    width: 120,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: value,\n      min: 0,\n      precision: 2,\n      style: {\n        width: '100%'\n      },\n      onChange: val => updateOrderItem(record.key, 'unit_price', val || 0)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '折扣率(%)',\n    dataIndex: 'discount_rate',\n    width: 100,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: value,\n      min: 0,\n      max: 100,\n      precision: 2,\n      style: {\n        width: '100%'\n      },\n      onChange: val => updateOrderItem(record.key, 'discount_rate', val || 0)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '税率(%)',\n    dataIndex: 'tax_rate',\n    width: 100,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: value,\n      min: 0,\n      max: 100,\n      precision: 2,\n      style: {\n        width: '100%'\n      },\n      onChange: val => updateOrderItem(record.key, 'tax_rate', val || 0)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '行总计',\n    dataIndex: 'line_total',\n    width: 120,\n    render: value => `¥${Number(value || 0).toFixed(2)}`\n  }, {\n    title: '操作',\n    width: 80,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"text\",\n      danger: true,\n      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 17\n      }, this),\n      onClick: () => removeOrderItem(record.key)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: order ? '编辑订单' : '新建订单',\n    open: visible,\n    onCancel: onCancel,\n    width: 1200,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      onClick: onCancel,\n      children: \"\\u53D6\\u6D88\"\n    }, \"cancel\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      loading: createOrderMutation.isLoading || updateOrderMutation.isLoading,\n      onClick: handleSubmit,\n      children: order ? '更新' : '创建'\n    }, \"submit\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 9\n    }, this)],\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      initialValues: {\n        priority: 'normal',\n        discount_amount: 0,\n        shipping_cost: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"customer_id\",\n            label: /*#__PURE__*/_jsxDEV(Space, {\n              children: [\"\\u5BA2\\u6237\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u5FEB\\u901F\\u521B\\u5EFA\\u5BA2\\u6237\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 29\n                  }, this),\n                  onClick: () => setCustomerModalVisible(true),\n                  children: \"\\u65B0\\u5EFA\\u5BA2\\u6237\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this),\n            rules: [{\n              required: true,\n              message: '请选择客户'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u9009\\u62E9\\u5BA2\\u6237\",\n              showSearch: true,\n              optionFilterProp: \"children\",\n              children: customersData === null || customersData === void 0 ? void 0 : (_customersData$data = customersData.data) === null || _customersData$data === void 0 ? void 0 : (_customersData$data$c = _customersData$data.customers) === null || _customersData$data$c === void 0 ? void 0 : _customersData$data$c.map(customer => /*#__PURE__*/_jsxDEV(Option, {\n                value: customer.id,\n                children: [customer.company_name, \" - \", customer.contact_person]\n              }, customer.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"delivery_date\",\n            label: \"\\u4EA4\\u4ED8\\u65E5\\u671F\",\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"priority\",\n            label: \"\\u4F18\\u5148\\u7EA7\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"low\",\n                children: \"\\u4F4E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"normal\",\n                children: \"\\u666E\\u901A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"high\",\n                children: \"\\u9AD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"urgent\",\n                children: \"\\u7D27\\u6025\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        children: \"\\u8BA2\\u5355\\u660E\\u7EC6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"dashed\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 19\n          }, this),\n          onClick: addOrderItem,\n          style: {\n            width: '100%'\n          },\n          children: \"\\u6DFB\\u52A0\\u8BA2\\u5355\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: itemColumns,\n        dataSource: orderItems,\n        pagination: false,\n        scroll: {\n          x: 800\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        children: \"\\u8D39\\u7528\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"discount_amount\",\n            label: \"\\u8BA2\\u5355\\u6298\\u6263\",\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              style: {\n                width: '100%'\n              },\n              min: 0,\n              precision: 2,\n              addonBefore: \"\\xA5\",\n              onChange: value => {\n                calculateTotals(orderItems, value || 0, form.getFieldValue('shipping_cost'));\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"shipping_cost\",\n            label: \"\\u8FD0\\u8D39\",\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              style: {\n                width: '100%'\n              },\n              min: 0,\n              precision: 2,\n              addonBefore: \"\\xA5\",\n              onChange: value => {\n                calculateTotals(orderItems, form.getFieldValue('discount_amount'), value || 0);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"payment_method\",\n            label: \"\\u4ED8\\u6B3E\\u65B9\\u5F0F\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u9009\\u62E9\\u4ED8\\u6B3E\\u65B9\\u5F0F\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"cash\",\n                children: \"\\u73B0\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"bank_transfer\",\n                children: \"\\u94F6\\u884C\\u8F6C\\u8D26\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"credit_card\",\n                children: \"\\u4FE1\\u7528\\u5361\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"check\",\n                children: \"\\u652F\\u7968\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"other\",\n                children: \"\\u5176\\u4ED6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"shipping_address\",\n            label: \"\\u6536\\u8D27\\u5730\\u5740\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              rows: 3,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u6536\\u8D27\\u5730\\u5740\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"tracking_number\",\n            label: \"\\u7269\\u6D41\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6D41\\u5355\\u53F7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"notes\",\n        label: \"\\u5907\\u6CE8\",\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 2,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        style: {\n          backgroundColor: '#fafafa'\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u5C0F\\u8BA1: \\xA5\", totals.subtotal.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u7A0E\\u989D: \\xA5\", totals.taxAmount.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u8FD0\\u8D39: \\xA5\", (form.getFieldValue('shipping_cost') || 0).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                fontSize: '16px'\n              },\n              children: [\"\\u603B\\u8BA1: \\xA5\", totals.totalAmount.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5FEB\\u901F\\u521B\\u5EFA\\u5BA2\\u6237\",\n      open: customerModalVisible,\n      onCancel: () => {\n        setCustomerModalVisible(false);\n        customerForm.resetFields();\n      },\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          setCustomerModalVisible(false);\n          customerForm.resetFields();\n        },\n        children: \"\\u53D6\\u6D88\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        loading: createCustomerMutation.isLoading,\n        onClick: () => {\n          customerForm.validateFields().then(values => {\n            createCustomerMutation.mutate(values);\n          }).catch(error => {\n            console.error('客户表单验证失败:', error);\n          });\n        },\n        children: \"\\u521B\\u5EFA\\u5BA2\\u6237\"\n      }, \"submit\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 11\n      }, this)],\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: customerForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"contact_person\",\n              label: \"\\u8054\\u7CFB\\u4EBA\",\n              rules: [{\n                required: true,\n                message: '请输入联系人'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8054\\u7CFB\\u4EBA\\u59D3\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"order_number\",\n              label: \"\\u8BA2\\u5355\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"shop_account\",\n              label: \"\\u5E97\\u94FA\\u8D26\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E97\\u94FA\\u8D26\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"shop_name\",\n              label: \"\\u5E97\\u94FA\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E97\\u94FA\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              label: \"\\u90AE\\u7BB1\",\n              rules: [{\n                type: 'email',\n                message: '请输入有效的邮箱地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"mobile\",\n              label: \"\\u624B\\u673A\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"customer_type\",\n              label: \"\\u5BA2\\u6237\\u7C7B\\u578B\",\n              initialValue: \"retail\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"retail\",\n                  children: \"\\u96F6\\u552E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"wholesale\",\n                  children: \"\\u6279\\u53D1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"distributor\",\n                  children: \"\\u5206\\u9500\\u5546\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"online\",\n                  children: \"\\u7EBF\\u4E0A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"payment_terms\",\n              label: \"\\u4ED8\\u6B3E\\u671F\\u9650(\\u5929)\",\n              initialValue: 30,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"address\",\n          label: \"\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BE6\\u7EC6\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"notes\",\n          label: \"\\u5907\\u6CE8\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 459,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderForm, \"uZqbZlyg7tPaOKgB+DSvDC9Tt38=\", false, function () {\n  return [Form.useForm, Form.useForm, useQueryClient, useQuery, useQuery, useMutation, useMutation, useMutation];\n});\n_c = OrderForm;\nexport default OrderForm;\nvar _c;\n$RefreshReg$(_c, \"OrderForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "Form", "Input", "Select", "DatePicker", "InputNumber", "<PERSON><PERSON>", "Table", "Row", "Col", "Card", "Divider", "message", "Space", "<PERSON><PERSON><PERSON>", "PlusOutlined", "DeleteOutlined", "UserAddOutlined", "EditOutlined", "useMutation", "useQuery", "useQueryClient", "orderAPI", "customerAPI", "productAPI", "dayjs", "jsxDEV", "_jsxDEV", "Option", "TextArea", "OrderForm", "visible", "order", "onCancel", "onSuccess", "_s", "_customersData$data", "_customersData$data$c", "form", "useForm", "customerForm", "queryClient", "orderItems", "setOrderItems", "totals", "setTotals", "subtotal", "taxAmount", "totalAmount", "customerModalVisible", "setCustomerModalVisible", "editingProduct", "setEditingProduct", "data", "customersData", "query<PERSON><PERSON>", "limit", "queryFn", "getCustomers", "productsData", "getProducts", "createOrderMutation", "mutationFn", "createOrder", "success", "onError", "error", "_error$response", "_error$response$data", "response", "updateOrderMutation", "id", "updateOrder", "_error$response2", "_error$response2$data", "createCustomerMutation", "createCustomer", "resetFields", "invalidateQueries", "setFieldValue", "customer", "_error$response3", "_error$response3$data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customer_id", "delivery_date", "priority", "discount_amount", "shipping_cost", "payment_method", "shipping_address", "tracking_number", "notes", "items", "map", "item", "_item$product", "_item$product2", "_item$product3", "key", "product_id", "product_name", "product", "name", "product_code", "unit", "quantity", "unit_price", "Number", "unit_cost", "discount_rate", "tax_rate", "tax_amount", "line_total", "calculateTotals", "discountAmount", "shippingCost", "reduce", "sum", "calculateLineTotal", "afterDiscount", "lineTotal", "addOrderItem", "newItem", "Date", "now", "newItems", "getFieldValue", "removeOrderItem", "filter", "updateOrderItem", "field", "value", "updatedItem", "_productsData$data", "_productsData$data$pr", "products", "find", "p", "selling_price", "cost_price", "includes", "calculated", "Object", "assign", "handleSubmit", "_values$delivery_date", "values", "validateFields", "length", "invalidItems", "orderData", "format", "mutate", "console", "itemColumns", "title", "dataIndex", "width", "render", "record", "_productsData$data2", "_productsData$data2$p", "Compact", "style", "children", "placeholder", "showSearch", "optionFilterProp", "allowClear", "onChange", "val", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "e", "target", "onBlur", "autoFocus", "cursor", "min", "precision", "max", "toFixed", "_", "type", "danger", "open", "footer", "loading", "isLoading", "layout", "initialValues", "gutter", "span", "<PERSON><PERSON>", "label", "size", "rules", "required", "customers", "company_name", "contact_person", "marginBottom", "columns", "dataSource", "pagination", "scroll", "x", "addonBefore", "rows", "backgroundColor", "fontWeight", "fontSize", "then", "catch", "initialValue", "_c", "$RefreshReg$"], "sources": ["D:/code/erp1/frontend/src/components/OrderForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Modal,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  InputNumber,\n  Button,\n  Table,\n  Row,\n  Col,\n  Card,\n  Divider,\n  message,\n  Space,\n  Tooltip\n} from 'antd';\nimport {\n  PlusOutlined,\n  DeleteOutlined,\n  UserAddOutlined,\n  EditOutlined\n} from '@ant-design/icons';\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { orderAPI, customerAPI, productAPI } from '../services/api';\nimport dayjs from 'dayjs';\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst OrderForm = ({ visible, order, onCancel, onSuccess }) => {\n  const [form] = Form.useForm();\n  const [customerForm] = Form.useForm();\n  const queryClient = useQueryClient();\n\n  // 状态管理\n  const [orderItems, setOrderItems] = useState([]);\n  const [totals, setTotals] = useState({\n    subtotal: 0,\n    taxAmount: 0,\n    totalAmount: 0\n  });\n  const [customerModalVisible, setCustomerModalVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // 获取客户列表\n  const { data: customersData } = useQuery({\n    queryKey: ['customers', { limit: 1000 }],\n    queryFn: () => customerAPI.getCustomers({ limit: 1000 })\n  });\n\n  // 获取产品列表\n  const { data: productsData } = useQuery({\n    queryKey: ['products', { limit: 1000 }],\n    queryFn: () => productAPI.getProducts({ limit: 1000 })\n  });\n\n  // 创建订单\n  const createOrderMutation = useMutation({\n    mutationFn: orderAPI.createOrder,\n    onSuccess: () => {\n      message.success('订单创建成功');\n      onSuccess();\n    },\n    onError: (error) => {\n      message.error(error.response?.data?.message || '创建失败');\n    }\n  });\n\n  // 更新订单\n  const updateOrderMutation = useMutation({\n    mutationFn: ({ id, data }) => orderAPI.updateOrder(id, data),\n    onSuccess: () => {\n      message.success('订单更新成功');\n      onSuccess();\n    },\n    onError: (error) => {\n      message.error(error.response?.data?.message || '更新失败');\n    }\n  });\n\n  // 创建客户\n  const createCustomerMutation = useMutation({\n    mutationFn: customerAPI.createCustomer,\n    onSuccess: (response) => {\n      message.success('客户创建成功');\n      setCustomerModalVisible(false);\n      customerForm.resetFields();\n      // 刷新客户列表\n      queryClient.invalidateQueries(['customers']);\n      // 自动选择新创建的客户\n      form.setFieldValue('customer_id', response.data.customer.id);\n    },\n    onError: (error) => {\n      message.error(error.response?.data?.message || '创建客户失败');\n    }\n  });\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (order) {\n      // 编辑模式\n      form.setFieldsValue({\n        customer_id: order.customer_id,\n        delivery_date: order.delivery_date ? dayjs(order.delivery_date) : null,\n        priority: order.priority,\n        discount_amount: order.discount_amount,\n        shipping_cost: order.shipping_cost,\n        payment_method: order.payment_method,\n        shipping_address: order.shipping_address,\n        tracking_number: order.tracking_number,\n        notes: order.notes\n      });\n      \n      if (order.items) {\n        setOrderItems(order.items.map(item => ({\n          key: item.id,\n          product_id: item.product_id,\n          product_name: item.product?.name,\n          product_code: item.product?.product_code,\n          unit: item.product?.unit,\n          quantity: item.quantity,\n          unit_price: Number(item.unit_price),\n          unit_cost: Number(item.unit_cost),\n          discount_rate: Number(item.discount_rate),\n          discount_amount: Number(item.discount_amount),\n          tax_rate: Number(item.tax_rate),\n          tax_amount: Number(item.tax_amount),\n          line_total: Number(item.line_total),\n          notes: item.notes\n        })));\n      }\n    } else {\n      // 新建模式\n      form.resetFields();\n      setOrderItems([]);\n      form.setFieldsValue({\n        priority: 'normal',\n        discount_amount: 0,\n        shipping_cost: 0\n      });\n    }\n  }, [order, form]);\n\n  // 计算订单总额\n  const calculateTotals = (items, discountAmount = 0, shippingCost = 0) => {\n    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);\n    const taxAmount = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);\n    const totalAmount = subtotal + shippingCost - discountAmount;\n    \n    setTotals({\n      subtotal,\n      taxAmount,\n      totalAmount\n    });\n    \n    return { subtotal, taxAmount, totalAmount };\n  };\n\n  // 计算行总计\n  const calculateLineTotal = (item) => {\n    const { quantity = 0, unit_price = 0, discount_rate = 0, tax_rate = 0 } = item;\n    const subtotal = quantity * unit_price;\n    const discountAmount = subtotal * (discount_rate / 100);\n    const afterDiscount = subtotal - discountAmount;\n    const taxAmount = afterDiscount * (tax_rate / 100);\n    const lineTotal = afterDiscount + taxAmount;\n    \n    return {\n      discount_amount: discountAmount,\n      tax_amount: taxAmount,\n      line_total: lineTotal\n    };\n  };\n\n  // 添加订单项\n  const addOrderItem = () => {\n    const newItem = {\n      key: Date.now(),\n      product_id: null,\n      product_name: '',\n      product_code: '',\n      unit: '',\n      quantity: 1,\n      unit_price: 0,\n      unit_cost: 0,\n      discount_rate: 0,\n      discount_amount: 0,\n      tax_rate: 0,\n      tax_amount: 0,\n      line_total: 0,\n      notes: ''\n    };\n    \n    const newItems = [...orderItems, newItem];\n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 删除订单项\n  const removeOrderItem = (key) => {\n    const newItems = orderItems.filter(item => item.key !== key);\n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 更新订单项\n  const updateOrderItem = (key, field, value) => {\n    const newItems = orderItems.map(item => {\n      if (item.key === key) {\n        const updatedItem = { ...item, [field]: value };\n        \n        // 如果是产品选择，自动填充产品信息\n        if (field === 'product_id' && value) {\n          const product = productsData?.data?.products?.find(p => p.id === value);\n          if (product) {\n            updatedItem.product_name = product.name;\n            updatedItem.product_code = product.product_code;\n            updatedItem.unit = product.unit;\n            updatedItem.unit_price = Number(product.selling_price || 0);\n            updatedItem.unit_cost = Number(product.cost_price || 0);\n          }\n        }\n        \n        // 重新计算行总计\n        if (['quantity', 'unit_price', 'discount_rate', 'tax_rate'].includes(field)) {\n          const calculated = calculateLineTotal(updatedItem);\n          Object.assign(updatedItem, calculated);\n        }\n        \n        return updatedItem;\n      }\n      return item;\n    });\n    \n    setOrderItems(newItems);\n    calculateTotals(newItems, form.getFieldValue('discount_amount'), form.getFieldValue('shipping_cost'));\n  };\n\n  // 表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (orderItems.length === 0) {\n        message.error('请至少添加一个订单项');\n        return;\n      }\n      \n      // 验证订单项\n      const invalidItems = orderItems.filter(item =>\n        (!item.product_id && !item.product_name) ||\n        item.quantity <= 0 ||\n        item.unit_price < 0\n      );\n\n      if (invalidItems.length > 0) {\n        message.error('请检查订单项信息是否完整（产品名称、数量、单价）');\n        return;\n      }\n      \n      const orderData = {\n        ...values,\n        delivery_date: values.delivery_date?.format('YYYY-MM-DD'),\n        items: orderItems.map(item => ({\n          product_id: item.product_id,\n          product_name: item.product_name,\n          product_code: item.product_code,\n          unit: item.unit,\n          quantity: item.quantity,\n          unit_price: item.unit_price,\n          unit_cost: item.unit_cost,\n          discount_rate: item.discount_rate,\n          tax_rate: item.tax_rate,\n          notes: item.notes\n        }))\n      };\n      \n      if (order) {\n        updateOrderMutation.mutate({ id: order.id, data: orderData });\n      } else {\n        createOrderMutation.mutate(orderData);\n      }\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  // 订单项表格列定义\n  const itemColumns = [\n    {\n      title: '产品',\n      dataIndex: 'product_id',\n      width: 200,\n      render: (value, record) => (\n        <Space.Compact style={{ width: '100%' }}>\n          <Select\n            value={value}\n            placeholder=\"选择产品\"\n            style={{ width: '85%' }}\n            showSearch\n            optionFilterProp=\"children\"\n            allowClear\n            onChange={(val) => updateOrderItem(record.key, 'product_id', val)}\n          >\n            {productsData?.data?.products?.map(product => (\n              <Option key={product.id} value={product.id}>\n                {product.product_code} - {product.name}\n              </Option>\n            ))}\n          </Select>\n          <Tooltip title=\"手动编辑产品信息\">\n            <Button\n              icon={<EditOutlined />}\n              style={{ width: '15%' }}\n              onClick={() => setEditingProduct(record.key)}\n            />\n          </Tooltip>\n        </Space.Compact>\n      )\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'product_name',\n      width: 150,\n      render: (value, record) => (\n        editingProduct === record.key ? (\n          <Input\n            value={value}\n            placeholder=\"产品名称\"\n            onChange={(e) => updateOrderItem(record.key, 'product_name', e.target.value)}\n            onBlur={() => setEditingProduct(null)}\n            autoFocus\n          />\n        ) : (\n          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>\n            {value || '点击编辑'}\n          </span>\n        )\n      )\n    },\n    {\n      title: '产品编码',\n      dataIndex: 'product_code',\n      width: 120,\n      render: (value, record) => (\n        editingProduct === record.key ? (\n          <Input\n            value={value}\n            placeholder=\"产品编码\"\n            onChange={(e) => updateOrderItem(record.key, 'product_code', e.target.value)}\n            onBlur={() => setEditingProduct(null)}\n          />\n        ) : (\n          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>\n            {value || '点击编辑'}\n          </span>\n        )\n      )\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      width: 80,\n      render: (value, record) => (\n        editingProduct === record.key ? (\n          <Input\n            value={value}\n            placeholder=\"单位\"\n            onChange={(e) => updateOrderItem(record.key, 'unit', e.target.value)}\n            onBlur={() => setEditingProduct(null)}\n          />\n        ) : (\n          <span onClick={() => setEditingProduct(record.key)} style={{ cursor: 'pointer' }}>\n            {value || '件'}\n          </span>\n        )\n      )\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      width: 100,\n      render: (value, record) => (\n        <InputNumber\n          value={value}\n          min={1}\n          style={{ width: '100%' }}\n          onChange={(val) => updateOrderItem(record.key, 'quantity', val || 1)}\n        />\n      )\n    },\n    {\n      title: '单价',\n      dataIndex: 'unit_price',\n      width: 120,\n      render: (value, record) => (\n        <InputNumber\n          value={value}\n          min={0}\n          precision={2}\n          style={{ width: '100%' }}\n          onChange={(val) => updateOrderItem(record.key, 'unit_price', val || 0)}\n        />\n      )\n    },\n    {\n      title: '折扣率(%)',\n      dataIndex: 'discount_rate',\n      width: 100,\n      render: (value, record) => (\n        <InputNumber\n          value={value}\n          min={0}\n          max={100}\n          precision={2}\n          style={{ width: '100%' }}\n          onChange={(val) => updateOrderItem(record.key, 'discount_rate', val || 0)}\n        />\n      )\n    },\n    {\n      title: '税率(%)',\n      dataIndex: 'tax_rate',\n      width: 100,\n      render: (value, record) => (\n        <InputNumber\n          value={value}\n          min={0}\n          max={100}\n          precision={2}\n          style={{ width: '100%' }}\n          onChange={(val) => updateOrderItem(record.key, 'tax_rate', val || 0)}\n        />\n      )\n    },\n    {\n      title: '行总计',\n      dataIndex: 'line_total',\n      width: 120,\n      render: (value) => `¥${Number(value || 0).toFixed(2)}`\n    },\n    {\n      title: '操作',\n      width: 80,\n      render: (_, record) => (\n        <Button\n          type=\"text\"\n          danger\n          icon={<DeleteOutlined />}\n          onClick={() => removeOrderItem(record.key)}\n        />\n      )\n    }\n  ];\n\n  return (\n    <Modal\n      title={order ? '编辑订单' : '新建订单'}\n      open={visible}\n      onCancel={onCancel}\n      width={1200}\n      footer={[\n        <Button key=\"cancel\" onClick={onCancel}>\n          取消\n        </Button>,\n        <Button\n          key=\"submit\"\n          type=\"primary\"\n          loading={createOrderMutation.isLoading || updateOrderMutation.isLoading}\n          onClick={handleSubmit}\n        >\n          {order ? '更新' : '创建'}\n        </Button>\n      ]}\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          priority: 'normal',\n          discount_amount: 0,\n          shipping_cost: 0\n        }}\n      >\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item\n              name=\"customer_id\"\n              label={\n                <Space>\n                  客户\n                  <Tooltip title=\"快速创建客户\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<UserAddOutlined />}\n                      onClick={() => setCustomerModalVisible(true)}\n                    >\n                      新建客户\n                    </Button>\n                  </Tooltip>\n                </Space>\n              }\n              rules={[{ required: true, message: '请选择客户' }]}\n            >\n              <Select\n                placeholder=\"选择客户\"\n                showSearch\n                optionFilterProp=\"children\"\n              >\n                {customersData?.data?.customers?.map(customer => (\n                  <Option key={customer.id} value={customer.id}>\n                    {customer.company_name} - {customer.contact_person}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n          <Col span={6}>\n            <Form.Item name=\"delivery_date\" label=\"交付日期\">\n              <DatePicker style={{ width: '100%' }} />\n            </Form.Item>\n          </Col>\n          <Col span={6}>\n            <Form.Item name=\"priority\" label=\"优先级\">\n              <Select>\n                <Option value=\"low\">低</Option>\n                <Option value=\"normal\">普通</Option>\n                <Option value=\"high\">高</Option>\n                <Option value=\"urgent\">紧急</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Divider>订单明细</Divider>\n        \n        <div style={{ marginBottom: 16 }}>\n          <Button\n            type=\"dashed\"\n            icon={<PlusOutlined />}\n            onClick={addOrderItem}\n            style={{ width: '100%' }}\n          >\n            添加订单项\n          </Button>\n        </div>\n\n        <Table\n          columns={itemColumns}\n          dataSource={orderItems}\n          pagination={false}\n          scroll={{ x: 800 }}\n          size=\"small\"\n        />\n\n        <Divider>费用信息</Divider>\n        \n        <Row gutter={16}>\n          <Col span={8}>\n            <Form.Item name=\"discount_amount\" label=\"订单折扣\">\n              <InputNumber\n                style={{ width: '100%' }}\n                min={0}\n                precision={2}\n                addonBefore=\"¥\"\n                onChange={(value) => {\n                  calculateTotals(orderItems, value || 0, form.getFieldValue('shipping_cost'));\n                }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={8}>\n            <Form.Item name=\"shipping_cost\" label=\"运费\">\n              <InputNumber\n                style={{ width: '100%' }}\n                min={0}\n                precision={2}\n                addonBefore=\"¥\"\n                onChange={(value) => {\n                  calculateTotals(orderItems, form.getFieldValue('discount_amount'), value || 0);\n                }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={8}>\n            <Form.Item name=\"payment_method\" label=\"付款方式\">\n              <Select placeholder=\"选择付款方式\">\n                <Option value=\"cash\">现金</Option>\n                <Option value=\"bank_transfer\">银行转账</Option>\n                <Option value=\"credit_card\">信用卡</Option>\n                <Option value=\"check\">支票</Option>\n                <Option value=\"other\">其他</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item name=\"shipping_address\" label=\"收货地址\">\n              <TextArea rows={3} placeholder=\"请输入收货地址\" />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item name=\"tracking_number\" label=\"物流单号\">\n              <Input placeholder=\"请输入物流单号\" />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item name=\"notes\" label=\"备注\">\n          <TextArea rows={2} placeholder=\"请输入备注信息\" />\n        </Form.Item>\n\n        {/* 订单总计 */}\n        <Card size=\"small\" style={{ backgroundColor: '#fafafa' }}>\n          <Row gutter={16}>\n            <Col span={6}>\n              <div>小计: ¥{totals.subtotal.toFixed(2)}</div>\n            </Col>\n            <Col span={6}>\n              <div>税额: ¥{totals.taxAmount.toFixed(2)}</div>\n            </Col>\n            <Col span={6}>\n              <div>运费: ¥{(form.getFieldValue('shipping_cost') || 0).toFixed(2)}</div>\n            </Col>\n            <Col span={6}>\n              <div style={{ fontWeight: 'bold', fontSize: '16px' }}>\n                总计: ¥{totals.totalAmount.toFixed(2)}\n              </div>\n            </Col>\n          </Row>\n        </Card>\n      </Form>\n\n      {/* 客户创建Modal */}\n      <Modal\n        title=\"快速创建客户\"\n        open={customerModalVisible}\n        onCancel={() => {\n          setCustomerModalVisible(false);\n          customerForm.resetFields();\n        }}\n        footer={[\n          <Button key=\"cancel\" onClick={() => {\n            setCustomerModalVisible(false);\n            customerForm.resetFields();\n          }}>\n            取消\n          </Button>,\n          <Button\n            key=\"submit\"\n            type=\"primary\"\n            loading={createCustomerMutation.isLoading}\n            onClick={() => {\n              customerForm.validateFields().then(values => {\n                createCustomerMutation.mutate(values);\n              }).catch(error => {\n                console.error('客户表单验证失败:', error);\n              });\n            }}\n          >\n            创建客户\n          </Button>\n        ]}\n      >\n        <Form\n          form={customerForm}\n          layout=\"vertical\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"contact_person\"\n                label=\"联系人\"\n                rules={[{ required: true, message: '请输入联系人' }]}\n              >\n                <Input placeholder=\"请输入联系人姓名\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"order_number\"\n                label=\"订单号\"\n              >\n                <Input placeholder=\"请输入订单号\" />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"shop_account\"\n                label=\"店铺账号\"\n              >\n                <Input placeholder=\"请输入店铺账号\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"shop_name\"\n                label=\"店铺名称\"\n              >\n                <Input placeholder=\"请输入店铺名称\" />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"email\"\n                label=\"邮箱\"\n                rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}\n              >\n                <Input placeholder=\"请输入邮箱地址\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item name=\"mobile\" label=\"手机号\">\n                <Input placeholder=\"请输入手机号\" />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item name=\"customer_type\" label=\"客户类型\" initialValue=\"retail\">\n                <Select>\n                  <Option value=\"retail\">零售</Option>\n                  <Option value=\"wholesale\">批发</Option>\n                  <Option value=\"distributor\">分销商</Option>\n                  <Option value=\"online\">线上</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item name=\"payment_terms\" label=\"付款期限(天)\" initialValue={30}>\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n          <Form.Item name=\"address\" label=\"地址\">\n            <TextArea rows={2} placeholder=\"请输入详细地址\" />\n          </Form.Item>\n          <Form.Item name=\"notes\" label=\"备注\">\n            <TextArea rows={2} placeholder=\"请输入备注信息\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Modal>\n  );\n};\n\nexport default OrderForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AAC7E,SAASC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,QAAQ,iBAAiB;AACnE,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGzB,MAAM;AACzB,MAAM;EAAE0B;AAAS,CAAC,GAAG3B,KAAK;AAE1B,MAAM4B,SAAS,GAAGA,CAAC;EAAEC,OAAO;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,qBAAA;EAC7D,MAAM,CAACC,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,YAAY,CAAC,GAAGvC,IAAI,CAACsC,OAAO,CAAC,CAAC;EACrC,MAAME,WAAW,GAAGpB,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC;IACnCgD,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM;IAAEuD,IAAI,EAAEC;EAAc,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,QAAQ,EAAE,CAAC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACxCC,OAAO,EAAEA,CAAA,KAAMlC,WAAW,CAACmC,YAAY,CAAC;MAAEF,KAAK,EAAE;IAAK,CAAC;EACzD,CAAC,CAAC;;EAEF;EACA,MAAM;IAAEH,IAAI,EAAEM;EAAa,CAAC,GAAGvC,QAAQ,CAAC;IACtCmC,QAAQ,EAAE,CAAC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACvCC,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAACoC,WAAW,CAAC;MAAEJ,KAAK,EAAE;IAAK,CAAC;EACvD,CAAC,CAAC;;EAEF;EACA,MAAMK,mBAAmB,GAAG1C,WAAW,CAAC;IACtC2C,UAAU,EAAExC,QAAQ,CAACyC,WAAW;IAChC7B,SAAS,EAAEA,CAAA,KAAM;MACftB,OAAO,CAACoD,OAAO,CAAC,QAAQ,CAAC;MACzB9B,SAAS,CAAC,CAAC;IACb,CAAC;IACD+B,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MAClBxD,OAAO,CAACsD,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACG,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBxD,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC,CAAC;;EAEF;EACA,MAAM0D,mBAAmB,GAAGnD,WAAW,CAAC;IACtC2C,UAAU,EAAEA,CAAC;MAAES,EAAE;MAAElB;IAAK,CAAC,KAAK/B,QAAQ,CAACkD,WAAW,CAACD,EAAE,EAAElB,IAAI,CAAC;IAC5DnB,SAAS,EAAEA,CAAA,KAAM;MACftB,OAAO,CAACoD,OAAO,CAAC,QAAQ,CAAC;MACzB9B,SAAS,CAAC,CAAC;IACb,CAAC;IACD+B,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAO,gBAAA,EAAAC,qBAAA;MAClB9D,OAAO,CAACsD,KAAK,CAAC,EAAAO,gBAAA,GAAAP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsB9D,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC,CAAC;;EAEF;EACA,MAAM+D,sBAAsB,GAAGxD,WAAW,CAAC;IACzC2C,UAAU,EAAEvC,WAAW,CAACqD,cAAc;IACtC1C,SAAS,EAAGmC,QAAQ,IAAK;MACvBzD,OAAO,CAACoD,OAAO,CAAC,QAAQ,CAAC;MACzBd,uBAAuB,CAAC,KAAK,CAAC;MAC9BV,YAAY,CAACqC,WAAW,CAAC,CAAC;MAC1B;MACApC,WAAW,CAACqC,iBAAiB,CAAC,CAAC,WAAW,CAAC,CAAC;MAC5C;MACAxC,IAAI,CAACyC,aAAa,CAAC,aAAa,EAAEV,QAAQ,CAAChB,IAAI,CAAC2B,QAAQ,CAACT,EAAE,CAAC;IAC9D,CAAC;IACDN,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MAClBtE,OAAO,CAACsD,KAAK,CAAC,EAAAe,gBAAA,GAAAf,KAAK,CAACG,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBtE,OAAO,KAAI,QAAQ,CAAC;IAC1D;EACF,CAAC,CAAC;;EAEF;EACAb,SAAS,CAAC,MAAM;IACd,IAAIiC,KAAK,EAAE;MACT;MACAM,IAAI,CAAC6C,cAAc,CAAC;QAClBC,WAAW,EAAEpD,KAAK,CAACoD,WAAW;QAC9BC,aAAa,EAAErD,KAAK,CAACqD,aAAa,GAAG5D,KAAK,CAACO,KAAK,CAACqD,aAAa,CAAC,GAAG,IAAI;QACtEC,QAAQ,EAAEtD,KAAK,CAACsD,QAAQ;QACxBC,eAAe,EAAEvD,KAAK,CAACuD,eAAe;QACtCC,aAAa,EAAExD,KAAK,CAACwD,aAAa;QAClCC,cAAc,EAAEzD,KAAK,CAACyD,cAAc;QACpCC,gBAAgB,EAAE1D,KAAK,CAAC0D,gBAAgB;QACxCC,eAAe,EAAE3D,KAAK,CAAC2D,eAAe;QACtCC,KAAK,EAAE5D,KAAK,CAAC4D;MACf,CAAC,CAAC;MAEF,IAAI5D,KAAK,CAAC6D,KAAK,EAAE;QACflD,aAAa,CAACX,KAAK,CAAC6D,KAAK,CAACC,GAAG,CAACC,IAAI;UAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;UAAA,OAAK;YACrCC,GAAG,EAAEJ,IAAI,CAACxB,EAAE;YACZ6B,UAAU,EAAEL,IAAI,CAACK,UAAU;YAC3BC,YAAY,GAAAL,aAAA,GAAED,IAAI,CAACO,OAAO,cAAAN,aAAA,uBAAZA,aAAA,CAAcO,IAAI;YAChCC,YAAY,GAAAP,cAAA,GAAEF,IAAI,CAACO,OAAO,cAAAL,cAAA,uBAAZA,cAAA,CAAcO,YAAY;YACxCC,IAAI,GAAAP,cAAA,GAAEH,IAAI,CAACO,OAAO,cAAAJ,cAAA,uBAAZA,cAAA,CAAcO,IAAI;YACxBC,QAAQ,EAAEX,IAAI,CAACW,QAAQ;YACvBC,UAAU,EAAEC,MAAM,CAACb,IAAI,CAACY,UAAU,CAAC;YACnCE,SAAS,EAAED,MAAM,CAACb,IAAI,CAACc,SAAS,CAAC;YACjCC,aAAa,EAAEF,MAAM,CAACb,IAAI,CAACe,aAAa,CAAC;YACzCvB,eAAe,EAAEqB,MAAM,CAACb,IAAI,CAACR,eAAe,CAAC;YAC7CwB,QAAQ,EAAEH,MAAM,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC/BC,UAAU,EAAEJ,MAAM,CAACb,IAAI,CAACiB,UAAU,CAAC;YACnCC,UAAU,EAAEL,MAAM,CAACb,IAAI,CAACkB,UAAU,CAAC;YACnCrB,KAAK,EAAEG,IAAI,CAACH;UACd,CAAC;QAAA,CAAC,CAAC,CAAC;MACN;IACF,CAAC,MAAM;MACL;MACAtD,IAAI,CAACuC,WAAW,CAAC,CAAC;MAClBlC,aAAa,CAAC,EAAE,CAAC;MACjBL,IAAI,CAAC6C,cAAc,CAAC;QAClBG,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,CAAC;QAClBC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACxD,KAAK,EAAEM,IAAI,CAAC,CAAC;;EAEjB;EACA,MAAM4E,eAAe,GAAGA,CAACrB,KAAK,EAAEsB,cAAc,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,KAAK;IACvE,MAAMtE,QAAQ,GAAG+C,KAAK,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEvB,IAAI,KAAKuB,GAAG,IAAIvB,IAAI,CAACkB,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7E,MAAMlE,SAAS,GAAG8C,KAAK,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEvB,IAAI,KAAKuB,GAAG,IAAIvB,IAAI,CAACiB,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9E,MAAMhE,WAAW,GAAGF,QAAQ,GAAGsE,YAAY,GAAGD,cAAc;IAE5DtE,SAAS,CAAC;MACRC,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,CAAC;IAEF,OAAO;MAAEF,QAAQ;MAAEC,SAAS;MAAEC;IAAY,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAIxB,IAAI,IAAK;IACnC,MAAM;MAAEW,QAAQ,GAAG,CAAC;MAAEC,UAAU,GAAG,CAAC;MAAEG,aAAa,GAAG,CAAC;MAAEC,QAAQ,GAAG;IAAE,CAAC,GAAGhB,IAAI;IAC9E,MAAMjD,QAAQ,GAAG4D,QAAQ,GAAGC,UAAU;IACtC,MAAMQ,cAAc,GAAGrE,QAAQ,IAAIgE,aAAa,GAAG,GAAG,CAAC;IACvD,MAAMU,aAAa,GAAG1E,QAAQ,GAAGqE,cAAc;IAC/C,MAAMpE,SAAS,GAAGyE,aAAa,IAAIT,QAAQ,GAAG,GAAG,CAAC;IAClD,MAAMU,SAAS,GAAGD,aAAa,GAAGzE,SAAS;IAE3C,OAAO;MACLwC,eAAe,EAAE4B,cAAc;MAC/BH,UAAU,EAAEjE,SAAS;MACrBkE,UAAU,EAAEQ;IACd,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAG;MACdxB,GAAG,EAAEyB,IAAI,CAACC,GAAG,CAAC,CAAC;MACfzB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,EAAE;MAChBG,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbE,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,CAAC;MAChBvB,eAAe,EAAE,CAAC;MAClBwB,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbrB,KAAK,EAAE;IACT,CAAC;IAED,MAAMkC,QAAQ,GAAG,CAAC,GAAGpF,UAAU,EAAEiF,OAAO,CAAC;IACzChF,aAAa,CAACmF,QAAQ,CAAC;IACvBZ,eAAe,CAACY,QAAQ,EAAExF,IAAI,CAACyF,aAAa,CAAC,iBAAiB,CAAC,EAAEzF,IAAI,CAACyF,aAAa,CAAC,eAAe,CAAC,CAAC;EACvG,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI7B,GAAG,IAAK;IAC/B,MAAM2B,QAAQ,GAAGpF,UAAU,CAACuF,MAAM,CAAClC,IAAI,IAAIA,IAAI,CAACI,GAAG,KAAKA,GAAG,CAAC;IAC5DxD,aAAa,CAACmF,QAAQ,CAAC;IACvBZ,eAAe,CAACY,QAAQ,EAAExF,IAAI,CAACyF,aAAa,CAAC,iBAAiB,CAAC,EAAEzF,IAAI,CAACyF,aAAa,CAAC,eAAe,CAAC,CAAC;EACvG,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAC/B,GAAG,EAAEgC,KAAK,EAAEC,KAAK,KAAK;IAC7C,MAAMN,QAAQ,GAAGpF,UAAU,CAACoD,GAAG,CAACC,IAAI,IAAI;MACtC,IAAIA,IAAI,CAACI,GAAG,KAAKA,GAAG,EAAE;QACpB,MAAMkC,WAAW,GAAG;UAAE,GAAGtC,IAAI;UAAE,CAACoC,KAAK,GAAGC;QAAM,CAAC;;QAE/C;QACA,IAAID,KAAK,KAAK,YAAY,IAAIC,KAAK,EAAE;UAAA,IAAAE,kBAAA,EAAAC,qBAAA;UACnC,MAAMjC,OAAO,GAAG3C,YAAY,aAAZA,YAAY,wBAAA2E,kBAAA,GAAZ3E,YAAY,CAAEN,IAAI,cAAAiF,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBE,QAAQ,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnE,EAAE,KAAK6D,KAAK,CAAC;UACvE,IAAI9B,OAAO,EAAE;YACX+B,WAAW,CAAChC,YAAY,GAAGC,OAAO,CAACC,IAAI;YACvC8B,WAAW,CAAC7B,YAAY,GAAGF,OAAO,CAACE,YAAY;YAC/C6B,WAAW,CAAC5B,IAAI,GAAGH,OAAO,CAACG,IAAI;YAC/B4B,WAAW,CAAC1B,UAAU,GAAGC,MAAM,CAACN,OAAO,CAACqC,aAAa,IAAI,CAAC,CAAC;YAC3DN,WAAW,CAACxB,SAAS,GAAGD,MAAM,CAACN,OAAO,CAACsC,UAAU,IAAI,CAAC,CAAC;UACzD;QACF;;QAEA;QACA,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACV,KAAK,CAAC,EAAE;UAC3E,MAAMW,UAAU,GAAGvB,kBAAkB,CAACc,WAAW,CAAC;UAClDU,MAAM,CAACC,MAAM,CAACX,WAAW,EAAES,UAAU,CAAC;QACxC;QAEA,OAAOT,WAAW;MACpB;MACA,OAAOtC,IAAI;IACb,CAAC,CAAC;IAEFpD,aAAa,CAACmF,QAAQ,CAAC;IACvBZ,eAAe,CAACY,QAAQ,EAAExF,IAAI,CAACyF,aAAa,CAAC,iBAAiB,CAAC,EAAEzF,IAAI,CAACyF,aAAa,CAAC,eAAe,CAAC,CAAC;EACvG,CAAC;;EAED;EACA,MAAMkB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,qBAAA;MACF,MAAMC,MAAM,GAAG,MAAM7G,IAAI,CAAC8G,cAAc,CAAC,CAAC;MAE1C,IAAI1G,UAAU,CAAC2G,MAAM,KAAK,CAAC,EAAE;QAC3BzI,OAAO,CAACsD,KAAK,CAAC,YAAY,CAAC;QAC3B;MACF;;MAEA;MACA,MAAMoF,YAAY,GAAG5G,UAAU,CAACuF,MAAM,CAAClC,IAAI,IACxC,CAACA,IAAI,CAACK,UAAU,IAAI,CAACL,IAAI,CAACM,YAAY,IACvCN,IAAI,CAACW,QAAQ,IAAI,CAAC,IAClBX,IAAI,CAACY,UAAU,GAAG,CACpB,CAAC;MAED,IAAI2C,YAAY,CAACD,MAAM,GAAG,CAAC,EAAE;QAC3BzI,OAAO,CAACsD,KAAK,CAAC,0BAA0B,CAAC;QACzC;MACF;MAEA,MAAMqF,SAAS,GAAG;QAChB,GAAGJ,MAAM;QACT9D,aAAa,GAAA6D,qBAAA,GAAEC,MAAM,CAAC9D,aAAa,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,CAAC,YAAY,CAAC;QACzD3D,KAAK,EAAEnD,UAAU,CAACoD,GAAG,CAACC,IAAI,KAAK;UAC7BK,UAAU,EAAEL,IAAI,CAACK,UAAU;UAC3BC,YAAY,EAAEN,IAAI,CAACM,YAAY;UAC/BG,YAAY,EAAET,IAAI,CAACS,YAAY;UAC/BC,IAAI,EAAEV,IAAI,CAACU,IAAI;UACfC,QAAQ,EAAEX,IAAI,CAACW,QAAQ;UACvBC,UAAU,EAAEZ,IAAI,CAACY,UAAU;UAC3BE,SAAS,EAAEd,IAAI,CAACc,SAAS;UACzBC,aAAa,EAAEf,IAAI,CAACe,aAAa;UACjCC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ;UACvBnB,KAAK,EAAEG,IAAI,CAACH;QACd,CAAC,CAAC;MACJ,CAAC;MAED,IAAI5D,KAAK,EAAE;QACTsC,mBAAmB,CAACmF,MAAM,CAAC;UAAElF,EAAE,EAAEvC,KAAK,CAACuC,EAAE;UAAElB,IAAI,EAAEkG;QAAU,CAAC,CAAC;MAC/D,CAAC,MAAM;QACL1F,mBAAmB,CAAC4F,MAAM,CAACF,SAAS,CAAC;MACvC;IACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdwF,OAAO,CAACxF,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMyF,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM;MAAA,IAAAC,mBAAA,EAAAC,qBAAA;MAAA,oBACpBvI,OAAA,CAACd,KAAK,CAACsJ,OAAO;QAACC,KAAK,EAAE;UAAEN,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBACtC1I,OAAA,CAACxB,MAAM;UACLiI,KAAK,EAAEA,KAAM;UACbkC,WAAW,EAAC,0BAAM;UAClBF,KAAK,EAAE;YAAEN,KAAK,EAAE;UAAM,CAAE;UACxBS,UAAU;UACVC,gBAAgB,EAAC,UAAU;UAC3BC,UAAU;UACVC,QAAQ,EAAGC,GAAG,IAAKzC,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,YAAY,EAAEwE,GAAG,CAAE;UAAAN,QAAA,EAEjE1G,YAAY,aAAZA,YAAY,wBAAAsG,mBAAA,GAAZtG,YAAY,CAAEN,IAAI,cAAA4G,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBzB,QAAQ,cAAA0B,qBAAA,uBAA5BA,qBAAA,CAA8BpE,GAAG,CAACQ,OAAO,iBACxC3E,OAAA,CAACC,MAAM;YAAkBwG,KAAK,EAAE9B,OAAO,CAAC/B,EAAG;YAAA8F,QAAA,GACxC/D,OAAO,CAACE,YAAY,EAAC,KAAG,EAACF,OAAO,CAACC,IAAI;UAAA,GAD3BD,OAAO,CAAC/B,EAAE;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTpJ,OAAA,CAACb,OAAO;UAAC8I,KAAK,EAAC,kDAAU;UAAAS,QAAA,eACvB1I,OAAA,CAACrB,MAAM;YACL0K,IAAI,eAAErJ,OAAA,CAACT,YAAY;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBX,KAAK,EAAE;cAAEN,KAAK,EAAE;YAAM,CAAE;YACxBmB,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC4G,MAAM,CAAC7D,GAAG;UAAE;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;EAEpB,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,KACpB7G,cAAc,KAAK6G,MAAM,CAAC7D,GAAG,gBAC3BxE,OAAA,CAACzB,KAAK;MACJkI,KAAK,EAAEA,KAAM;MACbkC,WAAW,EAAC,0BAAM;MAClBI,QAAQ,EAAGQ,CAAC,IAAKhD,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,cAAc,EAAE+E,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE;MAC7EgD,MAAM,EAAEA,CAAA,KAAMhI,iBAAiB,CAAC,IAAI,CAAE;MACtCiI,SAAS;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEFpJ,OAAA;MAAMsJ,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC4G,MAAM,CAAC7D,GAAG,CAAE;MAACiE,KAAK,EAAE;QAAEkB,MAAM,EAAE;MAAU,CAAE;MAAAjB,QAAA,EAC9EjC,KAAK,IAAI;IAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAGZ,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,KACpB7G,cAAc,KAAK6G,MAAM,CAAC7D,GAAG,gBAC3BxE,OAAA,CAACzB,KAAK;MACJkI,KAAK,EAAEA,KAAM;MACbkC,WAAW,EAAC,0BAAM;MAClBI,QAAQ,EAAGQ,CAAC,IAAKhD,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,cAAc,EAAE+E,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE;MAC7EgD,MAAM,EAAEA,CAAA,KAAMhI,iBAAiB,CAAC,IAAI;IAAE;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,gBAEFpJ,OAAA;MAAMsJ,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC4G,MAAM,CAAC7D,GAAG,CAAE;MAACiE,KAAK,EAAE;QAAEkB,MAAM,EAAE;MAAU,CAAE;MAAAjB,QAAA,EAC9EjC,KAAK,IAAI;IAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAGZ,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,KACpB7G,cAAc,KAAK6G,MAAM,CAAC7D,GAAG,gBAC3BxE,OAAA,CAACzB,KAAK;MACJkI,KAAK,EAAEA,KAAM;MACbkC,WAAW,EAAC,cAAI;MAChBI,QAAQ,EAAGQ,CAAC,IAAKhD,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,MAAM,EAAE+E,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE;MACrEgD,MAAM,EAAEA,CAAA,KAAMhI,iBAAiB,CAAC,IAAI;IAAE;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,gBAEFpJ,OAAA;MAAMsJ,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC4G,MAAM,CAAC7D,GAAG,CAAE;MAACiE,KAAK,EAAE;QAAEkB,MAAM,EAAE;MAAU,CAAE;MAAAjB,QAAA,EAC9EjC,KAAK,IAAI;IAAG;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAGZ,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,kBACpBrI,OAAA,CAACtB,WAAW;MACV+H,KAAK,EAAEA,KAAM;MACbmD,GAAG,EAAE,CAAE;MACPnB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAAO,CAAE;MACzBY,QAAQ,EAAGC,GAAG,IAAKzC,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,UAAU,EAAEwE,GAAG,IAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,kBACpBrI,OAAA,CAACtB,WAAW;MACV+H,KAAK,EAAEA,KAAM;MACbmD,GAAG,EAAE,CAAE;MACPC,SAAS,EAAE,CAAE;MACbpB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAAO,CAAE;MACzBY,QAAQ,EAAGC,GAAG,IAAKzC,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,YAAY,EAAEwE,GAAG,IAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,kBACpBrI,OAAA,CAACtB,WAAW;MACV+H,KAAK,EAAEA,KAAM;MACbmD,GAAG,EAAE,CAAE;MACPE,GAAG,EAAE,GAAI;MACTD,SAAS,EAAE,CAAE;MACbpB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAAO,CAAE;MACzBY,QAAQ,EAAGC,GAAG,IAAKzC,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,eAAe,EAAEwE,GAAG,IAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3B,KAAK,EAAE4B,MAAM,kBACpBrI,OAAA,CAACtB,WAAW;MACV+H,KAAK,EAAEA,KAAM;MACbmD,GAAG,EAAE,CAAE;MACPE,GAAG,EAAE,GAAI;MACTD,SAAS,EAAE,CAAE;MACbpB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAAO,CAAE;MACzBY,QAAQ,EAAGC,GAAG,IAAKzC,eAAe,CAAC8B,MAAM,CAAC7D,GAAG,EAAE,UAAU,EAAEwE,GAAG,IAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE;EAEL,CAAC,EACD;IACEnB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG3B,KAAK,IAAK,IAAIxB,MAAM,CAACwB,KAAK,IAAI,CAAC,CAAC,CAACsD,OAAO,CAAC,CAAC,CAAC;EACtD,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXE,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAAC4B,CAAC,EAAE3B,MAAM,kBAChBrI,OAAA,CAACrB,MAAM;MACLsL,IAAI,EAAC,MAAM;MACXC,MAAM;MACNb,IAAI,eAAErJ,OAAA,CAACX,cAAc;QAAA4J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBE,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACgC,MAAM,CAAC7D,GAAG;IAAE;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAEL,CAAC,CACF;EAED,oBACEpJ,OAAA,CAAC3B,KAAK;IACJ4J,KAAK,EAAE5H,KAAK,GAAG,MAAM,GAAG,MAAO;IAC/B8J,IAAI,EAAE/J,OAAQ;IACdE,QAAQ,EAAEA,QAAS;IACnB6H,KAAK,EAAE,IAAK;IACZiC,MAAM,EAAE,cACNpK,OAAA,CAACrB,MAAM;MAAc2K,OAAO,EAAEhJ,QAAS;MAAAoI,QAAA,EAAC;IAExC,GAFY,QAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEZ,CAAC,eACTpJ,OAAA,CAACrB,MAAM;MAELsL,IAAI,EAAC,SAAS;MACdI,OAAO,EAAEnI,mBAAmB,CAACoI,SAAS,IAAI3H,mBAAmB,CAAC2H,SAAU;MACxEhB,OAAO,EAAEhC,YAAa;MAAAoB,QAAA,EAErBrI,KAAK,GAAG,IAAI,GAAG;IAAI,GALhB,QAAQ;MAAA4I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMN,CAAC,CACT;IAAAV,QAAA,gBAEF1I,OAAA,CAAC1B,IAAI;MACHqC,IAAI,EAAEA,IAAK;MACX4J,MAAM,EAAC,UAAU;MACjBC,aAAa,EAAE;QACb7G,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,CAAC;QAClBC,aAAa,EAAE;MACjB,CAAE;MAAA6E,QAAA,gBAEF1I,OAAA,CAACnB,GAAG;QAAC4L,MAAM,EAAE,EAAG;QAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,EAAG;UAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YACR/F,IAAI,EAAC,aAAa;YAClBgG,KAAK,eACH5K,OAAA,CAACd,KAAK;cAAAwJ,QAAA,GAAC,cAEL,eAAA1I,OAAA,CAACb,OAAO;gBAAC8I,KAAK,EAAC,sCAAQ;gBAAAS,QAAA,eACrB1I,OAAA,CAACrB,MAAM;kBACLsL,IAAI,EAAC,MAAM;kBACXY,IAAI,EAAC,OAAO;kBACZxB,IAAI,eAAErJ,OAAA,CAACV,eAAe;oBAAA2J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM/H,uBAAuB,CAAC,IAAI,CAAE;kBAAAmH,QAAA,EAC9C;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR;YACD0B,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9L,OAAO,EAAE;YAAQ,CAAC,CAAE;YAAAyJ,QAAA,eAE9C1I,OAAA,CAACxB,MAAM;cACLmK,WAAW,EAAC,0BAAM;cAClBC,UAAU;cACVC,gBAAgB,EAAC,UAAU;cAAAH,QAAA,EAE1B/G,aAAa,aAAbA,aAAa,wBAAAlB,mBAAA,GAAbkB,aAAa,CAAED,IAAI,cAAAjB,mBAAA,wBAAAC,qBAAA,GAAnBD,mBAAA,CAAqBuK,SAAS,cAAAtK,qBAAA,uBAA9BA,qBAAA,CAAgCyD,GAAG,CAACd,QAAQ,iBAC3CrD,OAAA,CAACC,MAAM;gBAAmBwG,KAAK,EAAEpD,QAAQ,CAACT,EAAG;gBAAA8F,QAAA,GAC1CrF,QAAQ,CAAC4H,YAAY,EAAC,KAAG,EAAC5H,QAAQ,CAAC6H,cAAc;cAAA,GADvC7H,QAAQ,CAACT,EAAE;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,CAAE;UAAAhC,QAAA,eACX1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,eAAe;YAACgG,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAC1C1I,OAAA,CAACvB,UAAU;cAACgK,KAAK,EAAE;gBAAEN,KAAK,EAAE;cAAO;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,CAAE;UAAAhC,QAAA,eACX1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,UAAU;YAACgG,KAAK,EAAC,oBAAK;YAAAlC,QAAA,eACpC1I,OAAA,CAACxB,MAAM;cAAAkK,QAAA,gBACL1I,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,KAAK;gBAAAiC,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,QAAQ;gBAAAiC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,MAAM;gBAAAiC,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,QAAQ;gBAAAiC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpJ,OAAA,CAAChB,OAAO;QAAA0J,QAAA,EAAC;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAEvBpJ,OAAA;QAAKyI,KAAK,EAAE;UAAE0C,YAAY,EAAE;QAAG,CAAE;QAAAzC,QAAA,eAC/B1I,OAAA,CAACrB,MAAM;UACLsL,IAAI,EAAC,QAAQ;UACbZ,IAAI,eAAErJ,OAAA,CAACZ,YAAY;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBE,OAAO,EAAEvD,YAAa;UACtB0C,KAAK,EAAE;YAAEN,KAAK,EAAE;UAAO,CAAE;UAAAO,QAAA,EAC1B;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpJ,OAAA,CAACpB,KAAK;QACJwM,OAAO,EAAEpD,WAAY;QACrBqD,UAAU,EAAEtK,UAAW;QACvBuK,UAAU,EAAE,KAAM;QAClBC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAI,CAAE;QACnBX,IAAI,EAAC;MAAO;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEFpJ,OAAA,CAAChB,OAAO;QAAA0J,QAAA,EAAC;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAEvBpJ,OAAA,CAACnB,GAAG;QAAC4L,MAAM,EAAE,EAAG;QAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,CAAE;UAAAhC,QAAA,eACX1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,iBAAiB;YAACgG,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAC5C1I,OAAA,CAACtB,WAAW;cACV+J,KAAK,EAAE;gBAAEN,KAAK,EAAE;cAAO,CAAE;cACzByB,GAAG,EAAE,CAAE;cACPC,SAAS,EAAE,CAAE;cACb4B,WAAW,EAAC,MAAG;cACf1C,QAAQ,EAAGtC,KAAK,IAAK;gBACnBlB,eAAe,CAACxE,UAAU,EAAE0F,KAAK,IAAI,CAAC,EAAE9F,IAAI,CAACyF,aAAa,CAAC,eAAe,CAAC,CAAC;cAC9E;YAAE;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,CAAE;UAAAhC,QAAA,eACX1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,eAAe;YAACgG,KAAK,EAAC,cAAI;YAAAlC,QAAA,eACxC1I,OAAA,CAACtB,WAAW;cACV+J,KAAK,EAAE;gBAAEN,KAAK,EAAE;cAAO,CAAE;cACzByB,GAAG,EAAE,CAAE;cACPC,SAAS,EAAE,CAAE;cACb4B,WAAW,EAAC,MAAG;cACf1C,QAAQ,EAAGtC,KAAK,IAAK;gBACnBlB,eAAe,CAACxE,UAAU,EAAEJ,IAAI,CAACyF,aAAa,CAAC,iBAAiB,CAAC,EAAEK,KAAK,IAAI,CAAC,CAAC;cAChF;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,CAAE;UAAAhC,QAAA,eACX1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,gBAAgB;YAACgG,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAC3C1I,OAAA,CAACxB,MAAM;cAACmK,WAAW,EAAC,sCAAQ;cAAAD,QAAA,gBAC1B1I,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,MAAM;gBAAAiC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,eAAe;gBAAAiC,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,aAAa;gBAAAiC,QAAA,EAAC;cAAG;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,OAAO;gBAAAiC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCpJ,OAAA,CAACC,MAAM;gBAACwG,KAAK,EAAC,OAAO;gBAAAiC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpJ,OAAA,CAACnB,GAAG;QAAC4L,MAAM,EAAE,EAAG;QAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,EAAG;UAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,kBAAkB;YAACgG,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAC7C1I,OAAA,CAACE,QAAQ;cAACwL,IAAI,EAAE,CAAE;cAAC/C,WAAW,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;UAAC4L,IAAI,EAAE,EAAG;UAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;YAAC/F,IAAI,EAAC,iBAAiB;YAACgG,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAC5C1I,OAAA,CAACzB,KAAK;cAACoK,WAAW,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpJ,OAAA,CAAC1B,IAAI,CAACqM,IAAI;QAAC/F,IAAI,EAAC,OAAO;QAACgG,KAAK,EAAC,cAAI;QAAAlC,QAAA,eAChC1I,OAAA,CAACE,QAAQ;UAACwL,IAAI,EAAE,CAAE;UAAC/C,WAAW,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGZpJ,OAAA,CAACjB,IAAI;QAAC8L,IAAI,EAAC,OAAO;QAACpC,KAAK,EAAE;UAAEkD,eAAe,EAAE;QAAU,CAAE;QAAAjD,QAAA,eACvD1I,OAAA,CAACnB,GAAG;UAAC4L,MAAM,EAAE,EAAG;UAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,CAAE;YAAAhC,QAAA,eACX1I,OAAA;cAAA0I,QAAA,GAAK,oBAAK,EAACzH,MAAM,CAACE,QAAQ,CAAC4I,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,CAAE;YAAAhC,QAAA,eACX1I,OAAA;cAAA0I,QAAA,GAAK,oBAAK,EAACzH,MAAM,CAACG,SAAS,CAAC2I,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,CAAE;YAAAhC,QAAA,eACX1I,OAAA;cAAA0I,QAAA,GAAK,oBAAK,EAAC,CAAC/H,IAAI,CAACyF,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE2D,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,CAAE;YAAAhC,QAAA,eACX1I,OAAA;cAAKyI,KAAK,EAAE;gBAAEmD,UAAU,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAnD,QAAA,GAAC,oBAC/C,EAACzH,MAAM,CAACI,WAAW,CAAC0I,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPpJ,OAAA,CAAC3B,KAAK;MACJ4J,KAAK,EAAC,sCAAQ;MACdkC,IAAI,EAAE7I,oBAAqB;MAC3BhB,QAAQ,EAAEA,CAAA,KAAM;QACdiB,uBAAuB,CAAC,KAAK,CAAC;QAC9BV,YAAY,CAACqC,WAAW,CAAC,CAAC;MAC5B,CAAE;MACFkH,MAAM,EAAE,cACNpK,OAAA,CAACrB,MAAM;QAAc2K,OAAO,EAAEA,CAAA,KAAM;UAClC/H,uBAAuB,CAAC,KAAK,CAAC;UAC9BV,YAAY,CAACqC,WAAW,CAAC,CAAC;QAC5B,CAAE;QAAAwF,QAAA,EAAC;MAEH,GALY,QAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKZ,CAAC,eACTpJ,OAAA,CAACrB,MAAM;QAELsL,IAAI,EAAC,SAAS;QACdI,OAAO,EAAErH,sBAAsB,CAACsH,SAAU;QAC1ChB,OAAO,EAAEA,CAAA,KAAM;UACbzI,YAAY,CAAC4G,cAAc,CAAC,CAAC,CAACqE,IAAI,CAACtE,MAAM,IAAI;YAC3CxE,sBAAsB,CAAC8E,MAAM,CAACN,MAAM,CAAC;UACvC,CAAC,CAAC,CAACuE,KAAK,CAACxJ,KAAK,IAAI;YAChBwF,OAAO,CAACxF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACnC,CAAC,CAAC;QACJ,CAAE;QAAAmG,QAAA,EACH;MAED,GAZM,QAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYN,CAAC,CACT;MAAAV,QAAA,eAEF1I,OAAA,CAAC1B,IAAI;QACHqC,IAAI,EAAEE,YAAa;QACnB0J,MAAM,EAAC,UAAU;QAAA7B,QAAA,gBAEjB1I,OAAA,CAACnB,GAAG;UAAC4L,MAAM,EAAE,EAAG;UAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cACR/F,IAAI,EAAC,gBAAgB;cACrBgG,KAAK,EAAC,oBAAK;cACXE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAyJ,QAAA,eAE/C1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cACR/F,IAAI,EAAC,cAAc;cACnBgG,KAAK,EAAC,oBAAK;cAAAlC,QAAA,eAEX1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpJ,OAAA,CAACnB,GAAG;UAAC4L,MAAM,EAAE,EAAG;UAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cACR/F,IAAI,EAAC,cAAc;cACnBgG,KAAK,EAAC,0BAAM;cAAAlC,QAAA,eAEZ1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cACR/F,IAAI,EAAC,WAAW;cAChBgG,KAAK,EAAC,0BAAM;cAAAlC,QAAA,eAEZ1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpJ,OAAA,CAACnB,GAAG;UAAC4L,MAAM,EAAE,EAAG;UAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cACR/F,IAAI,EAAC,OAAO;cACZgG,KAAK,EAAC,cAAI;cACVE,KAAK,EAAE,CAAC;gBAAEb,IAAI,EAAE,OAAO;gBAAEhL,OAAO,EAAE;cAAa,CAAC,CAAE;cAAAyJ,QAAA,eAElD1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cAAC/F,IAAI,EAAC,QAAQ;cAACgG,KAAK,EAAC,oBAAK;cAAAlC,QAAA,eAClC1I,OAAA,CAACzB,KAAK;gBAACoK,WAAW,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpJ,OAAA,CAACnB,GAAG;UAAC4L,MAAM,EAAE,EAAG;UAAA/B,QAAA,gBACd1I,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cAAC/F,IAAI,EAAC,eAAe;cAACgG,KAAK,EAAC,0BAAM;cAACoB,YAAY,EAAC,QAAQ;cAAAtD,QAAA,eAChE1I,OAAA,CAACxB,MAAM;gBAAAkK,QAAA,gBACL1I,OAAA,CAACC,MAAM;kBAACwG,KAAK,EAAC,QAAQ;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCpJ,OAAA,CAACC,MAAM;kBAACwG,KAAK,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCpJ,OAAA,CAACC,MAAM;kBAACwG,KAAK,EAAC,aAAa;kBAAAiC,QAAA,EAAC;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCpJ,OAAA,CAACC,MAAM;kBAACwG,KAAK,EAAC,QAAQ;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpJ,OAAA,CAAClB,GAAG;YAAC4L,IAAI,EAAE,EAAG;YAAAhC,QAAA,eACZ1I,OAAA,CAAC1B,IAAI,CAACqM,IAAI;cAAC/F,IAAI,EAAC,eAAe;cAACgG,KAAK,EAAC,kCAAS;cAACoB,YAAY,EAAE,EAAG;cAAAtD,QAAA,eAC/D1I,OAAA,CAACtB,WAAW;gBAACkL,GAAG,EAAE,CAAE;gBAACnB,KAAK,EAAE;kBAAEN,KAAK,EAAE;gBAAO;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpJ,OAAA,CAAC1B,IAAI,CAACqM,IAAI;UAAC/F,IAAI,EAAC,SAAS;UAACgG,KAAK,EAAC,cAAI;UAAAlC,QAAA,eAClC1I,OAAA,CAACE,QAAQ;YAACwL,IAAI,EAAE,CAAE;YAAC/C,WAAW,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACZpJ,OAAA,CAAC1B,IAAI,CAACqM,IAAI;UAAC/F,IAAI,EAAC,OAAO;UAACgG,KAAK,EAAC,cAAI;UAAAlC,QAAA,eAChC1I,OAAA,CAACE,QAAQ;YAACwL,IAAI,EAAE,CAAE;YAAC/C,WAAW,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEZ,CAAC;AAAC5I,EAAA,CAltBIL,SAAS;EAAA,QACE7B,IAAI,CAACsC,OAAO,EACJtC,IAAI,CAACsC,OAAO,EACflB,cAAc,EAaFD,QAAQ,EAMTA,QAAQ,EAMXD,WAAW,EAYXA,WAAW,EAYRA,WAAW;AAAA;AAAAyM,EAAA,GApDtC9L,SAAS;AAotBf,eAAeA,SAAS;AAAC,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
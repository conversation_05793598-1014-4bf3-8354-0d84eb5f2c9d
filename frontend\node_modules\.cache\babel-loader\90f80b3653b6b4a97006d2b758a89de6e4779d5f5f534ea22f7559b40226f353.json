{"ast": null, "code": "export default function useShowNow(picker, mode, showNow, showToday, rangePicker) {\n  if (mode !== 'date' && mode !== 'time') {\n    return false;\n  }\n  if (showNow !== undefined) {\n    return showNow;\n  }\n\n  // Compatible with old version `showToday`\n  if (showToday !== undefined) {\n    return showToday;\n  }\n  return !rangePicker && (picker === 'date' || picker === 'time');\n}", "map": {"version": 3, "names": ["useShowNow", "picker", "mode", "showNow", "showToday", "rangePicker", "undefined"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/useShowNow.js"], "sourcesContent": ["export default function useShowNow(picker, mode, showNow, showToday, rangePicker) {\n  if (mode !== 'date' && mode !== 'time') {\n    return false;\n  }\n  if (showNow !== undefined) {\n    return showNow;\n  }\n\n  // Compatible with old version `showToday`\n  if (showToday !== undefined) {\n    return showToday;\n  }\n  return !rangePicker && (picker === 'date' || picker === 'time');\n}"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAE;EAChF,IAAIH,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;IACtC,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,KAAKG,SAAS,EAAE;IACzB,OAAOH,OAAO;EAChB;;EAEA;EACA,IAAIC,SAAS,KAAKE,SAAS,EAAE;IAC3B,OAAOF,SAAS;EAClB;EACA,OAAO,CAACC,WAAW,KAAKJ,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
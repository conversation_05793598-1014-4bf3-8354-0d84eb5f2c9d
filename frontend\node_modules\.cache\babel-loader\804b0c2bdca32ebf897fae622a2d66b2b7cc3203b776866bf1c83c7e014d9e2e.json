{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar enableStatistic = process.env.NODE_ENV !== 'production' || typeof CSSINJS_STATISTIC !== 'undefined';\nvar recording = true;\n\n/**\n * This function will do as `Object.assign` in production. But will use Object.defineProperty:get to\n * pass all value access in development. To support statistic field usage with alias token.\n */\nexport function merge() {\n  for (var _len = arguments.length, objs = new Array(_len), _key = 0; _key < _len; _key++) {\n    objs[_key] = arguments[_key];\n  }\n  /* istanbul ignore next */\n  if (!enableStatistic) {\n    return Object.assign.apply(Object, [{}].concat(objs));\n  }\n  recording = false;\n  var ret = {};\n  objs.forEach(function (obj) {\n    if (_typeof(obj) !== 'object') {\n      return;\n    }\n    var keys = Object.keys(obj);\n    keys.forEach(function (key) {\n      Object.defineProperty(ret, key, {\n        configurable: true,\n        enumerable: true,\n        get: function get() {\n          return obj[key];\n        }\n      });\n    });\n  });\n  recording = true;\n  return ret;\n}\n\n/** @internal Internal Usage. Not use in your production. */\nexport var statistic = {};\n\n/** @internal Internal Usage. Not use in your production. */\nexport var _statistic_build_ = {};\n\n/* istanbul ignore next */\nfunction noop() {}\n\n/** Statistic token usage case. Should use `merge` function if you do not want spread record. */\nvar statisticToken = function statisticToken(token) {\n  var tokenKeys;\n  var proxy = token;\n  var flush = noop;\n  if (enableStatistic && typeof Proxy !== 'undefined') {\n    tokenKeys = new Set();\n    proxy = new Proxy(token, {\n      get: function get(obj, prop) {\n        if (recording) {\n          var _tokenKeys;\n          (_tokenKeys = tokenKeys) === null || _tokenKeys === void 0 || _tokenKeys.add(prop);\n        }\n        return obj[prop];\n      }\n    });\n    flush = function flush(componentName, componentToken) {\n      var _statistic$componentN;\n      statistic[componentName] = {\n        global: Array.from(tokenKeys),\n        component: _objectSpread(_objectSpread({}, (_statistic$componentN = statistic[componentName]) === null || _statistic$componentN === void 0 ? void 0 : _statistic$componentN.component), componentToken)\n      };\n    };\n  }\n  return {\n    token: proxy,\n    keys: tokenKeys,\n    flush: flush\n  };\n};\nexport default statisticToken;", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "enableStatistic", "process", "env", "NODE_ENV", "CSSINJS_STATISTIC", "recording", "merge", "_len", "arguments", "length", "objs", "Array", "_key", "Object", "assign", "apply", "concat", "ret", "for<PERSON>ach", "obj", "keys", "key", "defineProperty", "configurable", "enumerable", "get", "statistic", "_statistic_build_", "noop", "statisticToken", "token", "tokenKeys", "proxy", "flush", "Proxy", "Set", "prop", "_tokenKeys", "add", "componentName", "componentToken", "_statistic$componentN", "global", "from", "component"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs-utils@1_3af55f7738cc1fa06ee42984195bbb7b/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar enableStatistic = process.env.NODE_ENV !== 'production' || typeof CSSINJS_STATISTIC !== 'undefined';\nvar recording = true;\n\n/**\n * This function will do as `Object.assign` in production. But will use Object.defineProperty:get to\n * pass all value access in development. To support statistic field usage with alias token.\n */\nexport function merge() {\n  for (var _len = arguments.length, objs = new Array(_len), _key = 0; _key < _len; _key++) {\n    objs[_key] = arguments[_key];\n  }\n  /* istanbul ignore next */\n  if (!enableStatistic) {\n    return Object.assign.apply(Object, [{}].concat(objs));\n  }\n  recording = false;\n  var ret = {};\n  objs.forEach(function (obj) {\n    if (_typeof(obj) !== 'object') {\n      return;\n    }\n    var keys = Object.keys(obj);\n    keys.forEach(function (key) {\n      Object.defineProperty(ret, key, {\n        configurable: true,\n        enumerable: true,\n        get: function get() {\n          return obj[key];\n        }\n      });\n    });\n  });\n  recording = true;\n  return ret;\n}\n\n/** @internal Internal Usage. Not use in your production. */\nexport var statistic = {};\n\n/** @internal Internal Usage. Not use in your production. */\nexport var _statistic_build_ = {};\n\n/* istanbul ignore next */\nfunction noop() {}\n\n/** Statistic token usage case. Should use `merge` function if you do not want spread record. */\nvar statisticToken = function statisticToken(token) {\n  var tokenKeys;\n  var proxy = token;\n  var flush = noop;\n  if (enableStatistic && typeof Proxy !== 'undefined') {\n    tokenKeys = new Set();\n    proxy = new Proxy(token, {\n      get: function get(obj, prop) {\n        if (recording) {\n          var _tokenKeys;\n          (_tokenKeys = tokenKeys) === null || _tokenKeys === void 0 || _tokenKeys.add(prop);\n        }\n        return obj[prop];\n      }\n    });\n    flush = function flush(componentName, componentToken) {\n      var _statistic$componentN;\n      statistic[componentName] = {\n        global: Array.from(tokenKeys),\n        component: _objectSpread(_objectSpread({}, (_statistic$componentN = statistic[componentName]) === null || _statistic$componentN === void 0 ? void 0 : _statistic$componentN.component), componentToken)\n      };\n    };\n  }\n  return {\n    token: proxy,\n    keys: tokenKeys,\n    flush: flush\n  };\n};\nexport default statisticToken;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOC,iBAAiB,KAAK,WAAW;AACvG,IAAIC,SAAS,GAAG,IAAI;;AAEpB;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA;EACA,IAAI,CAACZ,eAAe,EAAE;IACpB,OAAOa,MAAM,CAACC,MAAM,CAACC,KAAK,CAACF,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAACG,MAAM,CAACN,IAAI,CAAC,CAAC;EACvD;EACAL,SAAS,GAAG,KAAK;EACjB,IAAIY,GAAG,GAAG,CAAC,CAAC;EACZP,IAAI,CAACQ,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC1B,IAAIpB,OAAO,CAACoB,GAAG,CAAC,KAAK,QAAQ,EAAE;MAC7B;IACF;IACA,IAAIC,IAAI,GAAGP,MAAM,CAACO,IAAI,CAACD,GAAG,CAAC;IAC3BC,IAAI,CAACF,OAAO,CAAC,UAAUG,GAAG,EAAE;MAC1BR,MAAM,CAACS,cAAc,CAACL,GAAG,EAAEI,GAAG,EAAE;QAC9BE,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;UAClB,OAAON,GAAG,CAACE,GAAG,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACFhB,SAAS,GAAG,IAAI;EAChB,OAAOY,GAAG;AACZ;;AAEA;AACA,OAAO,IAAIS,SAAS,GAAG,CAAC,CAAC;;AAEzB;AACA,OAAO,IAAIC,iBAAiB,GAAG,CAAC,CAAC;;AAEjC;AACA,SAASC,IAAIA,CAAA,EAAG,CAAC;;AAEjB;AACA,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,SAAS;EACb,IAAIC,KAAK,GAAGF,KAAK;EACjB,IAAIG,KAAK,GAAGL,IAAI;EAChB,IAAI5B,eAAe,IAAI,OAAOkC,KAAK,KAAK,WAAW,EAAE;IACnDH,SAAS,GAAG,IAAII,GAAG,CAAC,CAAC;IACrBH,KAAK,GAAG,IAAIE,KAAK,CAACJ,KAAK,EAAE;MACvBL,GAAG,EAAE,SAASA,GAAGA,CAACN,GAAG,EAAEiB,IAAI,EAAE;QAC3B,IAAI/B,SAAS,EAAE;UACb,IAAIgC,UAAU;UACd,CAACA,UAAU,GAAGN,SAAS,MAAM,IAAI,IAAIM,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACC,GAAG,CAACF,IAAI,CAAC;QACpF;QACA,OAAOjB,GAAG,CAACiB,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;IACFH,KAAK,GAAG,SAASA,KAAKA,CAACM,aAAa,EAAEC,cAAc,EAAE;MACpD,IAAIC,qBAAqB;MACzBf,SAAS,CAACa,aAAa,CAAC,GAAG;QACzBG,MAAM,EAAE/B,KAAK,CAACgC,IAAI,CAACZ,SAAS,CAAC;QAC7Ba,SAAS,EAAE9C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC2C,qBAAqB,GAAGf,SAAS,CAACa,aAAa,CAAC,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,SAAS,CAAC,EAAEJ,cAAc;MACxM,CAAC;IACH,CAAC;EACH;EACA,OAAO;IACLV,KAAK,EAAEE,KAAK;IACZZ,IAAI,EAAEW,SAAS;IACfE,KAAK,EAAEA;EACT,CAAC;AACH,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
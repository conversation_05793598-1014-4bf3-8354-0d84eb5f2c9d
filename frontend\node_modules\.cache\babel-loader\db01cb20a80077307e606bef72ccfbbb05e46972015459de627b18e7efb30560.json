{"ast": null, "code": "// This icon file is generated automatically.\nvar SwapRightOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"0 0 1024 1024\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z\"\n      }\n    }]\n  },\n  \"name\": \"swap-right\",\n  \"theme\": \"outlined\"\n};\nexport default SwapRightOutlined;", "map": {"version": 3, "names": ["SwapRightOutlined"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/SwapRightOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar SwapRightOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"0 0 1024 1024\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z\" } }] }, \"name\": \"swap-right\", \"theme\": \"outlined\" };\nexport default SwapRightOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,iBAAiB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA2J;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,YAAY;EAAE,OAAO,EAAE;AAAW,CAAC;AAC7W,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
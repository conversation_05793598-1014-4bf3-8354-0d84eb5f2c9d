{"ast": null, "code": "export function getMotion(mode, motion, defaultMotions) {\n  if (motion) {\n    return motion;\n  }\n  if (defaultMotions) {\n    return defaultMotions[mode] || defaultMotions.other;\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["getMotion", "mode", "motion", "defaultMotions", "other", "undefined"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-menu@9.16.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-menu/es/utils/motionUtil.js"], "sourcesContent": ["export function getMotion(mode, motion, defaultMotions) {\n  if (motion) {\n    return motion;\n  }\n  if (defaultMotions) {\n    return defaultMotions[mode] || defaultMotions.other;\n  }\n  return undefined;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAEC,cAAc,EAAE;EACtD,IAAID,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIC,cAAc,EAAE;IAClB,OAAOA,cAAc,CAACF,IAAI,CAAC,IAAIE,cAAc,CAACC,KAAK;EACrD;EACA,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    itemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    itemMarginBlock,\n    itemWidth,\n    itemPaddingInline\n  } = token;\n  const paddingWithArrow = token.calc(menuArrowSize).add(padding).add(marginXS).equal();\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: unit(itemHeight),\n      paddingInline: itemPaddingInline,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: itemMarginBlock,\n      width: itemWidth\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: unit(itemHeight)\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    itemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionEaseOut,\n    paddingXL,\n    itemMarginInline,\n    fontSizeLG,\n    motionDurationFast,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary,\n    collapsedWidth,\n    collapsedIconSize\n  } = token;\n  const inlineItemStyle = {\n    height: itemHeight,\n    lineHeight: unit(itemHeight),\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      '&-inline, &-vertical': Object.assign({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: Object.assign(Object.assign({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${unit(token.calc(controlHeightLG).mul(2.5).equal())})`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationFast} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: collapsedWidth,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${unit(token.calc(collapsedIconSize).div(2).equal())} - ${unit(itemMarginInline)})`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: collapsedIconSize,\n          lineHeight: unit(itemHeight),\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;", "map": {"version": 3, "names": ["unit", "textEllipsis", "getVerticalInlineStyle", "token", "componentCls", "itemHeight", "itemMarginInline", "padding", "menuArrowSize", "marginXS", "itemMarginBlock", "itemWidth", "itemPaddingInline", "paddingWithArrow", "calc", "add", "equal", "position", "overflow", "height", "lineHeight", "paddingInline", "textOverflow", "marginInline", "marginBlock", "width", "paddingInlineEnd", "getVerticalStyle", "iconCls", "colorTextLightSolid", "dropdownWidth", "controlHeightLG", "motionEaseOut", "paddingXL", "fontSizeLG", "motionDurationFast", "motionDurationSlow", "paddingXS", "boxShadowSecondary", "collapsedWidth", "collapsedIconSize", "inlineItemStyle", "listStylePosition", "listStyleType", "Object", "assign", "boxShadow", "min<PERSON><PERSON><PERSON>", "maxHeight", "mul", "borderInlineEnd", "overflowX", "overflowY", "display", "alignItems", "transition", "join", "flex", "border", "borderRadius", "paddingInlineStart", "fontSize", "textAlign", "insetInlineStart", "div", "opacity", "margin", "pointerEvents", "color"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/menu/style/vertical.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    itemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    itemMarginBlock,\n    itemWidth,\n    itemPaddingInline\n  } = token;\n  const paddingWithArrow = token.calc(menuArrowSize).add(padding).add(marginXS).equal();\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: unit(itemHeight),\n      paddingInline: itemPaddingInline,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: itemMarginBlock,\n      width: itemWidth\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: unit(itemHeight)\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    itemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionEaseOut,\n    paddingXL,\n    itemMarginInline,\n    fontSizeLG,\n    motionDurationFast,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary,\n    collapsedWidth,\n    collapsedIconSize\n  } = token;\n  const inlineItemStyle = {\n    height: itemHeight,\n    lineHeight: unit(itemHeight),\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      '&-inline, &-vertical': Object.assign({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: Object.assign(Object.assign({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${unit(token.calc(controlHeightLG).mul(2.5).equal())})`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationFast} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: collapsedWidth,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${unit(token.calc(collapsedIconSize).div(2).equal())} - ${unit(itemMarginInline)})`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: collapsedIconSize,\n          lineHeight: unit(itemHeight),\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,YAAY,QAAQ,aAAa;AAC1C,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EACtC,MAAM;IACJC,YAAY;IACZC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,aAAa;IACbC,QAAQ;IACRC,eAAe;IACfC,SAAS;IACTC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,gBAAgB,GAAGV,KAAK,CAACW,IAAI,CAACN,aAAa,CAAC,CAACO,GAAG,CAACR,OAAO,CAAC,CAACQ,GAAG,CAACN,QAAQ,CAAC,CAACO,KAAK,CAAC,CAAC;EACrF,OAAO;IACL,CAAC,GAAGZ,YAAY,OAAO,GAAG;MACxBa,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACD,CAAC,GAAGd,YAAY,UAAUA,YAAY,gBAAgB,GAAG;MACvDe,MAAM,EAAEd,UAAU;MAClBe,UAAU,EAAEpB,IAAI,CAACK,UAAU,CAAC;MAC5BgB,aAAa,EAAET,iBAAiB;MAChCM,QAAQ,EAAE,QAAQ;MAClBI,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAEjB,gBAAgB;MAC9BkB,WAAW,EAAEd,eAAe;MAC5Be,KAAK,EAAEd;IACT,CAAC;IACD,CAAC,KAAKP,YAAY;AACtB,gBAAgBA,YAAY,cAAcA,YAAY,gBAAgB,GAAG;MACnEe,MAAM,EAAEd,UAAU;MAClBe,UAAU,EAAEpB,IAAI,CAACK,UAAU;IAC7B,CAAC;IACD,CAAC,GAAGD,YAAY,oBAAoBA,YAAY;AACpD,cAAcA,YAAY,gBAAgB,GAAG;MACvCsB,gBAAgB,EAAEb;IACpB;EACF,CAAC;AACH,CAAC;AACD,MAAMc,gBAAgB,GAAGxB,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZwB,OAAO;IACPvB,UAAU;IACVwB,mBAAmB;IACnBC,aAAa;IACbC,eAAe;IACfC,aAAa;IACbC,SAAS;IACT3B,gBAAgB;IAChB4B,UAAU;IACVC,kBAAkB;IAClBC,kBAAkB;IAClBC,SAAS;IACTC,kBAAkB;IAClBC,cAAc;IACdC;EACF,CAAC,GAAGrC,KAAK;EACT,MAAMsC,eAAe,GAAG;IACtBtB,MAAM,EAAEd,UAAU;IAClBe,UAAU,EAAEpB,IAAI,CAACK,UAAU,CAAC;IAC5BqC,iBAAiB,EAAE,QAAQ;IAC3BC,aAAa,EAAE;EACjB,CAAC;EACD,OAAO,CAAC;IACN,CAACvC,YAAY,GAAG;MACd,sBAAsB,EAAEwC,MAAM,CAACC,MAAM,CAAC;QACpC,CAAC,IAAIzC,YAAY,OAAO,GAAG;UACzB0C,SAAS,EAAE;QACb;MACF,CAAC,EAAE5C,sBAAsB,CAACC,KAAK,CAAC;IAClC,CAAC;IACD,CAAC,GAAGC,YAAY,gBAAgB,GAAG;MACjC,CAAC,GAAGA,YAAY,WAAW,GAAGwC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3C,sBAAsB,CAACC,KAAK,CAAC,CAAC,EAAE;QAC5F2C,SAAS,EAAER;MACb,CAAC;IACH;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGlC,YAAY,kBAAkBA,YAAY,YAAYA,YAAY,MAAM,GAAG;MAC7E2C,QAAQ,EAAEjB,aAAa;MACvBkB,SAAS,EAAE,gBAAgBhD,IAAI,CAACG,KAAK,CAACW,IAAI,CAACiB,eAAe,CAAC,CAACkB,GAAG,CAAC,GAAG,CAAC,CAACjC,KAAK,CAAC,CAAC,CAAC,GAAG;MAChFT,OAAO,EAAE,GAAG;MACZW,QAAQ,EAAE,QAAQ;MAClBgC,eAAe,EAAE,CAAC;MAClB;MACA;MACA,2BAA2B,EAAE;QAC3BC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE;MACb;IACF;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGhD,YAAY,SAAS,GAAG;MAC1BqB,KAAK,EAAE,MAAM;MACb;MACA,CAAC,IAAIrB,YAAY,OAAO,GAAG;QACzB,CAAC,GAAGA,YAAY,UAAUA,YAAY,gBAAgB,GAAG;UACvDiD,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAE,CAAC,gBAAgBnB,kBAAkB,EAAE,EAAE,cAAcA,kBAAkB,EAAE,EAAE,WAAWD,kBAAkB,IAAIH,aAAa,EAAE,CAAC,CAACwB,IAAI,CAAC,GAAG,CAAC;UAClJ,CAAC,KAAKpD,YAAY,gBAAgB,GAAG;YACnCqD,IAAI,EAAE,MAAM;YACZV,QAAQ,EAAE,CAAC;YACX7B,QAAQ,EAAE,QAAQ;YAClBI,YAAY,EAAE;UAChB,CAAC;UACD,KAAK,EAAE;YACLmC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACD;MACA,CAAC,GAAGrD,YAAY,OAAOA,YAAY,SAAS,GAAG;QAC7CG,OAAO,EAAE,CAAC;QACVmD,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfb,SAAS,EAAE,MAAM;QACjB,CAAC,OAAO1C,YAAY,cAAcA,YAAY,gBAAgB,GAAGqC,eAAe;QAChF,CAAC,KAAKrC,YAAY,mBAAmB,GAAG;UACtCwD,kBAAkB,EAAE3B;QACtB;MACF,CAAC;MACD;MACA,CAAC,GAAG7B,YAAY,OAAO,GAAGqC;IAC5B;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGrC,YAAY,mBAAmB,GAAG;MACpCqB,KAAK,EAAEc,cAAc;MACrB,CAAC,IAAInC,YAAY,OAAO,GAAG;QACzB,CAAC,GAAGA,YAAY,UAAUA,YAAY,YAAYA,YAAY,gBAAgB,GAAG;UAC/E,CAAC,KAAKA,YAAY,0BAA0B,GAAG;YAC7CyD,QAAQ,EAAE3B,UAAU;YACpB4B,SAAS,EAAE;UACb;QACF;MACF,CAAC;MACD,CAAC,KAAK1D,YAAY;AACxB,cAAcA,YAAY,iBAAiBA,YAAY,sBAAsBA,YAAY;AACzF,cAAcA,YAAY,iBAAiBA,YAAY,sBAAsBA,YAAY,cAAcA,YAAY;AACnH,cAAcA,YAAY,cAAcA,YAAY,gBAAgB,GAAG;QAC/D2D,gBAAgB,EAAE,CAAC;QACnB1C,aAAa,EAAE,cAAcrB,IAAI,CAACG,KAAK,CAACW,IAAI,CAAC0B,iBAAiB,CAAC,CAACwB,GAAG,CAAC,CAAC,CAAC,CAAChD,KAAK,CAAC,CAAC,CAAC,MAAMhB,IAAI,CAACM,gBAAgB,CAAC,GAAG;QAC9GgB,YAAY,EAAE,MAAM;QACpB,CAAC;AACT,cAAclB,YAAY;AAC1B,cAAcA,YAAY;AAC1B,WAAW,GAAG;UACJ6D,OAAO,EAAE;QACX,CAAC;QACD,CAAC,GAAG7D,YAAY,eAAewB,OAAO,EAAE,GAAG;UACzCsC,MAAM,EAAE,CAAC;UACTL,QAAQ,EAAErB,iBAAiB;UAC3BpB,UAAU,EAAEpB,IAAI,CAACK,UAAU,CAAC;UAC5B,QAAQ,EAAE;YACRgD,OAAO,EAAE,cAAc;YACvBY,OAAO,EAAE;UACX;QACF;MACF,CAAC;MACD,CAAC,GAAG7D,YAAY,eAAewB,OAAO,EAAE,GAAG;QACzCyB,OAAO,EAAE;MACX,CAAC;MACD,WAAW,EAAE;QACXc,aAAa,EAAE,MAAM;QACrB,CAAC,GAAG/D,YAAY,eAAewB,OAAO,EAAE,GAAG;UACzCyB,OAAO,EAAE;QACX,CAAC;QACD,YAAY,EAAE;UACZe,KAAK,EAAEvC;QACT;MACF,CAAC;MACD,CAAC,GAAGzB,YAAY,mBAAmB,GAAGwC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5C,YAAY,CAAC,EAAE;QACnFoB,aAAa,EAAEgB;MACjB,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
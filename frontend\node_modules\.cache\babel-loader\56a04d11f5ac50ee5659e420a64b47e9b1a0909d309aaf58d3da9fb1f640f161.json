{"ast": null, "code": "export default function getEntity(keyEntities, key) {\n  return keyEntities[key];\n}", "map": {"version": 3, "names": ["getEntity", "keyEntities", "key"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree@5.13.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tree/es/utils/keyUtil.js"], "sourcesContent": ["export default function getEntity(keyEntities, key) {\n  return keyEntities[key];\n}"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,WAAW,EAAEC,GAAG,EAAE;EAClD,OAAOD,WAAW,CAACC,GAAG,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
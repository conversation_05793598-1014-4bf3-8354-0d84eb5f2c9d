@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules\node-gyp\bin\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules\node-gyp\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules\node-gyp\bin\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules\node-gyp\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node-gyp@8.4.1\node_modules;D:\code\erp1\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\node-gyp.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\node-gyp.js" %*
)

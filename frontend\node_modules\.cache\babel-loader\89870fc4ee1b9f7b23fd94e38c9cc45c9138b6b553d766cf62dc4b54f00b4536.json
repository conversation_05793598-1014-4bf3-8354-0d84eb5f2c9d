{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nimport { isImageValid } from \"../util\";\nexport default function useStatus(_ref) {\n  var src = _ref.src,\n    isCustomPlaceholder = _ref.isCustomPlaceholder,\n    fallback = _ref.fallback;\n  var _useState = useState(isCustomPlaceholder ? 'loading' : 'normal'),\n    _useState2 = _slicedToArray(_useState, 2),\n    status = _useState2[0],\n    setStatus = _useState2[1];\n  var isLoaded = useRef(false);\n  var isError = status === 'error';\n\n  // https://github.com/react-component/image/pull/187\n  useEffect(function () {\n    var isCurrentSrc = true;\n    isImageValid(src).then(function (isValid) {\n      // https://github.com/ant-design/ant-design/issues/44948\n      // If src changes, the previous setStatus should not be triggered\n      if (!isValid && isCurrentSrc) {\n        setStatus('error');\n      }\n    });\n    return function () {\n      isCurrentSrc = false;\n    };\n  }, [src]);\n  useEffect(function () {\n    if (isCustomPlaceholder && !isLoaded.current) {\n      setStatus('loading');\n    } else if (isError) {\n      setStatus('normal');\n    }\n  }, [src]);\n  var onLoad = function onLoad() {\n    setStatus('normal');\n  };\n  var getImgRef = function getImgRef(img) {\n    isLoaded.current = false;\n    if (status === 'loading' && img !== null && img !== void 0 && img.complete && (img.naturalWidth || img.naturalHeight)) {\n      isLoaded.current = true;\n      onLoad();\n    }\n  };\n  var srcAndOnload = isError && fallback ? {\n    src: fallback\n  } : {\n    onLoad: onLoad,\n    src: src\n  };\n  return [getImgRef, srcAndOnload, status];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEffect", "useRef", "useState", "isImageValid", "useStatus", "_ref", "src", "isCustomPlaceholder", "fallback", "_useState", "_useState2", "status", "setStatus", "isLoaded", "isError", "isCurrentSrc", "then", "<PERSON><PERSON><PERSON><PERSON>", "current", "onLoad", "getImgRef", "img", "complete", "naturalWidth", "naturalHeight", "srcAndOnload"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-image@7.12.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-image/es/hooks/useStatus.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nimport { isImageValid } from \"../util\";\nexport default function useStatus(_ref) {\n  var src = _ref.src,\n    isCustomPlaceholder = _ref.isCustomPlaceholder,\n    fallback = _ref.fallback;\n  var _useState = useState(isCustomPlaceholder ? 'loading' : 'normal'),\n    _useState2 = _slicedToArray(_useState, 2),\n    status = _useState2[0],\n    setStatus = _useState2[1];\n  var isLoaded = useRef(false);\n  var isError = status === 'error';\n\n  // https://github.com/react-component/image/pull/187\n  useEffect(function () {\n    var isCurrentSrc = true;\n    isImageValid(src).then(function (isValid) {\n      // https://github.com/ant-design/ant-design/issues/44948\n      // If src changes, the previous setStatus should not be triggered\n      if (!isValid && isCurrentSrc) {\n        setStatus('error');\n      }\n    });\n    return function () {\n      isCurrentSrc = false;\n    };\n  }, [src]);\n  useEffect(function () {\n    if (isCustomPlaceholder && !isLoaded.current) {\n      setStatus('loading');\n    } else if (isError) {\n      setStatus('normal');\n    }\n  }, [src]);\n  var onLoad = function onLoad() {\n    setStatus('normal');\n  };\n  var getImgRef = function getImgRef(img) {\n    isLoaded.current = false;\n    if (status === 'loading' && img !== null && img !== void 0 && img.complete && (img.naturalWidth || img.naturalHeight)) {\n      isLoaded.current = true;\n      onLoad();\n    }\n  };\n  var srcAndOnload = isError && fallback ? {\n    src: fallback\n  } : {\n    onLoad: onLoad,\n    src: src\n  };\n  return [getImgRef, srcAndOnload, status];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,YAAY,QAAQ,SAAS;AACtC,eAAe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IAChBC,mBAAmB,GAAGF,IAAI,CAACE,mBAAmB;IAC9CC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC1B,IAAIC,SAAS,GAAGP,QAAQ,CAACK,mBAAmB,GAAG,SAAS,GAAG,QAAQ,CAAC;IAClEG,UAAU,GAAGX,cAAc,CAACU,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,IAAIG,QAAQ,GAAGZ,MAAM,CAAC,KAAK,CAAC;EAC5B,IAAIa,OAAO,GAAGH,MAAM,KAAK,OAAO;;EAEhC;EACAX,SAAS,CAAC,YAAY;IACpB,IAAIe,YAAY,GAAG,IAAI;IACvBZ,YAAY,CAACG,GAAG,CAAC,CAACU,IAAI,CAAC,UAAUC,OAAO,EAAE;MACxC;MACA;MACA,IAAI,CAACA,OAAO,IAAIF,YAAY,EAAE;QAC5BH,SAAS,CAAC,OAAO,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAO,YAAY;MACjBG,YAAY,GAAG,KAAK;IACtB,CAAC;EACH,CAAC,EAAE,CAACT,GAAG,CAAC,CAAC;EACTN,SAAS,CAAC,YAAY;IACpB,IAAIO,mBAAmB,IAAI,CAACM,QAAQ,CAACK,OAAO,EAAE;MAC5CN,SAAS,CAAC,SAAS,CAAC;IACtB,CAAC,MAAM,IAAIE,OAAO,EAAE;MAClBF,SAAS,CAAC,QAAQ,CAAC;IACrB;EACF,CAAC,EAAE,CAACN,GAAG,CAAC,CAAC;EACT,IAAIa,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7BP,SAAS,CAAC,QAAQ,CAAC;EACrB,CAAC;EACD,IAAIQ,SAAS,GAAG,SAASA,SAASA,CAACC,GAAG,EAAE;IACtCR,QAAQ,CAACK,OAAO,GAAG,KAAK;IACxB,IAAIP,MAAM,KAAK,SAAS,IAAIU,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,IAAIA,GAAG,CAACC,QAAQ,KAAKD,GAAG,CAACE,YAAY,IAAIF,GAAG,CAACG,aAAa,CAAC,EAAE;MACrHX,QAAQ,CAACK,OAAO,GAAG,IAAI;MACvBC,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,IAAIM,YAAY,GAAGX,OAAO,IAAIN,QAAQ,GAAG;IACvCF,GAAG,EAAEE;EACP,CAAC,GAAG;IACFW,MAAM,EAAEA,MAAM;IACdb,GAAG,EAAEA;EACP,CAAC;EACD,OAAO,CAACc,SAAS,EAAEK,YAAY,EAAEd,MAAM,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
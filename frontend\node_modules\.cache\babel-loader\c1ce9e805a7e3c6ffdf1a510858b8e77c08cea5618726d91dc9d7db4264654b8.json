{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { getTransBg } from './color-block';\nconst genSliderStyle = token => {\n  const {\n    componentCls,\n    colorPickerInsetShadow,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSizeSM,\n    colorPickerSliderHeight,\n    marginSM,\n    marginXS\n  } = token;\n  const handleInnerSize = token.calc(colorPickerHandlerSizeSM).sub(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const handleHoverSize = token.calc(colorPickerHandlerSizeSM).add(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const activeHandleStyle = {\n    '&:after': {\n      transform: 'scale(1)',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${token.colorPrimaryActive}`\n    }\n  };\n  return {\n    // ======================== Slider ========================\n    [`${componentCls}-slider`]: [getTransBg(unit(colorPickerSliderHeight), token.colorFillSecondary), {\n      margin: 0,\n      padding: 0,\n      height: colorPickerSliderHeight,\n      borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n      '&-rail': {\n        height: colorPickerSliderHeight,\n        borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n        boxShadow: colorPickerInsetShadow\n      },\n      [`& ${componentCls}-slider-handle`]: {\n        width: handleInnerSize,\n        height: handleInnerSize,\n        top: 0,\n        borderRadius: '100%',\n        '&:before': {\n          display: 'block',\n          position: 'absolute',\n          background: 'transparent',\n          left: {\n            _skip_check_: true,\n            value: '50%'\n          },\n          top: '50%',\n          transform: 'translate(-50%, -50%)',\n          width: handleHoverSize,\n          height: handleHoverSize,\n          borderRadius: '100%'\n        },\n        '&:after': {\n          width: colorPickerHandlerSizeSM,\n          height: colorPickerHandlerSizeSM,\n          border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n          boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`,\n          outline: 'none',\n          insetInlineStart: token.calc(lineWidthBold).mul(-1).equal(),\n          top: token.calc(lineWidthBold).mul(-1).equal(),\n          background: 'transparent',\n          transition: 'none'\n        },\n        '&:focus': activeHandleStyle\n      }\n    }],\n    // ======================== Layout ========================\n    [`${componentCls}-slider-container`]: {\n      display: 'flex',\n      gap: marginSM,\n      marginBottom: marginSM,\n      // Group\n      [`${componentCls}-slider-group`]: {\n        flex: 1,\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        display: 'flex',\n        '&-disabled-alpha': {\n          justifyContent: 'center'\n        }\n      }\n    },\n    [`${componentCls}-gradient-slider`]: {\n      marginBottom: marginXS,\n      [`& ${componentCls}-slider-handle`]: {\n        '&:after': {\n          transform: 'scale(0.8)'\n        },\n        '&-active, &:focus': activeHandleStyle\n      }\n    }\n  };\n};\nexport default genSliderStyle;", "map": {"version": 3, "names": ["unit", "getTransBg", "genSliderStyle", "token", "componentCls", "colorPickerInsetShadow", "colorBgElevated", "colorFillSecondary", "lineWidthBold", "colorPickerHandlerSizeSM", "colorPickerSliderHeight", "marginSM", "marginXS", "handleInnerSize", "calc", "sub", "mul", "equal", "handleHoverSize", "add", "activeHandleStyle", "transform", "boxShadow", "colorPrimaryActive", "margin", "padding", "height", "borderRadius", "div", "width", "top", "display", "position", "background", "left", "_skip_check_", "value", "border", "outline", "insetInlineStart", "transition", "gap", "marginBottom", "flex", "flexDirection", "justifyContent"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/style/slider.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { getTransBg } from './color-block';\nconst genSliderStyle = token => {\n  const {\n    componentCls,\n    colorPickerInsetShadow,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSizeSM,\n    colorPickerSliderHeight,\n    marginSM,\n    marginXS\n  } = token;\n  const handleInnerSize = token.calc(colorPickerHandlerSizeSM).sub(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const handleHoverSize = token.calc(colorPickerHandlerSizeSM).add(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const activeHandleStyle = {\n    '&:after': {\n      transform: 'scale(1)',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${token.colorPrimaryActive}`\n    }\n  };\n  return {\n    // ======================== Slider ========================\n    [`${componentCls}-slider`]: [getTransBg(unit(colorPickerSliderHeight), token.colorFillSecondary), {\n      margin: 0,\n      padding: 0,\n      height: colorPickerSliderHeight,\n      borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n      '&-rail': {\n        height: colorPickerSliderHeight,\n        borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n        boxShadow: colorPickerInsetShadow\n      },\n      [`& ${componentCls}-slider-handle`]: {\n        width: handleInnerSize,\n        height: handleInnerSize,\n        top: 0,\n        borderRadius: '100%',\n        '&:before': {\n          display: 'block',\n          position: 'absolute',\n          background: 'transparent',\n          left: {\n            _skip_check_: true,\n            value: '50%'\n          },\n          top: '50%',\n          transform: 'translate(-50%, -50%)',\n          width: handleHoverSize,\n          height: handleHoverSize,\n          borderRadius: '100%'\n        },\n        '&:after': {\n          width: colorPickerHandlerSizeSM,\n          height: colorPickerHandlerSizeSM,\n          border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n          boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`,\n          outline: 'none',\n          insetInlineStart: token.calc(lineWidthBold).mul(-1).equal(),\n          top: token.calc(lineWidthBold).mul(-1).equal(),\n          background: 'transparent',\n          transition: 'none'\n        },\n        '&:focus': activeHandleStyle\n      }\n    }],\n    // ======================== Layout ========================\n    [`${componentCls}-slider-container`]: {\n      display: 'flex',\n      gap: marginSM,\n      marginBottom: marginSM,\n      // Group\n      [`${componentCls}-slider-group`]: {\n        flex: 1,\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        display: 'flex',\n        '&-disabled-alpha': {\n          justifyContent: 'center'\n        }\n      }\n    },\n    [`${componentCls}-gradient-slider`]: {\n      marginBottom: marginXS,\n      [`& ${componentCls}-slider-handle`]: {\n        '&:after': {\n          transform: 'scale(0.8)'\n        },\n        '&-active, &:focus': activeHandleStyle\n      }\n    }\n  };\n};\nexport default genSliderStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,UAAU,QAAQ,eAAe;AAC1C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,sBAAsB;IACtBC,eAAe;IACfC,kBAAkB;IAClBC,aAAa;IACbC,wBAAwB;IACxBC,uBAAuB;IACvBC,QAAQ;IACRC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,eAAe,GAAGV,KAAK,CAACW,IAAI,CAACL,wBAAwB,CAAC,CAACM,GAAG,CAACZ,KAAK,CAACW,IAAI,CAACN,aAAa,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC;EAClH,MAAMC,eAAe,GAAGf,KAAK,CAACW,IAAI,CAACL,wBAAwB,CAAC,CAACU,GAAG,CAAChB,KAAK,CAACW,IAAI,CAACN,aAAa,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC;EAClH,MAAMG,iBAAiB,GAAG;IACxB,SAAS,EAAE;MACTC,SAAS,EAAE,UAAU;MACrBC,SAAS,EAAE,GAAGjB,sBAAsB,eAAeF,KAAK,CAACoB,kBAAkB;IAC7E;EACF,CAAC;EACD,OAAO;IACL;IACA,CAAC,GAAGnB,YAAY,SAAS,GAAG,CAACH,UAAU,CAACD,IAAI,CAACU,uBAAuB,CAAC,EAAEP,KAAK,CAACI,kBAAkB,CAAC,EAAE;MAChGiB,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAEhB,uBAAuB;MAC/BiB,YAAY,EAAExB,KAAK,CAACW,IAAI,CAACJ,uBAAuB,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAACX,KAAK,CAAC,CAAC;MAChE,QAAQ,EAAE;QACRS,MAAM,EAAEhB,uBAAuB;QAC/BiB,YAAY,EAAExB,KAAK,CAACW,IAAI,CAACJ,uBAAuB,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAACX,KAAK,CAAC,CAAC;QAChEK,SAAS,EAAEjB;MACb,CAAC;MACD,CAAC,KAAKD,YAAY,gBAAgB,GAAG;QACnCyB,KAAK,EAAEhB,eAAe;QACtBa,MAAM,EAAEb,eAAe;QACvBiB,GAAG,EAAE,CAAC;QACNH,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE;UACVI,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,aAAa;UACzBC,IAAI,EAAE;YACJC,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDN,GAAG,EAAE,KAAK;UACVT,SAAS,EAAE,uBAAuB;UAClCQ,KAAK,EAAEX,eAAe;UACtBQ,MAAM,EAAER,eAAe;UACvBS,YAAY,EAAE;QAChB,CAAC;QACD,SAAS,EAAE;UACTE,KAAK,EAAEpB,wBAAwB;UAC/BiB,MAAM,EAAEjB,wBAAwB;UAChC4B,MAAM,EAAE,GAAGrC,IAAI,CAACQ,aAAa,CAAC,UAAUF,eAAe,EAAE;UACzDgB,SAAS,EAAE,GAAGjB,sBAAsB,eAAeE,kBAAkB,EAAE;UACvE+B,OAAO,EAAE,MAAM;UACfC,gBAAgB,EAAEpC,KAAK,CAACW,IAAI,CAACN,aAAa,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAC3Da,GAAG,EAAE3B,KAAK,CAACW,IAAI,CAACN,aAAa,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAC9CgB,UAAU,EAAE,aAAa;UACzBO,UAAU,EAAE;QACd,CAAC;QACD,SAAS,EAAEpB;MACb;IACF,CAAC,CAAC;IACF;IACA,CAAC,GAAGhB,YAAY,mBAAmB,GAAG;MACpC2B,OAAO,EAAE,MAAM;MACfU,GAAG,EAAE9B,QAAQ;MACb+B,YAAY,EAAE/B,QAAQ;MACtB;MACA,CAAC,GAAGP,YAAY,eAAe,GAAG;QAChCuC,IAAI,EAAE,CAAC;QACPC,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,eAAe;QAC/Bd,OAAO,EAAE,MAAM;QACf,kBAAkB,EAAE;UAClBc,cAAc,EAAE;QAClB;MACF;IACF,CAAC;IACD,CAAC,GAAGzC,YAAY,kBAAkB,GAAG;MACnCsC,YAAY,EAAE9B,QAAQ;MACtB,CAAC,KAAKR,YAAY,gBAAgB,GAAG;QACnC,SAAS,EAAE;UACTiB,SAAS,EAAE;QACb,CAAC;QACD,mBAAmB,EAAED;MACvB;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAelB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "map": {"version": 3, "names": ["React", "useMergedConfig", "propConfig", "templateConfig", "useMemo", "support", "Object", "assign"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/hooks/useMergedConfig.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAEC,cAAc,EAAE;EAClE,OAAOH,KAAK,CAACI,OAAO,CAAC,MAAM;IACzB,MAAMC,OAAO,GAAG,CAAC,CAACH,UAAU;IAC5B,OAAO,CAACG,OAAO,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,cAAc,CAAC,EAAEE,OAAO,IAAI,OAAOH,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,IAAI,CAAC,CAAC;EACnI,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
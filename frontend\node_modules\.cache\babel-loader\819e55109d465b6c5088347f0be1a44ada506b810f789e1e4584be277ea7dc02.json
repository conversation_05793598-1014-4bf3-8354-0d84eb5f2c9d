{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { warning } from \"rc-util/es/warning\";\nvar uuid = 0;\n\n/**\n * Theme with algorithms to derive tokens from design tokens.\n * Use `createTheme` first which will help to manage the theme instance cache.\n */\nvar Theme = /*#__PURE__*/function () {\n  function Theme(derivatives) {\n    _classCallCheck(this, Theme);\n    _defineProperty(this, \"derivatives\", void 0);\n    _defineProperty(this, \"id\", void 0);\n    this.derivatives = Array.isArray(derivatives) ? derivatives : [derivatives];\n    this.id = uuid;\n    if (derivatives.length === 0) {\n      warning(derivatives.length > 0, '[Ant Design CSS-in-JS] Theme should have at least one derivative function.');\n    }\n    uuid += 1;\n  }\n  _createClass(Theme, [{\n    key: \"getDerivativeToken\",\n    value: function getDerivativeToken(token) {\n      return this.derivatives.reduce(function (result, derivative) {\n        return derivative(token, result);\n      }, undefined);\n    }\n  }]);\n  return Theme;\n}();\nexport { Theme as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "warning", "uuid", "Theme", "derivatives", "Array", "isArray", "id", "length", "key", "value", "getDerivativeToken", "token", "reduce", "result", "derivative", "undefined", "default"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/theme/Theme.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { warning } from \"rc-util/es/warning\";\nvar uuid = 0;\n\n/**\n * Theme with algorithms to derive tokens from design tokens.\n * Use `createTheme` first which will help to manage the theme instance cache.\n */\nvar Theme = /*#__PURE__*/function () {\n  function Theme(derivatives) {\n    _classCallCheck(this, Theme);\n    _defineProperty(this, \"derivatives\", void 0);\n    _defineProperty(this, \"id\", void 0);\n    this.derivatives = Array.isArray(derivatives) ? derivatives : [derivatives];\n    this.id = uuid;\n    if (derivatives.length === 0) {\n      warning(derivatives.length > 0, '[Ant Design CSS-in-JS] Theme should have at least one derivative function.');\n    }\n    uuid += 1;\n  }\n  _createClass(Theme, [{\n    key: \"getDerivativeToken\",\n    value: function getDerivativeToken(token) {\n      return this.derivatives.reduce(function (result, derivative) {\n        return derivative(token, result);\n      }, undefined);\n    }\n  }]);\n  return Theme;\n}();\nexport { Theme as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,IAAIC,IAAI,GAAG,CAAC;;AAEZ;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,WAAW,EAAE;IAC1BN,eAAe,CAAC,IAAI,EAAEK,KAAK,CAAC;IAC5BH,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IAC5CA,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACnC,IAAI,CAACI,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;IAC3E,IAAI,CAACG,EAAE,GAAGL,IAAI;IACd,IAAIE,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5BP,OAAO,CAACG,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE,4EAA4E,CAAC;IAC/G;IACAN,IAAI,IAAI,CAAC;EACX;EACAH,YAAY,CAACI,KAAK,EAAE,CAAC;IACnBM,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASC,kBAAkBA,CAACC,KAAK,EAAE;MACxC,OAAO,IAAI,CAACR,WAAW,CAACS,MAAM,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;QAC3D,OAAOA,UAAU,CAACH,KAAK,EAAEE,MAAM,CAAC;MAClC,CAAC,EAAEE,SAAS,CAAC;IACf;EACF,CAAC,CAAC,CAAC;EACH,OAAOb,KAAK;AACd,CAAC,CAAC,CAAC;AACH,SAASA,KAAK,IAAIc,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
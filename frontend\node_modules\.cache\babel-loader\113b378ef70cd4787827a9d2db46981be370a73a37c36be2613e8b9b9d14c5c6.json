{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nimport { useRef } from 'react';\nimport isFF from \"../utils/isFirefox\";\nimport useOriginScroll from \"./useOriginScroll\";\nexport default function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, horizontalScroll,\n/***\n * Return `true` when you need to prevent default event\n */\nonWheelDelta) {\n  var offsetRef = useRef(0);\n  var nextFrameRef = useRef(null);\n\n  // Firefox patch\n  var wheelValueRef = useRef(null);\n  var isMouseScrollRef = useRef(false);\n\n  // Scroll status sync\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);\n  function onWheelY(e, deltaY) {\n    raf.cancel(nextFrameRef.current);\n\n    // Do nothing when scroll at the edge, Skip check when is in scroll\n    if (originScroll(false, deltaY)) return;\n\n    // Skip if nest List has handled this event\n    var event = e;\n    if (!event._virtualHandled) {\n      event._virtualHandled = true;\n    } else {\n      return;\n    }\n    offsetRef.current += deltaY;\n    wheelValueRef.current = deltaY;\n\n    // Proxy of scroll events\n    if (!isFF) {\n      event.preventDefault();\n    }\n    nextFrameRef.current = raf(function () {\n      // Patch a multiple for Firefox to fix wheel number too small\n      // ref: https://github.com/ant-design/ant-design/issues/26372#issuecomment-*********\n      var patchMultiple = isMouseScrollRef.current ? 10 : 1;\n      onWheelDelta(offsetRef.current * patchMultiple, false);\n      offsetRef.current = 0;\n    });\n  }\n  function onWheelX(event, deltaX) {\n    onWheelDelta(deltaX, true);\n    if (!isFF) {\n      event.preventDefault();\n    }\n  }\n\n  // Check for which direction does wheel do. `sx` means `shift + wheel`\n  var wheelDirectionRef = useRef(null);\n  var wheelDirectionCleanRef = useRef(null);\n  function onWheel(event) {\n    if (!inVirtual) return;\n\n    // Wait for 2 frame to clean direction\n    raf.cancel(wheelDirectionCleanRef.current);\n    wheelDirectionCleanRef.current = raf(function () {\n      wheelDirectionRef.current = null;\n    }, 2);\n    var deltaX = event.deltaX,\n      deltaY = event.deltaY,\n      shiftKey = event.shiftKey;\n    var mergedDeltaX = deltaX;\n    var mergedDeltaY = deltaY;\n    if (wheelDirectionRef.current === 'sx' || !wheelDirectionRef.current && (shiftKey || false) && deltaY && !deltaX) {\n      mergedDeltaX = deltaY;\n      mergedDeltaY = 0;\n      wheelDirectionRef.current = 'sx';\n    }\n    var absX = Math.abs(mergedDeltaX);\n    var absY = Math.abs(mergedDeltaY);\n    if (wheelDirectionRef.current === null) {\n      wheelDirectionRef.current = horizontalScroll && absX > absY ? 'x' : 'y';\n    }\n    if (wheelDirectionRef.current === 'y') {\n      onWheelY(event, mergedDeltaY);\n    } else {\n      onWheelX(event, mergedDeltaX);\n    }\n  }\n\n  // A patch for firefox\n  function onFireFoxScroll(event) {\n    if (!inVirtual) return;\n    isMouseScrollRef.current = event.detail === wheelValueRef.current;\n  }\n  return [onWheel, onFireFoxScroll];\n}", "map": {"version": 3, "names": ["raf", "useRef", "isFF", "useOriginScroll", "useFrameWheel", "inVirtual", "isScrollAtTop", "isScrollAtBottom", "isScrollAtLeft", "isScrollAtRight", "horizontalScroll", "onWheelDelta", "offsetRef", "nextFrameRef", "wheelValueRef", "isMouseScrollRef", "originScroll", "onWheelY", "e", "deltaY", "cancel", "current", "event", "_virtualHandled", "preventDefault", "patchMultiple", "onWheelX", "deltaX", "wheelDirectionRef", "wheelDirectionCleanRef", "onWheel", "shift<PERSON>ey", "mergedDeltaX", "mergedDeltaY", "absX", "Math", "abs", "absY", "onFireFoxScroll", "detail"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-virtual-list@3.19.1_reac_faad33af14cf2eb0b57167af327c7e91/node_modules/rc-virtual-list/es/hooks/useFrameWheel.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nimport { useRef } from 'react';\nimport isFF from \"../utils/isFirefox\";\nimport useOriginScroll from \"./useOriginScroll\";\nexport default function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, horizontalScroll,\n/***\n * Return `true` when you need to prevent default event\n */\nonWheelDelta) {\n  var offsetRef = useRef(0);\n  var nextFrameRef = useRef(null);\n\n  // Firefox patch\n  var wheelValueRef = useRef(null);\n  var isMouseScrollRef = useRef(false);\n\n  // Scroll status sync\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);\n  function onWheelY(e, deltaY) {\n    raf.cancel(nextFrameRef.current);\n\n    // Do nothing when scroll at the edge, Skip check when is in scroll\n    if (originScroll(false, deltaY)) return;\n\n    // Skip if nest List has handled this event\n    var event = e;\n    if (!event._virtualHandled) {\n      event._virtualHandled = true;\n    } else {\n      return;\n    }\n    offsetRef.current += deltaY;\n    wheelValueRef.current = deltaY;\n\n    // Proxy of scroll events\n    if (!isFF) {\n      event.preventDefault();\n    }\n    nextFrameRef.current = raf(function () {\n      // Patch a multiple for Firefox to fix wheel number too small\n      // ref: https://github.com/ant-design/ant-design/issues/26372#issuecomment-*********\n      var patchMultiple = isMouseScrollRef.current ? 10 : 1;\n      onWheelDelta(offsetRef.current * patchMultiple, false);\n      offsetRef.current = 0;\n    });\n  }\n  function onWheelX(event, deltaX) {\n    onWheelDelta(deltaX, true);\n    if (!isFF) {\n      event.preventDefault();\n    }\n  }\n\n  // Check for which direction does wheel do. `sx` means `shift + wheel`\n  var wheelDirectionRef = useRef(null);\n  var wheelDirectionCleanRef = useRef(null);\n  function onWheel(event) {\n    if (!inVirtual) return;\n\n    // Wait for 2 frame to clean direction\n    raf.cancel(wheelDirectionCleanRef.current);\n    wheelDirectionCleanRef.current = raf(function () {\n      wheelDirectionRef.current = null;\n    }, 2);\n    var deltaX = event.deltaX,\n      deltaY = event.deltaY,\n      shiftKey = event.shiftKey;\n    var mergedDeltaX = deltaX;\n    var mergedDeltaY = deltaY;\n    if (wheelDirectionRef.current === 'sx' || !wheelDirectionRef.current && (shiftKey || false) && deltaY && !deltaX) {\n      mergedDeltaX = deltaY;\n      mergedDeltaY = 0;\n      wheelDirectionRef.current = 'sx';\n    }\n    var absX = Math.abs(mergedDeltaX);\n    var absY = Math.abs(mergedDeltaY);\n    if (wheelDirectionRef.current === null) {\n      wheelDirectionRef.current = horizontalScroll && absX > absY ? 'x' : 'y';\n    }\n    if (wheelDirectionRef.current === 'y') {\n      onWheelY(event, mergedDeltaY);\n    } else {\n      onWheelX(event, mergedDeltaX);\n    }\n  }\n\n  // A patch for firefox\n  function onFireFoxScroll(event) {\n    if (!inVirtual) return;\n    isMouseScrollRef.current = event.detail === wheelValueRef.current;\n  }\n  return [onWheel, onFireFoxScroll];\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB;AACnI;AACA;AACA;AACAC,YAAY,EAAE;EACZ,IAAIC,SAAS,GAAGX,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIY,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACA,IAAIa,aAAa,GAAGb,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIc,gBAAgB,GAAGd,MAAM,CAAC,KAAK,CAAC;;EAEpC;EACA,IAAIe,YAAY,GAAGb,eAAe,CAACG,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,CAAC;EACpG,SAASQ,QAAQA,CAACC,CAAC,EAAEC,MAAM,EAAE;IAC3BnB,GAAG,CAACoB,MAAM,CAACP,YAAY,CAACQ,OAAO,CAAC;;IAEhC;IACA,IAAIL,YAAY,CAAC,KAAK,EAAEG,MAAM,CAAC,EAAE;;IAEjC;IACA,IAAIG,KAAK,GAAGJ,CAAC;IACb,IAAI,CAACI,KAAK,CAACC,eAAe,EAAE;MAC1BD,KAAK,CAACC,eAAe,GAAG,IAAI;IAC9B,CAAC,MAAM;MACL;IACF;IACAX,SAAS,CAACS,OAAO,IAAIF,MAAM;IAC3BL,aAAa,CAACO,OAAO,GAAGF,MAAM;;IAE9B;IACA,IAAI,CAACjB,IAAI,EAAE;MACToB,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;IACAX,YAAY,CAACQ,OAAO,GAAGrB,GAAG,CAAC,YAAY;MACrC;MACA;MACA,IAAIyB,aAAa,GAAGV,gBAAgB,CAACM,OAAO,GAAG,EAAE,GAAG,CAAC;MACrDV,YAAY,CAACC,SAAS,CAACS,OAAO,GAAGI,aAAa,EAAE,KAAK,CAAC;MACtDb,SAAS,CAACS,OAAO,GAAG,CAAC;IACvB,CAAC,CAAC;EACJ;EACA,SAASK,QAAQA,CAACJ,KAAK,EAAEK,MAAM,EAAE;IAC/BhB,YAAY,CAACgB,MAAM,EAAE,IAAI,CAAC;IAC1B,IAAI,CAACzB,IAAI,EAAE;MACToB,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;EACF;;EAEA;EACA,IAAII,iBAAiB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI4B,sBAAsB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACzC,SAAS6B,OAAOA,CAACR,KAAK,EAAE;IACtB,IAAI,CAACjB,SAAS,EAAE;;IAEhB;IACAL,GAAG,CAACoB,MAAM,CAACS,sBAAsB,CAACR,OAAO,CAAC;IAC1CQ,sBAAsB,CAACR,OAAO,GAAGrB,GAAG,CAAC,YAAY;MAC/C4B,iBAAiB,CAACP,OAAO,GAAG,IAAI;IAClC,CAAC,EAAE,CAAC,CAAC;IACL,IAAIM,MAAM,GAAGL,KAAK,CAACK,MAAM;MACvBR,MAAM,GAAGG,KAAK,CAACH,MAAM;MACrBY,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IAC3B,IAAIC,YAAY,GAAGL,MAAM;IACzB,IAAIM,YAAY,GAAGd,MAAM;IACzB,IAAIS,iBAAiB,CAACP,OAAO,KAAK,IAAI,IAAI,CAACO,iBAAiB,CAACP,OAAO,KAAKU,QAAQ,IAAI,KAAK,CAAC,IAAIZ,MAAM,IAAI,CAACQ,MAAM,EAAE;MAChHK,YAAY,GAAGb,MAAM;MACrBc,YAAY,GAAG,CAAC;MAChBL,iBAAiB,CAACP,OAAO,GAAG,IAAI;IAClC;IACA,IAAIa,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACJ,YAAY,CAAC;IACjC,IAAIK,IAAI,GAAGF,IAAI,CAACC,GAAG,CAACH,YAAY,CAAC;IACjC,IAAIL,iBAAiB,CAACP,OAAO,KAAK,IAAI,EAAE;MACtCO,iBAAiB,CAACP,OAAO,GAAGX,gBAAgB,IAAIwB,IAAI,GAAGG,IAAI,GAAG,GAAG,GAAG,GAAG;IACzE;IACA,IAAIT,iBAAiB,CAACP,OAAO,KAAK,GAAG,EAAE;MACrCJ,QAAQ,CAACK,KAAK,EAAEW,YAAY,CAAC;IAC/B,CAAC,MAAM;MACLP,QAAQ,CAACJ,KAAK,EAAEU,YAAY,CAAC;IAC/B;EACF;;EAEA;EACA,SAASM,eAAeA,CAAChB,KAAK,EAAE;IAC9B,IAAI,CAACjB,SAAS,EAAE;IAChBU,gBAAgB,CAACM,OAAO,GAAGC,KAAK,CAACiB,MAAM,KAAKzB,aAAa,CAACO,OAAO;EACnE;EACA,OAAO,CAACS,OAAO,EAAEQ,eAAe,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
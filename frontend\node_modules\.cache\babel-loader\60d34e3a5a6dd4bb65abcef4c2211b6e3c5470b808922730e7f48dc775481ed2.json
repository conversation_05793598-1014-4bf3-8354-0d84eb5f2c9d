{"ast": null, "code": "import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (typeof value === 'string' && /NaN/g.test(value) || Number.isNaN(value)) {\n    lintWarning(\"Unexpected 'NaN' in property '\".concat(key, \": \").concat(value, \"'.\"), info);\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "linter", "key", "value", "info", "test", "Number", "isNaN", "concat"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/linters/NaNLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (typeof value === 'string' && /NaN/g.test(value) || Number.isNaN(value)) {\n    lintWarning(\"Unexpected 'NaN' in property '\".concat(key, \": \").concat(value, \"'.\"), info);\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,MAAM,CAACE,IAAI,CAACF,KAAK,CAAC,IAAIG,MAAM,CAACC,KAAK,CAACJ,KAAK,CAAC,EAAE;IAC1EH,WAAW,CAAC,gCAAgC,CAACQ,MAAM,CAACN,GAAG,EAAE,IAAI,CAAC,CAACM,MAAM,CAACL,KAAK,EAAE,IAAI,CAAC,EAAEC,IAAI,CAAC;EAC3F;AACF,CAAC;AACD,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
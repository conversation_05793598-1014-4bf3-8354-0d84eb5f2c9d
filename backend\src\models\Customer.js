const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  customer_code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 20],
      notEmpty: true
    }
  },
  contact_person: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      len: [2, 50],
      notEmpty: true
    }
  },
  order_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '订单号'
  },
  shop_account: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '店铺账号'
  },
  shop_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '店铺名称'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      len: [0, 20]
    }
  },
  mobile: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      len: [0, 20]
    }
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [0, 50]
    }
  },
  province: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [0, 50]
    }
  },
  postal_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    validate: {
      len: [0, 10]
    }
  },
  tax_number: {
    type: DataTypes.STRING(30),
    allowNull: true,
    validate: {
      len: [0, 30]
    }
  },
  credit_limit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0.00,
    validate: {
      min: 0
    }
  },

  discount_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: 0.00,
    validate: {
      min: 0,
      max: 100
    },
    comment: '折扣率（%）'
  },
  customer_type: {
    type: DataTypes.ENUM('retail', 'wholesale', 'distributor', 'online'),
    allowNull: false,
    defaultValue: 'retail'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'suspended'),
    allowNull: false,
    defaultValue: 'active'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'customers',
  indexes: [
    {
      fields: ['customer_code']
    },
    {
      fields: ['status']
    },
    {
      fields: ['shop_account']
    }
  ]
});

module.exports = Customer;

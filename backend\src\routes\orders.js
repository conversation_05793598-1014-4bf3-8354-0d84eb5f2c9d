const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const orderController = require('../controllers/orderController');
const { auth, authorize } = require('../middleware/auth');

// 订单验证规则
const orderValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('客户ID必须是有效的整数'),
  body('delivery_date')
    .optional()
    .isISO8601()
    .withMessage('交付日期格式不正确'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('优先级必须是: low, normal, high, urgent'),
  body('discount_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('折扣金额必须大于等于0'),
  body('shipping_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('运费必须大于等于0'),
  body('payment_method')
    .optional()
    .isIn(['cash', 'bank_transfer', 'credit_card', 'check', 'other'])
    .withMessage('付款方式无效'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('订单明细不能为空'),
  body('items.*.product_id')
    .isInt({ min: 1 })
    .withMessage('产品ID必须是有效的整数'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('数量必须大于0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('单价必须大于等于0'),
  body('items.*.discount_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('折扣率必须在0-100之间'),
  body('items.*.tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('税率必须在0-100之间')
];

// 订单状态更新验证
const statusValidation = [
  body('status')
    .isIn(['draft', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'])
    .withMessage('订单状态无效')
];

// 所有路由都需要认证
router.use(auth);

// 获取所有订单
router.get('/', orderController.getAllOrders);

// 获取单个订单详情
router.get('/:id', orderController.getOrderById);

// 创建订单
router.post('/', orderValidation, orderController.createOrder);

// 更新订单
router.put('/:id', orderValidation, orderController.updateOrder);

// 更新订单状态
router.patch('/:id/status', statusValidation, orderController.updateOrderStatus);

// 删除订单
router.delete('/:id', orderController.deleteOrder);

module.exports = router;

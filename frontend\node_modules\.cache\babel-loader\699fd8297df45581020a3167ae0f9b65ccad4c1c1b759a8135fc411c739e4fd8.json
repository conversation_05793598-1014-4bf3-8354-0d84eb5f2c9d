{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { formatValue } from \"../../utils/dateUtil\";\nimport { PanelContext, useInfo } from \"../context\";\nimport PanelHeader from \"../PanelHeader\";\nimport TimePanelBody from \"./TimePanelBody\";\nexport default function TimePanel(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    showTime = props.showTime;\n  var _ref = showTime || {},\n    format = _ref.format;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n\n  // ========================== Base ==========================\n  var _useInfo = useInfo(props, 'time'),\n    _useInfo2 = _slicedToArray(_useInfo, 1),\n    info = _useInfo2[0];\n\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: info\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls)\n  }, /*#__PURE__*/React.createElement(PanelHeader, null, value ? formatValue(value, {\n    locale: locale,\n    format: format,\n    generateConfig: generateConfig\n  }) : \"\\xA0\"), /*#__PURE__*/React.createElement(TimePanelBody, showTime)));\n}", "map": {"version": 3, "names": ["_slicedToArray", "classNames", "React", "formatValue", "PanelContext", "useInfo", "PanelHeader", "TimePanelBody", "TimePanel", "props", "prefixCls", "value", "locale", "generateConfig", "showTime", "_ref", "format", "panelPrefixCls", "concat", "_useInfo", "_useInfo2", "info", "createElement", "Provider", "className"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerPanel/TimePanel/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { formatValue } from \"../../utils/dateUtil\";\nimport { PanelContext, useInfo } from \"../context\";\nimport PanelHeader from \"../PanelHeader\";\nimport TimePanelBody from \"./TimePanelBody\";\nexport default function TimePanel(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    showTime = props.showTime;\n  var _ref = showTime || {},\n    format = _ref.format;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n\n  // ========================== Base ==========================\n  var _useInfo = useInfo(props, 'time'),\n    _useInfo2 = _slicedToArray(_useInfo, 1),\n    info = _useInfo2[0];\n\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: info\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls)\n  }, /*#__PURE__*/React.createElement(PanelHeader, null, value ? formatValue(value, {\n    locale: locale,\n    format: format,\n    generateConfig: generateConfig\n  }) : \"\\xA0\"), /*#__PURE__*/React.createElement(TimePanelBody, showTime)));\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,YAAY,EAAEC,OAAO,QAAQ,YAAY;AAClD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,eAAe,SAASC,SAASA,CAACC,KAAK,EAAE;EACvC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;EAC3B,IAAIC,IAAI,GAAGD,QAAQ,IAAI,CAAC,CAAC;IACvBE,MAAM,GAAGD,IAAI,CAACC,MAAM;EACtB,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,aAAa,CAAC;;EAExD;EACA,IAAIS,QAAQ,GAAGd,OAAO,CAACI,KAAK,EAAE,MAAM,CAAC;IACnCW,SAAS,GAAGpB,cAAc,CAACmB,QAAQ,EAAE,CAAC,CAAC;IACvCE,IAAI,GAAGD,SAAS,CAAC,CAAC,CAAC;;EAErB;EACA,OAAO,aAAalB,KAAK,CAACoB,aAAa,CAAClB,YAAY,CAACmB,QAAQ,EAAE;IAC7DZ,KAAK,EAAEU;EACT,CAAC,EAAE,aAAanB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACzCE,SAAS,EAAEvB,UAAU,CAACgB,cAAc;EACtC,CAAC,EAAE,aAAaf,KAAK,CAACoB,aAAa,CAAChB,WAAW,EAAE,IAAI,EAAEK,KAAK,GAAGR,WAAW,CAACQ,KAAK,EAAE;IAChFC,MAAM,EAAEA,MAAM;IACdI,MAAM,EAAEA,MAAM;IACdH,cAAc,EAAEA;EAClB,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,aAAaX,KAAK,CAACoB,aAAa,CAACf,aAAa,EAAEO,QAAQ,CAAC,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
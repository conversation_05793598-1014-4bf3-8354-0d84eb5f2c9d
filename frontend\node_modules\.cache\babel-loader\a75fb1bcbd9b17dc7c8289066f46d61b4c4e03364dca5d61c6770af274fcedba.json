{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport var SEARCH_MARK = '__rc_cascader_search_mark__';\nvar defaultFilter = function defaultFilter(search, options, _ref) {\n  var _ref$label = _ref.label,\n    label = _ref$label === void 0 ? '' : _ref$label;\n  return options.some(function (opt) {\n    return String(opt[label]).toLowerCase().includes(search.toLowerCase());\n  });\n};\nvar defaultRender = function defaultRender(inputValue, path, prefixCls, fieldNames) {\n  return path.map(function (opt) {\n    return opt[fieldNames.label];\n  }).join(' / ');\n};\nvar useSearchOptions = function useSearchOptions(search, options, fieldNames, prefixCls, config, enableHalfPath) {\n  var _config$filter = config.filter,\n    filter = _config$filter === void 0 ? defaultFilter : _config$filter,\n    _config$render = config.render,\n    render = _config$render === void 0 ? defaultRender : _config$render,\n    _config$limit = config.limit,\n    limit = _config$limit === void 0 ? 50 : _config$limit,\n    sort = config.sort;\n  return React.useMemo(function () {\n    var filteredOptions = [];\n    if (!search) {\n      return [];\n    }\n    function dig(list, pathOptions) {\n      var parentDisabled = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      list.forEach(function (option) {\n        // Perf saving when `sort` is disabled and `limit` is provided\n        if (!sort && limit !== false && limit > 0 && filteredOptions.length >= limit) {\n          return;\n        }\n        var connectedPathOptions = [].concat(_toConsumableArray(pathOptions), [option]);\n        var children = option[fieldNames.children];\n        var mergedDisabled = parentDisabled || option.disabled;\n\n        // If current option is filterable\n        if (\n        // If is leaf option\n        !children || children.length === 0 ||\n        // If is changeOnSelect or multiple\n        enableHalfPath) {\n          if (filter(search, connectedPathOptions, {\n            label: fieldNames.label\n          })) {\n            var _objectSpread2;\n            filteredOptions.push(_objectSpread(_objectSpread({}, option), {}, (_objectSpread2 = {\n              disabled: mergedDisabled\n            }, _defineProperty(_objectSpread2, fieldNames.label, render(search, connectedPathOptions, prefixCls, fieldNames)), _defineProperty(_objectSpread2, SEARCH_MARK, connectedPathOptions), _defineProperty(_objectSpread2, fieldNames.children, undefined), _objectSpread2)));\n          }\n        }\n        if (children) {\n          dig(option[fieldNames.children], connectedPathOptions, mergedDisabled);\n        }\n      });\n    }\n    dig(options, []);\n\n    // Do sort\n    if (sort) {\n      filteredOptions.sort(function (a, b) {\n        return sort(a[SEARCH_MARK], b[SEARCH_MARK], search, fieldNames);\n      });\n    }\n    return limit !== false && limit > 0 ? filteredOptions.slice(0, limit) : filteredOptions;\n  }, [search, options, fieldNames, prefixCls, render, enableHalfPath, filter, sort, limit]);\n};\nexport default useSearchOptions;", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_toConsumableArray", "React", "SEARCH_MARK", "defaultFilter", "search", "options", "_ref", "_ref$label", "label", "some", "opt", "String", "toLowerCase", "includes", "defaultRender", "inputValue", "path", "prefixCls", "fieldNames", "map", "join", "useSearchOptions", "config", "enableHalfPath", "_config$filter", "filter", "_config$render", "render", "_config$limit", "limit", "sort", "useMemo", "filteredOptions", "dig", "list", "pathOptions", "parentDisabled", "arguments", "length", "undefined", "for<PERSON>ach", "option", "connectedPathOptions", "concat", "children", "mergedDisabled", "disabled", "_objectSpread2", "push", "a", "b", "slice"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/hooks/useSearchOptions.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport var SEARCH_MARK = '__rc_cascader_search_mark__';\nvar defaultFilter = function defaultFilter(search, options, _ref) {\n  var _ref$label = _ref.label,\n    label = _ref$label === void 0 ? '' : _ref$label;\n  return options.some(function (opt) {\n    return String(opt[label]).toLowerCase().includes(search.toLowerCase());\n  });\n};\nvar defaultRender = function defaultRender(inputValue, path, prefixCls, fieldNames) {\n  return path.map(function (opt) {\n    return opt[fieldNames.label];\n  }).join(' / ');\n};\nvar useSearchOptions = function useSearchOptions(search, options, fieldNames, prefixCls, config, enableHalfPath) {\n  var _config$filter = config.filter,\n    filter = _config$filter === void 0 ? defaultFilter : _config$filter,\n    _config$render = config.render,\n    render = _config$render === void 0 ? defaultRender : _config$render,\n    _config$limit = config.limit,\n    limit = _config$limit === void 0 ? 50 : _config$limit,\n    sort = config.sort;\n  return React.useMemo(function () {\n    var filteredOptions = [];\n    if (!search) {\n      return [];\n    }\n    function dig(list, pathOptions) {\n      var parentDisabled = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      list.forEach(function (option) {\n        // Perf saving when `sort` is disabled and `limit` is provided\n        if (!sort && limit !== false && limit > 0 && filteredOptions.length >= limit) {\n          return;\n        }\n        var connectedPathOptions = [].concat(_toConsumableArray(pathOptions), [option]);\n        var children = option[fieldNames.children];\n        var mergedDisabled = parentDisabled || option.disabled;\n\n        // If current option is filterable\n        if (\n        // If is leaf option\n        !children || children.length === 0 ||\n        // If is changeOnSelect or multiple\n        enableHalfPath) {\n          if (filter(search, connectedPathOptions, {\n            label: fieldNames.label\n          })) {\n            var _objectSpread2;\n            filteredOptions.push(_objectSpread(_objectSpread({}, option), {}, (_objectSpread2 = {\n              disabled: mergedDisabled\n            }, _defineProperty(_objectSpread2, fieldNames.label, render(search, connectedPathOptions, prefixCls, fieldNames)), _defineProperty(_objectSpread2, SEARCH_MARK, connectedPathOptions), _defineProperty(_objectSpread2, fieldNames.children, undefined), _objectSpread2)));\n          }\n        }\n        if (children) {\n          dig(option[fieldNames.children], connectedPathOptions, mergedDisabled);\n        }\n      });\n    }\n    dig(options, []);\n\n    // Do sort\n    if (sort) {\n      filteredOptions.sort(function (a, b) {\n        return sort(a[SEARCH_MARK], b[SEARCH_MARK], search, fieldNames);\n      });\n    }\n    return limit !== false && limit > 0 ? filteredOptions.slice(0, limit) : filteredOptions;\n  }, [search, options, fieldNames, prefixCls, render, enableHalfPath, filter, sort, limit]);\n};\nexport default useSearchOptions;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,WAAW,GAAG,6BAA6B;AACtD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAChE,IAAIC,UAAU,GAAGD,IAAI,CAACE,KAAK;IACzBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,UAAU;EACjD,OAAOF,OAAO,CAACI,IAAI,CAAC,UAAUC,GAAG,EAAE;IACjC,OAAOC,MAAM,CAACD,GAAG,CAACF,KAAK,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,MAAM,CAACQ,WAAW,CAAC,CAAC,CAAC;EACxE,CAAC,CAAC;AACJ,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAClF,OAAOF,IAAI,CAACG,GAAG,CAAC,UAAUT,GAAG,EAAE;IAC7B,OAAOA,GAAG,CAACQ,UAAU,CAACV,KAAK,CAAC;EAC9B,CAAC,CAAC,CAACY,IAAI,CAAC,KAAK,CAAC;AAChB,CAAC;AACD,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACjB,MAAM,EAAEC,OAAO,EAAEa,UAAU,EAAED,SAAS,EAAEK,MAAM,EAAEC,cAAc,EAAE;EAC/G,IAAIC,cAAc,GAAGF,MAAM,CAACG,MAAM;IAChCA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGrB,aAAa,GAAGqB,cAAc;IACnEE,cAAc,GAAGJ,MAAM,CAACK,MAAM;IAC9BA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGZ,aAAa,GAAGY,cAAc;IACnEE,aAAa,GAAGN,MAAM,CAACO,KAAK;IAC5BA,KAAK,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;IACrDE,IAAI,GAAGR,MAAM,CAACQ,IAAI;EACpB,OAAO7B,KAAK,CAAC8B,OAAO,CAAC,YAAY;IAC/B,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAI,CAAC5B,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IACA,SAAS6B,GAAGA,CAACC,IAAI,EAAEC,WAAW,EAAE;MAC9B,IAAIC,cAAc,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC9FH,IAAI,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAE;QAC7B;QACA,IAAI,CAACX,IAAI,IAAID,KAAK,KAAK,KAAK,IAAIA,KAAK,GAAG,CAAC,IAAIG,eAAe,CAACM,MAAM,IAAIT,KAAK,EAAE;UAC5E;QACF;QACA,IAAIa,oBAAoB,GAAG,EAAE,CAACC,MAAM,CAAC3C,kBAAkB,CAACmC,WAAW,CAAC,EAAE,CAACM,MAAM,CAAC,CAAC;QAC/E,IAAIG,QAAQ,GAAGH,MAAM,CAACvB,UAAU,CAAC0B,QAAQ,CAAC;QAC1C,IAAIC,cAAc,GAAGT,cAAc,IAAIK,MAAM,CAACK,QAAQ;;QAEtD;QACA;QACA;QACA,CAACF,QAAQ,IAAIA,QAAQ,CAACN,MAAM,KAAK,CAAC;QAClC;QACAf,cAAc,EAAE;UACd,IAAIE,MAAM,CAACrB,MAAM,EAAEsC,oBAAoB,EAAE;YACvClC,KAAK,EAAEU,UAAU,CAACV;UACpB,CAAC,CAAC,EAAE;YACF,IAAIuC,cAAc;YAClBf,eAAe,CAACgB,IAAI,CAACjD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,MAAM,CAAC,EAAE,CAAC,CAAC,GAAGM,cAAc,GAAG;cAClFD,QAAQ,EAAED;YACZ,CAAC,EAAE/C,eAAe,CAACiD,cAAc,EAAE7B,UAAU,CAACV,KAAK,EAAEmB,MAAM,CAACvB,MAAM,EAAEsC,oBAAoB,EAAEzB,SAAS,EAAEC,UAAU,CAAC,CAAC,EAAEpB,eAAe,CAACiD,cAAc,EAAE7C,WAAW,EAAEwC,oBAAoB,CAAC,EAAE5C,eAAe,CAACiD,cAAc,EAAE7B,UAAU,CAAC0B,QAAQ,EAAEL,SAAS,CAAC,EAAEQ,cAAc,CAAC,CAAC,CAAC;UAC3Q;QACF;QACA,IAAIH,QAAQ,EAAE;UACZX,GAAG,CAACQ,MAAM,CAACvB,UAAU,CAAC0B,QAAQ,CAAC,EAAEF,oBAAoB,EAAEG,cAAc,CAAC;QACxE;MACF,CAAC,CAAC;IACJ;IACAZ,GAAG,CAAC5B,OAAO,EAAE,EAAE,CAAC;;IAEhB;IACA,IAAIyB,IAAI,EAAE;MACRE,eAAe,CAACF,IAAI,CAAC,UAAUmB,CAAC,EAAEC,CAAC,EAAE;QACnC,OAAOpB,IAAI,CAACmB,CAAC,CAAC/C,WAAW,CAAC,EAAEgD,CAAC,CAAChD,WAAW,CAAC,EAAEE,MAAM,EAAEc,UAAU,CAAC;MACjE,CAAC,CAAC;IACJ;IACA,OAAOW,KAAK,KAAK,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAGG,eAAe,CAACmB,KAAK,CAAC,CAAC,EAAEtB,KAAK,CAAC,GAAGG,eAAe;EACzF,CAAC,EAAE,CAAC5B,MAAM,EAAEC,OAAO,EAAEa,UAAU,EAAED,SAAS,EAAEU,MAAM,EAAEJ,cAAc,EAAEE,MAAM,EAAEK,IAAI,EAAED,KAAK,CAAC,CAAC;AAC3F,CAAC;AACD,eAAeR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
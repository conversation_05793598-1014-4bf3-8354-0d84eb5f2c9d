{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport Button from '../../button';\nimport { convertLegacyProps } from '../../button/buttonHelpers';\nimport { ModalContext } from '../context';\nconst NormalOkBtn = () => {\n  const {\n    confirmLoading,\n    okButtonProps,\n    okType,\n    okTextLocale,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(Button, Object.assign({}, convertLegacyProps(okType), {\n    loading: confirmLoading,\n    onClick: onOk\n  }, okButtonProps), okTextLocale);\n};\nexport default NormalOkBtn;", "map": {"version": 3, "names": ["React", "useContext", "<PERSON><PERSON>", "convertLegacyProps", "ModalContext", "NormalOkBtn", "confirmLoading", "okButtonProps", "okType", "okTextLocale", "onOk", "createElement", "Object", "assign", "loading", "onClick"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/modal/components/NormalOkBtn.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport Button from '../../button';\nimport { convertLegacyProps } from '../../button/buttonHelpers';\nimport { ModalContext } from '../context';\nconst NormalOkBtn = () => {\n  const {\n    confirmLoading,\n    okButtonProps,\n    okType,\n    okTextLocale,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(Button, Object.assign({}, convertLegacyProps(okType), {\n    loading: confirmLoading,\n    onClick: onOk\n  }, okButtonProps), okTextLocale);\n};\nexport default NormalOkBtn;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,YAAY,QAAQ,YAAY;AACzC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAM;IACJC,cAAc;IACdC,aAAa;IACbC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGT,UAAU,CAACG,YAAY,CAAC;EAC5B,OAAO,aAAaJ,KAAK,CAACW,aAAa,CAACT,MAAM,EAAEU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,kBAAkB,CAACK,MAAM,CAAC,EAAE;IAC5FM,OAAO,EAAER,cAAc;IACvBS,OAAO,EAAEL;EACX,CAAC,EAAEH,aAAa,CAAC,EAAEE,YAAY,CAAC;AAClC,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport Checkbox from '../checkbox';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nconst ListItem = props => {\n  const {\n    renderedText,\n    renderedEl,\n    item,\n    checked,\n    disabled,\n    prefixCls,\n    onClick,\n    onRemove,\n    showRemove\n  } = props;\n  const className = classNames(`${prefixCls}-content-item`, {\n    [`${prefixCls}-content-item-disabled`]: disabled || item.disabled,\n    [`${prefixCls}-content-item-checked`]: checked && !item.disabled\n  });\n  let title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const liProps = {\n    className,\n    title\n  };\n  const labelNode = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-item-text`\n  }, renderedEl);\n  if (showRemove) {\n    return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), labelNode, /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      disabled: disabled || item.disabled,\n      className: `${prefixCls}-content-item-remove`,\n      \"aria-label\": contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.remove,\n      onClick: () => onRemove === null || onRemove === void 0 ? void 0 : onRemove(item)\n    }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n  }\n  // Default click to select\n  liProps.onClick = disabled || item.disabled ? undefined : event => onClick(item, event);\n  return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n    className: `${prefixCls}-checkbox`,\n    checked: checked,\n    disabled: disabled || item.disabled\n  }), labelNode);\n};\nexport default /*#__PURE__*/React.memo(ListItem);", "map": {"version": 3, "names": ["React", "DeleteOutlined", "classNames", "Checkbox", "useLocale", "defaultLocale", "ListItem", "props", "renderedText", "renderedEl", "item", "checked", "disabled", "prefixCls", "onClick", "onRemove", "showRemove", "className", "title", "String", "contextLocale", "Transfer", "liProps", "labelNode", "createElement", "Object", "assign", "type", "remove", "undefined", "event", "memo"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/transfer/ListItem.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport Checkbox from '../checkbox';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nconst ListItem = props => {\n  const {\n    renderedText,\n    renderedEl,\n    item,\n    checked,\n    disabled,\n    prefixCls,\n    onClick,\n    onRemove,\n    showRemove\n  } = props;\n  const className = classNames(`${prefixCls}-content-item`, {\n    [`${prefixCls}-content-item-disabled`]: disabled || item.disabled,\n    [`${prefixCls}-content-item-checked`]: checked && !item.disabled\n  });\n  let title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const liProps = {\n    className,\n    title\n  };\n  const labelNode = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-item-text`\n  }, renderedEl);\n  if (showRemove) {\n    return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), labelNode, /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      disabled: disabled || item.disabled,\n      className: `${prefixCls}-content-item-remove`,\n      \"aria-label\": contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.remove,\n      onClick: () => onRemove === null || onRemove === void 0 ? void 0 : onRemove(item)\n    }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n  }\n  // Default click to select\n  liProps.onClick = disabled || item.disabled ? undefined : event => onClick(item, event);\n  return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n    className: `${prefixCls}-checkbox`,\n    checked: checked,\n    disabled: disabled || item.disabled\n  }), labelNode);\n};\nexport default /*#__PURE__*/React.memo(ListItem);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,YAAY;IACZC,UAAU;IACVC,IAAI;IACJC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,SAAS,GAAGf,UAAU,CAAC,GAAGW,SAAS,eAAe,EAAE;IACxD,CAAC,GAAGA,SAAS,wBAAwB,GAAGD,QAAQ,IAAIF,IAAI,CAACE,QAAQ;IACjE,CAAC,GAAGC,SAAS,uBAAuB,GAAGF,OAAO,IAAI,CAACD,IAAI,CAACE;EAC1D,CAAC,CAAC;EACF,IAAIM,KAAK;EACT,IAAI,OAAOV,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACxEU,KAAK,GAAGC,MAAM,CAACX,YAAY,CAAC;EAC9B;EACA,MAAM,CAACY,aAAa,CAAC,GAAGhB,SAAS,CAAC,UAAU,EAAEC,aAAa,CAACgB,QAAQ,CAAC;EACrE,MAAMC,OAAO,GAAG;IACdL,SAAS;IACTC;EACF,CAAC;EACD,MAAMK,SAAS,GAAG,aAAavB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IACzDP,SAAS,EAAE,GAAGJ,SAAS;EACzB,CAAC,EAAEJ,UAAU,CAAC;EACd,IAAIO,UAAU,EAAE;IACd,OAAO,aAAahB,KAAK,CAACwB,aAAa,CAAC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAEC,SAAS,EAAE,aAAavB,KAAK,CAACwB,aAAa,CAAC,QAAQ,EAAE;MAC9HG,IAAI,EAAE,QAAQ;MACdf,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE,QAAQ;MACnCK,SAAS,EAAE,GAAGJ,SAAS,sBAAsB;MAC7C,YAAY,EAAEO,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACQ,MAAM;MAChGd,OAAO,EAAEA,CAAA,KAAMC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACL,IAAI;IAClF,CAAC,EAAE,aAAaV,KAAK,CAACwB,aAAa,CAACvB,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;EAC7D;EACA;EACAqB,OAAO,CAACR,OAAO,GAAGF,QAAQ,IAAIF,IAAI,CAACE,QAAQ,GAAGiB,SAAS,GAAGC,KAAK,IAAIhB,OAAO,CAACJ,IAAI,EAAEoB,KAAK,CAAC;EACvF,OAAO,aAAa9B,KAAK,CAACwB,aAAa,CAAC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE,aAAatB,KAAK,CAACwB,aAAa,CAACrB,QAAQ,EAAE;IACnHc,SAAS,EAAE,GAAGJ,SAAS,WAAW;IAClCF,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE;EAC7B,CAAC,CAAC,EAAEW,SAAS,CAAC;AAChB,CAAC;AACD,eAAe,aAAavB,KAAK,CAAC+B,IAAI,CAACzB,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport * as React from 'react';\nimport { toPathKeys } from \"../utils/commonUtil\";\nexport default function useValues(multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues) {\n  // Fill `rawValues` with checked conduction values\n  return React.useMemo(function () {\n    var _getMissingValues = getMissingValues(rawValues),\n      _getMissingValues2 = _slicedToArray(_getMissingValues, 2),\n      existValues = _getMissingValues2[0],\n      missingValues = _getMissingValues2[1];\n    if (!multiple || !rawValues.length) {\n      return [existValues, [], missingValues];\n    }\n    var keyPathValues = toPathKeys(existValues);\n    var keyPathEntities = getPathKeyEntities();\n    var _conductCheck = conductCheck(keyPathValues, true, keyPathEntities),\n      checkedKeys = _conductCheck.checkedKeys,\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n    // Convert key back to value cells\n    return [getValueByKeyPath(checkedKeys), getValueByKeyPath(halfCheckedKeys), missingValues];\n  }, [multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues]);\n}", "map": {"version": 3, "names": ["_slicedToArray", "conduct<PERSON>heck", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useValues", "multiple", "rawValues", "getPathKeyEntities", "getValueByKeyPath", "getMissingValues", "useMemo", "_getM<PERSON>ing<PERSON><PERSON><PERSON>", "_getMissingValues2", "existValues", "<PERSON><PERSON><PERSON><PERSON>", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyPathEntities", "_conductCheck", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/hooks/useValues.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport * as React from 'react';\nimport { toPathKeys } from \"../utils/commonUtil\";\nexport default function useValues(multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues) {\n  // Fill `rawValues` with checked conduction values\n  return React.useMemo(function () {\n    var _getMissingValues = getMissingValues(rawValues),\n      _getMissingValues2 = _slicedToArray(_getMissingValues, 2),\n      existValues = _getMissingValues2[0],\n      missingValues = _getMissingValues2[1];\n    if (!multiple || !rawValues.length) {\n      return [existValues, [], missingValues];\n    }\n    var keyPathValues = toPathKeys(existValues);\n    var keyPathEntities = getPathKeyEntities();\n    var _conductCheck = conductCheck(keyPathValues, true, keyPathEntities),\n      checkedKeys = _conductCheck.checkedKeys,\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n    // Convert key back to value cells\n    return [getValueByKeyPath(checkedKeys), getValueByKeyPath(halfCheckedKeys), missingValues];\n  }, [multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues]);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,qBAAqB;AAChD,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAE;EAC9G;EACA,OAAOP,KAAK,CAACQ,OAAO,CAAC,YAAY;IAC/B,IAAIC,iBAAiB,GAAGF,gBAAgB,CAACH,SAAS,CAAC;MACjDM,kBAAkB,GAAGZ,cAAc,CAACW,iBAAiB,EAAE,CAAC,CAAC;MACzDE,WAAW,GAAGD,kBAAkB,CAAC,CAAC,CAAC;MACnCE,aAAa,GAAGF,kBAAkB,CAAC,CAAC,CAAC;IACvC,IAAI,CAACP,QAAQ,IAAI,CAACC,SAAS,CAACS,MAAM,EAAE;MAClC,OAAO,CAACF,WAAW,EAAE,EAAE,EAAEC,aAAa,CAAC;IACzC;IACA,IAAIE,aAAa,GAAGb,UAAU,CAACU,WAAW,CAAC;IAC3C,IAAII,eAAe,GAAGV,kBAAkB,CAAC,CAAC;IAC1C,IAAIW,aAAa,GAAGjB,YAAY,CAACe,aAAa,EAAE,IAAI,EAAEC,eAAe,CAAC;MACpEE,WAAW,GAAGD,aAAa,CAACC,WAAW;MACvCC,eAAe,GAAGF,aAAa,CAACE,eAAe;;IAEjD;IACA,OAAO,CAACZ,iBAAiB,CAACW,WAAW,CAAC,EAAEX,iBAAiB,CAACY,eAAe,CAAC,EAAEN,aAAa,CAAC;EAC5F,CAAC,EAAE,CAACT,QAAQ,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
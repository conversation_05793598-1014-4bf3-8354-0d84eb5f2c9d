{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nexport default function usePresets(presets, legacyRanges) {\n  return React.useMemo(function () {\n    if (presets) {\n      return presets;\n    }\n    if (legacyRanges) {\n      warning(false, '`ranges` is deprecated. Please use `presets` instead.');\n      return Object.entries(legacyRanges).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          label = _ref2[0],\n          value = _ref2[1];\n        return {\n          label: label,\n          value: value\n        };\n      });\n    }\n    return [];\n  }, [presets, legacyRanges]);\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "warning", "usePresets", "presets", "legacyRanges", "useMemo", "Object", "entries", "map", "_ref", "_ref2", "label", "value"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/usePresets.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nexport default function usePresets(presets, legacyRanges) {\n  return React.useMemo(function () {\n    if (presets) {\n      return presets;\n    }\n    if (legacyRanges) {\n      warning(false, '`ranges` is deprecated. Please use `presets` instead.');\n      return Object.entries(legacyRanges).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          label = _ref2[0],\n          value = _ref2[1];\n        return {\n          label: label,\n          value: value\n        };\n      });\n    }\n    return [];\n  }, [presets, legacyRanges]);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,eAAe,SAASC,UAAUA,CAACC,OAAO,EAAEC,YAAY,EAAE;EACxD,OAAOJ,KAAK,CAACK,OAAO,CAAC,YAAY;IAC/B,IAAIF,OAAO,EAAE;MACX,OAAOA,OAAO;IAChB;IACA,IAAIC,YAAY,EAAE;MAChBH,OAAO,CAAC,KAAK,EAAE,uDAAuD,CAAC;MACvE,OAAOK,MAAM,CAACC,OAAO,CAACH,YAAY,CAAC,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAE;QACtD,IAAIC,KAAK,GAAGX,cAAc,CAACU,IAAI,EAAE,CAAC,CAAC;UACjCE,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;UAChBE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC;QAClB,OAAO;UACLC,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA;QACT,CAAC;MACH,CAAC,CAAC;IACJ;IACA,OAAO,EAAE;EACX,CAAC,EAAE,CAACT,OAAO,EAAEC,YAAY,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
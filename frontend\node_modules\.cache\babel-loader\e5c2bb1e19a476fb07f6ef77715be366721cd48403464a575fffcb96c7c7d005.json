{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isValidText } from './util';\nconst MeasureText = /*#__PURE__*/React.forwardRef(({\n  style,\n  children\n}, ref) => {\n  const spanRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    isExceed: () => {\n      const span = spanRef.current;\n      return span.scrollHeight > span.clientHeight;\n    },\n    getHeight: () => spanRef.current.clientHeight\n  }));\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: spanRef,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      pointerEvents: 'none',\n      backgroundColor: 'rgba(255, 0, 0, 0.65)'\n    }, style)\n  }, children);\n});\nconst getNodesLen = nodeList => nodeList.reduce((totalLen, node) => totalLen + (isValidText(node) ? String(node).length : 1), 0);\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = isValidText(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\n// Measure for the `text` is exceed the `rows` or not\nconst STATUS_MEASURE_NONE = 0;\nconst STATUS_MEASURE_PREPARE = 1;\nconst STATUS_MEASURE_START = 2;\nconst STATUS_MEASURE_NEED_ELLIPSIS = 3;\nconst STATUS_MEASURE_NO_NEED_ELLIPSIS = 4;\nconst lineClipStyle = {\n  display: '-webkit-box',\n  overflow: 'hidden',\n  WebkitBoxOrient: 'vertical'\n};\nexport default function EllipsisMeasure(props) {\n  const {\n    enableMeasure,\n    width,\n    text,\n    children,\n    rows,\n    expanded,\n    miscDeps,\n    onEllipsis\n  } = props;\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const nodeLen = React.useMemo(() => getNodesLen(nodeList), [text]);\n  // ========================= Full Content =========================\n  // Used for measure only, which means it's always render as no need ellipsis\n  const fullContent = React.useMemo(() => children(nodeList, false), [text]);\n  // ========================= Cut Content ==========================\n  const [ellipsisCutIndex, setEllipsisCutIndex] = React.useState(null);\n  const cutMidRef = React.useRef(null);\n  // ========================= NeedEllipsis =========================\n  const measureWhiteSpaceRef = React.useRef(null);\n  const needEllipsisRef = React.useRef(null);\n  // Measure for `rows-1` height, to avoid operation exceed the line height\n  const descRowsEllipsisRef = React.useRef(null);\n  const symbolRowEllipsisRef = React.useRef(null);\n  const [canEllipsis, setCanEllipsis] = React.useState(false);\n  const [needEllipsis, setNeedEllipsis] = React.useState(STATUS_MEASURE_NONE);\n  const [ellipsisHeight, setEllipsisHeight] = React.useState(0);\n  const [parentWhiteSpace, setParentWhiteSpace] = React.useState(null);\n  // Trigger start measure\n  useLayoutEffect(() => {\n    if (enableMeasure && width && nodeLen) {\n      setNeedEllipsis(STATUS_MEASURE_PREPARE);\n    } else {\n      setNeedEllipsis(STATUS_MEASURE_NONE);\n    }\n  }, [width, text, rows, enableMeasure, nodeList]);\n  // Measure process\n  useLayoutEffect(() => {\n    var _a, _b, _c, _d;\n    if (needEllipsis === STATUS_MEASURE_PREPARE) {\n      setNeedEllipsis(STATUS_MEASURE_START);\n      // Parent ref `white-space`\n      const nextWhiteSpace = measureWhiteSpaceRef.current && getComputedStyle(measureWhiteSpaceRef.current).whiteSpace;\n      setParentWhiteSpace(nextWhiteSpace);\n    } else if (needEllipsis === STATUS_MEASURE_START) {\n      const isOverflow = !!((_a = needEllipsisRef.current) === null || _a === void 0 ? void 0 : _a.isExceed());\n      setNeedEllipsis(isOverflow ? STATUS_MEASURE_NEED_ELLIPSIS : STATUS_MEASURE_NO_NEED_ELLIPSIS);\n      setEllipsisCutIndex(isOverflow ? [0, nodeLen] : null);\n      setCanEllipsis(isOverflow);\n      // Get the basic height of ellipsis rows\n      const baseRowsEllipsisHeight = ((_b = needEllipsisRef.current) === null || _b === void 0 ? void 0 : _b.getHeight()) || 0;\n      // Get the height of `rows - 1` + symbol height\n      const descRowsEllipsisHeight = rows === 1 ? 0 : ((_c = descRowsEllipsisRef.current) === null || _c === void 0 ? void 0 : _c.getHeight()) || 0;\n      const symbolRowEllipsisHeight = ((_d = symbolRowEllipsisRef.current) === null || _d === void 0 ? void 0 : _d.getHeight()) || 0;\n      const maxRowsHeight = Math.max(baseRowsEllipsisHeight,\n      // height of rows with ellipsis\n      descRowsEllipsisHeight + symbolRowEllipsisHeight);\n      setEllipsisHeight(maxRowsHeight + 1);\n      onEllipsis(isOverflow);\n    }\n  }, [needEllipsis]);\n  // ========================= Cut Measure ==========================\n  const cutMidIndex = ellipsisCutIndex ? Math.ceil((ellipsisCutIndex[0] + ellipsisCutIndex[1]) / 2) : 0;\n  useLayoutEffect(() => {\n    var _a;\n    const [minIndex, maxIndex] = ellipsisCutIndex || [0, 0];\n    if (minIndex !== maxIndex) {\n      const midHeight = ((_a = cutMidRef.current) === null || _a === void 0 ? void 0 : _a.getHeight()) || 0;\n      const isOverflow = midHeight > ellipsisHeight;\n      let targetMidIndex = cutMidIndex;\n      if (maxIndex - minIndex === 1) {\n        targetMidIndex = isOverflow ? minIndex : maxIndex;\n      }\n      setEllipsisCutIndex(isOverflow ? [minIndex, targetMidIndex] : [targetMidIndex, maxIndex]);\n    }\n  }, [ellipsisCutIndex, cutMidIndex]);\n  // ========================= Text Content =========================\n  const finalContent = React.useMemo(() => {\n    // Skip everything if `enableMeasure` is disabled\n    if (!enableMeasure) {\n      return children(nodeList, false);\n    }\n    if (needEllipsis !== STATUS_MEASURE_NEED_ELLIPSIS || !ellipsisCutIndex || ellipsisCutIndex[0] !== ellipsisCutIndex[1]) {\n      const content = children(nodeList, false);\n      // Limit the max line count to avoid scrollbar blink unless no need ellipsis\n      // https://github.com/ant-design/ant-design/issues/42958\n      if ([STATUS_MEASURE_NO_NEED_ELLIPSIS, STATUS_MEASURE_NONE].includes(needEllipsis)) {\n        return content;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: Object.assign(Object.assign({}, lineClipStyle), {\n          WebkitLineClamp: rows\n        })\n      }, content);\n    }\n    return children(expanded ? nodeList : sliceNodes(nodeList, ellipsisCutIndex[0]), canEllipsis);\n  }, [expanded, needEllipsis, ellipsisCutIndex, nodeList].concat(_toConsumableArray(miscDeps)));\n  // ============================ Render ============================\n  const measureStyle = {\n    width,\n    margin: 0,\n    padding: 0,\n    whiteSpace: parentWhiteSpace === 'nowrap' ? 'normal' : 'inherit'\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, finalContent, needEllipsis === STATUS_MEASURE_START && (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows\n    }),\n    ref: needEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows - 1\n    }),\n    ref: descRowsEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: 1\n    }),\n    ref: symbolRowEllipsisRef\n  }, children([], true)))), needEllipsis === STATUS_MEASURE_NEED_ELLIPSIS && ellipsisCutIndex && ellipsisCutIndex[0] !== ellipsisCutIndex[1] && (/*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign({}, measureStyle), {\n      top: 400\n    }),\n    ref: cutMidRef\n  }, children(sliceNodes(nodeList, cutMidIndex), true))), needEllipsis === STATUS_MEASURE_PREPARE && (/*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      whiteSpace: 'inherit'\n    },\n    ref: measureWhiteSpaceRef\n  })));\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "toArray", "useLayoutEffect", "isValidText", "MeasureText", "forwardRef", "style", "children", "ref", "spanRef", "useRef", "useImperativeHandle", "isExceed", "span", "current", "scrollHeight", "clientHeight", "getHeight", "createElement", "Object", "assign", "position", "display", "left", "top", "pointerEvents", "backgroundColor", "getNodesLen", "nodeList", "reduce", "totalLen", "node", "String", "length", "sliceNodes", "len", "currLen", "currentNodeList", "i", "canCut", "nodeLen", "nextLen", "restLen", "push", "slice", "STATUS_MEASURE_NONE", "STATUS_MEASURE_PREPARE", "STATUS_MEASURE_START", "STATUS_MEASURE_NEED_ELLIPSIS", "STATUS_MEASURE_NO_NEED_ELLIPSIS", "lineClipStyle", "overflow", "WebkitBoxOrient", "EllipsisMeasure", "props", "enableMeasure", "width", "text", "rows", "expanded", "miscDeps", "onEllipsis", "useMemo", "fullContent", "ellipsisCutIndex", "setEllipsisCutIndex", "useState", "cutMidRef", "measureWhiteSpaceRef", "needEllipsisRef", "descRowsEllipsisRef", "symbolRowEllipsisRef", "canEllipsis", "setCanEllipsis", "needEllipsis", "setNeedEllipsis", "ellipsisHeight", "setEllipsisHeight", "parentWhiteSpace", "setParentWhiteSpace", "_a", "_b", "_c", "_d", "nextWhiteSpace", "getComputedStyle", "whiteSpace", "isOverflow", "baseRowsEllipsisHeight", "descRowsEllipsisHeight", "symbolRowEllipsisHeight", "maxRowsHeight", "Math", "max", "cutMidIndex", "ceil", "minIndex", "maxIndex", "midHeight", "targetMidIndex", "finalContent", "content", "includes", "WebkitLineClamp", "concat", "measureStyle", "margin", "padding", "Fragment"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/Base/Ellipsis.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isValidText } from './util';\nconst MeasureText = /*#__PURE__*/React.forwardRef(({\n  style,\n  children\n}, ref) => {\n  const spanRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    isExceed: () => {\n      const span = spanRef.current;\n      return span.scrollHeight > span.clientHeight;\n    },\n    getHeight: () => spanRef.current.clientHeight\n  }));\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: spanRef,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      pointerEvents: 'none',\n      backgroundColor: 'rgba(255, 0, 0, 0.65)'\n    }, style)\n  }, children);\n});\nconst getNodesLen = nodeList => nodeList.reduce((totalLen, node) => totalLen + (isValidText(node) ? String(node).length : 1), 0);\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = isValidText(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\n// Measure for the `text` is exceed the `rows` or not\nconst STATUS_MEASURE_NONE = 0;\nconst STATUS_MEASURE_PREPARE = 1;\nconst STATUS_MEASURE_START = 2;\nconst STATUS_MEASURE_NEED_ELLIPSIS = 3;\nconst STATUS_MEASURE_NO_NEED_ELLIPSIS = 4;\nconst lineClipStyle = {\n  display: '-webkit-box',\n  overflow: 'hidden',\n  WebkitBoxOrient: 'vertical'\n};\nexport default function EllipsisMeasure(props) {\n  const {\n    enableMeasure,\n    width,\n    text,\n    children,\n    rows,\n    expanded,\n    miscDeps,\n    onEllipsis\n  } = props;\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const nodeLen = React.useMemo(() => getNodesLen(nodeList), [text]);\n  // ========================= Full Content =========================\n  // Used for measure only, which means it's always render as no need ellipsis\n  const fullContent = React.useMemo(() => children(nodeList, false), [text]);\n  // ========================= Cut Content ==========================\n  const [ellipsisCutIndex, setEllipsisCutIndex] = React.useState(null);\n  const cutMidRef = React.useRef(null);\n  // ========================= NeedEllipsis =========================\n  const measureWhiteSpaceRef = React.useRef(null);\n  const needEllipsisRef = React.useRef(null);\n  // Measure for `rows-1` height, to avoid operation exceed the line height\n  const descRowsEllipsisRef = React.useRef(null);\n  const symbolRowEllipsisRef = React.useRef(null);\n  const [canEllipsis, setCanEllipsis] = React.useState(false);\n  const [needEllipsis, setNeedEllipsis] = React.useState(STATUS_MEASURE_NONE);\n  const [ellipsisHeight, setEllipsisHeight] = React.useState(0);\n  const [parentWhiteSpace, setParentWhiteSpace] = React.useState(null);\n  // Trigger start measure\n  useLayoutEffect(() => {\n    if (enableMeasure && width && nodeLen) {\n      setNeedEllipsis(STATUS_MEASURE_PREPARE);\n    } else {\n      setNeedEllipsis(STATUS_MEASURE_NONE);\n    }\n  }, [width, text, rows, enableMeasure, nodeList]);\n  // Measure process\n  useLayoutEffect(() => {\n    var _a, _b, _c, _d;\n    if (needEllipsis === STATUS_MEASURE_PREPARE) {\n      setNeedEllipsis(STATUS_MEASURE_START);\n      // Parent ref `white-space`\n      const nextWhiteSpace = measureWhiteSpaceRef.current && getComputedStyle(measureWhiteSpaceRef.current).whiteSpace;\n      setParentWhiteSpace(nextWhiteSpace);\n    } else if (needEllipsis === STATUS_MEASURE_START) {\n      const isOverflow = !!((_a = needEllipsisRef.current) === null || _a === void 0 ? void 0 : _a.isExceed());\n      setNeedEllipsis(isOverflow ? STATUS_MEASURE_NEED_ELLIPSIS : STATUS_MEASURE_NO_NEED_ELLIPSIS);\n      setEllipsisCutIndex(isOverflow ? [0, nodeLen] : null);\n      setCanEllipsis(isOverflow);\n      // Get the basic height of ellipsis rows\n      const baseRowsEllipsisHeight = ((_b = needEllipsisRef.current) === null || _b === void 0 ? void 0 : _b.getHeight()) || 0;\n      // Get the height of `rows - 1` + symbol height\n      const descRowsEllipsisHeight = rows === 1 ? 0 : ((_c = descRowsEllipsisRef.current) === null || _c === void 0 ? void 0 : _c.getHeight()) || 0;\n      const symbolRowEllipsisHeight = ((_d = symbolRowEllipsisRef.current) === null || _d === void 0 ? void 0 : _d.getHeight()) || 0;\n      const maxRowsHeight = Math.max(baseRowsEllipsisHeight,\n      // height of rows with ellipsis\n      descRowsEllipsisHeight + symbolRowEllipsisHeight);\n      setEllipsisHeight(maxRowsHeight + 1);\n      onEllipsis(isOverflow);\n    }\n  }, [needEllipsis]);\n  // ========================= Cut Measure ==========================\n  const cutMidIndex = ellipsisCutIndex ? Math.ceil((ellipsisCutIndex[0] + ellipsisCutIndex[1]) / 2) : 0;\n  useLayoutEffect(() => {\n    var _a;\n    const [minIndex, maxIndex] = ellipsisCutIndex || [0, 0];\n    if (minIndex !== maxIndex) {\n      const midHeight = ((_a = cutMidRef.current) === null || _a === void 0 ? void 0 : _a.getHeight()) || 0;\n      const isOverflow = midHeight > ellipsisHeight;\n      let targetMidIndex = cutMidIndex;\n      if (maxIndex - minIndex === 1) {\n        targetMidIndex = isOverflow ? minIndex : maxIndex;\n      }\n      setEllipsisCutIndex(isOverflow ? [minIndex, targetMidIndex] : [targetMidIndex, maxIndex]);\n    }\n  }, [ellipsisCutIndex, cutMidIndex]);\n  // ========================= Text Content =========================\n  const finalContent = React.useMemo(() => {\n    // Skip everything if `enableMeasure` is disabled\n    if (!enableMeasure) {\n      return children(nodeList, false);\n    }\n    if (needEllipsis !== STATUS_MEASURE_NEED_ELLIPSIS || !ellipsisCutIndex || ellipsisCutIndex[0] !== ellipsisCutIndex[1]) {\n      const content = children(nodeList, false);\n      // Limit the max line count to avoid scrollbar blink unless no need ellipsis\n      // https://github.com/ant-design/ant-design/issues/42958\n      if ([STATUS_MEASURE_NO_NEED_ELLIPSIS, STATUS_MEASURE_NONE].includes(needEllipsis)) {\n        return content;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: Object.assign(Object.assign({}, lineClipStyle), {\n          WebkitLineClamp: rows\n        })\n      }, content);\n    }\n    return children(expanded ? nodeList : sliceNodes(nodeList, ellipsisCutIndex[0]), canEllipsis);\n  }, [expanded, needEllipsis, ellipsisCutIndex, nodeList].concat(_toConsumableArray(miscDeps)));\n  // ============================ Render ============================\n  const measureStyle = {\n    width,\n    margin: 0,\n    padding: 0,\n    whiteSpace: parentWhiteSpace === 'nowrap' ? 'normal' : 'inherit'\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, finalContent, needEllipsis === STATUS_MEASURE_START && (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows\n    }),\n    ref: needEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows - 1\n    }),\n    ref: descRowsEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: 1\n    }),\n    ref: symbolRowEllipsisRef\n  }, children([], true)))), needEllipsis === STATUS_MEASURE_NEED_ELLIPSIS && ellipsisCutIndex && ellipsisCutIndex[0] !== ellipsisCutIndex[1] && (/*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign({}, measureStyle), {\n      top: 400\n    }),\n    ref: cutMidRef\n  }, children(sliceNodes(nodeList, cutMidIndex), true))), needEllipsis === STATUS_MEASURE_PREPARE && (/*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      whiteSpace: 'inherit'\n    },\n    ref: measureWhiteSpaceRef\n  })));\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,WAAW,QAAQ,QAAQ;AACpC,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACjDC,KAAK;EACLC;AACF,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,OAAO,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAClCV,KAAK,CAACW,mBAAmB,CAACH,GAAG,EAAE,OAAO;IACpCI,QAAQ,EAAEA,CAAA,KAAM;MACd,MAAMC,IAAI,GAAGJ,OAAO,CAACK,OAAO;MAC5B,OAAOD,IAAI,CAACE,YAAY,GAAGF,IAAI,CAACG,YAAY;IAC9C,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAMR,OAAO,CAACK,OAAO,CAACE;EACnC,CAAC,CAAC,CAAC;EACH,OAAO,aAAahB,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC9C,aAAa,EAAE,IAAI;IACnBV,GAAG,EAAEC,OAAO;IACZH,KAAK,EAAEa,MAAM,CAACC,MAAM,CAAC;MACnBC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNC,aAAa,EAAE,MAAM;MACrBC,eAAe,EAAE;IACnB,CAAC,EAAEpB,KAAK;EACV,CAAC,EAAEC,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,MAAMoB,WAAW,GAAGC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAACC,QAAQ,EAAEC,IAAI,KAAKD,QAAQ,IAAI3B,WAAW,CAAC4B,IAAI,CAAC,GAAGC,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAChI,SAASC,UAAUA,CAACN,QAAQ,EAAEO,GAAG,EAAE;EACjC,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMC,eAAe,GAAG,EAAE;EAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,QAAQ,CAACK,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC3C;IACA,IAAIF,OAAO,KAAKD,GAAG,EAAE;MACnB,OAAOE,eAAe;IACxB;IACA,MAAMN,IAAI,GAAGH,QAAQ,CAACU,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAGpC,WAAW,CAAC4B,IAAI,CAAC;IAChC,MAAMS,OAAO,GAAGD,MAAM,GAAGP,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,GAAG,CAAC;IAChD,MAAMQ,OAAO,GAAGL,OAAO,GAAGI,OAAO;IACjC;IACA;IACA,IAAIC,OAAO,GAAGN,GAAG,EAAE;MACjB,MAAMO,OAAO,GAAGP,GAAG,GAAGC,OAAO;MAC7BC,eAAe,CAACM,IAAI,CAACX,MAAM,CAACD,IAAI,CAAC,CAACa,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC,CAAC;MACpD,OAAOL,eAAe;IACxB;IACAA,eAAe,CAACM,IAAI,CAACZ,IAAI,CAAC;IAC1BK,OAAO,GAAGK,OAAO;EACnB;EACA,OAAOb,QAAQ;AACjB;AACA;AACA,MAAMiB,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,4BAA4B,GAAG,CAAC;AACtC,MAAMC,+BAA+B,GAAG,CAAC;AACzC,MAAMC,aAAa,GAAG;EACpB5B,OAAO,EAAE,aAAa;EACtB6B,QAAQ,EAAE,QAAQ;EAClBC,eAAe,EAAE;AACnB,CAAC;AACD,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7C,MAAM;IACJC,aAAa;IACbC,KAAK;IACLC,IAAI;IACJlD,QAAQ;IACRmD,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGP,KAAK;EACT,MAAM1B,QAAQ,GAAG5B,KAAK,CAAC8D,OAAO,CAAC,MAAM7D,OAAO,CAACwD,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAC3D,MAAMjB,OAAO,GAAGxC,KAAK,CAAC8D,OAAO,CAAC,MAAMnC,WAAW,CAACC,QAAQ,CAAC,EAAE,CAAC6B,IAAI,CAAC,CAAC;EAClE;EACA;EACA,MAAMM,WAAW,GAAG/D,KAAK,CAAC8D,OAAO,CAAC,MAAMvD,QAAQ,CAACqB,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC6B,IAAI,CAAC,CAAC;EAC1E;EACA,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,KAAK,CAACkE,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAMC,SAAS,GAAGnE,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EACpC;EACA,MAAM0D,oBAAoB,GAAGpE,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAC/C,MAAM2D,eAAe,GAAGrE,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAC1C;EACA,MAAM4D,mBAAmB,GAAGtE,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAC9C,MAAM6D,oBAAoB,GAAGvE,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAC/C,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAGzE,KAAK,CAACkE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG3E,KAAK,CAACkE,QAAQ,CAACrB,mBAAmB,CAAC;EAC3E,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,KAAK,CAACkE,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,KAAK,CAACkE,QAAQ,CAAC,IAAI,CAAC;EACpE;EACAhE,eAAe,CAAC,MAAM;IACpB,IAAIqD,aAAa,IAAIC,KAAK,IAAIhB,OAAO,EAAE;MACrCmC,eAAe,CAAC7B,sBAAsB,CAAC;IACzC,CAAC,MAAM;MACL6B,eAAe,CAAC9B,mBAAmB,CAAC;IACtC;EACF,CAAC,EAAE,CAACW,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEH,aAAa,EAAE3B,QAAQ,CAAC,CAAC;EAChD;EACA1B,eAAe,CAAC,MAAM;IACpB,IAAI8E,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,IAAIT,YAAY,KAAK5B,sBAAsB,EAAE;MAC3C6B,eAAe,CAAC5B,oBAAoB,CAAC;MACrC;MACA,MAAMqC,cAAc,GAAGhB,oBAAoB,CAACtD,OAAO,IAAIuE,gBAAgB,CAACjB,oBAAoB,CAACtD,OAAO,CAAC,CAACwE,UAAU;MAChHP,mBAAmB,CAACK,cAAc,CAAC;IACrC,CAAC,MAAM,IAAIV,YAAY,KAAK3B,oBAAoB,EAAE;MAChD,MAAMwC,UAAU,GAAG,CAAC,EAAE,CAACP,EAAE,GAAGX,eAAe,CAACvD,OAAO,MAAM,IAAI,IAAIkE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpE,QAAQ,CAAC,CAAC,CAAC;MACxG+D,eAAe,CAACY,UAAU,GAAGvC,4BAA4B,GAAGC,+BAA+B,CAAC;MAC5FgB,mBAAmB,CAACsB,UAAU,GAAG,CAAC,CAAC,EAAE/C,OAAO,CAAC,GAAG,IAAI,CAAC;MACrDiC,cAAc,CAACc,UAAU,CAAC;MAC1B;MACA,MAAMC,sBAAsB,GAAG,CAAC,CAACP,EAAE,GAAGZ,eAAe,CAACvD,OAAO,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChE,SAAS,CAAC,CAAC,KAAK,CAAC;MACxH;MACA,MAAMwE,sBAAsB,GAAG/B,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAACwB,EAAE,GAAGZ,mBAAmB,CAACxD,OAAO,MAAM,IAAI,IAAIoE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjE,SAAS,CAAC,CAAC,KAAK,CAAC;MAC7I,MAAMyE,uBAAuB,GAAG,CAAC,CAACP,EAAE,GAAGZ,oBAAoB,CAACzD,OAAO,MAAM,IAAI,IAAIqE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClE,SAAS,CAAC,CAAC,KAAK,CAAC;MAC9H,MAAM0E,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACL,sBAAsB;MACrD;MACAC,sBAAsB,GAAGC,uBAAuB,CAAC;MACjDb,iBAAiB,CAACc,aAAa,GAAG,CAAC,CAAC;MACpC9B,UAAU,CAAC0B,UAAU,CAAC;IACxB;EACF,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;EAClB;EACA,MAAMoB,WAAW,GAAG9B,gBAAgB,GAAG4B,IAAI,CAACG,IAAI,CAAC,CAAC/B,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACrG9D,eAAe,CAAC,MAAM;IACpB,IAAI8E,EAAE;IACN,MAAM,CAACgB,QAAQ,EAAEC,QAAQ,CAAC,GAAGjC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,IAAIgC,QAAQ,KAAKC,QAAQ,EAAE;MACzB,MAAMC,SAAS,GAAG,CAAC,CAAClB,EAAE,GAAGb,SAAS,CAACrD,OAAO,MAAM,IAAI,IAAIkE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/D,SAAS,CAAC,CAAC,KAAK,CAAC;MACrG,MAAMsE,UAAU,GAAGW,SAAS,GAAGtB,cAAc;MAC7C,IAAIuB,cAAc,GAAGL,WAAW;MAChC,IAAIG,QAAQ,GAAGD,QAAQ,KAAK,CAAC,EAAE;QAC7BG,cAAc,GAAGZ,UAAU,GAAGS,QAAQ,GAAGC,QAAQ;MACnD;MACAhC,mBAAmB,CAACsB,UAAU,GAAG,CAACS,QAAQ,EAAEG,cAAc,CAAC,GAAG,CAACA,cAAc,EAAEF,QAAQ,CAAC,CAAC;IAC3F;EACF,CAAC,EAAE,CAACjC,gBAAgB,EAAE8B,WAAW,CAAC,CAAC;EACnC;EACA,MAAMM,YAAY,GAAGpG,KAAK,CAAC8D,OAAO,CAAC,MAAM;IACvC;IACA,IAAI,CAACP,aAAa,EAAE;MAClB,OAAOhD,QAAQ,CAACqB,QAAQ,EAAE,KAAK,CAAC;IAClC;IACA,IAAI8C,YAAY,KAAK1B,4BAA4B,IAAI,CAACgB,gBAAgB,IAAIA,gBAAgB,CAAC,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,CAAC,EAAE;MACrH,MAAMqC,OAAO,GAAG9F,QAAQ,CAACqB,QAAQ,EAAE,KAAK,CAAC;MACzC;MACA;MACA,IAAI,CAACqB,+BAA+B,EAAEJ,mBAAmB,CAAC,CAACyD,QAAQ,CAAC5B,YAAY,CAAC,EAAE;QACjF,OAAO2B,OAAO;MAChB;MACA,OAAO,aAAarG,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;QAC9CZ,KAAK,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE8B,aAAa,CAAC,EAAE;UACrDqD,eAAe,EAAE7C;QACnB,CAAC;MACH,CAAC,EAAE2C,OAAO,CAAC;IACb;IACA,OAAO9F,QAAQ,CAACoD,QAAQ,GAAG/B,QAAQ,GAAGM,UAAU,CAACN,QAAQ,EAAEoC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAEQ,WAAW,CAAC;EAC/F,CAAC,EAAE,CAACb,QAAQ,EAAEe,YAAY,EAAEV,gBAAgB,EAAEpC,QAAQ,CAAC,CAAC4E,MAAM,CAACzG,kBAAkB,CAAC6D,QAAQ,CAAC,CAAC,CAAC;EAC7F;EACA,MAAM6C,YAAY,GAAG;IACnBjD,KAAK;IACLkD,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVrB,UAAU,EAAER,gBAAgB,KAAK,QAAQ,GAAG,QAAQ,GAAG;EACzD,CAAC;EACD,OAAO,aAAa9E,KAAK,CAACkB,aAAa,CAAClB,KAAK,CAAC4G,QAAQ,EAAE,IAAI,EAAER,YAAY,EAAE1B,YAAY,KAAK3B,oBAAoB,KAAK,aAAa/C,KAAK,CAACkB,aAAa,CAAClB,KAAK,CAAC4G,QAAQ,EAAE,IAAI,EAAE,aAAa5G,KAAK,CAACkB,aAAa,CAACd,WAAW,EAAE;IACzNE,KAAK,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqF,YAAY,CAAC,EAAEvD,aAAa,CAAC,EAAE;MAClFqD,eAAe,EAAE7C;IACnB,CAAC,CAAC;IACFlD,GAAG,EAAE6D;EACP,CAAC,EAAEN,WAAW,CAAC,EAAE,aAAa/D,KAAK,CAACkB,aAAa,CAACd,WAAW,EAAE;IAC7DE,KAAK,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqF,YAAY,CAAC,EAAEvD,aAAa,CAAC,EAAE;MAClFqD,eAAe,EAAE7C,IAAI,GAAG;IAC1B,CAAC,CAAC;IACFlD,GAAG,EAAE8D;EACP,CAAC,EAAEP,WAAW,CAAC,EAAE,aAAa/D,KAAK,CAACkB,aAAa,CAACd,WAAW,EAAE;IAC7DE,KAAK,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqF,YAAY,CAAC,EAAEvD,aAAa,CAAC,EAAE;MAClFqD,eAAe,EAAE;IACnB,CAAC,CAAC;IACF/F,GAAG,EAAE+D;EACP,CAAC,EAAEhE,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEmE,YAAY,KAAK1B,4BAA4B,IAAIgB,gBAAgB,IAAIA,gBAAgB,CAAC,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,CAAC,KAAK,aAAahE,KAAK,CAACkB,aAAa,CAACd,WAAW,EAAE;IAC3LE,KAAK,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqF,YAAY,CAAC,EAAE;MACpDjF,GAAG,EAAE;IACP,CAAC,CAAC;IACFhB,GAAG,EAAE2D;EACP,CAAC,EAAE5D,QAAQ,CAAC2B,UAAU,CAACN,QAAQ,EAAEkE,WAAW,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAEpB,YAAY,KAAK5B,sBAAsB,KAAK,aAAa9C,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC3IZ,KAAK,EAAE;MACLgF,UAAU,EAAE;IACd,CAAC;IACD9E,GAAG,EAAE4D;EACP,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var mergedBuiltinPlacements = React.useMemo(function () {\n    return builtinPlacements || getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [builtinPlacements, dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // =================== Popup Width ===================\n  var isNumberPopupWidth = typeof dropdownMatchSelectWidth === 'number';\n  var stretch = React.useMemo(function () {\n    if (isNumberPopupWidth) {\n      return null;\n    }\n    return dropdownMatchSelectWidth === false ? 'minWidth' : 'width';\n  }, [dropdownMatchSelectWidth, isNumberPopupWidth]);\n  var popupStyle = dropdownStyle;\n  if (isNumberPopupWidth) {\n    popupStyle = _objectSpread(_objectSpread({}, popupStyle), {}, {\n      width: dropdownMatchSelectWidth\n    });\n  }\n\n  // ======================= Ref =======================\n  var triggerPopupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        var _triggerPopupRef$curr;\n        return (_triggerPopupRef$curr = triggerPopupRef.current) === null || _triggerPopupRef$curr === void 0 ? void 0 : _triggerPopupRef$curr.popupElement;\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: mergedBuiltinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    ref: triggerPopupRef,\n    stretch: stretch,\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nif (process.env.NODE_ENV !== 'production') {\n  RefSelectTrigger.displayName = 'SelectTrigger';\n}\nexport default RefSelectTrigger;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON>", "classNames", "React", "getBuiltInPlacements", "dropdownMatchSelectWidth", "adjustX", "bottomLeft", "points", "offset", "overflow", "adjustY", "htmlRegion", "bottomRight", "topLeft", "topRight", "SelectTrigger", "props", "ref", "prefixCls", "disabled", "visible", "children", "popupElement", "animation", "transitionName", "dropdownStyle", "dropdownClassName", "_props$direction", "direction", "placement", "builtinPlacements", "dropdownRender", "dropdownAlign", "getPopupContainer", "empty", "getTriggerDOMNode", "onPopupVisibleChange", "onPopupMouseEnter", "restProps", "dropdownPrefixCls", "concat", "popupNode", "mergedBuiltinPlacements", "useMemo", "mergedTransitionName", "isNumberPopup<PERSON>th", "stretch", "popupStyle", "width", "triggerPopupRef", "useRef", "useImperativeHandle", "getPopupElement", "_triggerPopupRef$curr", "current", "createElement", "showAction", "hideAction", "popupPlacement", "popupTransitionName", "popup", "onMouseEnter", "popupAlign", "popupVisible", "popupClassName", "RefSelectTrigger", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/SelectTrigger.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var mergedBuiltinPlacements = React.useMemo(function () {\n    return builtinPlacements || getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [builtinPlacements, dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // =================== Popup Width ===================\n  var isNumberPopupWidth = typeof dropdownMatchSelectWidth === 'number';\n  var stretch = React.useMemo(function () {\n    if (isNumberPopupWidth) {\n      return null;\n    }\n    return dropdownMatchSelectWidth === false ? 'minWidth' : 'width';\n  }, [dropdownMatchSelectWidth, isNumberPopupWidth]);\n  var popupStyle = dropdownStyle;\n  if (isNumberPopupWidth) {\n    popupStyle = _objectSpread(_objectSpread({}, popupStyle), {}, {\n      width: dropdownMatchSelectWidth\n    });\n  }\n\n  // ======================= Ref =======================\n  var triggerPopupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        var _triggerPopupRef$curr;\n        return (_triggerPopupRef$curr = triggerPopupRef.current) === null || _triggerPopupRef$curr === void 0 ? void 0 : _triggerPopupRef$curr.popupElement;\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: mergedBuiltinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    ref: triggerPopupRef,\n    stretch: stretch,\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nif (process.env.NODE_ENV !== 'production') {\n  RefSelectTrigger.displayName = 'SelectTrigger';\n}\nexport default RefSelectTrigger;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,OAAO,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;AACnW,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,wBAAwB,EAAE;EACjF;EACA,IAAIC,OAAO,GAAGD,wBAAwB,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EACvD,OAAO;IACLE,UAAU,EAAE;MACVC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDE,OAAO,EAAE;MACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDG,QAAQ,EAAE;MACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC;AACD,IAAII,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,iBAAiB,GAAGV,KAAK,CAACU,iBAAiB;IAC3CC,gBAAgB,GAAGX,KAAK,CAACY,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,iBAAiB,GAAGd,KAAK,CAACc,iBAAiB;IAC3C1B,wBAAwB,GAAGY,KAAK,CAACZ,wBAAwB;IACzD2B,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,aAAa,GAAGhB,KAAK,CAACgB,aAAa;IACnCC,iBAAiB,GAAGjB,KAAK,CAACiB,iBAAiB;IAC3CC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,oBAAoB,GAAGpB,KAAK,CAACoB,oBAAoB;IACjDC,iBAAiB,GAAGrB,KAAK,CAACqB,iBAAiB;IAC3CC,SAAS,GAAGxC,wBAAwB,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EACxD,IAAIwC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,WAAW,CAAC;EACzD,IAAIuB,SAAS,GAAGnB,YAAY;EAC5B,IAAIS,cAAc,EAAE;IAClBU,SAAS,GAAGV,cAAc,CAACT,YAAY,CAAC;EAC1C;EACA,IAAIoB,uBAAuB,GAAGxC,KAAK,CAACyC,OAAO,CAAC,YAAY;IACtD,OAAOb,iBAAiB,IAAI3B,oBAAoB,CAACC,wBAAwB,CAAC;EAC5E,CAAC,EAAE,CAAC0B,iBAAiB,EAAE1B,wBAAwB,CAAC,CAAC;;EAEjD;EACA,IAAIwC,oBAAoB,GAAGrB,SAAS,GAAG,EAAE,CAACiB,MAAM,CAACD,iBAAiB,EAAE,GAAG,CAAC,CAACC,MAAM,CAACjB,SAAS,CAAC,GAAGC,cAAc;;EAE3G;EACA,IAAIqB,kBAAkB,GAAG,OAAOzC,wBAAwB,KAAK,QAAQ;EACrE,IAAI0C,OAAO,GAAG5C,KAAK,CAACyC,OAAO,CAAC,YAAY;IACtC,IAAIE,kBAAkB,EAAE;MACtB,OAAO,IAAI;IACb;IACA,OAAOzC,wBAAwB,KAAK,KAAK,GAAG,UAAU,GAAG,OAAO;EAClE,CAAC,EAAE,CAACA,wBAAwB,EAAEyC,kBAAkB,CAAC,CAAC;EAClD,IAAIE,UAAU,GAAGtB,aAAa;EAC9B,IAAIoB,kBAAkB,EAAE;IACtBE,UAAU,GAAGlD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkD,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5DC,KAAK,EAAE5C;IACT,CAAC,CAAC;EACJ;;EAEA;EACA,IAAI6C,eAAe,GAAG/C,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EACxChD,KAAK,CAACiD,mBAAmB,CAAClC,GAAG,EAAE,YAAY;IACzC,OAAO;MACLmC,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,IAAIC,qBAAqB;QACzB,OAAO,CAACA,qBAAqB,GAAGJ,eAAe,CAACK,OAAO,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC/B,YAAY;MACrJ;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAapB,KAAK,CAACqD,aAAa,CAACvD,OAAO,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE2C,SAAS,EAAE;IACvEkB,UAAU,EAAEpB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDqB,UAAU,EAAErB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDsB,cAAc,EAAE7B,SAAS,KAAKD,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,YAAY,CAAC;IACjFE,iBAAiB,EAAEY,uBAAuB;IAC1CxB,SAAS,EAAEqB,iBAAiB;IAC5BoB,mBAAmB,EAAEf,oBAAoB;IACzCgB,KAAK,EAAE,aAAa1D,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAE;MAC7CM,YAAY,EAAExB;IAChB,CAAC,EAAEI,SAAS,CAAC;IACbxB,GAAG,EAAEgC,eAAe;IACpBH,OAAO,EAAEA,OAAO;IAChBgB,UAAU,EAAE9B,aAAa;IACzB+B,YAAY,EAAE3C,OAAO;IACrBa,iBAAiB,EAAEA,iBAAiB;IACpC+B,cAAc,EAAE/D,UAAU,CAACyB,iBAAiB,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4C,MAAM,CAACD,iBAAiB,EAAE,QAAQ,CAAC,EAAEL,KAAK,CAAC,CAAC;IACjHa,UAAU,EAAEA,UAAU;IACtBZ,iBAAiB,EAAEA,iBAAiB;IACpCC,oBAAoB,EAAEA;EACxB,CAAC,CAAC,EAAEf,QAAQ,CAAC;AACf,CAAC;AACD,IAAI4C,gBAAgB,GAAG,aAAa/D,KAAK,CAACgE,UAAU,CAACnD,aAAa,CAAC;AACnE,IAAIoD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,gBAAgB,CAACK,WAAW,GAAG,eAAe;AAChD;AACA,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * Cursor rule:\n * 1. Only `showSearch` enabled\n * 2. Only `open` is `true`\n * 3. When typing, set `open` to `true` which hit rule of 2\n *\n * Accessibility:\n * - https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html\n */\n\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport useLock from \"../hooks/useLock\";\nimport { isValidateOpenKey } from \"../utils/keyUtil\";\nimport MultipleSelector from \"./MultipleSelector\";\nimport SingleSelector from \"./SingleSelector\";\nvar Selector = function Selector(props, ref) {\n  var inputRef = useRef(null);\n  var compositionStatusRef = useRef(false);\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    mode = props.mode,\n    showSearch = props.showSearch,\n    tokenWithEnter = props.tokenWithEnter,\n    disabled = props.disabled,\n    prefix = props.prefix,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSubmit = props.onSearchSubmit,\n    onToggleOpen = props.onToggleOpen,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputBlur = props.onInputBlur,\n    domRef = props.domRef;\n\n  // ======================= Ref =======================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(options) {\n        inputRef.current.focus(options);\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  });\n\n  // ====================== Input ======================\n  var _useLock = useLock(0),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getInputMouseDown = _useLock2[0],\n    setInputMouseDown = _useLock2[1];\n  var onInternalInputKeyDown = function onInternalInputKeyDown(event) {\n    var which = event.which;\n\n    // Compatible with multiple lines in TextArea\n    var isTextAreaElement = inputRef.current instanceof HTMLTextAreaElement;\n    if (!isTextAreaElement && open && (which === KeyCode.UP || which === KeyCode.DOWN)) {\n      event.preventDefault();\n    }\n    if (onInputKeyDown) {\n      onInputKeyDown(event);\n    }\n    if (which === KeyCode.ENTER && mode === 'tags' && !compositionStatusRef.current && !open) {\n      // When menu isn't open, OptionList won't trigger a value change\n      // So when enter is pressed, the tag's input value should be emitted here to let selector know\n      onSearchSubmit === null || onSearchSubmit === void 0 || onSearchSubmit(event.target.value);\n    }\n    // Move within the text box\n    if (isTextAreaElement && !open && ~[KeyCode.UP, KeyCode.DOWN, KeyCode.LEFT, KeyCode.RIGHT].indexOf(which)) {\n      return;\n    }\n    if (isValidateOpenKey(which)) {\n      onToggleOpen(true);\n    }\n  };\n\n  /**\n   * We can not use `findDOMNode` sine it will get warning,\n   * have to use timer to check if is input element.\n   */\n  var onInternalInputMouseDown = function onInternalInputMouseDown() {\n    setInputMouseDown(true);\n  };\n\n  // When paste come, ignore next onChange\n  var pastedTextRef = useRef(null);\n  var triggerOnSearch = function triggerOnSearch(value) {\n    if (onSearch(value, true, compositionStatusRef.current) !== false) {\n      onToggleOpen(true);\n    }\n  };\n  var onInputCompositionStart = function onInputCompositionStart() {\n    compositionStatusRef.current = true;\n  };\n  var onInputCompositionEnd = function onInputCompositionEnd(e) {\n    compositionStatusRef.current = false;\n\n    // Trigger search again to support `tokenSeparators` with typewriting\n    if (mode !== 'combobox') {\n      triggerOnSearch(e.target.value);\n    }\n  };\n  var onInputChange = function onInputChange(event) {\n    var value = event.target.value;\n\n    // Pasted text should replace back to origin content\n    if (tokenWithEnter && pastedTextRef.current && /[\\r\\n]/.test(pastedTextRef.current)) {\n      // CRLF will be treated as a single space for input element\n      var replacedText = pastedTextRef.current.replace(/[\\r\\n]+$/, '').replace(/\\r\\n/g, ' ').replace(/[\\r\\n]/g, ' ');\n      value = value.replace(replacedText, pastedTextRef.current);\n    }\n    pastedTextRef.current = null;\n    triggerOnSearch(value);\n  };\n  var onInputPaste = function onInputPaste(e) {\n    var clipboardData = e.clipboardData;\n    var value = clipboardData === null || clipboardData === void 0 ? void 0 : clipboardData.getData('text');\n    pastedTextRef.current = value || '';\n  };\n  var onClick = function onClick(_ref) {\n    var target = _ref.target;\n    if (target !== inputRef.current) {\n      // Should focus input if click the selector\n      var isIE = document.body.style.msTouchAction !== undefined;\n      if (isIE) {\n        setTimeout(function () {\n          inputRef.current.focus();\n        });\n      } else {\n        inputRef.current.focus();\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    var inputMouseDown = getInputMouseDown();\n\n    // when mode is combobox and it is disabled, don't prevent default behavior\n    // https://github.com/ant-design/ant-design/issues/37320\n    // https://github.com/ant-design/ant-design/issues/48281\n    if (event.target !== inputRef.current && !inputMouseDown && !(mode === 'combobox' && disabled)) {\n      event.preventDefault();\n    }\n    if (mode !== 'combobox' && (!showSearch || !inputMouseDown) || !open) {\n      if (open && autoClearSearchValue !== false) {\n        onSearch('', true, false);\n      }\n      onToggleOpen();\n    }\n  };\n\n  // ================= Inner Selector ==================\n  var sharedProps = {\n    inputRef: inputRef,\n    onInputKeyDown: onInternalInputKeyDown,\n    onInputMouseDown: onInternalInputMouseDown,\n    onInputChange: onInputChange,\n    onInputPaste: onInputPaste,\n    onInputCompositionStart: onInputCompositionStart,\n    onInputCompositionEnd: onInputCompositionEnd,\n    onInputBlur: onInputBlur\n  };\n  var selectNode = mode === 'multiple' || mode === 'tags' ? /*#__PURE__*/React.createElement(MultipleSelector, _extends({}, props, sharedProps)) : /*#__PURE__*/React.createElement(SingleSelector, _extends({}, props, sharedProps));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: \"\".concat(prefixCls, \"-selector\"),\n    onClick: onClick,\n    onMouseDown: onMouseDown\n  }, prefix && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-prefix\")\n  }, prefix), selectNode);\n};\nvar ForwardSelector = /*#__PURE__*/React.forwardRef(Selector);\nif (process.env.NODE_ENV !== 'production') {\n  ForwardSelector.displayName = 'Selector';\n}\nexport default ForwardSelector;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "KeyCode", "React", "useRef", "useLock", "isValidateOpenKey", "MultipleSelector", "SingleSelector", "Selector", "props", "ref", "inputRef", "compositionStatusRef", "prefixCls", "open", "mode", "showSearch", "tokenWithEnter", "disabled", "prefix", "autoClearSearchValue", "onSearch", "onSearchSubmit", "onToggleOpen", "onInputKeyDown", "onInputBlur", "domRef", "useImperativeHandle", "focus", "options", "current", "blur", "_useLock", "_useLock2", "getInputMouseDown", "setInputMouseDown", "onInternalInputKeyDown", "event", "which", "isTextAreaElement", "HTMLTextAreaElement", "UP", "DOWN", "preventDefault", "ENTER", "target", "value", "LEFT", "RIGHT", "indexOf", "onInternalInputMouseDown", "pastedTextRef", "triggerOnSearch", "onInputCompositionStart", "onInputCompositionEnd", "e", "onInputChange", "test", "replacedText", "replace", "onInputPaste", "clipboardData", "getData", "onClick", "_ref", "isIE", "document", "body", "style", "msTouchAction", "undefined", "setTimeout", "onMouseDown", "inputMouseDown", "sharedProps", "onInputMouseDown", "selectNode", "createElement", "className", "concat", "ForwardSelector", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/Selector/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * Cursor rule:\n * 1. Only `showSearch` enabled\n * 2. Only `open` is `true`\n * 3. When typing, set `open` to `true` which hit rule of 2\n *\n * Accessibility:\n * - https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html\n */\n\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport useLock from \"../hooks/useLock\";\nimport { isValidateOpenKey } from \"../utils/keyUtil\";\nimport MultipleSelector from \"./MultipleSelector\";\nimport SingleSelector from \"./SingleSelector\";\nvar Selector = function Selector(props, ref) {\n  var inputRef = useRef(null);\n  var compositionStatusRef = useRef(false);\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    mode = props.mode,\n    showSearch = props.showSearch,\n    tokenWithEnter = props.tokenWithEnter,\n    disabled = props.disabled,\n    prefix = props.prefix,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSubmit = props.onSearchSubmit,\n    onToggleOpen = props.onToggleOpen,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputBlur = props.onInputBlur,\n    domRef = props.domRef;\n\n  // ======================= Ref =======================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(options) {\n        inputRef.current.focus(options);\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  });\n\n  // ====================== Input ======================\n  var _useLock = useLock(0),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getInputMouseDown = _useLock2[0],\n    setInputMouseDown = _useLock2[1];\n  var onInternalInputKeyDown = function onInternalInputKeyDown(event) {\n    var which = event.which;\n\n    // Compatible with multiple lines in TextArea\n    var isTextAreaElement = inputRef.current instanceof HTMLTextAreaElement;\n    if (!isTextAreaElement && open && (which === KeyCode.UP || which === KeyCode.DOWN)) {\n      event.preventDefault();\n    }\n    if (onInputKeyDown) {\n      onInputKeyDown(event);\n    }\n    if (which === KeyCode.ENTER && mode === 'tags' && !compositionStatusRef.current && !open) {\n      // When menu isn't open, OptionList won't trigger a value change\n      // So when enter is pressed, the tag's input value should be emitted here to let selector know\n      onSearchSubmit === null || onSearchSubmit === void 0 || onSearchSubmit(event.target.value);\n    }\n    // Move within the text box\n    if (isTextAreaElement && !open && ~[KeyCode.UP, KeyCode.DOWN, KeyCode.LEFT, KeyCode.RIGHT].indexOf(which)) {\n      return;\n    }\n    if (isValidateOpenKey(which)) {\n      onToggleOpen(true);\n    }\n  };\n\n  /**\n   * We can not use `findDOMNode` sine it will get warning,\n   * have to use timer to check if is input element.\n   */\n  var onInternalInputMouseDown = function onInternalInputMouseDown() {\n    setInputMouseDown(true);\n  };\n\n  // When paste come, ignore next onChange\n  var pastedTextRef = useRef(null);\n  var triggerOnSearch = function triggerOnSearch(value) {\n    if (onSearch(value, true, compositionStatusRef.current) !== false) {\n      onToggleOpen(true);\n    }\n  };\n  var onInputCompositionStart = function onInputCompositionStart() {\n    compositionStatusRef.current = true;\n  };\n  var onInputCompositionEnd = function onInputCompositionEnd(e) {\n    compositionStatusRef.current = false;\n\n    // Trigger search again to support `tokenSeparators` with typewriting\n    if (mode !== 'combobox') {\n      triggerOnSearch(e.target.value);\n    }\n  };\n  var onInputChange = function onInputChange(event) {\n    var value = event.target.value;\n\n    // Pasted text should replace back to origin content\n    if (tokenWithEnter && pastedTextRef.current && /[\\r\\n]/.test(pastedTextRef.current)) {\n      // CRLF will be treated as a single space for input element\n      var replacedText = pastedTextRef.current.replace(/[\\r\\n]+$/, '').replace(/\\r\\n/g, ' ').replace(/[\\r\\n]/g, ' ');\n      value = value.replace(replacedText, pastedTextRef.current);\n    }\n    pastedTextRef.current = null;\n    triggerOnSearch(value);\n  };\n  var onInputPaste = function onInputPaste(e) {\n    var clipboardData = e.clipboardData;\n    var value = clipboardData === null || clipboardData === void 0 ? void 0 : clipboardData.getData('text');\n    pastedTextRef.current = value || '';\n  };\n  var onClick = function onClick(_ref) {\n    var target = _ref.target;\n    if (target !== inputRef.current) {\n      // Should focus input if click the selector\n      var isIE = document.body.style.msTouchAction !== undefined;\n      if (isIE) {\n        setTimeout(function () {\n          inputRef.current.focus();\n        });\n      } else {\n        inputRef.current.focus();\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    var inputMouseDown = getInputMouseDown();\n\n    // when mode is combobox and it is disabled, don't prevent default behavior\n    // https://github.com/ant-design/ant-design/issues/37320\n    // https://github.com/ant-design/ant-design/issues/48281\n    if (event.target !== inputRef.current && !inputMouseDown && !(mode === 'combobox' && disabled)) {\n      event.preventDefault();\n    }\n    if (mode !== 'combobox' && (!showSearch || !inputMouseDown) || !open) {\n      if (open && autoClearSearchValue !== false) {\n        onSearch('', true, false);\n      }\n      onToggleOpen();\n    }\n  };\n\n  // ================= Inner Selector ==================\n  var sharedProps = {\n    inputRef: inputRef,\n    onInputKeyDown: onInternalInputKeyDown,\n    onInputMouseDown: onInternalInputMouseDown,\n    onInputChange: onInputChange,\n    onInputPaste: onInputPaste,\n    onInputCompositionStart: onInputCompositionStart,\n    onInputCompositionEnd: onInputCompositionEnd,\n    onInputBlur: onInputBlur\n  };\n  var selectNode = mode === 'multiple' || mode === 'tags' ? /*#__PURE__*/React.createElement(MultipleSelector, _extends({}, props, sharedProps)) : /*#__PURE__*/React.createElement(SingleSelector, _extends({}, props, sharedProps));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: \"\".concat(prefixCls, \"-selector\"),\n    onClick: onClick,\n    onMouseDown: onMouseDown\n  }, prefix && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-prefix\")\n  }, prefix), selectNode);\n};\nvar ForwardSelector = /*#__PURE__*/React.forwardRef(Selector);\nif (process.env.NODE_ENV !== 'production') {\n  ForwardSelector.displayName = 'Selector';\n}\nexport default ForwardSelector;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3C,IAAIC,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIS,oBAAoB,GAAGT,MAAM,CAAC,KAAK,CAAC;EACxC,IAAIU,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,IAAI,GAAGL,KAAK,CAACK,IAAI;IACjBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,oBAAoB,GAAGX,KAAK,CAACW,oBAAoB;IACjDC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,cAAc,GAAGb,KAAK,CAACa,cAAc;IACrCC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;;EAEvB;EACAxB,KAAK,CAACyB,mBAAmB,CAACjB,GAAG,EAAE,YAAY;IACzC,OAAO;MACLkB,KAAK,EAAE,SAASA,KAAKA,CAACC,OAAO,EAAE;QAC7BlB,QAAQ,CAACmB,OAAO,CAACF,KAAK,CAACC,OAAO,CAAC;MACjC,CAAC;MACDE,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBpB,QAAQ,CAACmB,OAAO,CAACC,IAAI,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIC,QAAQ,GAAG5B,OAAO,CAAC,CAAC,CAAC;IACvB6B,SAAS,GAAGjC,cAAc,CAACgC,QAAQ,EAAE,CAAC,CAAC;IACvCE,iBAAiB,GAAGD,SAAS,CAAC,CAAC,CAAC;IAChCE,iBAAiB,GAAGF,SAAS,CAAC,CAAC,CAAC;EAClC,IAAIG,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAE;IAClE,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;;IAEvB;IACA,IAAIC,iBAAiB,GAAG5B,QAAQ,CAACmB,OAAO,YAAYU,mBAAmB;IACvE,IAAI,CAACD,iBAAiB,IAAIzB,IAAI,KAAKwB,KAAK,KAAKrC,OAAO,CAACwC,EAAE,IAAIH,KAAK,KAAKrC,OAAO,CAACyC,IAAI,CAAC,EAAE;MAClFL,KAAK,CAACM,cAAc,CAAC,CAAC;IACxB;IACA,IAAInB,cAAc,EAAE;MAClBA,cAAc,CAACa,KAAK,CAAC;IACvB;IACA,IAAIC,KAAK,KAAKrC,OAAO,CAAC2C,KAAK,IAAI7B,IAAI,KAAK,MAAM,IAAI,CAACH,oBAAoB,CAACkB,OAAO,IAAI,CAAChB,IAAI,EAAE;MACxF;MACA;MACAQ,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACe,KAAK,CAACQ,MAAM,CAACC,KAAK,CAAC;IAC5F;IACA;IACA,IAAIP,iBAAiB,IAAI,CAACzB,IAAI,IAAI,CAAC,CAACb,OAAO,CAACwC,EAAE,EAAExC,OAAO,CAACyC,IAAI,EAAEzC,OAAO,CAAC8C,IAAI,EAAE9C,OAAO,CAAC+C,KAAK,CAAC,CAACC,OAAO,CAACX,KAAK,CAAC,EAAE;MACzG;IACF;IACA,IAAIjC,iBAAiB,CAACiC,KAAK,CAAC,EAAE;MAC5Bf,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAI2B,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACjEf,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,IAAIgB,aAAa,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIiD,eAAe,GAAG,SAASA,eAAeA,CAACN,KAAK,EAAE;IACpD,IAAIzB,QAAQ,CAACyB,KAAK,EAAE,IAAI,EAAElC,oBAAoB,CAACkB,OAAO,CAAC,KAAK,KAAK,EAAE;MACjEP,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;EACD,IAAI8B,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;IAC/DzC,oBAAoB,CAACkB,OAAO,GAAG,IAAI;EACrC,CAAC;EACD,IAAIwB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,CAAC,EAAE;IAC5D3C,oBAAoB,CAACkB,OAAO,GAAG,KAAK;;IAEpC;IACA,IAAIf,IAAI,KAAK,UAAU,EAAE;MACvBqC,eAAe,CAACG,CAAC,CAACV,MAAM,CAACC,KAAK,CAAC;IACjC;EACF,CAAC;EACD,IAAIU,aAAa,GAAG,SAASA,aAAaA,CAACnB,KAAK,EAAE;IAChD,IAAIS,KAAK,GAAGT,KAAK,CAACQ,MAAM,CAACC,KAAK;;IAE9B;IACA,IAAI7B,cAAc,IAAIkC,aAAa,CAACrB,OAAO,IAAI,QAAQ,CAAC2B,IAAI,CAACN,aAAa,CAACrB,OAAO,CAAC,EAAE;MACnF;MACA,IAAI4B,YAAY,GAAGP,aAAa,CAACrB,OAAO,CAAC6B,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC9Gb,KAAK,GAAGA,KAAK,CAACa,OAAO,CAACD,YAAY,EAAEP,aAAa,CAACrB,OAAO,CAAC;IAC5D;IACAqB,aAAa,CAACrB,OAAO,GAAG,IAAI;IAC5BsB,eAAe,CAACN,KAAK,CAAC;EACxB,CAAC;EACD,IAAIc,YAAY,GAAG,SAASA,YAAYA,CAACL,CAAC,EAAE;IAC1C,IAAIM,aAAa,GAAGN,CAAC,CAACM,aAAa;IACnC,IAAIf,KAAK,GAAGe,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvGX,aAAa,CAACrB,OAAO,GAAGgB,KAAK,IAAI,EAAE;EACrC,CAAC;EACD,IAAIiB,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;IACnC,IAAInB,MAAM,GAAGmB,IAAI,CAACnB,MAAM;IACxB,IAAIA,MAAM,KAAKlC,QAAQ,CAACmB,OAAO,EAAE;MAC/B;MACA,IAAImC,IAAI,GAAGC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,aAAa,KAAKC,SAAS;MAC1D,IAAIL,IAAI,EAAE;QACRM,UAAU,CAAC,YAAY;UACrB5D,QAAQ,CAACmB,OAAO,CAACF,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjB,QAAQ,CAACmB,OAAO,CAACF,KAAK,CAAC,CAAC;MAC1B;IACF;EACF,CAAC;EACD,IAAI4C,WAAW,GAAG,SAASA,WAAWA,CAACnC,KAAK,EAAE;IAC5C,IAAIoC,cAAc,GAAGvC,iBAAiB,CAAC,CAAC;;IAExC;IACA;IACA;IACA,IAAIG,KAAK,CAACQ,MAAM,KAAKlC,QAAQ,CAACmB,OAAO,IAAI,CAAC2C,cAAc,IAAI,EAAE1D,IAAI,KAAK,UAAU,IAAIG,QAAQ,CAAC,EAAE;MAC9FmB,KAAK,CAACM,cAAc,CAAC,CAAC;IACxB;IACA,IAAI5B,IAAI,KAAK,UAAU,KAAK,CAACC,UAAU,IAAI,CAACyD,cAAc,CAAC,IAAI,CAAC3D,IAAI,EAAE;MACpE,IAAIA,IAAI,IAAIM,oBAAoB,KAAK,KAAK,EAAE;QAC1CC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;MAC3B;MACAE,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,IAAImD,WAAW,GAAG;IAChB/D,QAAQ,EAAEA,QAAQ;IAClBa,cAAc,EAAEY,sBAAsB;IACtCuC,gBAAgB,EAAEzB,wBAAwB;IAC1CM,aAAa,EAAEA,aAAa;IAC5BI,YAAY,EAAEA,YAAY;IAC1BP,uBAAuB,EAAEA,uBAAuB;IAChDC,qBAAqB,EAAEA,qBAAqB;IAC5C7B,WAAW,EAAEA;EACf,CAAC;EACD,IAAImD,UAAU,GAAG7D,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,GAAG,aAAab,KAAK,CAAC2E,aAAa,CAACvE,gBAAgB,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAEiE,WAAW,CAAC,CAAC,GAAG,aAAaxE,KAAK,CAAC2E,aAAa,CAACtE,cAAc,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAEiE,WAAW,CAAC,CAAC;EACnO,OAAO,aAAaxE,KAAK,CAAC2E,aAAa,CAAC,KAAK,EAAE;IAC7CnE,GAAG,EAAEgB,MAAM;IACXoD,SAAS,EAAE,EAAE,CAACC,MAAM,CAAClE,SAAS,EAAE,WAAW,CAAC;IAC5CkD,OAAO,EAAEA,OAAO;IAChBS,WAAW,EAAEA;EACf,CAAC,EAAErD,MAAM,IAAI,aAAajB,KAAK,CAAC2E,aAAa,CAAC,KAAK,EAAE;IACnDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAClE,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEM,MAAM,CAAC,EAAEyD,UAAU,CAAC;AACzB,CAAC;AACD,IAAII,eAAe,GAAG,aAAa9E,KAAK,CAAC+E,UAAU,CAACzE,QAAQ,CAAC;AAC7D,IAAI0E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,eAAe,CAACK,WAAW,GAAG,UAAU;AAC1C;AACA,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
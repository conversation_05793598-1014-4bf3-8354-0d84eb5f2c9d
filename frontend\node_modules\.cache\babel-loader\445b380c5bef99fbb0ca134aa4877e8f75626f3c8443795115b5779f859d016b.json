{"ast": null, "code": "import ThemeCache from \"./ThemeCache\";\nimport Theme from \"./Theme\";\nvar cacheThemes = new ThemeCache();\n\n/**\n * Same as new Theme, but will always return same one if `derivative` not changed.\n */\nexport default function createTheme(derivatives) {\n  var derivativeArr = Array.isArray(derivatives) ? derivatives : [derivatives];\n  // Create new theme if not exist\n  if (!cacheThemes.has(derivativeArr)) {\n    cacheThemes.set(derivativeArr, new Theme(derivativeArr));\n  }\n\n  // Get theme from cache and return\n  return cacheThemes.get(derivativeArr);\n}", "map": {"version": 3, "names": ["ThemeCache", "Theme", "cacheThemes", "createTheme", "derivatives", "derivativeArr", "Array", "isArray", "has", "set", "get"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/theme/createTheme.js"], "sourcesContent": ["import ThemeCache from \"./ThemeCache\";\nimport Theme from \"./Theme\";\nvar cacheThemes = new ThemeCache();\n\n/**\n * Same as new Theme, but will always return same one if `derivative` not changed.\n */\nexport default function createTheme(derivatives) {\n  var derivativeArr = Array.isArray(derivatives) ? derivatives : [derivatives];\n  // Create new theme if not exist\n  if (!cacheThemes.has(derivativeArr)) {\n    cacheThemes.set(derivativeArr, new Theme(derivativeArr));\n  }\n\n  // Get theme from cache and return\n  return cacheThemes.get(derivativeArr);\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,WAAW,GAAG,IAAIF,UAAU,CAAC,CAAC;;AAElC;AACA;AACA;AACA,eAAe,SAASG,WAAWA,CAACC,WAAW,EAAE;EAC/C,IAAIC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;EAC5E;EACA,IAAI,CAACF,WAAW,CAACM,GAAG,CAACH,aAAa,CAAC,EAAE;IACnCH,WAAW,CAACO,GAAG,CAACJ,aAAa,EAAE,IAAIJ,KAAK,CAACI,aAAa,CAAC,CAAC;EAC1D;;EAEA;EACA,OAAOH,WAAW,CAACQ,GAAG,CAACL,aAAa,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
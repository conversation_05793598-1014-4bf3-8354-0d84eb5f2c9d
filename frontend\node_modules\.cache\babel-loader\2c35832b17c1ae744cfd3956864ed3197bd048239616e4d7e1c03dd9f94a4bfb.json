{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport warning from \"rc-util/es/warning\";\nimport { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport { isFormInstance } from \"./utils/typeUtil\";\nimport { getNamePath, getValue } from \"./utils/valueUtil\";\nexport function stringify(value) {\n  try {\n    return JSON.stringify(value);\n  } catch (err) {\n    return Math.random();\n  }\n}\nvar useWatchWarning = process.env.NODE_ENV !== 'production' ? function (namePath) {\n  var fullyStr = namePath.join('__RC_FIELD_FORM_SPLIT__');\n  var nameStrRef = useRef(fullyStr);\n  warning(nameStrRef.current === fullyStr, '`useWatch` is not support dynamic `namePath`. Please provide static instead.');\n} : function () {};\n\n// ------- selector type -------\n\n// ------- selector type end -------\n\nfunction useWatch() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var dependencies = args[0],\n    _args$ = args[1],\n    _form = _args$ === void 0 ? {} : _args$;\n  var options = isFormInstance(_form) ? {\n    form: _form\n  } : _form;\n  var form = options.form;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    setValue = _useState2[1];\n  var valueStr = useMemo(function () {\n    return stringify(value);\n  }, [value]);\n  var valueStrRef = useRef(valueStr);\n  valueStrRef.current = valueStr;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init;\n\n  // Warning if not exist form instance\n  if (process.env.NODE_ENV !== 'production') {\n    warning(args.length === 2 ? form ? isValidForm : true : isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useWatchWarning(namePath);\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n    var getFieldsValue = formInstance.getFieldsValue,\n      getInternalHooks = formInstance.getInternalHooks;\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n      registerWatch = _getInternalHooks.registerWatch;\n    var getWatchValue = function getWatchValue(values, allValues) {\n      var watchValue = options.preserve ? allValues : values;\n      return typeof dependencies === 'function' ? dependencies(watchValue) : getValue(watchValue, namePathRef.current);\n    };\n    var cancelRegister = registerWatch(function (values, allValues) {\n      var newValue = getWatchValue(values, allValues);\n      var nextValueStr = stringify(newValue);\n\n      // Compare stringify in case it's nest object\n      if (valueStrRef.current !== nextValueStr) {\n        valueStrRef.current = nextValueStr;\n        setValue(newValue);\n      }\n    });\n\n    // TODO: We can improve this perf in future\n    var initialValue = getWatchValue(getFieldsValue(), getFieldsValue(true));\n\n    // React 18 has the bug that will queue update twice even the value is not changed\n    // ref: https://github.com/facebook/react/issues/27213\n    if (value !== initialValue) {\n      setValue(initialValue);\n    }\n    return cancelRegister;\n  },\n  // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [isValidForm]);\n  return value;\n}\nexport default useWatch;", "map": {"version": 3, "names": ["_slicedToArray", "warning", "useContext", "useEffect", "useMemo", "useRef", "useState", "FieldContext", "HOOK_MARK", "isFormInstance", "getNamePath", "getValue", "stringify", "value", "JSON", "err", "Math", "random", "useWatchWarning", "process", "env", "NODE_ENV", "namePath", "fullyStr", "join", "nameStrRef", "current", "useWatch", "_len", "arguments", "length", "args", "Array", "_key", "dependencies", "_args$", "_form", "options", "form", "_useState", "_useState2", "setValue", "valueStr", "valueStrRef", "fieldContext", "formInstance", "isValidForm", "_init", "namePathRef", "getFieldsValue", "getInternalHooks", "_getInternalHooks", "registerWatch", "getWatchValue", "values", "allValues", "watchValue", "preserve", "cancelRegister", "newValue", "nextValueStr", "initialValue"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-field-form@2.7.0_react-d_64f5b93340a107ccce19d223ada77c46/node_modules/rc-field-form/es/useWatch.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport warning from \"rc-util/es/warning\";\nimport { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport { isFormInstance } from \"./utils/typeUtil\";\nimport { getNamePath, getValue } from \"./utils/valueUtil\";\nexport function stringify(value) {\n  try {\n    return JSON.stringify(value);\n  } catch (err) {\n    return Math.random();\n  }\n}\nvar useWatchWarning = process.env.NODE_ENV !== 'production' ? function (namePath) {\n  var fullyStr = namePath.join('__RC_FIELD_FORM_SPLIT__');\n  var nameStrRef = useRef(fullyStr);\n  warning(nameStrRef.current === fullyStr, '`useWatch` is not support dynamic `namePath`. Please provide static instead.');\n} : function () {};\n\n// ------- selector type -------\n\n// ------- selector type end -------\n\nfunction useWatch() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var dependencies = args[0],\n    _args$ = args[1],\n    _form = _args$ === void 0 ? {} : _args$;\n  var options = isFormInstance(_form) ? {\n    form: _form\n  } : _form;\n  var form = options.form;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    setValue = _useState2[1];\n  var valueStr = useMemo(function () {\n    return stringify(value);\n  }, [value]);\n  var valueStrRef = useRef(valueStr);\n  valueStrRef.current = valueStr;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init;\n\n  // Warning if not exist form instance\n  if (process.env.NODE_ENV !== 'production') {\n    warning(args.length === 2 ? form ? isValidForm : true : isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useWatchWarning(namePath);\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n    var getFieldsValue = formInstance.getFieldsValue,\n      getInternalHooks = formInstance.getInternalHooks;\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n      registerWatch = _getInternalHooks.registerWatch;\n    var getWatchValue = function getWatchValue(values, allValues) {\n      var watchValue = options.preserve ? allValues : values;\n      return typeof dependencies === 'function' ? dependencies(watchValue) : getValue(watchValue, namePathRef.current);\n    };\n    var cancelRegister = registerWatch(function (values, allValues) {\n      var newValue = getWatchValue(values, allValues);\n      var nextValueStr = stringify(newValue);\n\n      // Compare stringify in case it's nest object\n      if (valueStrRef.current !== nextValueStr) {\n        valueStrRef.current = nextValueStr;\n        setValue(newValue);\n      }\n    });\n\n    // TODO: We can improve this perf in future\n    var initialValue = getWatchValue(getFieldsValue(), getFieldsValue(true));\n\n    // React 18 has the bug that will queue update twice even the value is not changed\n    // ref: https://github.com/facebook/react/issues/27213\n    if (value !== initialValue) {\n      setValue(initialValue);\n    }\n    return cancelRegister;\n  },\n  // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [isValidForm]);\n  return value;\n}\nexport default useWatch;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxE,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AACzD,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,IAAI;IACF,OAAOC,IAAI,CAACF,SAAS,CAACC,KAAK,CAAC;EAC9B,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZ,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC;EACtB;AACF;AACA,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,UAAUC,QAAQ,EAAE;EAChF,IAAIC,QAAQ,GAAGD,QAAQ,CAACE,IAAI,CAAC,yBAAyB,CAAC;EACvD,IAAIC,UAAU,GAAGpB,MAAM,CAACkB,QAAQ,CAAC;EACjCtB,OAAO,CAACwB,UAAU,CAACC,OAAO,KAAKH,QAAQ,EAAE,8EAA8E,CAAC;AAC1H,CAAC,GAAG,YAAY,CAAC,CAAC;;AAElB;;AAEA;;AAEA,SAASI,QAAQA,CAAA,EAAG;EAClB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,IAAIC,YAAY,GAAGH,IAAI,CAAC,CAAC,CAAC;IACxBI,MAAM,GAAGJ,IAAI,CAAC,CAAC,CAAC;IAChBK,KAAK,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,MAAM;EACzC,IAAIE,OAAO,GAAG5B,cAAc,CAAC2B,KAAK,CAAC,GAAG;IACpCE,IAAI,EAAEF;EACR,CAAC,GAAGA,KAAK;EACT,IAAIE,IAAI,GAAGD,OAAO,CAACC,IAAI;EACvB,IAAIC,SAAS,GAAGjC,QAAQ,CAAC,CAAC;IACxBkC,UAAU,GAAGxC,cAAc,CAACuC,SAAS,EAAE,CAAC,CAAC;IACzC1B,KAAK,GAAG2B,UAAU,CAAC,CAAC,CAAC;IACrBC,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIE,QAAQ,GAAGtC,OAAO,CAAC,YAAY;IACjC,OAAOQ,SAAS,CAACC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,IAAI8B,WAAW,GAAGtC,MAAM,CAACqC,QAAQ,CAAC;EAClCC,WAAW,CAACjB,OAAO,GAAGgB,QAAQ;EAC9B,IAAIE,YAAY,GAAG1C,UAAU,CAACK,YAAY,CAAC;EAC3C,IAAIsC,YAAY,GAAGP,IAAI,IAAIM,YAAY;EACvC,IAAIE,WAAW,GAAGD,YAAY,IAAIA,YAAY,CAACE,KAAK;;EAEpD;EACA,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCpB,OAAO,CAAC8B,IAAI,CAACD,MAAM,KAAK,CAAC,GAAGQ,IAAI,GAAGQ,WAAW,GAAG,IAAI,GAAGA,WAAW,EAAE,8EAA8E,CAAC;EACtJ;EACA,IAAIxB,QAAQ,GAAGZ,WAAW,CAACwB,YAAY,CAAC;EACxC,IAAIc,WAAW,GAAG3C,MAAM,CAACiB,QAAQ,CAAC;EAClC0B,WAAW,CAACtB,OAAO,GAAGJ,QAAQ;EAC9BJ,eAAe,CAACI,QAAQ,CAAC;EACzBnB,SAAS,CAAC,YAAY;IACpB;IACA,IAAI,CAAC2C,WAAW,EAAE;MAChB;IACF;IACA,IAAIG,cAAc,GAAGJ,YAAY,CAACI,cAAc;MAC9CC,gBAAgB,GAAGL,YAAY,CAACK,gBAAgB;IAClD,IAAIC,iBAAiB,GAAGD,gBAAgB,CAAC1C,SAAS,CAAC;MACjD4C,aAAa,GAAGD,iBAAiB,CAACC,aAAa;IACjD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,SAAS,EAAE;MAC5D,IAAIC,UAAU,GAAGnB,OAAO,CAACoB,QAAQ,GAAGF,SAAS,GAAGD,MAAM;MACtD,OAAO,OAAOpB,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACsB,UAAU,CAAC,GAAG7C,QAAQ,CAAC6C,UAAU,EAAER,WAAW,CAACtB,OAAO,CAAC;IAClH,CAAC;IACD,IAAIgC,cAAc,GAAGN,aAAa,CAAC,UAAUE,MAAM,EAAEC,SAAS,EAAE;MAC9D,IAAII,QAAQ,GAAGN,aAAa,CAACC,MAAM,EAAEC,SAAS,CAAC;MAC/C,IAAIK,YAAY,GAAGhD,SAAS,CAAC+C,QAAQ,CAAC;;MAEtC;MACA,IAAIhB,WAAW,CAACjB,OAAO,KAAKkC,YAAY,EAAE;QACxCjB,WAAW,CAACjB,OAAO,GAAGkC,YAAY;QAClCnB,QAAQ,CAACkB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIE,YAAY,GAAGR,aAAa,CAACJ,cAAc,CAAC,CAAC,EAAEA,cAAc,CAAC,IAAI,CAAC,CAAC;;IAExE;IACA;IACA,IAAIpC,KAAK,KAAKgD,YAAY,EAAE;MAC1BpB,QAAQ,CAACoB,YAAY,CAAC;IACxB;IACA,OAAOH,cAAc;EACvB,CAAC;EACD;EACA;EACA,CAACZ,WAAW,CAAC,CAAC;EACd,OAAOjC,KAAK;AACd;AACA,eAAec,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from \"rc-util/es/warning\";\nimport * as React from 'react';\nvar fullClone = _objectSpread({}, React);\nvar useInsertionEffect = fullClone.useInsertionEffect;\n\n// DO NOT register functions in useEffect cleanup function, or functions that registered will never be called.\nvar useCleanupRegister = function useCleanupRegister(deps) {\n  var effectCleanups = [];\n  var cleanupFlag = false;\n  function register(fn) {\n    if (cleanupFlag) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '[Ant Design CSS-in-JS] You are registering a cleanup function after unmount, which will not have any effect.');\n      }\n      return;\n    }\n    effectCleanups.push(fn);\n  }\n  React.useEffect(function () {\n    // Compatible with strict mode\n    cleanupFlag = false;\n    return function () {\n      cleanupFlag = true;\n      if (effectCleanups.length) {\n        effectCleanups.forEach(function (fn) {\n          return fn();\n        });\n      }\n    };\n  }, deps);\n  return register;\n};\nvar useRun = function useRun() {\n  return function (fn) {\n    fn();\n  };\n};\n\n// Only enable register in React 18\nvar useEffectCleanupRegister = typeof useInsertionEffect !== 'undefined' ? useCleanupRegister : useRun;\nexport default useEffectCleanupRegister;", "map": {"version": 3, "names": ["_objectSpread", "warning", "React", "fullClone", "useInsertionEffect", "useCleanupRegister", "deps", "effectCleanups", "cleanupFlag", "register", "fn", "process", "env", "NODE_ENV", "push", "useEffect", "length", "for<PERSON>ach", "useRun", "useEffectCleanupRegister"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/hooks/useEffectCleanupRegister.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from \"rc-util/es/warning\";\nimport * as React from 'react';\nvar fullClone = _objectSpread({}, React);\nvar useInsertionEffect = fullClone.useInsertionEffect;\n\n// DO NOT register functions in useEffect cleanup function, or functions that registered will never be called.\nvar useCleanupRegister = function useCleanupRegister(deps) {\n  var effectCleanups = [];\n  var cleanupFlag = false;\n  function register(fn) {\n    if (cleanupFlag) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '[Ant Design CSS-in-JS] You are registering a cleanup function after unmount, which will not have any effect.');\n      }\n      return;\n    }\n    effectCleanups.push(fn);\n  }\n  React.useEffect(function () {\n    // Compatible with strict mode\n    cleanupFlag = false;\n    return function () {\n      cleanupFlag = true;\n      if (effectCleanups.length) {\n        effectCleanups.forEach(function (fn) {\n          return fn();\n        });\n      }\n    };\n  }, deps);\n  return register;\n};\nvar useRun = function useRun() {\n  return function (fn) {\n    fn();\n  };\n};\n\n// Only enable register in React 18\nvar useEffectCleanupRegister = typeof useInsertionEffect !== 'undefined' ? useCleanupRegister : useRun;\nexport default useEffectCleanupRegister;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC,EAAEE,KAAK,CAAC;AACxC,IAAIE,kBAAkB,GAAGD,SAAS,CAACC,kBAAkB;;AAErD;AACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,IAAI,EAAE;EACzD,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,WAAW,GAAG,KAAK;EACvB,SAASC,QAAQA,CAACC,EAAE,EAAE;IACpB,IAAIF,WAAW,EAAE;MACf,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCZ,OAAO,CAAC,KAAK,EAAE,8GAA8G,CAAC;MAChI;MACA;IACF;IACAM,cAAc,CAACO,IAAI,CAACJ,EAAE,CAAC;EACzB;EACAR,KAAK,CAACa,SAAS,CAAC,YAAY;IAC1B;IACAP,WAAW,GAAG,KAAK;IACnB,OAAO,YAAY;MACjBA,WAAW,GAAG,IAAI;MAClB,IAAID,cAAc,CAACS,MAAM,EAAE;QACzBT,cAAc,CAACU,OAAO,CAAC,UAAUP,EAAE,EAAE;UACnC,OAAOA,EAAE,CAAC,CAAC;QACb,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,EAAEJ,IAAI,CAAC;EACR,OAAOG,QAAQ;AACjB,CAAC;AACD,IAAIS,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAO,UAAUR,EAAE,EAAE;IACnBA,EAAE,CAAC,CAAC;EACN,CAAC;AACH,CAAC;;AAED;AACA,IAAIS,wBAAwB,GAAG,OAAOf,kBAAkB,KAAK,WAAW,GAAGC,kBAAkB,GAAGa,MAAM;AACtG,eAAeC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
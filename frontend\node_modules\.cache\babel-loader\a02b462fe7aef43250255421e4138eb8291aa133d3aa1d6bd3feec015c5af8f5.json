{"ast": null, "code": "import _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _class, _class2;\n// Copyright (c) Project Nayuki. (MIT License)\n// https://www.nayuki.io/page/qr-code-generator-library\n\n// Modification with code reorder and prettier\n\n// --------------------------------------------\n\n// Appends the given number of low-order bits of the given value\n// to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\nfunction appendBits(val, len, bb) {\n  if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');\n  for (var i = len - 1; i >= 0; i-- // Append bit by bit\n  ) bb.push(val >>> i & 1);\n}\n\n// Returns true iff the i'th bit of x is set to 1.\nfunction getBit(x, i) {\n  return (x >>> i & 1) != 0;\n}\n\n// Throws an exception if the given condition is false.\nfunction assert(cond) {\n  if (!cond) throw new Error('Assertion error');\n}\n\n/*---- Public helper enumeration ----*/\n/*\n * Describes how a segment's data bits are numbererpreted. Immutable.\n */\nexport var Mode = /*#__PURE__*/function () {\n  function Mode(modeBits, numBitsCharCount) {\n    _classCallCheck(this, Mode);\n    /*-- Constructor and fields --*/\n    // The mode indicator bits, which is a unumber4 value (range 0 to 15).\n    _defineProperty(this, \"modeBits\", void 0);\n    // Number of character count bits for three different version ranges.\n    _defineProperty(this, \"numBitsCharCount\", void 0);\n    this.modeBits = modeBits;\n    this.numBitsCharCount = numBitsCharCount;\n  }\n\n  /*-- Method --*/\n\n  // (Package-private) Returns the bit width of the character count field for a segment in\n  // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n  _createClass(Mode, [{\n    key: \"numCharCountBits\",\n    value: function numCharCountBits(ver) {\n      return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n    }\n  }]);\n  return Mode;\n}();\n\n/*---- Public helper enumeration ----*/\n\n/*\n * The error correction level in a QR Code symbol. Immutable.\n */\n_class = Mode;\n/*-- Constants --*/\n_defineProperty(Mode, \"NUMERIC\", new _class(0x1, [10, 12, 14]));\n_defineProperty(Mode, \"ALPHANUMERIC\", new _class(0x2, [9, 11, 13]));\n_defineProperty(Mode, \"BYTE\", new _class(0x4, [8, 16, 16]));\n_defineProperty(Mode, \"KANJI\", new _class(0x8, [8, 10, 12]));\n_defineProperty(Mode, \"ECI\", new _class(0x7, [0, 0, 0]));\nexport var Ecc = /*#__PURE__*/_createClass(function Ecc(ordinal, formatBits) {\n  _classCallCheck(this, Ecc);\n  // The QR Code can tolerate about 30% erroneous codewords\n  /*-- Constructor and fields --*/\n  // In the range 0 to 3 (unsigned 2-bit numbereger).\n  _defineProperty(this, \"ordinal\", void 0);\n  // (Package-private) In the range 0 to 3 (unsigned 2-bit numbereger).\n  _defineProperty(this, \"formatBits\", void 0);\n  this.ordinal = ordinal;\n  this.formatBits = formatBits;\n});\n\n/*\n * A segment of character/binary/control data in a QR Code symbol.\n * Instances of this class are immutable.\n * The mid-level way to create a segment is to take the payload data\n * and call a static factory function such as QrSegment.makeNumeric().\n * The low-level way to create a segment is to custom-make the bit buffer\n * and call the QrSegment() constructor with appropriate values.\n * This segment class imposes no length restrictions, but QR Codes have restrictions.\n * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n */\n_class2 = Ecc;\n/*-- Constants --*/\n_defineProperty(Ecc, \"LOW\", new _class2(0, 1));\n// The QR Code can tolerate about  7% erroneous codewords\n_defineProperty(Ecc, \"MEDIUM\", new _class2(1, 0));\n// The QR Code can tolerate about 15% erroneous codewords\n_defineProperty(Ecc, \"QUARTILE\", new _class2(2, 3));\n// The QR Code can tolerate about 25% erroneous codewords\n_defineProperty(Ecc, \"HIGH\", new _class2(3, 2));\nexport var QrSegment = /*#__PURE__*/function () {\n  // Creates a new QR Code segment with the given attributes and data.\n  // The character count (numChars) must agree with the mode and the bit buffer length,\n  // but the constranumber isn't checked. The given bit buffer is cloned and stored.\n  function QrSegment(mode, numChars, bitData) {\n    _classCallCheck(this, QrSegment);\n    /*-- Constructor (low level) and fields --*/\n    // The mode indicator of this segment.\n    _defineProperty(this, \"mode\", void 0);\n    // The length of this segment's unencoded data. Measured in characters for\n    // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n    // Always zero or positive. Not the same as the data's bit length.\n    _defineProperty(this, \"numChars\", void 0);\n    // The data bits of this segment. Accessed through getData().\n    _defineProperty(this, \"bitData\", void 0);\n    this.mode = mode;\n    this.numChars = numChars;\n    this.bitData = bitData;\n    if (numChars < 0) throw new RangeError('Invalid argument');\n    this.bitData = bitData.slice(); // Make defensive copy\n  }\n\n  /*-- Methods --*/\n\n  // Returns a new copy of the data bits of this segment.\n  _createClass(QrSegment, [{\n    key: \"getData\",\n    value: function getData() {\n      return this.bitData.slice(); // Make defensive copy\n    }\n\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n  }], [{\n    key: \"makeBytes\",\n    value: /*-- Static factory functions (mid level) --*/\n\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    function makeBytes(data) {\n      var bb = [];\n      var _iterator = _createForOfIteratorHelper(data),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var b = _step.value;\n          appendBits(b, 8, bb);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      return new QrSegment(Mode.BYTE, data.length, bb);\n    }\n\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n  }, {\n    key: \"makeNumeric\",\n    value: function makeNumeric(digits) {\n      if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');\n      var bb = [];\n      for (var i = 0; i < digits.length;) {\n        // Consume up to 3 digits per iteration\n        var n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new QrSegment(Mode.NUMERIC, digits.length, bb);\n    }\n\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n  }, {\n    key: \"makeAlphanumeric\",\n    value: function makeAlphanumeric(text) {\n      if (!QrSegment.isAlphanumeric(text)) throw new RangeError('String contains unencodable characters in alphanumeric mode');\n      var bb = [];\n      var i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        // Process groups of 2\n        var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        // 1 character remaining\n        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new QrSegment(Mode.ALPHANUMERIC, text.length, bb);\n    }\n\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n  }, {\n    key: \"makeSegments\",\n    value: function makeSegments(text) {\n      // Select the most efficient segment encoding automatically\n      if (text == '') return [];else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n    }\n\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n  }, {\n    key: \"makeEci\",\n    value: function makeEci(assignVal) {\n      var bb = [];\n      if (assignVal < 0) throw new RangeError('ECI assignment value out of range');else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1000000) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError('ECI assignment value out of range');\n      return new QrSegment(Mode.ECI, 0, bb);\n    }\n\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n  }, {\n    key: \"isNumeric\",\n    value: function isNumeric(text) {\n      return QrSegment.NUMERIC_REGEX.test(text);\n    }\n\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n  }, {\n    key: \"isAlphanumeric\",\n    value: function isAlphanumeric(text) {\n      return QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n  }, {\n    key: \"getTotalBits\",\n    value: function getTotalBits(segs, version) {\n      var result = 0;\n      var _iterator2 = _createForOfIteratorHelper(segs),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var seg = _step2.value;\n          var ccbits = seg.mode.numCharCountBits(version);\n          if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width\n          result += 4 + ccbits + seg.bitData.length;\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n      return result;\n    }\n\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n  }, {\n    key: \"toUtf8ByteArray\",\n    value: function toUtf8ByteArray(input) {\n      var str = encodeURI(input);\n      var result = [];\n      for (var i = 0; i < str.length; i++) {\n        if (str.charAt(i) != '%') result.push(str.charCodeAt(i));else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n\n    /*-- Constants --*/\n\n    // Describes precisely all strings that are encodable in numeric mode.\n  }]);\n  return QrSegment;\n}();\n\n/*\n * A QR Code symbol, which is a type of two-dimension barcode.\n * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n * Instances of this class represent an immutable square grid of dark and light cells.\n * The class provides static factory functions to create a QR Code from text or binary data.\n * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n *\n * Ways to create a QR Code object:\n * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n * - Low level: Custom-make the array of data codeword bytes (including\n *   segment headers and final padding, excluding error correction codewords),\n *   supply the appropriate version number, and call the QrCode() constructor.\n * (Note that all ways require supplying the desired error correction level.)\n */\n_defineProperty(QrSegment, \"NUMERIC_REGEX\", /^[0-9]*$/);\n// Describes precisely all strings that are encodable in alphanumeric mode.\n_defineProperty(QrSegment, \"ALPHANUMERIC_REGEX\", /^[A-Z0-9 $%*+.\\/:-]*$/);\n// The set of all legal characters in alphanumeric mode,\n// where each character value maps to the index in the string.\n_defineProperty(QrSegment, \"ALPHANUMERIC_CHARSET\", '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:');\nexport var QrCode = /*#__PURE__*/function () {\n  // Creates a new QR Code with the given version number,\n  // error correction level, data codeword bytes, and mask number.\n  // This is a low-level API that most users should not use directly.\n  // A mid-level API is the encodeSegments() function.\n  function QrCode(\n  // The version number of this QR Code, which is between 1 and 40 (inclusive).\n  // This determines the size of this barcode.\n  version,\n  // The error correction level used in this QR Code.\n  errorCorrectionLevel, dataCodewords, oriMsk) {\n    _classCallCheck(this, QrCode);\n    /*-- Fields --*/\n    // The width and height of this QR Code, measured in modules, between\n    // 21 and 177 (inclusive). This is equal to version * 4 + 17.\n    _defineProperty(this, \"size\", void 0);\n    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).\n    // Even if a QR Code is created with automatic masking requested (mask = -1),\n    // the resulting object still has a mask value between 0 and 7.\n    _defineProperty(this, \"mask\", void 0);\n    // The modules of this QR Code (false = light, true = dark).\n    // Immutable after constructor finishes. Accessed through getModule().\n    _defineProperty(this, \"modules\", []);\n    // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n    _defineProperty(this, \"isFunction\", []);\n    /*-- Constructor (low level) and fields --*/\n    // The version number of this QR Code, which is between 1 and 40 (inclusive).\n    // This determines the size of this barcode.\n    _defineProperty(this, \"version\", void 0);\n    // The error correction level used in this QR Code.\n    _defineProperty(this, \"errorCorrectionLevel\", void 0);\n    var msk = oriMsk;\n    this.version = version;\n    this.errorCorrectionLevel = errorCorrectionLevel;\n    // Check scalar arguments\n    if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError('Version value out of range');\n    if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');\n    this.size = version * 4 + 17;\n\n    // Initialize both grids to be size*size arrays of Boolean false\n    var row = [];\n    for (var i = 0; i < this.size; i++) row.push(false);\n    for (var _i = 0; _i < this.size; _i++) {\n      this.modules.push(row.slice()); // Initially all light\n      this.isFunction.push(row.slice());\n    }\n\n    // Compute ECC, draw modules\n    this.drawFunctionPatterns();\n    var allCodewords = this.addEccAndInterleave(dataCodewords);\n    this.drawCodewords(allCodewords);\n\n    // Do masking\n    if (msk == -1) {\n      // Automatically choose best mask\n      var minPenalty = 1000000000;\n      for (var _i2 = 0; _i2 < 8; _i2++) {\n        this.applyMask(_i2);\n        this.drawFormatBits(_i2);\n        var penalty = this.getPenaltyScore();\n        if (penalty < minPenalty) {\n          msk = _i2;\n          minPenalty = penalty;\n        }\n        this.applyMask(_i2); // Undoes the mask due to XOR\n      }\n    }\n    assert(0 <= msk && msk <= 7);\n    this.mask = msk;\n    this.applyMask(msk); // Apply the final choice of mask\n    this.drawFormatBits(msk); // Overwrite old format bits\n\n    this.isFunction = [];\n  }\n\n  /*-- Accessor methods --*/\n\n  // Returns the color of the module (pixel) at the given coordinates, which is false\n  // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n  // If the given coordinates are out of bounds, then false (light) is returned.\n  _createClass(QrCode, [{\n    key: \"getModule\",\n    value: function getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n\n    // Modified to expose modules for easy access\n  }, {\n    key: \"getModules\",\n    value: function getModules() {\n      return this.modules;\n    }\n\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n\n    // Reads this object's version field, and draws and marks all function modules.\n  }, {\n    key: \"drawFunctionPatterns\",\n    value: function drawFunctionPatterns() {\n      // Draw horizontal and vertical timing patterns\n      for (var i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n\n      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n\n      // Draw numerous alignment patterns\n      var alignPatPos = this.getAlignmentPatternPositions();\n      var numAlign = alignPatPos.length;\n      for (var _i3 = 0; _i3 < numAlign; _i3++) {\n        for (var j = 0; j < numAlign; j++) {\n          // Don't draw on the three finder corners\n          if (!(_i3 == 0 && j == 0 || _i3 == 0 && j == numAlign - 1 || _i3 == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[_i3], alignPatPos[j]);\n        }\n      }\n\n      // Draw configuration data\n      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n      this.drawVersion();\n    }\n\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n  }, {\n    key: \"drawFormatBits\",\n    value: function drawFormatBits(mask) {\n      // Calculate error correction code and pack bits\n      var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is unumber2, mask is unumber3\n      var rem = data;\n      for (var i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 0x537;\n      var bits = (data << 10 | rem) ^ 0x5412; // unumber15\n      assert(bits >>> 15 == 0);\n\n      // Draw first copy\n      for (var _i4 = 0; _i4 <= 5; _i4++) this.setFunctionModule(8, _i4, getBit(bits, _i4));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (var _i5 = 9; _i5 < 15; _i5++) this.setFunctionModule(14 - _i5, 8, getBit(bits, _i5));\n\n      // Draw second copy\n      for (var _i6 = 0; _i6 < 8; _i6++) this.setFunctionModule(this.size - 1 - _i6, 8, getBit(bits, _i6));\n      for (var _i7 = 8; _i7 < 15; _i7++) this.setFunctionModule(8, this.size - 15 + _i7, getBit(bits, _i7));\n      this.setFunctionModule(8, this.size - 8, true); // Always dark\n    }\n\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n  }, {\n    key: \"drawVersion\",\n    value: function drawVersion() {\n      if (this.version < 7) return;\n\n      // Calculate error correction code and pack bits\n      var rem = this.version; // version is unumber6, in the range [7, 40]\n      for (var i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 0x1f25;\n      var bits = this.version << 12 | rem; // unumber18\n      assert(bits >>> 18 == 0);\n\n      // Draw two copies\n      for (var _i8 = 0; _i8 < 18; _i8++) {\n        var color = getBit(bits, _i8);\n        var a = this.size - 11 + _i8 % 3;\n        var b = Math.floor(_i8 / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n  }, {\n    key: \"drawFinderPattern\",\n    value: function drawFinderPattern(x, y) {\n      for (var dy = -4; dy <= 4; dy++) {\n        for (var dx = -4; dx <= 4; dx++) {\n          var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n          var xx = x + dx;\n          var yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n  }, {\n    key: \"drawAlignmentPattern\",\n    value: function drawAlignmentPattern(x, y) {\n      for (var dy = -2; dy <= 2; dy++) {\n        for (var dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n  }, {\n    key: \"setFunctionModule\",\n    value: function setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n  }, {\n    key: \"addEccAndInterleave\",\n    value: function addEccAndInterleave(data) {\n      var ver = this.version;\n      var ecl = this.errorCorrectionLevel;\n      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');\n\n      // Calculate parameter numbers\n      var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n      var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n\n      // Split data numbero blocks and append ECC to each block\n      var blocks = [];\n      var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (var i = 0, k = 0; i < numBlocks; i++) {\n        var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n\n      // Interleave (not concatenate) the bytes from every block numbero a single sequence\n      var result = [];\n      var _loop = function _loop(_i9) {\n        blocks.forEach(function (block, j) {\n          // Skip the padding byte in short blocks\n          if (_i9 != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[_i9]);\n        });\n      };\n      for (var _i9 = 0; _i9 < blocks[0].length; _i9++) {\n        _loop(_i9);\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n  }, {\n    key: \"drawCodewords\",\n    value: function drawCodewords(data) {\n      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError('Invalid argument');\n      var i = 0; // Bit index numbero the data\n      // Do the funny zigzag scan\n      for (var right = this.size - 1; right >= 1; right -= 2) {\n        // Index of right column in each column pair\n        if (right == 6) right = 5;\n        for (var vert = 0; vert < this.size; vert++) {\n          // Vertical counter\n          for (var j = 0; j < 2; j++) {\n            var x = right - j; // Actual x coordinate\n            var upward = (right + 1 & 2) == 0;\n            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n            // 0/false/light by the constructor and are left unchanged by this method\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n  }, {\n    key: \"applyMask\",\n    value: function applyMask(mask) {\n      if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');\n      for (var y = 0; y < this.size; y++) {\n        for (var x = 0; x < this.size; x++) {\n          var invert = void 0;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error('Unreachable');\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n  }, {\n    key: \"getPenaltyScore\",\n    value: function getPenaltyScore() {\n      var result = 0;\n\n      // Adjacent modules in row having same color, and finder-like patterns\n      for (var y = 0; y < this.size; y++) {\n        var runColor = false;\n        var runX = 0;\n        var runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += QrCode.PENALTY_N1;else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n      }\n      // Adjacent modules in column having same color, and finder-like patterns\n      for (var _x = 0; _x < this.size; _x++) {\n        var _runColor = false;\n        var runY = 0;\n        var _runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var _y = 0; _y < this.size; _y++) {\n          if (this.modules[_y][_x] == _runColor) {\n            runY++;\n            if (runY == 5) result += QrCode.PENALTY_N1;else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, _runHistory);\n            if (!_runColor) result += this.finderPenaltyCountPatterns(_runHistory) * QrCode.PENALTY_N3;\n            _runColor = this.modules[_y][_x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(_runColor, runY, _runHistory) * QrCode.PENALTY_N3;\n      }\n\n      // 2*2 blocks of modules having same color\n      for (var _y2 = 0; _y2 < this.size - 1; _y2++) {\n        for (var _x2 = 0; _x2 < this.size - 1; _x2++) {\n          var color = this.modules[_y2][_x2];\n          if (color == this.modules[_y2][_x2 + 1] && color == this.modules[_y2 + 1][_x2] && color == this.modules[_y2 + 1][_x2 + 1]) result += QrCode.PENALTY_N2;\n        }\n      }\n\n      // Balance of dark and light modules\n      var dark = 0;\n      var _iterator3 = _createForOfIteratorHelper(this.modules),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var row = _step3.value;\n          dark = row.reduce(function (sum, color) {\n            return sum + (color ? 1 : 0);\n          }, dark);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n      // Compute the smallest numbereger k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n      var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n      return result;\n    }\n\n    /*-- Private helper functions --*/\n\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of numberegers.\n  }, {\n    key: \"getAlignmentPatternPositions\",\n    value: function getAlignmentPatternPositions() {\n      if (this.version == 1) return [];else {\n        var numAlign = Math.floor(this.version / 7) + 2;\n        var step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        var result = [6];\n        for (var pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    }\n\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n  }, {\n    key: \"finderPenaltyCountPatterns\",\n    value:\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    function finderPenaltyCountPatterns(runHistory) {\n      var n = runHistory[1];\n      assert(n <= this.size * 3);\n      var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n  }, {\n    key: \"finderPenaltyTerminateAndCount\",\n    value: function finderPenaltyTerminateAndCount(currentRunColor, oriCurrentRunLength, runHistory) {\n      var currentRunLength = oriCurrentRunLength;\n      if (currentRunColor) {\n        // Terminate dark run\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size; // Add light border to final run\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n  }, {\n    key: \"finderPenaltyAddHistory\",\n    value: function finderPenaltyAddHistory(oriCurrentRunLength, runHistory) {\n      var currentRunLength = oriCurrentRunLength;\n      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n\n    /*-- Constants and tables --*/\n\n    // The minimum version number supported in the QR Code Model 2 standard.\n  }], [{\n    key: \"encodeText\",\n    value: /*-- Static factory functions (high level) --*/\n\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code ponumbers (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    function encodeText(text, ecl) {\n      var segs = QrSegment.makeSegments(text);\n      return QrCode.encodeSegments(segs, ecl);\n    }\n\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n  }, {\n    key: \"encodeBinary\",\n    value: function encodeBinary(data, ecl) {\n      var seg = QrSegment.makeBytes(data);\n      return QrCode.encodeSegments([seg], ecl);\n    }\n\n    /*-- Static factory functions (mid level) --*/\n\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n  }, {\n    key: \"encodeSegments\",\n    value: function encodeSegments(segs, oriEcl) {\n      var minVersion = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n      var maxVersion = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 40;\n      var mask = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n      var boostEcl = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;\n      if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError('Invalid value');\n\n      // Find the minimal version number to use\n      var version;\n      var dataUsedBits;\n      for (version = minVersion;; version++) {\n        var _dataCapacityBits = QrCode.getNumDataCodewords(version, oriEcl) * 8; // Number of data bits available\n        var usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= _dataCapacityBits) {\n          dataUsedBits = usedBits;\n          break; // This version number is found to be suitable\n        }\n        if (version >= maxVersion)\n          // All versions in the range could not fit the given data\n          throw new RangeError('Data too long');\n      }\n      var ecl = oriEcl;\n      // Increase the error correction level while the data still fits in the current version number\n      for (var _i10 = 0, _arr = [Ecc.MEDIUM, Ecc.QUARTILE, Ecc.HIGH]; _i10 < _arr.length; _i10++) {\n        var newEcl = _arr[_i10];\n        // From low to high\n        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n\n      // Concatenate all segments to create the data bit string\n      var bb = [];\n      var _iterator4 = _createForOfIteratorHelper(segs),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var seg = _step4.value;\n          appendBits(seg.mode.modeBits, 4, bb);\n          appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n          var _iterator5 = _createForOfIteratorHelper(seg.getData()),\n            _step5;\n          try {\n            for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n              var b = _step5.value;\n              bb.push(b);\n            }\n          } catch (err) {\n            _iterator5.e(err);\n          } finally {\n            _iterator5.f();\n          }\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n      assert(bb.length == dataUsedBits);\n\n      // Add terminator and pad up to a byte if applicable\n      var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n\n      // Pad with alternating bytes until data capacity is reached\n      for (var padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11) appendBits(padByte, 8, bb);\n\n      // Pack bits numbero bytes in big endian\n      var dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach(function (b, i) {\n        return dataCodewords[i >>> 3] |= b << 7 - (i & 7);\n      });\n\n      // Create the QR Code object\n      return new QrCode(version, ecl, dataCodewords, mask);\n    }\n  }, {\n    key: \"getNumRawDataModules\",\n    value: function getNumRawDataModules(ver) {\n      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');\n      var result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        var numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n  }, {\n    key: \"getNumDataCodewords\",\n    value: function getNumDataCodewords(ver, ecl) {\n      return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n  }, {\n    key: \"reedSolomonComputeDivisor\",\n    value: function reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');\n      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the unumber8 array [255, 8, 93].\n      var result = [];\n      for (var i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1); // Start off with the monomial x^0\n\n      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n      // and drop the highest monomial term which is always 1x^degree.\n      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n      var root = 1;\n      for (var _i11 = 0; _i11 < degree; _i11++) {\n        // Multiply the current product by (x - r^i)\n        for (var j = 0; j < result.length; j++) {\n          result[j] = QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = QrCode.reedSolomonMultiply(root, 0x02);\n      }\n      return result;\n    }\n\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n  }, {\n    key: \"reedSolomonComputeRemainder\",\n    value: function reedSolomonComputeRemainder(data, divisor) {\n      var result = divisor.map(function () {\n        return 0;\n      });\n      var _iterator6 = _createForOfIteratorHelper(data),\n        _step6;\n      try {\n        var _loop2 = function _loop2() {\n          var b = _step6.value;\n          // Polynomial division\n          var factor = b ^ result.shift();\n          result.push(0);\n          divisor.forEach(function (coef, i) {\n            return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n          });\n        };\n        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n          _loop2();\n        }\n      } catch (err) {\n        _iterator6.e(err);\n      } finally {\n        _iterator6.f();\n      }\n      return result;\n    }\n\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit numberegers. This could be implemented as a lookup table of 256*256 entries of unumber8.\n  }, {\n    key: \"reedSolomonMultiply\",\n    value: function reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');\n      // Russian peasant multiplication\n      var z = 0;\n      for (var i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 0x11d;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n  }]);\n  return QrCode;\n}();\n_defineProperty(QrCode, \"MIN_VERSION\", 1);\n// The maximum version number supported in the QR Code Model 2 standard.\n_defineProperty(QrCode, \"MAX_VERSION\", 40);\n// For use in getPenaltyScore(), when evaluating which mask is best.\n_defineProperty(QrCode, \"PENALTY_N1\", 3);\n_defineProperty(QrCode, \"PENALTY_N2\", 3);\n_defineProperty(QrCode, \"PENALTY_N3\", 40);\n_defineProperty(QrCode, \"PENALTY_N4\", 10);\n_defineProperty(QrCode, \"ECC_CODEWORDS_PER_BLOCK\", [\n// Version: (note that index 0 is for padding, and is set to an illegal value)\n//0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n[-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n// Low\n[-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n// Medium\n[-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n// Quartile\n[-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30] // High\n]);\n_defineProperty(QrCode, \"NUM_ERROR_CORRECTION_BLOCKS\", [\n// Version: (note that index 0 is for padding, and is set to an illegal value)\n//0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n[-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n// Low\n[-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n// Medium\n[-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n// Quartile\n[-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81] // High\n]);", "map": {"version": 3, "names": ["_createForOfIteratorHelper", "_classCallCheck", "_createClass", "_defineProperty", "_class", "_class2", "appendBits", "val", "len", "bb", "RangeError", "i", "push", "getBit", "x", "assert", "cond", "Error", "Mode", "modeBits", "numBitsCharCount", "key", "value", "numCharCountBits", "ver", "Math", "floor", "Ecc", "ordinal", "formatBits", "QrSegment", "mode", "numChars", "bitData", "slice", "getData", "makeBytes", "data", "_iterator", "_step", "s", "n", "done", "b", "err", "e", "f", "BYTE", "length", "makeNumeric", "digits", "isNumeric", "min", "parseInt", "substring", "NUMERIC", "makeAlphanumeric", "text", "isAlphanumeric", "temp", "ALPHANUMERIC_CHARSET", "indexOf", "char<PERSON>t", "ALPHANUMERIC", "makeSegments", "toUtf8ByteArray", "makeEci", "assignVal", "ECI", "NUMERIC_REGEX", "test", "ALPHANUMERIC_REGEX", "getTotalBits", "segs", "version", "result", "_iterator2", "_step2", "seg", "ccbits", "Infinity", "input", "str", "encodeURI", "charCodeAt", "QrCode", "errorCorrectionLevel", "dataCodewords", "oriMsk", "msk", "MIN_VERSION", "MAX_VERSION", "size", "row", "_i", "modules", "isFunction", "drawFunctionPatterns", "allCodewords", "addEccAndInterleave", "drawCodewords", "min<PERSON><PERSON><PERSON><PERSON>", "_i2", "applyMask", "drawFormatBits", "penalty", "getPenaltyScore", "mask", "getModule", "y", "getModules", "setFunctionModule", "drawFinderPattern", "alignPatPos", "getAlignmentPatternPositions", "numAlign", "_i3", "j", "drawAlignmentPattern", "drawVersion", "rem", "bits", "_i4", "_i5", "_i6", "_i7", "_i8", "color", "a", "dy", "dx", "dist", "max", "abs", "xx", "yy", "isDark", "ecl", "getNumDataCodewords", "numBlocks", "NUM_ERROR_CORRECTION_BLOCKS", "blockEccLen", "ECC_CODEWORDS_PER_BLOCK", "rawCodewords", "getNumRawDataModules", "numShortBlocks", "shortBlockLen", "blocks", "rsDiv", "reedSolomonComputeDivisor", "k", "dat", "ecc", "reedSolomonComputeRemainder", "concat", "_loop", "_i9", "for<PERSON>ach", "block", "right", "vert", "upward", "invert", "runColor", "runX", "runHistory", "PENALTY_N1", "finderPenaltyAddHistory", "finderPenaltyCountPatterns", "PENALTY_N3", "finderPenaltyTerminateAndCount", "_x", "_runColor", "runY", "_runHistory", "_y", "_y2", "_x2", "PENALTY_N2", "dark", "_iterator3", "_step3", "reduce", "sum", "total", "ceil", "PENALTY_N4", "step", "pos", "splice", "core", "currentRunColor", "oriCurrent<PERSON>un<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "encodeText", "encodeSegments", "encodeBinary", "oriEcl", "minVersion", "arguments", "undefined", "maxVersion", "boostEcl", "dataUsedBits", "_dataCapacityBits", "usedBits", "_i10", "_arr", "MEDIUM", "QUARTILE", "HIGH", "newEcl", "_iterator4", "_step4", "_iterator5", "_step5", "dataCapacityBits", "padByte", "degree", "root", "_i11", "reedSolomonMultiply", "divisor", "map", "_iterator6", "_step6", "_loop2", "factor", "shift", "coef", "z"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+qrcode@1.0.0__80c0c9a2a24555eee6024b4b25bfb6ac/node_modules/@rc-component/qrcode/es/libs/qrcodegen.js"], "sourcesContent": ["import _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _class, _class2;\n// Copyright (c) Project Nayuki. (MIT License)\n// https://www.nayuki.io/page/qr-code-generator-library\n\n// Modification with code reorder and prettier\n\n// --------------------------------------------\n\n// Appends the given number of low-order bits of the given value\n// to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\nfunction appendBits(val, len, bb) {\n  if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');\n  for (var i = len - 1; i >= 0; i-- // Append bit by bit\n  ) bb.push(val >>> i & 1);\n}\n\n// Returns true iff the i'th bit of x is set to 1.\nfunction getBit(x, i) {\n  return (x >>> i & 1) != 0;\n}\n\n// Throws an exception if the given condition is false.\nfunction assert(cond) {\n  if (!cond) throw new Error('Assertion error');\n}\n\n/*---- Public helper enumeration ----*/\n/*\n * Describes how a segment's data bits are numbererpreted. Immutable.\n */\nexport var Mode = /*#__PURE__*/function () {\n  function Mode(modeBits, numBitsCharCount) {\n    _classCallCheck(this, Mode);\n    /*-- Constructor and fields --*/\n    // The mode indicator bits, which is a unumber4 value (range 0 to 15).\n    _defineProperty(this, \"modeBits\", void 0);\n    // Number of character count bits for three different version ranges.\n    _defineProperty(this, \"numBitsCharCount\", void 0);\n    this.modeBits = modeBits;\n    this.numBitsCharCount = numBitsCharCount;\n  }\n\n  /*-- Method --*/\n\n  // (Package-private) Returns the bit width of the character count field for a segment in\n  // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n  _createClass(Mode, [{\n    key: \"numCharCountBits\",\n    value: function numCharCountBits(ver) {\n      return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n    }\n  }]);\n  return Mode;\n}();\n\n/*---- Public helper enumeration ----*/\n\n/*\n * The error correction level in a QR Code symbol. Immutable.\n */\n_class = Mode;\n/*-- Constants --*/\n_defineProperty(Mode, \"NUMERIC\", new _class(0x1, [10, 12, 14]));\n_defineProperty(Mode, \"ALPHANUMERIC\", new _class(0x2, [9, 11, 13]));\n_defineProperty(Mode, \"BYTE\", new _class(0x4, [8, 16, 16]));\n_defineProperty(Mode, \"KANJI\", new _class(0x8, [8, 10, 12]));\n_defineProperty(Mode, \"ECI\", new _class(0x7, [0, 0, 0]));\nexport var Ecc = /*#__PURE__*/_createClass(function Ecc(ordinal, formatBits) {\n  _classCallCheck(this, Ecc);\n  // The QR Code can tolerate about 30% erroneous codewords\n  /*-- Constructor and fields --*/\n  // In the range 0 to 3 (unsigned 2-bit numbereger).\n  _defineProperty(this, \"ordinal\", void 0);\n  // (Package-private) In the range 0 to 3 (unsigned 2-bit numbereger).\n  _defineProperty(this, \"formatBits\", void 0);\n  this.ordinal = ordinal;\n  this.formatBits = formatBits;\n});\n\n/*\n * A segment of character/binary/control data in a QR Code symbol.\n * Instances of this class are immutable.\n * The mid-level way to create a segment is to take the payload data\n * and call a static factory function such as QrSegment.makeNumeric().\n * The low-level way to create a segment is to custom-make the bit buffer\n * and call the QrSegment() constructor with appropriate values.\n * This segment class imposes no length restrictions, but QR Codes have restrictions.\n * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n */\n_class2 = Ecc;\n/*-- Constants --*/\n_defineProperty(Ecc, \"LOW\", new _class2(0, 1));\n// The QR Code can tolerate about  7% erroneous codewords\n_defineProperty(Ecc, \"MEDIUM\", new _class2(1, 0));\n// The QR Code can tolerate about 15% erroneous codewords\n_defineProperty(Ecc, \"QUARTILE\", new _class2(2, 3));\n// The QR Code can tolerate about 25% erroneous codewords\n_defineProperty(Ecc, \"HIGH\", new _class2(3, 2));\nexport var QrSegment = /*#__PURE__*/function () {\n  // Creates a new QR Code segment with the given attributes and data.\n  // The character count (numChars) must agree with the mode and the bit buffer length,\n  // but the constranumber isn't checked. The given bit buffer is cloned and stored.\n  function QrSegment(mode, numChars, bitData) {\n    _classCallCheck(this, QrSegment);\n    /*-- Constructor (low level) and fields --*/\n    // The mode indicator of this segment.\n    _defineProperty(this, \"mode\", void 0);\n    // The length of this segment's unencoded data. Measured in characters for\n    // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n    // Always zero or positive. Not the same as the data's bit length.\n    _defineProperty(this, \"numChars\", void 0);\n    // The data bits of this segment. Accessed through getData().\n    _defineProperty(this, \"bitData\", void 0);\n    this.mode = mode;\n    this.numChars = numChars;\n    this.bitData = bitData;\n    if (numChars < 0) throw new RangeError('Invalid argument');\n    this.bitData = bitData.slice(); // Make defensive copy\n  }\n\n  /*-- Methods --*/\n\n  // Returns a new copy of the data bits of this segment.\n  _createClass(QrSegment, [{\n    key: \"getData\",\n    value: function getData() {\n      return this.bitData.slice(); // Make defensive copy\n    }\n\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n  }], [{\n    key: \"makeBytes\",\n    value: /*-- Static factory functions (mid level) --*/\n\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    function makeBytes(data) {\n      var bb = [];\n      var _iterator = _createForOfIteratorHelper(data),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var b = _step.value;\n          appendBits(b, 8, bb);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      return new QrSegment(Mode.BYTE, data.length, bb);\n    }\n\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n  }, {\n    key: \"makeNumeric\",\n    value: function makeNumeric(digits) {\n      if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');\n      var bb = [];\n      for (var i = 0; i < digits.length;) {\n        // Consume up to 3 digits per iteration\n        var n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new QrSegment(Mode.NUMERIC, digits.length, bb);\n    }\n\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n  }, {\n    key: \"makeAlphanumeric\",\n    value: function makeAlphanumeric(text) {\n      if (!QrSegment.isAlphanumeric(text)) throw new RangeError('String contains unencodable characters in alphanumeric mode');\n      var bb = [];\n      var i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        // Process groups of 2\n        var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        // 1 character remaining\n        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new QrSegment(Mode.ALPHANUMERIC, text.length, bb);\n    }\n\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n  }, {\n    key: \"makeSegments\",\n    value: function makeSegments(text) {\n      // Select the most efficient segment encoding automatically\n      if (text == '') return [];else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n    }\n\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n  }, {\n    key: \"makeEci\",\n    value: function makeEci(assignVal) {\n      var bb = [];\n      if (assignVal < 0) throw new RangeError('ECI assignment value out of range');else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1000000) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError('ECI assignment value out of range');\n      return new QrSegment(Mode.ECI, 0, bb);\n    }\n\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n  }, {\n    key: \"isNumeric\",\n    value: function isNumeric(text) {\n      return QrSegment.NUMERIC_REGEX.test(text);\n    }\n\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n  }, {\n    key: \"isAlphanumeric\",\n    value: function isAlphanumeric(text) {\n      return QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n  }, {\n    key: \"getTotalBits\",\n    value: function getTotalBits(segs, version) {\n      var result = 0;\n      var _iterator2 = _createForOfIteratorHelper(segs),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var seg = _step2.value;\n          var ccbits = seg.mode.numCharCountBits(version);\n          if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width\n          result += 4 + ccbits + seg.bitData.length;\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n      return result;\n    }\n\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n  }, {\n    key: \"toUtf8ByteArray\",\n    value: function toUtf8ByteArray(input) {\n      var str = encodeURI(input);\n      var result = [];\n      for (var i = 0; i < str.length; i++) {\n        if (str.charAt(i) != '%') result.push(str.charCodeAt(i));else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n\n    /*-- Constants --*/\n\n    // Describes precisely all strings that are encodable in numeric mode.\n  }]);\n  return QrSegment;\n}();\n\n/*\n * A QR Code symbol, which is a type of two-dimension barcode.\n * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n * Instances of this class represent an immutable square grid of dark and light cells.\n * The class provides static factory functions to create a QR Code from text or binary data.\n * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n *\n * Ways to create a QR Code object:\n * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n * - Low level: Custom-make the array of data codeword bytes (including\n *   segment headers and final padding, excluding error correction codewords),\n *   supply the appropriate version number, and call the QrCode() constructor.\n * (Note that all ways require supplying the desired error correction level.)\n */\n_defineProperty(QrSegment, \"NUMERIC_REGEX\", /^[0-9]*$/);\n// Describes precisely all strings that are encodable in alphanumeric mode.\n_defineProperty(QrSegment, \"ALPHANUMERIC_REGEX\", /^[A-Z0-9 $%*+.\\/:-]*$/);\n// The set of all legal characters in alphanumeric mode,\n// where each character value maps to the index in the string.\n_defineProperty(QrSegment, \"ALPHANUMERIC_CHARSET\", '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:');\nexport var QrCode = /*#__PURE__*/function () {\n  // Creates a new QR Code with the given version number,\n  // error correction level, data codeword bytes, and mask number.\n  // This is a low-level API that most users should not use directly.\n  // A mid-level API is the encodeSegments() function.\n  function QrCode(\n  // The version number of this QR Code, which is between 1 and 40 (inclusive).\n  // This determines the size of this barcode.\n  version,\n  // The error correction level used in this QR Code.\n  errorCorrectionLevel, dataCodewords, oriMsk) {\n    _classCallCheck(this, QrCode);\n    /*-- Fields --*/\n    // The width and height of this QR Code, measured in modules, between\n    // 21 and 177 (inclusive). This is equal to version * 4 + 17.\n    _defineProperty(this, \"size\", void 0);\n    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).\n    // Even if a QR Code is created with automatic masking requested (mask = -1),\n    // the resulting object still has a mask value between 0 and 7.\n    _defineProperty(this, \"mask\", void 0);\n    // The modules of this QR Code (false = light, true = dark).\n    // Immutable after constructor finishes. Accessed through getModule().\n    _defineProperty(this, \"modules\", []);\n    // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n    _defineProperty(this, \"isFunction\", []);\n    /*-- Constructor (low level) and fields --*/\n    // The version number of this QR Code, which is between 1 and 40 (inclusive).\n    // This determines the size of this barcode.\n    _defineProperty(this, \"version\", void 0);\n    // The error correction level used in this QR Code.\n    _defineProperty(this, \"errorCorrectionLevel\", void 0);\n    var msk = oriMsk;\n    this.version = version;\n    this.errorCorrectionLevel = errorCorrectionLevel;\n    // Check scalar arguments\n    if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError('Version value out of range');\n    if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');\n    this.size = version * 4 + 17;\n\n    // Initialize both grids to be size*size arrays of Boolean false\n    var row = [];\n    for (var i = 0; i < this.size; i++) row.push(false);\n    for (var _i = 0; _i < this.size; _i++) {\n      this.modules.push(row.slice()); // Initially all light\n      this.isFunction.push(row.slice());\n    }\n\n    // Compute ECC, draw modules\n    this.drawFunctionPatterns();\n    var allCodewords = this.addEccAndInterleave(dataCodewords);\n    this.drawCodewords(allCodewords);\n\n    // Do masking\n    if (msk == -1) {\n      // Automatically choose best mask\n      var minPenalty = 1000000000;\n      for (var _i2 = 0; _i2 < 8; _i2++) {\n        this.applyMask(_i2);\n        this.drawFormatBits(_i2);\n        var penalty = this.getPenaltyScore();\n        if (penalty < minPenalty) {\n          msk = _i2;\n          minPenalty = penalty;\n        }\n        this.applyMask(_i2); // Undoes the mask due to XOR\n      }\n    }\n    assert(0 <= msk && msk <= 7);\n    this.mask = msk;\n    this.applyMask(msk); // Apply the final choice of mask\n    this.drawFormatBits(msk); // Overwrite old format bits\n\n    this.isFunction = [];\n  }\n\n  /*-- Accessor methods --*/\n\n  // Returns the color of the module (pixel) at the given coordinates, which is false\n  // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n  // If the given coordinates are out of bounds, then false (light) is returned.\n  _createClass(QrCode, [{\n    key: \"getModule\",\n    value: function getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n\n    // Modified to expose modules for easy access\n  }, {\n    key: \"getModules\",\n    value: function getModules() {\n      return this.modules;\n    }\n\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n\n    // Reads this object's version field, and draws and marks all function modules.\n  }, {\n    key: \"drawFunctionPatterns\",\n    value: function drawFunctionPatterns() {\n      // Draw horizontal and vertical timing patterns\n      for (var i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n\n      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n\n      // Draw numerous alignment patterns\n      var alignPatPos = this.getAlignmentPatternPositions();\n      var numAlign = alignPatPos.length;\n      for (var _i3 = 0; _i3 < numAlign; _i3++) {\n        for (var j = 0; j < numAlign; j++) {\n          // Don't draw on the three finder corners\n          if (!(_i3 == 0 && j == 0 || _i3 == 0 && j == numAlign - 1 || _i3 == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[_i3], alignPatPos[j]);\n        }\n      }\n\n      // Draw configuration data\n      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n      this.drawVersion();\n    }\n\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n  }, {\n    key: \"drawFormatBits\",\n    value: function drawFormatBits(mask) {\n      // Calculate error correction code and pack bits\n      var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is unumber2, mask is unumber3\n      var rem = data;\n      for (var i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 0x537;\n      var bits = (data << 10 | rem) ^ 0x5412; // unumber15\n      assert(bits >>> 15 == 0);\n\n      // Draw first copy\n      for (var _i4 = 0; _i4 <= 5; _i4++) this.setFunctionModule(8, _i4, getBit(bits, _i4));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (var _i5 = 9; _i5 < 15; _i5++) this.setFunctionModule(14 - _i5, 8, getBit(bits, _i5));\n\n      // Draw second copy\n      for (var _i6 = 0; _i6 < 8; _i6++) this.setFunctionModule(this.size - 1 - _i6, 8, getBit(bits, _i6));\n      for (var _i7 = 8; _i7 < 15; _i7++) this.setFunctionModule(8, this.size - 15 + _i7, getBit(bits, _i7));\n      this.setFunctionModule(8, this.size - 8, true); // Always dark\n    }\n\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n  }, {\n    key: \"drawVersion\",\n    value: function drawVersion() {\n      if (this.version < 7) return;\n\n      // Calculate error correction code and pack bits\n      var rem = this.version; // version is unumber6, in the range [7, 40]\n      for (var i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 0x1f25;\n      var bits = this.version << 12 | rem; // unumber18\n      assert(bits >>> 18 == 0);\n\n      // Draw two copies\n      for (var _i8 = 0; _i8 < 18; _i8++) {\n        var color = getBit(bits, _i8);\n        var a = this.size - 11 + _i8 % 3;\n        var b = Math.floor(_i8 / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n  }, {\n    key: \"drawFinderPattern\",\n    value: function drawFinderPattern(x, y) {\n      for (var dy = -4; dy <= 4; dy++) {\n        for (var dx = -4; dx <= 4; dx++) {\n          var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n          var xx = x + dx;\n          var yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n  }, {\n    key: \"drawAlignmentPattern\",\n    value: function drawAlignmentPattern(x, y) {\n      for (var dy = -2; dy <= 2; dy++) {\n        for (var dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n  }, {\n    key: \"setFunctionModule\",\n    value: function setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n  }, {\n    key: \"addEccAndInterleave\",\n    value: function addEccAndInterleave(data) {\n      var ver = this.version;\n      var ecl = this.errorCorrectionLevel;\n      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');\n\n      // Calculate parameter numbers\n      var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n      var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n\n      // Split data numbero blocks and append ECC to each block\n      var blocks = [];\n      var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (var i = 0, k = 0; i < numBlocks; i++) {\n        var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n\n      // Interleave (not concatenate) the bytes from every block numbero a single sequence\n      var result = [];\n      var _loop = function _loop(_i9) {\n        blocks.forEach(function (block, j) {\n          // Skip the padding byte in short blocks\n          if (_i9 != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[_i9]);\n        });\n      };\n      for (var _i9 = 0; _i9 < blocks[0].length; _i9++) {\n        _loop(_i9);\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n  }, {\n    key: \"drawCodewords\",\n    value: function drawCodewords(data) {\n      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError('Invalid argument');\n      var i = 0; // Bit index numbero the data\n      // Do the funny zigzag scan\n      for (var right = this.size - 1; right >= 1; right -= 2) {\n        // Index of right column in each column pair\n        if (right == 6) right = 5;\n        for (var vert = 0; vert < this.size; vert++) {\n          // Vertical counter\n          for (var j = 0; j < 2; j++) {\n            var x = right - j; // Actual x coordinate\n            var upward = (right + 1 & 2) == 0;\n            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n            // 0/false/light by the constructor and are left unchanged by this method\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n  }, {\n    key: \"applyMask\",\n    value: function applyMask(mask) {\n      if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');\n      for (var y = 0; y < this.size; y++) {\n        for (var x = 0; x < this.size; x++) {\n          var invert = void 0;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error('Unreachable');\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n  }, {\n    key: \"getPenaltyScore\",\n    value: function getPenaltyScore() {\n      var result = 0;\n\n      // Adjacent modules in row having same color, and finder-like patterns\n      for (var y = 0; y < this.size; y++) {\n        var runColor = false;\n        var runX = 0;\n        var runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += QrCode.PENALTY_N1;else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n      }\n      // Adjacent modules in column having same color, and finder-like patterns\n      for (var _x = 0; _x < this.size; _x++) {\n        var _runColor = false;\n        var runY = 0;\n        var _runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var _y = 0; _y < this.size; _y++) {\n          if (this.modules[_y][_x] == _runColor) {\n            runY++;\n            if (runY == 5) result += QrCode.PENALTY_N1;else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, _runHistory);\n            if (!_runColor) result += this.finderPenaltyCountPatterns(_runHistory) * QrCode.PENALTY_N3;\n            _runColor = this.modules[_y][_x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(_runColor, runY, _runHistory) * QrCode.PENALTY_N3;\n      }\n\n      // 2*2 blocks of modules having same color\n      for (var _y2 = 0; _y2 < this.size - 1; _y2++) {\n        for (var _x2 = 0; _x2 < this.size - 1; _x2++) {\n          var color = this.modules[_y2][_x2];\n          if (color == this.modules[_y2][_x2 + 1] && color == this.modules[_y2 + 1][_x2] && color == this.modules[_y2 + 1][_x2 + 1]) result += QrCode.PENALTY_N2;\n        }\n      }\n\n      // Balance of dark and light modules\n      var dark = 0;\n      var _iterator3 = _createForOfIteratorHelper(this.modules),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var row = _step3.value;\n          dark = row.reduce(function (sum, color) {\n            return sum + (color ? 1 : 0);\n          }, dark);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n      // Compute the smallest numbereger k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n      var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n      return result;\n    }\n\n    /*-- Private helper functions --*/\n\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of numberegers.\n  }, {\n    key: \"getAlignmentPatternPositions\",\n    value: function getAlignmentPatternPositions() {\n      if (this.version == 1) return [];else {\n        var numAlign = Math.floor(this.version / 7) + 2;\n        var step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        var result = [6];\n        for (var pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    }\n\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n  }, {\n    key: \"finderPenaltyCountPatterns\",\n    value:\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    function finderPenaltyCountPatterns(runHistory) {\n      var n = runHistory[1];\n      assert(n <= this.size * 3);\n      var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n  }, {\n    key: \"finderPenaltyTerminateAndCount\",\n    value: function finderPenaltyTerminateAndCount(currentRunColor, oriCurrentRunLength, runHistory) {\n      var currentRunLength = oriCurrentRunLength;\n      if (currentRunColor) {\n        // Terminate dark run\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size; // Add light border to final run\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n  }, {\n    key: \"finderPenaltyAddHistory\",\n    value: function finderPenaltyAddHistory(oriCurrentRunLength, runHistory) {\n      var currentRunLength = oriCurrentRunLength;\n      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n\n    /*-- Constants and tables --*/\n\n    // The minimum version number supported in the QR Code Model 2 standard.\n  }], [{\n    key: \"encodeText\",\n    value: /*-- Static factory functions (high level) --*/\n\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code ponumbers (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    function encodeText(text, ecl) {\n      var segs = QrSegment.makeSegments(text);\n      return QrCode.encodeSegments(segs, ecl);\n    }\n\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n  }, {\n    key: \"encodeBinary\",\n    value: function encodeBinary(data, ecl) {\n      var seg = QrSegment.makeBytes(data);\n      return QrCode.encodeSegments([seg], ecl);\n    }\n\n    /*-- Static factory functions (mid level) --*/\n\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n  }, {\n    key: \"encodeSegments\",\n    value: function encodeSegments(segs, oriEcl) {\n      var minVersion = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n      var maxVersion = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 40;\n      var mask = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n      var boostEcl = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;\n      if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError('Invalid value');\n\n      // Find the minimal version number to use\n      var version;\n      var dataUsedBits;\n      for (version = minVersion;; version++) {\n        var _dataCapacityBits = QrCode.getNumDataCodewords(version, oriEcl) * 8; // Number of data bits available\n        var usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= _dataCapacityBits) {\n          dataUsedBits = usedBits;\n          break; // This version number is found to be suitable\n        }\n        if (version >= maxVersion)\n          // All versions in the range could not fit the given data\n          throw new RangeError('Data too long');\n      }\n      var ecl = oriEcl;\n      // Increase the error correction level while the data still fits in the current version number\n      for (var _i10 = 0, _arr = [Ecc.MEDIUM, Ecc.QUARTILE, Ecc.HIGH]; _i10 < _arr.length; _i10++) {\n        var newEcl = _arr[_i10];\n        // From low to high\n        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n\n      // Concatenate all segments to create the data bit string\n      var bb = [];\n      var _iterator4 = _createForOfIteratorHelper(segs),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var seg = _step4.value;\n          appendBits(seg.mode.modeBits, 4, bb);\n          appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n          var _iterator5 = _createForOfIteratorHelper(seg.getData()),\n            _step5;\n          try {\n            for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n              var b = _step5.value;\n              bb.push(b);\n            }\n          } catch (err) {\n            _iterator5.e(err);\n          } finally {\n            _iterator5.f();\n          }\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n      assert(bb.length == dataUsedBits);\n\n      // Add terminator and pad up to a byte if applicable\n      var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n\n      // Pad with alternating bytes until data capacity is reached\n      for (var padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11) appendBits(padByte, 8, bb);\n\n      // Pack bits numbero bytes in big endian\n      var dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach(function (b, i) {\n        return dataCodewords[i >>> 3] |= b << 7 - (i & 7);\n      });\n\n      // Create the QR Code object\n      return new QrCode(version, ecl, dataCodewords, mask);\n    }\n  }, {\n    key: \"getNumRawDataModules\",\n    value: function getNumRawDataModules(ver) {\n      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');\n      var result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        var numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n  }, {\n    key: \"getNumDataCodewords\",\n    value: function getNumDataCodewords(ver, ecl) {\n      return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n  }, {\n    key: \"reedSolomonComputeDivisor\",\n    value: function reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');\n      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the unumber8 array [255, 8, 93].\n      var result = [];\n      for (var i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1); // Start off with the monomial x^0\n\n      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n      // and drop the highest monomial term which is always 1x^degree.\n      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n      var root = 1;\n      for (var _i11 = 0; _i11 < degree; _i11++) {\n        // Multiply the current product by (x - r^i)\n        for (var j = 0; j < result.length; j++) {\n          result[j] = QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = QrCode.reedSolomonMultiply(root, 0x02);\n      }\n      return result;\n    }\n\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n  }, {\n    key: \"reedSolomonComputeRemainder\",\n    value: function reedSolomonComputeRemainder(data, divisor) {\n      var result = divisor.map(function () {\n        return 0;\n      });\n      var _iterator6 = _createForOfIteratorHelper(data),\n        _step6;\n      try {\n        var _loop2 = function _loop2() {\n          var b = _step6.value;\n          // Polynomial division\n          var factor = b ^ result.shift();\n          result.push(0);\n          divisor.forEach(function (coef, i) {\n            return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n          });\n        };\n        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n          _loop2();\n        }\n      } catch (err) {\n        _iterator6.e(err);\n      } finally {\n        _iterator6.f();\n      }\n      return result;\n    }\n\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit numberegers. This could be implemented as a lookup table of 256*256 entries of unumber8.\n  }, {\n    key: \"reedSolomonMultiply\",\n    value: function reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');\n      // Russian peasant multiplication\n      var z = 0;\n      for (var i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 0x11d;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n  }]);\n  return QrCode;\n}();\n_defineProperty(QrCode, \"MIN_VERSION\", 1);\n// The maximum version number supported in the QR Code Model 2 standard.\n_defineProperty(QrCode, \"MAX_VERSION\", 40);\n// For use in getPenaltyScore(), when evaluating which mask is best.\n_defineProperty(QrCode, \"PENALTY_N1\", 3);\n_defineProperty(QrCode, \"PENALTY_N2\", 3);\n_defineProperty(QrCode, \"PENALTY_N3\", 40);\n_defineProperty(QrCode, \"PENALTY_N4\", 10);\n_defineProperty(QrCode, \"ECC_CODEWORDS_PER_BLOCK\", [\n// Version: (note that index 0 is for padding, and is set to an illegal value)\n//0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n[-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n// Low\n[-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n// Medium\n[-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n// Quartile\n[-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30] // High\n]);\n_defineProperty(QrCode, \"NUM_ERROR_CORRECTION_BLOCKS\", [\n// Version: (note that index 0 is for padding, and is set to an illegal value)\n//0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n[-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n// Low\n[-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n// Medium\n[-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n// Quartile\n[-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81] // High\n]);"], "mappings": "AAAA,OAAOA,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,EAAEC,OAAO;AACnB;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;EAChC,IAAID,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,EAAE,IAAID,GAAG,KAAKC,GAAG,IAAI,CAAC,EAAE,MAAM,IAAIE,UAAU,CAAC,oBAAoB,CAAC;EACvF,KAAK,IAAIC,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAC;EAAA,EAChCF,EAAE,CAACG,IAAI,CAACL,GAAG,KAAKI,CAAC,GAAG,CAAC,CAAC;AAC1B;;AAEA;AACA,SAASE,MAAMA,CAACC,CAAC,EAAEH,CAAC,EAAE;EACpB,OAAO,CAACG,CAAC,KAAKH,CAAC,GAAG,CAAC,KAAK,CAAC;AAC3B;;AAEA;AACA,SAASI,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI,CAACA,IAAI,EAAE,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA,OAAO,IAAIC,IAAI,GAAG,aAAa,YAAY;EACzC,SAASA,IAAIA,CAACC,QAAQ,EAAEC,gBAAgB,EAAE;IACxCnB,eAAe,CAAC,IAAI,EAAEiB,IAAI,CAAC;IAC3B;IACA;IACAf,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACzC;IACAA,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACjD,IAAI,CAACgB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;;EAEA;;EAEA;EACA;EACAlB,YAAY,CAACgB,IAAI,EAAE,CAAC;IAClBG,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAASC,gBAAgBA,CAACC,GAAG,EAAE;MACpC,OAAO,IAAI,CAACJ,gBAAgB,CAACK,IAAI,CAACC,KAAK,CAAC,CAACF,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC,CAAC;EACH,OAAON,IAAI;AACb,CAAC,CAAC,CAAC;;AAEH;;AAEA;AACA;AACA;AACAd,MAAM,GAAGc,IAAI;AACb;AACAf,eAAe,CAACe,IAAI,EAAE,SAAS,EAAE,IAAId,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC/DD,eAAe,CAACe,IAAI,EAAE,cAAc,EAAE,IAAId,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACnED,eAAe,CAACe,IAAI,EAAE,MAAM,EAAE,IAAId,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3DD,eAAe,CAACe,IAAI,EAAE,OAAO,EAAE,IAAId,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5DD,eAAe,CAACe,IAAI,EAAE,KAAK,EAAE,IAAId,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxD,OAAO,IAAIuB,GAAG,GAAG,aAAazB,YAAY,CAAC,SAASyB,GAAGA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAC3E5B,eAAe,CAAC,IAAI,EAAE0B,GAAG,CAAC;EAC1B;EACA;EACA;EACAxB,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EACxC;EACAA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;EAC3C,IAAI,CAACyB,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;AAC9B,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxB,OAAO,GAAGsB,GAAG;AACb;AACAxB,eAAe,CAACwB,GAAG,EAAE,KAAK,EAAE,IAAItB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C;AACAF,eAAe,CAACwB,GAAG,EAAE,QAAQ,EAAE,IAAItB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD;AACAF,eAAe,CAACwB,GAAG,EAAE,UAAU,EAAE,IAAItB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD;AACAF,eAAe,CAACwB,GAAG,EAAE,MAAM,EAAE,IAAItB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,OAAO,IAAIyB,SAAS,GAAG,aAAa,YAAY;EAC9C;EACA;EACA;EACA,SAASA,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC1ChC,eAAe,CAAC,IAAI,EAAE6B,SAAS,CAAC;IAChC;IACA;IACA3B,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC;IACA;IACA;IACAA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACzC;IACAA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC4B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAID,QAAQ,GAAG,CAAC,EAAE,MAAM,IAAItB,UAAU,CAAC,kBAAkB,CAAC;IAC1D,IAAI,CAACuB,OAAO,GAAGA,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEA;;EAEA;EACAhC,YAAY,CAAC4B,SAAS,EAAE,CAAC;IACvBT,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASa,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACF,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA;IACA;EACF,CAAC,CAAC,EAAE,CAAC;IACHb,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;;IAEP;IACA;IACA;IACA,SAASc,SAASA,CAACC,IAAI,EAAE;MACvB,IAAI5B,EAAE,GAAG,EAAE;MACX,IAAI6B,SAAS,GAAGtC,0BAA0B,CAACqC,IAAI,CAAC;QAC9CE,KAAK;MACP,IAAI;QACF,KAAKD,SAAS,CAACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAACD,KAAK,GAAGD,SAAS,CAACG,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;UAClD,IAAIC,CAAC,GAAGJ,KAAK,CAACjB,KAAK;UACnBhB,UAAU,CAACqC,CAAC,EAAE,CAAC,EAAElC,EAAE,CAAC;QACtB;MACF,CAAC,CAAC,OAAOmC,GAAG,EAAE;QACZN,SAAS,CAACO,CAAC,CAACD,GAAG,CAAC;MAClB,CAAC,SAAS;QACRN,SAAS,CAACQ,CAAC,CAAC,CAAC;MACf;MACA,OAAO,IAAIhB,SAAS,CAACZ,IAAI,CAAC6B,IAAI,EAAEV,IAAI,CAACW,MAAM,EAAEvC,EAAE,CAAC;IAClD;;IAEA;EACF,CAAC,EAAE;IACDY,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAAS2B,WAAWA,CAACC,MAAM,EAAE;MAClC,IAAI,CAACpB,SAAS,CAACqB,SAAS,CAACD,MAAM,CAAC,EAAE,MAAM,IAAIxC,UAAU,CAAC,wCAAwC,CAAC;MAChG,IAAID,EAAE,GAAG,EAAE;MACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,MAAM,CAACF,MAAM,GAAG;QAClC;QACA,IAAIP,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACF,MAAM,CAACF,MAAM,GAAGrC,CAAC,EAAE,CAAC,CAAC;QACtCL,UAAU,CAAC+C,QAAQ,CAACH,MAAM,CAACI,SAAS,CAAC3C,CAAC,EAAEA,CAAC,GAAG8B,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEhC,EAAE,CAAC;QACnEE,CAAC,IAAI8B,CAAC;MACR;MACA,OAAO,IAAIX,SAAS,CAACZ,IAAI,CAACqC,OAAO,EAAEL,MAAM,CAACF,MAAM,EAAEvC,EAAE,CAAC;IACvD;;IAEA;IACA;IACA;EACF,CAAC,EAAE;IACDY,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAASkC,gBAAgBA,CAACC,IAAI,EAAE;MACrC,IAAI,CAAC3B,SAAS,CAAC4B,cAAc,CAACD,IAAI,CAAC,EAAE,MAAM,IAAI/C,UAAU,CAAC,6DAA6D,CAAC;MACxH,IAAID,EAAE,GAAG,EAAE;MACX,IAAIE,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI8C,IAAI,CAACT,MAAM,EAAErC,CAAC,IAAI,CAAC,EAAE;QACxC;QACA,IAAIgD,IAAI,GAAG7B,SAAS,CAAC8B,oBAAoB,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAACnD,CAAC,CAAC,CAAC,GAAG,EAAE;QACtEgD,IAAI,IAAI7B,SAAS,CAAC8B,oBAAoB,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAACnD,CAAC,GAAG,CAAC,CAAC,CAAC;QAClEL,UAAU,CAACqD,IAAI,EAAE,EAAE,EAAElD,EAAE,CAAC;MAC1B;MACA,IAAIE,CAAC,GAAG8C,IAAI,CAACT,MAAM;QACjB;QACA1C,UAAU,CAACwB,SAAS,CAAC8B,oBAAoB,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAACnD,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEF,EAAE,CAAC;MAC3E,OAAO,IAAIqB,SAAS,CAACZ,IAAI,CAAC6C,YAAY,EAAEN,IAAI,CAACT,MAAM,EAAEvC,EAAE,CAAC;IAC1D;;IAEA;IACA;EACF,CAAC,EAAE;IACDY,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAAS0C,YAAYA,CAACP,IAAI,EAAE;MACjC;MACA,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,KAAK,IAAI3B,SAAS,CAACqB,SAAS,CAACM,IAAI,CAAC,EAAE,OAAO,CAAC3B,SAAS,CAACmB,WAAW,CAACQ,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI3B,SAAS,CAAC4B,cAAc,CAACD,IAAI,CAAC,EAAE,OAAO,CAAC3B,SAAS,CAAC0B,gBAAgB,CAACC,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC3B,SAAS,CAACM,SAAS,CAACN,SAAS,CAACmC,eAAe,CAACR,IAAI,CAAC,CAAC,CAAC;IAC1P;;IAEA;IACA;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS4C,OAAOA,CAACC,SAAS,EAAE;MACjC,IAAI1D,EAAE,GAAG,EAAE;MACX,IAAI0D,SAAS,GAAG,CAAC,EAAE,MAAM,IAAIzD,UAAU,CAAC,mCAAmC,CAAC,CAAC,KAAK,IAAIyD,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE7D,UAAU,CAAC6D,SAAS,EAAE,CAAC,EAAE1D,EAAE,CAAC,CAAC,KAAK,IAAI0D,SAAS,GAAG,CAAC,IAAI,EAAE,EAAE;QACnK7D,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEG,EAAE,CAAC;QACpBH,UAAU,CAAC6D,SAAS,EAAE,EAAE,EAAE1D,EAAE,CAAC;MAC/B,CAAC,MAAM,IAAI0D,SAAS,GAAG,OAAO,EAAE;QAC9B7D,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEG,EAAE,CAAC;QACpBH,UAAU,CAAC6D,SAAS,EAAE,EAAE,EAAE1D,EAAE,CAAC;MAC/B,CAAC,MAAM,MAAM,IAAIC,UAAU,CAAC,mCAAmC,CAAC;MAChE,OAAO,IAAIoB,SAAS,CAACZ,IAAI,CAACkD,GAAG,EAAE,CAAC,EAAE3D,EAAE,CAAC;IACvC;;IAEA;IACA;EACF,CAAC,EAAE;IACDY,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAAS6B,SAASA,CAACM,IAAI,EAAE;MAC9B,OAAO3B,SAAS,CAACuC,aAAa,CAACC,IAAI,CAACb,IAAI,CAAC;IAC3C;;IAEA;IACA;IACA;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAASoC,cAAcA,CAACD,IAAI,EAAE;MACnC,OAAO3B,SAAS,CAACyC,kBAAkB,CAACD,IAAI,CAACb,IAAI,CAAC;IAChD;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASkD,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC1C,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,UAAU,GAAG5E,0BAA0B,CAACyE,IAAI,CAAC;QAC/CI,MAAM;MACR,IAAI;QACF,KAAKD,UAAU,CAACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACqC,MAAM,GAAGD,UAAU,CAACnC,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;UACrD,IAAIoC,GAAG,GAAGD,MAAM,CAACvD,KAAK;UACtB,IAAIyD,MAAM,GAAGD,GAAG,CAAC/C,IAAI,CAACR,gBAAgB,CAACmD,OAAO,CAAC;UAC/C,IAAII,GAAG,CAAC9C,QAAQ,IAAI,CAAC,IAAI+C,MAAM,EAAE,OAAOC,QAAQ,CAAC,CAAC;UAClDL,MAAM,IAAI,CAAC,GAAGI,MAAM,GAAGD,GAAG,CAAC7C,OAAO,CAACe,MAAM;QAC3C;MACF,CAAC,CAAC,OAAOJ,GAAG,EAAE;QACZgC,UAAU,CAAC/B,CAAC,CAACD,GAAG,CAAC;MACnB,CAAC,SAAS;QACRgC,UAAU,CAAC9B,CAAC,CAAC,CAAC;MAChB;MACA,OAAO6B,MAAM;IACf;;IAEA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAAS2C,eAAeA,CAACgB,KAAK,EAAE;MACrC,IAAIC,GAAG,GAAGC,SAAS,CAACF,KAAK,CAAC;MAC1B,IAAIN,MAAM,GAAG,EAAE;MACf,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,GAAG,CAAClC,MAAM,EAAErC,CAAC,EAAE,EAAE;QACnC,IAAIuE,GAAG,CAACpB,MAAM,CAACnD,CAAC,CAAC,IAAI,GAAG,EAAEgE,MAAM,CAAC/D,IAAI,CAACsE,GAAG,CAACE,UAAU,CAACzE,CAAC,CAAC,CAAC,CAAC,KAAK;UAC5DgE,MAAM,CAAC/D,IAAI,CAACyC,QAAQ,CAAC6B,GAAG,CAAC5B,SAAS,CAAC3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACtDA,CAAC,IAAI,CAAC;QACR;MACF;MACA,OAAOgE,MAAM;IACf;;IAEA;;IAEA;EACF,CAAC,CAAC,CAAC;EACH,OAAO7C,SAAS;AAClB,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3B,eAAe,CAAC2B,SAAS,EAAE,eAAe,EAAE,UAAU,CAAC;AACvD;AACA3B,eAAe,CAAC2B,SAAS,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;AACzE;AACA;AACA3B,eAAe,CAAC2B,SAAS,EAAE,sBAAsB,EAAE,+CAA+C,CAAC;AACnG,OAAO,IAAIuD,MAAM,GAAG,aAAa,YAAY;EAC3C;EACA;EACA;EACA;EACA,SAASA,MAAMA;EACf;EACA;EACAX,OAAO;EACP;EACAY,oBAAoB,EAAEC,aAAa,EAAEC,MAAM,EAAE;IAC3CvF,eAAe,CAAC,IAAI,EAAEoF,MAAM,CAAC;IAC7B;IACA;IACA;IACAlF,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC;IACA;IACA;IACAA,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC;IACA;IACAA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;IACpC;IACAA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,CAAC;IACvC;IACA;IACA;IACAA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACxC;IACAA,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,KAAK,CAAC,CAAC;IACrD,IAAIsF,GAAG,GAAGD,MAAM;IAChB,IAAI,CAACd,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACY,oBAAoB,GAAGA,oBAAoB;IAChD;IACA,IAAIZ,OAAO,GAAGW,MAAM,CAACK,WAAW,IAAIhB,OAAO,GAAGW,MAAM,CAACM,WAAW,EAAE,MAAM,IAAIjF,UAAU,CAAC,4BAA4B,CAAC;IACpH,IAAI+E,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI/E,UAAU,CAAC,yBAAyB,CAAC;IACxE,IAAI,CAACkF,IAAI,GAAGlB,OAAO,GAAG,CAAC,GAAG,EAAE;;IAE5B;IACA,IAAImB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiF,IAAI,EAAEjF,CAAC,EAAE,EAAEkF,GAAG,CAACjF,IAAI,CAAC,KAAK,CAAC;IACnD,KAAK,IAAIkF,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACF,IAAI,EAAEE,EAAE,EAAE,EAAE;MACrC,IAAI,CAACC,OAAO,CAACnF,IAAI,CAACiF,GAAG,CAAC3D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,IAAI,CAAC8D,UAAU,CAACpF,IAAI,CAACiF,GAAG,CAAC3D,KAAK,CAAC,CAAC,CAAC;IACnC;;IAEA;IACA,IAAI,CAAC+D,oBAAoB,CAAC,CAAC;IAC3B,IAAIC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACZ,aAAa,CAAC;IAC1D,IAAI,CAACa,aAAa,CAACF,YAAY,CAAC;;IAEhC;IACA,IAAIT,GAAG,IAAI,CAAC,CAAC,EAAE;MACb;MACA,IAAIY,UAAU,GAAG,UAAU;MAC3B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QAChC,IAAI,CAACC,SAAS,CAACD,GAAG,CAAC;QACnB,IAAI,CAACE,cAAc,CAACF,GAAG,CAAC;QACxB,IAAIG,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;QACpC,IAAID,OAAO,GAAGJ,UAAU,EAAE;UACxBZ,GAAG,GAAGa,GAAG;UACTD,UAAU,GAAGI,OAAO;QACtB;QACA,IAAI,CAACF,SAAS,CAACD,GAAG,CAAC,CAAC,CAAC;MACvB;IACF;IACAvF,MAAM,CAAC,CAAC,IAAI0E,GAAG,IAAIA,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACkB,IAAI,GAAGlB,GAAG;IACf,IAAI,CAACc,SAAS,CAACd,GAAG,CAAC,CAAC,CAAC;IACrB,IAAI,CAACe,cAAc,CAACf,GAAG,CAAC,CAAC,CAAC;;IAE1B,IAAI,CAACO,UAAU,GAAG,EAAE;EACtB;;EAEA;;EAEA;EACA;EACA;EACA9F,YAAY,CAACmF,MAAM,EAAE,CAAC;IACpBhE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASsF,SAASA,CAAC9F,CAAC,EAAE+F,CAAC,EAAE;MAC9B,OAAO,CAAC,IAAI/F,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC8E,IAAI,IAAI,CAAC,IAAIiB,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACjB,IAAI,IAAI,IAAI,CAACG,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC;IACjF;;IAEA;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,SAASwF,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAACf,OAAO;IACrB;;IAEA;;IAEA;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAAS2E,oBAAoBA,CAAA,EAAG;MACrC;MACA,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiF,IAAI,EAAEjF,CAAC,EAAE,EAAE;QAClC,IAAI,CAACoG,iBAAiB,CAAC,CAAC,EAAEpG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAACoG,iBAAiB,CAACpG,CAAC,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACqG,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,IAAI,CAACA,iBAAiB,CAAC,IAAI,CAACpB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;MACxC,IAAI,CAACoB,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACpB,IAAI,GAAG,CAAC,CAAC;;MAExC;MACA,IAAIqB,WAAW,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;MACrD,IAAIC,QAAQ,GAAGF,WAAW,CAACjE,MAAM;MACjC,KAAK,IAAIoE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,QAAQ,EAAEC,GAAG,EAAE,EAAE;QACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;UACjC;UACA,IAAI,EAAED,GAAG,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,IAAID,GAAG,IAAI,CAAC,IAAIC,CAAC,IAAIF,QAAQ,GAAG,CAAC,IAAIC,GAAG,IAAID,QAAQ,GAAG,CAAC,IAAIE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,oBAAoB,CAACL,WAAW,CAACG,GAAG,CAAC,EAAEH,WAAW,CAACI,CAAC,CAAC,CAAC;QAC1J;MACF;;MAEA;MACA,IAAI,CAACb,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAACe,WAAW,CAAC,CAAC;IACpB;;IAEA;IACA;EACF,CAAC,EAAE;IACDlG,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAASkF,cAAcA,CAACG,IAAI,EAAE;MACnC;MACA,IAAItE,IAAI,GAAG,IAAI,CAACiD,oBAAoB,CAACzD,UAAU,IAAI,CAAC,GAAG8E,IAAI,CAAC,CAAC;MAC7D,IAAIa,GAAG,GAAGnF,IAAI;MACd,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE6G,GAAG,GAAGA,GAAG,IAAI,CAAC,GAAG,CAACA,GAAG,KAAK,CAAC,IAAI,KAAK;MACjE,IAAIC,IAAI,GAAG,CAACpF,IAAI,IAAI,EAAE,GAAGmF,GAAG,IAAI,MAAM,CAAC,CAAC;MACxCzG,MAAM,CAAC0G,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;;MAExB;MACA,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE,IAAI,CAACX,iBAAiB,CAAC,CAAC,EAAEW,GAAG,EAAE7G,MAAM,CAAC4G,IAAI,EAAEC,GAAG,CAAC,CAAC;MACpF,IAAI,CAACX,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAElG,MAAM,CAAC4G,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAElG,MAAM,CAAC4G,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAElG,MAAM,CAAC4G,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE,IAAI,CAACZ,iBAAiB,CAAC,EAAE,GAAGY,GAAG,EAAE,CAAC,EAAE9G,MAAM,CAAC4G,IAAI,EAAEE,GAAG,CAAC,CAAC;;MAEzF;MACA,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE,IAAI,CAACb,iBAAiB,CAAC,IAAI,CAACnB,IAAI,GAAG,CAAC,GAAGgC,GAAG,EAAE,CAAC,EAAE/G,MAAM,CAAC4G,IAAI,EAAEG,GAAG,CAAC,CAAC;MACnG,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE,IAAI,CAACd,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACnB,IAAI,GAAG,EAAE,GAAGiC,GAAG,EAAEhH,MAAM,CAAC4G,IAAI,EAAEI,GAAG,CAAC,CAAC;MACrG,IAAI,CAACd,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACnB,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;;IAEA;IACA;EACF,CAAC,EAAE;IACDvE,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASiG,WAAWA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAAC7C,OAAO,GAAG,CAAC,EAAE;;MAEtB;MACA,IAAI8C,GAAG,GAAG,IAAI,CAAC9C,OAAO,CAAC,CAAC;MACxB,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE6G,GAAG,GAAGA,GAAG,IAAI,CAAC,GAAG,CAACA,GAAG,KAAK,EAAE,IAAI,MAAM;MACnE,IAAIC,IAAI,GAAG,IAAI,CAAC/C,OAAO,IAAI,EAAE,GAAG8C,GAAG,CAAC,CAAC;MACrCzG,MAAM,CAAC0G,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;;MAExB;MACA,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE;QACjC,IAAIC,KAAK,GAAGlH,MAAM,CAAC4G,IAAI,EAAEK,GAAG,CAAC;QAC7B,IAAIE,CAAC,GAAG,IAAI,CAACpC,IAAI,GAAG,EAAE,GAAGkC,GAAG,GAAG,CAAC;QAChC,IAAInF,CAAC,GAAGlB,IAAI,CAACC,KAAK,CAACoG,GAAG,GAAG,CAAC,CAAC;QAC3B,IAAI,CAACf,iBAAiB,CAACiB,CAAC,EAAErF,CAAC,EAAEoF,KAAK,CAAC;QACnC,IAAI,CAAChB,iBAAiB,CAACpE,CAAC,EAAEqF,CAAC,EAAED,KAAK,CAAC;MACrC;IACF;;IAEA;IACA;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAAS0F,iBAAiBA,CAAClG,CAAC,EAAE+F,CAAC,EAAE;MACtC,KAAK,IAAIoB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;UAC/B,IAAIC,IAAI,GAAG1G,IAAI,CAAC2G,GAAG,CAAC3G,IAAI,CAAC4G,GAAG,CAACH,EAAE,CAAC,EAAEzG,IAAI,CAAC4G,GAAG,CAACJ,EAAE,CAAC,CAAC,CAAC,CAAC;UACjD,IAAIK,EAAE,GAAGxH,CAAC,GAAGoH,EAAE;UACf,IAAIK,EAAE,GAAG1B,CAAC,GAAGoB,EAAE;UACf,IAAI,CAAC,IAAIK,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC1C,IAAI,IAAI,CAAC,IAAI2C,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC3C,IAAI,EAAE,IAAI,CAACmB,iBAAiB,CAACuB,EAAE,EAAEC,EAAE,EAAEJ,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC;QACpH;MACF;IACF;;IAEA;IACA;EACF,CAAC,EAAE;IACD9G,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASgG,oBAAoBA,CAACxG,CAAC,EAAE+F,CAAC,EAAE;MACzC,KAAK,IAAIoB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE,IAAI,CAACnB,iBAAiB,CAACjG,CAAC,GAAGoH,EAAE,EAAErB,CAAC,GAAGoB,EAAE,EAAExG,IAAI,CAAC2G,GAAG,CAAC3G,IAAI,CAAC4G,GAAG,CAACH,EAAE,CAAC,EAAEzG,IAAI,CAAC4G,GAAG,CAACJ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;MACpH;IACF;;IAEA;IACA;EACF,CAAC,EAAE;IACD5G,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASyF,iBAAiBA,CAACjG,CAAC,EAAE+F,CAAC,EAAE2B,MAAM,EAAE;MAC9C,IAAI,CAACzC,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC,GAAG0H,MAAM;MAC3B,IAAI,CAACxC,UAAU,CAACa,CAAC,CAAC,CAAC/F,CAAC,CAAC,GAAG,IAAI;IAC9B;;IAEA;;IAEA;IACA;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAAS6E,mBAAmBA,CAAC9D,IAAI,EAAE;MACxC,IAAIb,GAAG,GAAG,IAAI,CAACkD,OAAO;MACtB,IAAI+D,GAAG,GAAG,IAAI,CAACnD,oBAAoB;MACnC,IAAIjD,IAAI,CAACW,MAAM,IAAIqC,MAAM,CAACqD,mBAAmB,CAAClH,GAAG,EAAEiH,GAAG,CAAC,EAAE,MAAM,IAAI/H,UAAU,CAAC,kBAAkB,CAAC;;MAEjG;MACA,IAAIiI,SAAS,GAAGtD,MAAM,CAACuD,2BAA2B,CAACH,GAAG,CAAC7G,OAAO,CAAC,CAACJ,GAAG,CAAC;MACpE,IAAIqH,WAAW,GAAGxD,MAAM,CAACyD,uBAAuB,CAACL,GAAG,CAAC7G,OAAO,CAAC,CAACJ,GAAG,CAAC;MAClE,IAAIuH,YAAY,GAAGtH,IAAI,CAACC,KAAK,CAAC2D,MAAM,CAAC2D,oBAAoB,CAACxH,GAAG,CAAC,GAAG,CAAC,CAAC;MACnE,IAAIyH,cAAc,GAAGN,SAAS,GAAGI,YAAY,GAAGJ,SAAS;MACzD,IAAIO,aAAa,GAAGzH,IAAI,CAACC,KAAK,CAACqH,YAAY,GAAGJ,SAAS,CAAC;;MAExD;MACA,IAAIQ,MAAM,GAAG,EAAE;MACf,IAAIC,KAAK,GAAG/D,MAAM,CAACgE,yBAAyB,CAACR,WAAW,CAAC;MACzD,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAE2I,CAAC,GAAG,CAAC,EAAE3I,CAAC,GAAGgI,SAAS,EAAEhI,CAAC,EAAE,EAAE;QACzC,IAAI4I,GAAG,GAAGlH,IAAI,CAACH,KAAK,CAACoH,CAAC,EAAEA,CAAC,GAAGJ,aAAa,GAAGL,WAAW,IAAIlI,CAAC,GAAGsI,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACvFK,CAAC,IAAIC,GAAG,CAACvG,MAAM;QACf,IAAIwG,GAAG,GAAGnE,MAAM,CAACoE,2BAA2B,CAACF,GAAG,EAAEH,KAAK,CAAC;QACxD,IAAIzI,CAAC,GAAGsI,cAAc,EAAEM,GAAG,CAAC3I,IAAI,CAAC,CAAC,CAAC;QACnCuI,MAAM,CAACvI,IAAI,CAAC2I,GAAG,CAACG,MAAM,CAACF,GAAG,CAAC,CAAC;MAC9B;;MAEA;MACA,IAAI7E,MAAM,GAAG,EAAE;MACf,IAAIgF,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;QAC9BT,MAAM,CAACU,OAAO,CAAC,UAAUC,KAAK,EAAEzC,CAAC,EAAE;UACjC;UACA,IAAIuC,GAAG,IAAIV,aAAa,GAAGL,WAAW,IAAIxB,CAAC,IAAI4B,cAAc,EAAEtE,MAAM,CAAC/D,IAAI,CAACkJ,KAAK,CAACF,GAAG,CAAC,CAAC;QACxF,CAAC,CAAC;MACJ,CAAC;MACD,KAAK,IAAIA,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,MAAM,CAAC,CAAC,CAAC,CAACnG,MAAM,EAAE4G,GAAG,EAAE,EAAE;QAC/CD,KAAK,CAACC,GAAG,CAAC;MACZ;MACA7I,MAAM,CAAC4D,MAAM,CAAC3B,MAAM,IAAI+F,YAAY,CAAC;MACrC,OAAOpE,MAAM;IACf;;IAEA;IACA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAAS8E,aAAaA,CAAC/D,IAAI,EAAE;MAClC,IAAIA,IAAI,CAACW,MAAM,IAAIvB,IAAI,CAACC,KAAK,CAAC2D,MAAM,CAAC2D,oBAAoB,CAAC,IAAI,CAACtE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAIhE,UAAU,CAAC,kBAAkB,CAAC;MACtH,IAAIC,CAAC,GAAG,CAAC,CAAC,CAAC;MACX;MACA,KAAK,IAAIoJ,KAAK,GAAG,IAAI,CAACnE,IAAI,GAAG,CAAC,EAAEmE,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,EAAE;QACtD;QACA,IAAIA,KAAK,IAAI,CAAC,EAAEA,KAAK,GAAG,CAAC;QACzB,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACpE,IAAI,EAAEoE,IAAI,EAAE,EAAE;UAC3C;UACA,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC1B,IAAIvG,CAAC,GAAGiJ,KAAK,GAAG1C,CAAC,CAAC,CAAC;YACnB,IAAI4C,MAAM,GAAG,CAACF,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;YACjC,IAAIlD,CAAC,GAAGoD,MAAM,GAAG,IAAI,CAACrE,IAAI,GAAG,CAAC,GAAGoE,IAAI,GAAGA,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAChE,UAAU,CAACa,CAAC,CAAC,CAAC/F,CAAC,CAAC,IAAIH,CAAC,GAAG0B,IAAI,CAACW,MAAM,GAAG,CAAC,EAAE;cACjD,IAAI,CAAC+C,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC,GAAGD,MAAM,CAACwB,IAAI,CAAC1B,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;cACvDA,CAAC,EAAE;YACL;YACA;YACA;UACF;QACF;MACF;MACAI,MAAM,CAACJ,CAAC,IAAI0B,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC;IAC9B;;IAEA;IACA;IACA;IACA;IACA;EACF,CAAC,EAAE;IACD3B,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASiF,SAASA,CAACI,IAAI,EAAE;MAC9B,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE,MAAM,IAAIjG,UAAU,CAAC,yBAAyB,CAAC;MACzE,KAAK,IAAImG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,IAAI,EAAEiB,CAAC,EAAE,EAAE;QAClC,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8E,IAAI,EAAE9E,CAAC,EAAE,EAAE;UAClC,IAAIoJ,MAAM,GAAG,KAAK,CAAC;UACnB,QAAQvD,IAAI;YACV,KAAK,CAAC;cACJuD,MAAM,GAAG,CAACpJ,CAAC,GAAG+F,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACF,KAAK,CAAC;cACJqD,MAAM,GAAGrD,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACF,KAAK,CAAC;cACJqD,MAAM,GAAGpJ,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACF,KAAK,CAAC;cACJoJ,MAAM,GAAG,CAACpJ,CAAC,GAAG+F,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACF,KAAK,CAAC;cACJqD,MAAM,GAAG,CAACzI,IAAI,CAACC,KAAK,CAACZ,CAAC,GAAG,CAAC,CAAC,GAAGW,IAAI,CAACC,KAAK,CAACmF,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzD;YACF,KAAK,CAAC;cACJqD,MAAM,GAAGpJ,CAAC,GAAG+F,CAAC,GAAG,CAAC,GAAG/F,CAAC,GAAG+F,CAAC,GAAG,CAAC,IAAI,CAAC;cACnC;YACF,KAAK,CAAC;cACJqD,MAAM,GAAG,CAACpJ,CAAC,GAAG+F,CAAC,GAAG,CAAC,GAAG/F,CAAC,GAAG+F,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cACzC;YACF,KAAK,CAAC;cACJqD,MAAM,GAAG,CAAC,CAACpJ,CAAC,GAAG+F,CAAC,IAAI,CAAC,GAAG/F,CAAC,GAAG+F,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cAC3C;YACF;cACE,MAAM,IAAI5F,KAAK,CAAC,aAAa,CAAC;UAClC;UACA,IAAI,CAAC,IAAI,CAAC+E,UAAU,CAACa,CAAC,CAAC,CAAC/F,CAAC,CAAC,IAAIoJ,MAAM,EAAE,IAAI,CAACnE,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC,GAAG,CAAC,IAAI,CAACiF,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC;QAChF;MACF;IACF;;IAEA;IACA;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAASoF,eAAeA,CAAA,EAAG;MAChC,IAAI/B,MAAM,GAAG,CAAC;;MAEd;MACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,IAAI,EAAEiB,CAAC,EAAE,EAAE;QAClC,IAAIsD,QAAQ,GAAG,KAAK;QACpB,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC,KAAK,IAAIvJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8E,IAAI,EAAE9E,CAAC,EAAE,EAAE;UAClC,IAAI,IAAI,CAACiF,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC,IAAIqJ,QAAQ,EAAE;YAClCC,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EAAEzF,MAAM,IAAIU,MAAM,CAACiF,UAAU,CAAC,KAAK,IAAIF,IAAI,GAAG,CAAC,EAAEzF,MAAM,EAAE;UACxE,CAAC,MAAM;YACL,IAAI,CAAC4F,uBAAuB,CAACH,IAAI,EAAEC,UAAU,CAAC;YAC9C,IAAI,CAACF,QAAQ,EAAExF,MAAM,IAAI,IAAI,CAAC6F,0BAA0B,CAACH,UAAU,CAAC,GAAGhF,MAAM,CAACoF,UAAU;YACxFN,QAAQ,GAAG,IAAI,CAACpE,OAAO,CAACc,CAAC,CAAC,CAAC/F,CAAC,CAAC;YAC7BsJ,IAAI,GAAG,CAAC;UACV;QACF;QACAzF,MAAM,IAAI,IAAI,CAAC+F,8BAA8B,CAACP,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAAC,GAAGhF,MAAM,CAACoF,UAAU;MAC/F;MACA;MACA,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAAC/E,IAAI,EAAE+E,EAAE,EAAE,EAAE;QACrC,IAAIC,SAAS,GAAG,KAAK;QACrB,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACnF,IAAI,EAAEmF,EAAE,EAAE,EAAE;UACrC,IAAI,IAAI,CAAChF,OAAO,CAACgF,EAAE,CAAC,CAACJ,EAAE,CAAC,IAAIC,SAAS,EAAE;YACrCC,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EAAElG,MAAM,IAAIU,MAAM,CAACiF,UAAU,CAAC,KAAK,IAAIO,IAAI,GAAG,CAAC,EAAElG,MAAM,EAAE;UACxE,CAAC,MAAM;YACL,IAAI,CAAC4F,uBAAuB,CAACM,IAAI,EAAEC,WAAW,CAAC;YAC/C,IAAI,CAACF,SAAS,EAAEjG,MAAM,IAAI,IAAI,CAAC6F,0BAA0B,CAACM,WAAW,CAAC,GAAGzF,MAAM,CAACoF,UAAU;YAC1FG,SAAS,GAAG,IAAI,CAAC7E,OAAO,CAACgF,EAAE,CAAC,CAACJ,EAAE,CAAC;YAChCE,IAAI,GAAG,CAAC;UACV;QACF;QACAlG,MAAM,IAAI,IAAI,CAAC+F,8BAA8B,CAACE,SAAS,EAAEC,IAAI,EAAEC,WAAW,CAAC,GAAGzF,MAAM,CAACoF,UAAU;MACjG;;MAEA;MACA,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACpF,IAAI,GAAG,CAAC,EAAEoF,GAAG,EAAE,EAAE;QAC5C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACrF,IAAI,GAAG,CAAC,EAAEqF,GAAG,EAAE,EAAE;UAC5C,IAAIlD,KAAK,GAAG,IAAI,CAAChC,OAAO,CAACiF,GAAG,CAAC,CAACC,GAAG,CAAC;UAClC,IAAIlD,KAAK,IAAI,IAAI,CAAChC,OAAO,CAACiF,GAAG,CAAC,CAACC,GAAG,GAAG,CAAC,CAAC,IAAIlD,KAAK,IAAI,IAAI,CAAChC,OAAO,CAACiF,GAAG,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,IAAIlD,KAAK,IAAI,IAAI,CAAChC,OAAO,CAACiF,GAAG,GAAG,CAAC,CAAC,CAACC,GAAG,GAAG,CAAC,CAAC,EAAEtG,MAAM,IAAIU,MAAM,CAAC6F,UAAU;QACxJ;MACF;;MAEA;MACA,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,UAAU,GAAGpL,0BAA0B,CAAC,IAAI,CAAC+F,OAAO,CAAC;QACvDsF,MAAM;MACR,IAAI;QACF,KAAKD,UAAU,CAAC5I,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC6I,MAAM,GAAGD,UAAU,CAAC3I,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;UACrD,IAAImD,GAAG,GAAGwF,MAAM,CAAC/J,KAAK;UACtB6J,IAAI,GAAGtF,GAAG,CAACyF,MAAM,CAAC,UAAUC,GAAG,EAAExD,KAAK,EAAE;YACtC,OAAOwD,GAAG,IAAIxD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UAC9B,CAAC,EAAEoD,IAAI,CAAC;QACV;MACF,CAAC,CAAC,OAAOvI,GAAG,EAAE;QACZwI,UAAU,CAACvI,CAAC,CAACD,GAAG,CAAC;MACnB,CAAC,SAAS;QACRwI,UAAU,CAACtI,CAAC,CAAC,CAAC;MAChB;MACA,IAAI0I,KAAK,GAAG,IAAI,CAAC5F,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MACnC;MACA,IAAI0D,CAAC,GAAG7H,IAAI,CAACgK,IAAI,CAAChK,IAAI,CAAC4G,GAAG,CAAC8C,IAAI,GAAG,EAAE,GAAGK,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,CAAC,GAAG,CAAC;MAC/DzK,MAAM,CAAC,CAAC,IAAIuI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC;MACxB3E,MAAM,IAAI2E,CAAC,GAAGjE,MAAM,CAACqG,UAAU;MAC/B3K,MAAM,CAAC,CAAC,IAAI4D,MAAM,IAAIA,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;MAC1C,OAAOA,MAAM;IACf;;IAEA;;IAEA;IACA;IACA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,8BAA8B;IACnCC,KAAK,EAAE,SAAS4F,4BAA4BA,CAAA,EAAG;MAC7C,IAAI,IAAI,CAACxC,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,KAAK;QACpC,IAAIyC,QAAQ,GAAG1F,IAAI,CAACC,KAAK,CAAC,IAAI,CAACgD,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;QAC/C,IAAIiH,IAAI,GAAG,IAAI,CAACjH,OAAO,IAAI,EAAE,GAAG,EAAE,GAAGjD,IAAI,CAACgK,IAAI,CAAC,CAAC,IAAI,CAAC/G,OAAO,GAAG,CAAC,GAAG,CAAC,KAAKyC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/F,IAAIxC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChB,KAAK,IAAIiH,GAAG,GAAG,IAAI,CAAChG,IAAI,GAAG,CAAC,EAAEjB,MAAM,CAAC3B,MAAM,GAAGmE,QAAQ,EAAEyE,GAAG,IAAID,IAAI,EAAEhH,MAAM,CAACkH,MAAM,CAAC,CAAC,EAAE,CAAC,EAAED,GAAG,CAAC;QAC7F,OAAOjH,MAAM;MACf;IACF;;IAEA;IACA;IACA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,4BAA4B;IACjCC,KAAK;IACL;IACA;IACA,SAASkJ,0BAA0BA,CAACH,UAAU,EAAE;MAC9C,IAAI5H,CAAC,GAAG4H,UAAU,CAAC,CAAC,CAAC;MACrBtJ,MAAM,CAAC0B,CAAC,IAAI,IAAI,CAACmD,IAAI,GAAG,CAAC,CAAC;MAC1B,IAAIkG,IAAI,GAAGrJ,CAAC,GAAG,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,GAAG,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC;MAC5G,OAAO,CAACqJ,IAAI,IAAIzB,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,GAAG,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,GAAG,CAAC,GAAG,CAAC,KAAKqJ,IAAI,IAAIzB,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,GAAG,CAAC,IAAI4H,UAAU,CAAC,CAAC,CAAC,IAAI5H,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxI;;IAEA;EACF,CAAC,EAAE;IACDpB,GAAG,EAAE,gCAAgC;IACrCC,KAAK,EAAE,SAASoJ,8BAA8BA,CAACqB,eAAe,EAAEC,mBAAmB,EAAE3B,UAAU,EAAE;MAC/F,IAAI4B,gBAAgB,GAAGD,mBAAmB;MAC1C,IAAID,eAAe,EAAE;QACnB;QACA,IAAI,CAACxB,uBAAuB,CAAC0B,gBAAgB,EAAE5B,UAAU,CAAC;QAC1D4B,gBAAgB,GAAG,CAAC;MACtB;MACAA,gBAAgB,IAAI,IAAI,CAACrG,IAAI,CAAC,CAAC;MAC/B,IAAI,CAAC2E,uBAAuB,CAAC0B,gBAAgB,EAAE5B,UAAU,CAAC;MAC1D,OAAO,IAAI,CAACG,0BAA0B,CAACH,UAAU,CAAC;IACpD;;IAEA;EACF,CAAC,EAAE;IACDhJ,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE,SAASiJ,uBAAuBA,CAACyB,mBAAmB,EAAE3B,UAAU,EAAE;MACvE,IAAI4B,gBAAgB,GAAGD,mBAAmB;MAC1C,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE4B,gBAAgB,IAAI,IAAI,CAACrG,IAAI,CAAC,CAAC;MACvDyE,UAAU,CAAC6B,GAAG,CAAC,CAAC;MAChB7B,UAAU,CAAC8B,OAAO,CAACF,gBAAgB,CAAC;IACtC;;IAEA;;IAEA;EACF,CAAC,CAAC,EAAE,CAAC;IACH5K,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;;IAEP;IACA;IACA;IACA;IACA;IACA,SAAS8K,UAAUA,CAAC3I,IAAI,EAAEgF,GAAG,EAAE;MAC7B,IAAIhE,IAAI,GAAG3C,SAAS,CAACkC,YAAY,CAACP,IAAI,CAAC;MACvC,OAAO4B,MAAM,CAACgH,cAAc,CAAC5H,IAAI,EAAEgE,GAAG,CAAC;IACzC;;IAEA;IACA;IACA;IACA;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASgL,YAAYA,CAACjK,IAAI,EAAEoG,GAAG,EAAE;MACtC,IAAI3D,GAAG,GAAGhD,SAAS,CAACM,SAAS,CAACC,IAAI,CAAC;MACnC,OAAOgD,MAAM,CAACgH,cAAc,CAAC,CAACvH,GAAG,CAAC,EAAE2D,GAAG,CAAC;IAC1C;;IAEA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAAS+K,cAAcA,CAAC5H,IAAI,EAAE8H,MAAM,EAAE;MAC3C,IAAIC,UAAU,GAAGC,SAAS,CAACzJ,MAAM,GAAG,CAAC,IAAIyJ,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MACtF,IAAIE,UAAU,GAAGF,SAAS,CAACzJ,MAAM,GAAG,CAAC,IAAIyJ,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACvF,IAAI9F,IAAI,GAAG8F,SAAS,CAACzJ,MAAM,GAAG,CAAC,IAAIyJ,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACjF,IAAIG,QAAQ,GAAGH,SAAS,CAACzJ,MAAM,GAAG,CAAC,IAAIyJ,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MACvF,IAAI,EAAEpH,MAAM,CAACK,WAAW,IAAI8G,UAAU,IAAIA,UAAU,IAAIG,UAAU,IAAIA,UAAU,IAAItH,MAAM,CAACM,WAAW,CAAC,IAAIgB,IAAI,GAAG,CAAC,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE,MAAM,IAAIjG,UAAU,CAAC,eAAe,CAAC;;MAEvK;MACA,IAAIgE,OAAO;MACX,IAAImI,YAAY;MAChB,KAAKnI,OAAO,GAAG8H,UAAU,GAAG9H,OAAO,EAAE,EAAE;QACrC,IAAIoI,iBAAiB,GAAGzH,MAAM,CAACqD,mBAAmB,CAAChE,OAAO,EAAE6H,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACzE,IAAIQ,QAAQ,GAAGjL,SAAS,CAAC0C,YAAY,CAACC,IAAI,EAAEC,OAAO,CAAC;QACpD,IAAIqI,QAAQ,IAAID,iBAAiB,EAAE;UACjCD,YAAY,GAAGE,QAAQ;UACvB,MAAM,CAAC;QACT;QACA,IAAIrI,OAAO,IAAIiI,UAAU;UACvB;UACA,MAAM,IAAIjM,UAAU,CAAC,eAAe,CAAC;MACzC;MACA,IAAI+H,GAAG,GAAG8D,MAAM;MAChB;MACA,KAAK,IAAIS,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAG,CAACtL,GAAG,CAACuL,MAAM,EAAEvL,GAAG,CAACwL,QAAQ,EAAExL,GAAG,CAACyL,IAAI,CAAC,EAAEJ,IAAI,GAAGC,IAAI,CAACjK,MAAM,EAAEgK,IAAI,EAAE,EAAE;QAC1F,IAAIK,MAAM,GAAGJ,IAAI,CAACD,IAAI,CAAC;QACvB;QACA,IAAIJ,QAAQ,IAAIC,YAAY,IAAIxH,MAAM,CAACqD,mBAAmB,CAAChE,OAAO,EAAE2I,MAAM,CAAC,GAAG,CAAC,EAAE5E,GAAG,GAAG4E,MAAM;MAC/F;;MAEA;MACA,IAAI5M,EAAE,GAAG,EAAE;MACX,IAAI6M,UAAU,GAAGtN,0BAA0B,CAACyE,IAAI,CAAC;QAC/C8I,MAAM;MACR,IAAI;QACF,KAAKD,UAAU,CAAC9K,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC+K,MAAM,GAAGD,UAAU,CAAC7K,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;UACrD,IAAIoC,GAAG,GAAGyI,MAAM,CAACjM,KAAK;UACtBhB,UAAU,CAACwE,GAAG,CAAC/C,IAAI,CAACZ,QAAQ,EAAE,CAAC,EAAEV,EAAE,CAAC;UACpCH,UAAU,CAACwE,GAAG,CAAC9C,QAAQ,EAAE8C,GAAG,CAAC/C,IAAI,CAACR,gBAAgB,CAACmD,OAAO,CAAC,EAAEjE,EAAE,CAAC;UAChE,IAAI+M,UAAU,GAAGxN,0BAA0B,CAAC8E,GAAG,CAAC3C,OAAO,CAAC,CAAC,CAAC;YACxDsL,MAAM;UACR,IAAI;YACF,KAAKD,UAAU,CAAChL,CAAC,CAAC,CAAC,EAAE,CAAC,CAACiL,MAAM,GAAGD,UAAU,CAAC/K,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;cACrD,IAAIC,CAAC,GAAG8K,MAAM,CAACnM,KAAK;cACpBb,EAAE,CAACG,IAAI,CAAC+B,CAAC,CAAC;YACZ;UACF,CAAC,CAAC,OAAOC,GAAG,EAAE;YACZ4K,UAAU,CAAC3K,CAAC,CAACD,GAAG,CAAC;UACnB,CAAC,SAAS;YACR4K,UAAU,CAAC1K,CAAC,CAAC,CAAC;UAChB;QACF;MACF,CAAC,CAAC,OAAOF,GAAG,EAAE;QACZ0K,UAAU,CAACzK,CAAC,CAACD,GAAG,CAAC;MACnB,CAAC,SAAS;QACR0K,UAAU,CAACxK,CAAC,CAAC,CAAC;MAChB;MACA/B,MAAM,CAACN,EAAE,CAACuC,MAAM,IAAI6J,YAAY,CAAC;;MAEjC;MACA,IAAIa,gBAAgB,GAAGrI,MAAM,CAACqD,mBAAmB,CAAChE,OAAO,EAAE+D,GAAG,CAAC,GAAG,CAAC;MACnE1H,MAAM,CAACN,EAAE,CAACuC,MAAM,IAAI0K,gBAAgB,CAAC;MACrCpN,UAAU,CAAC,CAAC,EAAEmB,IAAI,CAAC2B,GAAG,CAAC,CAAC,EAAEsK,gBAAgB,GAAGjN,EAAE,CAACuC,MAAM,CAAC,EAAEvC,EAAE,CAAC;MAC5DH,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGG,EAAE,CAACuC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAEvC,EAAE,CAAC;MAC1CM,MAAM,CAACN,EAAE,CAACuC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;;MAE1B;MACA,KAAK,IAAI2K,OAAO,GAAG,IAAI,EAAElN,EAAE,CAACuC,MAAM,GAAG0K,gBAAgB,EAAEC,OAAO,IAAI,IAAI,GAAG,IAAI,EAAErN,UAAU,CAACqN,OAAO,EAAE,CAAC,EAAElN,EAAE,CAAC;;MAEzG;MACA,IAAI8E,aAAa,GAAG,EAAE;MACtB,OAAOA,aAAa,CAACvC,MAAM,GAAG,CAAC,GAAGvC,EAAE,CAACuC,MAAM,EAAEuC,aAAa,CAAC3E,IAAI,CAAC,CAAC,CAAC;MAClEH,EAAE,CAACoJ,OAAO,CAAC,UAAUlH,CAAC,EAAEhC,CAAC,EAAE;QACzB,OAAO4E,aAAa,CAAC5E,CAAC,KAAK,CAAC,CAAC,IAAIgC,CAAC,IAAI,CAAC,IAAIhC,CAAC,GAAG,CAAC,CAAC;MACnD,CAAC,CAAC;;MAEF;MACA,OAAO,IAAI0E,MAAM,CAACX,OAAO,EAAE+D,GAAG,EAAElD,aAAa,EAAEoB,IAAI,CAAC;IACtD;EACF,CAAC,EAAE;IACDtF,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAAS0H,oBAAoBA,CAACxH,GAAG,EAAE;MACxC,IAAIA,GAAG,GAAG6D,MAAM,CAACK,WAAW,IAAIlE,GAAG,GAAG6D,MAAM,CAACM,WAAW,EAAE,MAAM,IAAIjF,UAAU,CAAC,6BAA6B,CAAC;MAC7G,IAAIiE,MAAM,GAAG,CAAC,EAAE,GAAGnD,GAAG,GAAG,GAAG,IAAIA,GAAG,GAAG,EAAE;MACxC,IAAIA,GAAG,IAAI,CAAC,EAAE;QACZ,IAAI2F,QAAQ,GAAG1F,IAAI,CAACC,KAAK,CAACF,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;QACtCmD,MAAM,IAAI,CAAC,EAAE,GAAGwC,QAAQ,GAAG,EAAE,IAAIA,QAAQ,GAAG,EAAE;QAC9C,IAAI3F,GAAG,IAAI,CAAC,EAAEmD,MAAM,IAAI,EAAE;MAC5B;MACA5D,MAAM,CAAC,GAAG,IAAI4D,MAAM,IAAIA,MAAM,IAAI,KAAK,CAAC;MACxC,OAAOA,MAAM;IACf;;IAEA;IACA;IACA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAASoH,mBAAmBA,CAAClH,GAAG,EAAEiH,GAAG,EAAE;MAC5C,OAAOhH,IAAI,CAACC,KAAK,CAAC2D,MAAM,CAAC2D,oBAAoB,CAACxH,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG6D,MAAM,CAACyD,uBAAuB,CAACL,GAAG,CAAC7G,OAAO,CAAC,CAACJ,GAAG,CAAC,GAAG6D,MAAM,CAACuD,2BAA2B,CAACH,GAAG,CAAC7G,OAAO,CAAC,CAACJ,GAAG,CAAC;IACnK;;IAEA;IACA;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,2BAA2B;IAChCC,KAAK,EAAE,SAAS+H,yBAAyBA,CAACuE,MAAM,EAAE;MAChD,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,GAAG,EAAE,MAAM,IAAIlN,UAAU,CAAC,qBAAqB,CAAC;MAC3E;MACA;MACA,IAAIiE,MAAM,GAAG,EAAE;MACf,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiN,MAAM,GAAG,CAAC,EAAEjN,CAAC,EAAE,EAAEgE,MAAM,CAAC/D,IAAI,CAAC,CAAC,CAAC;MACnD+D,MAAM,CAAC/D,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhB;MACA;MACA;MACA,IAAIiN,IAAI,GAAG,CAAC;MACZ,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,MAAM,EAAEE,IAAI,EAAE,EAAE;QACxC;QACA,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,MAAM,CAAC3B,MAAM,EAAEqE,CAAC,EAAE,EAAE;UACtC1C,MAAM,CAAC0C,CAAC,CAAC,GAAGhC,MAAM,CAAC0I,mBAAmB,CAACpJ,MAAM,CAAC0C,CAAC,CAAC,EAAEwG,IAAI,CAAC;UACvD,IAAIxG,CAAC,GAAG,CAAC,GAAG1C,MAAM,CAAC3B,MAAM,EAAE2B,MAAM,CAAC0C,CAAC,CAAC,IAAI1C,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;QACvD;QACAwG,IAAI,GAAGxI,MAAM,CAAC0I,mBAAmB,CAACF,IAAI,EAAE,IAAI,CAAC;MAC/C;MACA,OAAOlJ,MAAM;IACf;;IAEA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,6BAA6B;IAClCC,KAAK,EAAE,SAASmI,2BAA2BA,CAACpH,IAAI,EAAE2L,OAAO,EAAE;MACzD,IAAIrJ,MAAM,GAAGqJ,OAAO,CAACC,GAAG,CAAC,YAAY;QACnC,OAAO,CAAC;MACV,CAAC,CAAC;MACF,IAAIC,UAAU,GAAGlO,0BAA0B,CAACqC,IAAI,CAAC;QAC/C8L,MAAM;MACR,IAAI;QACF,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;UAC7B,IAAIzL,CAAC,GAAGwL,MAAM,CAAC7M,KAAK;UACpB;UACA,IAAI+M,MAAM,GAAG1L,CAAC,GAAGgC,MAAM,CAAC2J,KAAK,CAAC,CAAC;UAC/B3J,MAAM,CAAC/D,IAAI,CAAC,CAAC,CAAC;UACdoN,OAAO,CAACnE,OAAO,CAAC,UAAU0E,IAAI,EAAE5N,CAAC,EAAE;YACjC,OAAOgE,MAAM,CAAChE,CAAC,CAAC,IAAI0E,MAAM,CAAC0I,mBAAmB,CAACQ,IAAI,EAAEF,MAAM,CAAC;UAC9D,CAAC,CAAC;QACJ,CAAC;QACD,KAAKH,UAAU,CAAC1L,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC2L,MAAM,GAAGD,UAAU,CAACzL,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;UACrD0L,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC,OAAOxL,GAAG,EAAE;QACZsL,UAAU,CAACrL,CAAC,CAACD,GAAG,CAAC;MACnB,CAAC,SAAS;QACRsL,UAAU,CAACpL,CAAC,CAAC,CAAC;MAChB;MACA,OAAO6B,MAAM;IACf;;IAEA;IACA;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAASyM,mBAAmBA,CAACjN,CAAC,EAAE+F,CAAC,EAAE;MACxC,IAAI/F,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI+F,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,IAAInG,UAAU,CAAC,mBAAmB,CAAC;MAC3E;MACA,IAAI8N,CAAC,GAAG,CAAC;MACT,KAAK,IAAI7N,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3B6N,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAI,KAAK;QAC9BA,CAAC,IAAI,CAAC3H,CAAC,KAAKlG,CAAC,GAAG,CAAC,IAAIG,CAAC;MACxB;MACAC,MAAM,CAACyN,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;MACpB,OAAOA,CAAC;IACV;EACF,CAAC,CAAC,CAAC;EACH,OAAOnJ,MAAM;AACf,CAAC,CAAC,CAAC;AACHlF,eAAe,CAACkF,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;AACzC;AACAlF,eAAe,CAACkF,MAAM,EAAE,aAAa,EAAE,EAAE,CAAC;AAC1C;AACAlF,eAAe,CAACkF,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;AACxClF,eAAe,CAACkF,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;AACxClF,eAAe,CAACkF,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;AACzClF,eAAe,CAACkF,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;AACzClF,eAAe,CAACkF,MAAM,EAAE,yBAAyB,EAAE;AACnD;AACA;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnK;AACA,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpK;AACA,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpK;AACA,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAAA,CACpK,CAAC;AACFlF,eAAe,CAACkF,MAAM,EAAE,6BAA6B,EAAE;AACvD;AACA;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC7I;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACtJ;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzJ;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAAA,CAC1J,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
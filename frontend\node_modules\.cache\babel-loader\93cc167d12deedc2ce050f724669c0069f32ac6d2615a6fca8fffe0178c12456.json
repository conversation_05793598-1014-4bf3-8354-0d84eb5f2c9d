{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport { UnstableContext } from 'rc-tree';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport * as React from 'react';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nimport { useEvent } from 'rc-util';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open,\n    notFoundContent = _useBaseProps.notFoundContent;\n  var _React$useContext = React.useContext(TreeSelectContext),\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    listItemScrollOffset = _React$useContext.listItemScrollOffset,\n    treeData = _React$useContext.treeData,\n    fieldNames = _React$useContext.fieldNames,\n    onSelect = _React$useContext.onSelect,\n    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n    treeExpandAction = _React$useContext.treeExpandAction,\n    treeTitleRender = _React$useContext.treeTitleRender,\n    onPopupScroll = _React$useContext.onPopupScroll,\n    leftMaxCount = _React$useContext.leftMaxCount,\n    leafCountOnly = _React$useContext.leafCountOnly,\n    valueEntities = _React$useContext.valueEntities;\n  var _React$useContext2 = React.useContext(LegacyContext),\n    checkable = _React$useContext2.checkable,\n    checkedKeys = _React$useContext2.checkedKeys,\n    halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n    treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n    onTreeExpand = _React$useContext2.onTreeExpand,\n    treeIcon = _React$useContext2.treeIcon,\n    showTreeIcon = _React$useContext2.showTreeIcon,\n    switcherIcon = _React$useContext2.switcherIcon,\n    treeLine = _React$useContext2.treeLine,\n    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n    loadData = _React$useContext2.loadData,\n    treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n    treeMotion = _React$useContext2.treeMotion,\n    onTreeLoad = _React$useContext2.onTreeLoad,\n    keyEntities = _React$useContext2.keyEntities;\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // ========================== Values ==========================\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]);\n\n  // ========================== Scroll ==========================\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 || _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n\n  // ========================== Events ==========================\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // =========================== Keys ===========================\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedKeys = _React$useState2[0],\n    setExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    searchExpandedKeys = _React$useState4[0],\n    setSearchExpandedKeys = _React$useState4[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  };\n\n  // ========================== Search ==========================\n  var lowerSearchValue = String(searchValue).toLowerCase();\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  };\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [searchValue]);\n\n  // ========================= Disabled =========================\n  // Cache disabled states in React state to ensure re-render when cache updates\n  var _React$useState5 = React.useState(function () {\n      return new Map();\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    disabledCache = _React$useState6[0],\n    setDisabledCache = _React$useState6[1];\n  React.useEffect(function () {\n    if (leftMaxCount) {\n      setDisabledCache(new Map());\n    }\n  }, [leftMaxCount]);\n  function getDisabledWithCache(node) {\n    var value = node[fieldNames.value];\n    if (!disabledCache.has(value)) {\n      var entity = valueEntities.get(value);\n      var isLeaf = (entity.children || []).length === 0;\n      if (!isLeaf) {\n        var checkableChildren = entity.children.filter(function (childTreeNode) {\n          return !childTreeNode.node.disabled && !childTreeNode.node.disableCheckbox && !checkedKeys.includes(childTreeNode.node[fieldNames.value]);\n        });\n        var checkableChildrenCount = checkableChildren.length;\n        disabledCache.set(value, checkableChildrenCount > leftMaxCount);\n      } else {\n        disabledCache.set(value, false);\n      }\n    }\n    return disabledCache.get(value);\n  }\n  var nodeDisabled = useEvent(function (node) {\n    var nodeValue = node[fieldNames.value];\n    if (checkedKeys.includes(nodeValue)) {\n      return false;\n    }\n    if (leftMaxCount === null) {\n      return false;\n    }\n    if (leftMaxCount <= 0) {\n      return true;\n    }\n\n    // This is a low performance calculation\n    if (leafCountOnly && leftMaxCount) {\n      return getDisabledWithCache(node);\n    }\n    return false;\n  });\n\n  // ========================== Get First Selectable Node ==========================\n  var getFirstMatchingNode = function getFirstMatchingNode(nodes) {\n    var _iterator = _createForOfIteratorHelper(nodes),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var node = _step.value;\n        if (node.disabled || node.selectable === false) {\n          continue;\n        }\n        if (searchValue) {\n          if (filterTreeNode(node)) {\n            return node;\n          }\n        } else {\n          return node;\n        }\n        if (node[fieldNames.children]) {\n          var matchInChildren = getFirstMatchingNode(node[fieldNames.children]);\n          if (matchInChildren) {\n            return matchInChildren;\n          }\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return null;\n  };\n\n  // ========================== Active ==========================\n  var _React$useState7 = React.useState(null),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    activeKey = _React$useState8[0],\n    setActiveKey = _React$useState8[1];\n  var activeEntity = keyEntities[activeKey];\n  React.useEffect(function () {\n    if (!open) {\n      return;\n    }\n    var nextActiveKey = null;\n    var getFirstNode = function getFirstNode() {\n      var firstNode = getFirstMatchingNode(memoTreeData);\n      return firstNode ? firstNode[fieldNames.value] : null;\n    };\n\n    // single mode active first checked node\n    if (!multiple && checkedKeys.length && !searchValue) {\n      nextActiveKey = checkedKeys[0];\n    } else {\n      nextActiveKey = getFirstNode();\n    }\n    setActiveKey(nextActiveKey);\n  }, [open, searchValue]);\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 || _treeRef$current3.onKeyDown(event);\n            break;\n\n          // >>> Select item\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var isNodeDisabled = nodeDisabled(activeEntity.node);\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                  selectable = _ref.selectable,\n                  value = _ref.value,\n                  disabled = _ref.disabled;\n                if (selectable !== false && !disabled && !isNodeDisabled) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n  var hasLoadDataFn = useMemo(function () {\n    return searchValue ? false : true;\n  }, [searchValue, treeExpandedKeys || expandedKeys], function (_ref2, _ref3) {\n    var _ref4 = _slicedToArray(_ref2, 1),\n      preSearchValue = _ref4[0];\n    var _ref5 = _slicedToArray(_ref3, 2),\n      nextSearchValue = _ref5[0],\n      nextExcludeSearchExpandedKeys = _ref5[1];\n    return preSearchValue !== nextSearchValue && !!(nextSearchValue || nextExcludeSearchExpandedKeys);\n  });\n  var syncLoadData = hasLoadDataFn ? loadData : null;\n\n  // ========================== Render ==========================\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: {\n      nodeDisabled: nodeDisabled\n    }\n  }, /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    itemScrollOffset: listItemScrollOffset,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: syncLoadData,\n    motion: treeMotion,\n    activeKey: activeKey\n    // We handle keys by out instead tree self\n    ,\n\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll,\n    titleRender: treeTitleRender\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction,\n    onScroll: onPopupScroll\n  }))));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;", "map": {"version": 3, "names": ["_extends", "_createForOfIteratorHelper", "_toConsumableArray", "_slicedToArray", "useBaseProps", "Tree", "UnstableContext", "KeyCode", "useMemo", "React", "LegacyContext", "TreeSelectContext", "getAllKeys", "isCheckDisabled", "useEvent", "HIDDEN_STYLE", "width", "height", "display", "overflow", "opacity", "border", "padding", "margin", "OptionList", "_", "ref", "_useBaseProps", "prefixCls", "multiple", "searchValue", "toggle<PERSON><PERSON>", "open", "notFoundContent", "_React$useContext", "useContext", "virtual", "listHeight", "listItemHeight", "listItemScrollOffset", "treeData", "fieldNames", "onSelect", "dropdownMatchSelectWidth", "treeExpandAction", "treeTitleRender", "onPopupScroll", "leftMaxCount", "leafCountOnly", "valueEntities", "_React$useContext2", "checkable", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "treeExpandedKeys", "treeDefaultExpandAll", "treeDefaultExpandedKeys", "onTreeExpand", "treeIcon", "showTreeIcon", "switcherIcon", "treeLine", "treeNodeFilterProp", "loadData", "treeLoaded<PERSON><PERSON>s", "treeMotion", "onTreeLoad", "keyEntities", "treeRef", "useRef", "memoTreeData", "prev", "next", "mergedCheckedKeys", "checked", "halfChecked", "useEffect", "length", "_treeRef$current", "current", "scrollTo", "key", "onListMouseDown", "event", "preventDefault", "onInternalSelect", "__", "info", "node", "selected", "includes", "_React$useState", "useState", "_React$useState2", "expandedKeys", "setExpandedKeys", "_React$useState3", "_React$useState4", "searchExpandedKeys", "setSearchExpandedKeys", "mergedExpandedKeys", "onInternalExpand", "keys", "lowerSearchValue", "String", "toLowerCase", "filterTreeNode", "treeNode", "_React$useState5", "Map", "_React$useState6", "disabledCache", "setDisabledCache", "getDisabledWithCache", "value", "has", "entity", "get", "<PERSON><PERSON><PERSON><PERSON>", "children", "checkableC<PERSON><PERSON>n", "filter", "childTreeNode", "disabled", "disableCheckbox", "checkableChildrenCount", "set", "nodeDisabled", "nodeValue", "getFirstMatchingNode", "nodes", "_iterator", "_step", "s", "n", "done", "selectable", "matchInChildren", "err", "e", "f", "_React$useState7", "_React$useState8", "active<PERSON><PERSON>", "setActiveKey", "activeEntity", "nextActiveKey", "getFirstNode", "firstNode", "useImperativeHandle", "_treeRef$current2", "onKeyDown", "_treeRef$current3", "which", "UP", "DOWN", "LEFT", "RIGHT", "ENTER", "isNodeDisabled", "_ref", "ESC", "onKeyUp", "hasLoadDataFn", "_ref2", "_ref3", "_ref4", "preSearchValue", "_ref5", "nextSearchValue", "nextExcludeSearchExpandedKeys", "syncLoadData", "createElement", "role", "className", "concat", "onMouseDown", "treeProps", "loadedKeys", "style", "Provider", "focusable", "itemHeight", "itemScrollOffset", "icon", "showIcon", "showLine", "motion", "checkStrictly", "<PERSON><PERSON><PERSON><PERSON>", "defaultExpandAll", "titleRender", "onActiveChange", "onCheck", "onExpand", "onLoad", "expandAction", "onScroll", "RefOptionList", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree-select@5.27.0_react_436633a6a2f00ab1ebd4c6e59066bdb2/node_modules/rc-tree-select/es/OptionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport { UnstableContext } from 'rc-tree';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport * as React from 'react';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nimport { useEvent } from 'rc-util';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open,\n    notFoundContent = _useBaseProps.notFoundContent;\n  var _React$useContext = React.useContext(TreeSelectContext),\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    listItemScrollOffset = _React$useContext.listItemScrollOffset,\n    treeData = _React$useContext.treeData,\n    fieldNames = _React$useContext.fieldNames,\n    onSelect = _React$useContext.onSelect,\n    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n    treeExpandAction = _React$useContext.treeExpandAction,\n    treeTitleRender = _React$useContext.treeTitleRender,\n    onPopupScroll = _React$useContext.onPopupScroll,\n    leftMaxCount = _React$useContext.leftMaxCount,\n    leafCountOnly = _React$useContext.leafCountOnly,\n    valueEntities = _React$useContext.valueEntities;\n  var _React$useContext2 = React.useContext(LegacyContext),\n    checkable = _React$useContext2.checkable,\n    checkedKeys = _React$useContext2.checkedKeys,\n    halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n    treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n    onTreeExpand = _React$useContext2.onTreeExpand,\n    treeIcon = _React$useContext2.treeIcon,\n    showTreeIcon = _React$useContext2.showTreeIcon,\n    switcherIcon = _React$useContext2.switcherIcon,\n    treeLine = _React$useContext2.treeLine,\n    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n    loadData = _React$useContext2.loadData,\n    treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n    treeMotion = _React$useContext2.treeMotion,\n    onTreeLoad = _React$useContext2.onTreeLoad,\n    keyEntities = _React$useContext2.keyEntities;\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // ========================== Values ==========================\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]);\n\n  // ========================== Scroll ==========================\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 || _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n\n  // ========================== Events ==========================\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // =========================== Keys ===========================\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedKeys = _React$useState2[0],\n    setExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    searchExpandedKeys = _React$useState4[0],\n    setSearchExpandedKeys = _React$useState4[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  };\n\n  // ========================== Search ==========================\n  var lowerSearchValue = String(searchValue).toLowerCase();\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  };\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [searchValue]);\n\n  // ========================= Disabled =========================\n  // Cache disabled states in React state to ensure re-render when cache updates\n  var _React$useState5 = React.useState(function () {\n      return new Map();\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    disabledCache = _React$useState6[0],\n    setDisabledCache = _React$useState6[1];\n  React.useEffect(function () {\n    if (leftMaxCount) {\n      setDisabledCache(new Map());\n    }\n  }, [leftMaxCount]);\n  function getDisabledWithCache(node) {\n    var value = node[fieldNames.value];\n    if (!disabledCache.has(value)) {\n      var entity = valueEntities.get(value);\n      var isLeaf = (entity.children || []).length === 0;\n      if (!isLeaf) {\n        var checkableChildren = entity.children.filter(function (childTreeNode) {\n          return !childTreeNode.node.disabled && !childTreeNode.node.disableCheckbox && !checkedKeys.includes(childTreeNode.node[fieldNames.value]);\n        });\n        var checkableChildrenCount = checkableChildren.length;\n        disabledCache.set(value, checkableChildrenCount > leftMaxCount);\n      } else {\n        disabledCache.set(value, false);\n      }\n    }\n    return disabledCache.get(value);\n  }\n  var nodeDisabled = useEvent(function (node) {\n    var nodeValue = node[fieldNames.value];\n    if (checkedKeys.includes(nodeValue)) {\n      return false;\n    }\n    if (leftMaxCount === null) {\n      return false;\n    }\n    if (leftMaxCount <= 0) {\n      return true;\n    }\n\n    // This is a low performance calculation\n    if (leafCountOnly && leftMaxCount) {\n      return getDisabledWithCache(node);\n    }\n    return false;\n  });\n\n  // ========================== Get First Selectable Node ==========================\n  var getFirstMatchingNode = function getFirstMatchingNode(nodes) {\n    var _iterator = _createForOfIteratorHelper(nodes),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var node = _step.value;\n        if (node.disabled || node.selectable === false) {\n          continue;\n        }\n        if (searchValue) {\n          if (filterTreeNode(node)) {\n            return node;\n          }\n        } else {\n          return node;\n        }\n        if (node[fieldNames.children]) {\n          var matchInChildren = getFirstMatchingNode(node[fieldNames.children]);\n          if (matchInChildren) {\n            return matchInChildren;\n          }\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return null;\n  };\n\n  // ========================== Active ==========================\n  var _React$useState7 = React.useState(null),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    activeKey = _React$useState8[0],\n    setActiveKey = _React$useState8[1];\n  var activeEntity = keyEntities[activeKey];\n  React.useEffect(function () {\n    if (!open) {\n      return;\n    }\n    var nextActiveKey = null;\n    var getFirstNode = function getFirstNode() {\n      var firstNode = getFirstMatchingNode(memoTreeData);\n      return firstNode ? firstNode[fieldNames.value] : null;\n    };\n\n    // single mode active first checked node\n    if (!multiple && checkedKeys.length && !searchValue) {\n      nextActiveKey = checkedKeys[0];\n    } else {\n      nextActiveKey = getFirstNode();\n    }\n    setActiveKey(nextActiveKey);\n  }, [open, searchValue]);\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 || _treeRef$current3.onKeyDown(event);\n            break;\n\n          // >>> Select item\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var isNodeDisabled = nodeDisabled(activeEntity.node);\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                  selectable = _ref.selectable,\n                  value = _ref.value,\n                  disabled = _ref.disabled;\n                if (selectable !== false && !disabled && !isNodeDisabled) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n  var hasLoadDataFn = useMemo(function () {\n    return searchValue ? false : true;\n  }, [searchValue, treeExpandedKeys || expandedKeys], function (_ref2, _ref3) {\n    var _ref4 = _slicedToArray(_ref2, 1),\n      preSearchValue = _ref4[0];\n    var _ref5 = _slicedToArray(_ref3, 2),\n      nextSearchValue = _ref5[0],\n      nextExcludeSearchExpandedKeys = _ref5[1];\n    return preSearchValue !== nextSearchValue && !!(nextSearchValue || nextExcludeSearchExpandedKeys);\n  });\n  var syncLoadData = hasLoadDataFn ? loadData : null;\n\n  // ========================== Render ==========================\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: {\n      nodeDisabled: nodeDisabled\n    }\n  }, /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    itemScrollOffset: listItemScrollOffset,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: syncLoadData,\n    motion: treeMotion,\n    activeKey: activeKey\n    // We handle keys by out instead tree self\n    ,\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll,\n    titleRender: treeTitleRender\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction,\n    onScroll: onPopupScroll\n  }))));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,eAAe,QAAQ,SAAS;AACzC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,UAAU,EAAEC,eAAe,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,SAAS;AAClC,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC3C,IAAIC,aAAa,GAAGvB,YAAY,CAAC,CAAC;IAChCwB,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;IACjCC,WAAW,GAAGH,aAAa,CAACG,WAAW;IACvCC,UAAU,GAAGJ,aAAa,CAACI,UAAU;IACrCC,IAAI,GAAGL,aAAa,CAACK,IAAI;IACzBC,eAAe,GAAGN,aAAa,CAACM,eAAe;EACjD,IAAIC,iBAAiB,GAAGzB,KAAK,CAAC0B,UAAU,CAACxB,iBAAiB,CAAC;IACzDyB,OAAO,GAAGF,iBAAiB,CAACE,OAAO;IACnCC,UAAU,GAAGH,iBAAiB,CAACG,UAAU;IACzCC,cAAc,GAAGJ,iBAAiB,CAACI,cAAc;IACjDC,oBAAoB,GAAGL,iBAAiB,CAACK,oBAAoB;IAC7DC,QAAQ,GAAGN,iBAAiB,CAACM,QAAQ;IACrCC,UAAU,GAAGP,iBAAiB,CAACO,UAAU;IACzCC,QAAQ,GAAGR,iBAAiB,CAACQ,QAAQ;IACrCC,wBAAwB,GAAGT,iBAAiB,CAACS,wBAAwB;IACrEC,gBAAgB,GAAGV,iBAAiB,CAACU,gBAAgB;IACrDC,eAAe,GAAGX,iBAAiB,CAACW,eAAe;IACnDC,aAAa,GAAGZ,iBAAiB,CAACY,aAAa;IAC/CC,YAAY,GAAGb,iBAAiB,CAACa,YAAY;IAC7CC,aAAa,GAAGd,iBAAiB,CAACc,aAAa;IAC/CC,aAAa,GAAGf,iBAAiB,CAACe,aAAa;EACjD,IAAIC,kBAAkB,GAAGzC,KAAK,CAAC0B,UAAU,CAACzB,aAAa,CAAC;IACtDyC,SAAS,GAAGD,kBAAkB,CAACC,SAAS;IACxCC,WAAW,GAAGF,kBAAkB,CAACE,WAAW;IAC5CC,eAAe,GAAGH,kBAAkB,CAACG,eAAe;IACpDC,gBAAgB,GAAGJ,kBAAkB,CAACI,gBAAgB;IACtDC,oBAAoB,GAAGL,kBAAkB,CAACK,oBAAoB;IAC9DC,uBAAuB,GAAGN,kBAAkB,CAACM,uBAAuB;IACpEC,YAAY,GAAGP,kBAAkB,CAACO,YAAY;IAC9CC,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ;IACtCC,YAAY,GAAGT,kBAAkB,CAACS,YAAY;IAC9CC,YAAY,GAAGV,kBAAkB,CAACU,YAAY;IAC9CC,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ;IACtCC,kBAAkB,GAAGZ,kBAAkB,CAACY,kBAAkB;IAC1DC,QAAQ,GAAGb,kBAAkB,CAACa,QAAQ;IACtCC,cAAc,GAAGd,kBAAkB,CAACc,cAAc;IAClDC,UAAU,GAAGf,kBAAkB,CAACe,UAAU;IAC1CC,UAAU,GAAGhB,kBAAkB,CAACgB,UAAU;IAC1CC,WAAW,GAAGjB,kBAAkB,CAACiB,WAAW;EAC9C,IAAIC,OAAO,GAAG3D,KAAK,CAAC4D,MAAM,CAAC,CAAC;EAC5B,IAAIC,YAAY,GAAG9D,OAAO,CAAC,YAAY;IACrC,OAAOgC,QAAQ;EACjB,CAAC;EACD;EACA,CAACR,IAAI,EAAEQ,QAAQ,CAAC,EAAE,UAAU+B,IAAI,EAAEC,IAAI,EAAE;IACtC,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,iBAAiB,GAAGhE,KAAK,CAACD,OAAO,CAAC,YAAY;IAChD,IAAI,CAAC2C,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IACA,OAAO;MACLuB,OAAO,EAAEtB,WAAW;MACpBuB,WAAW,EAAEtB;IACf,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,EAAEC,WAAW,EAAEC,eAAe,CAAC,CAAC;;EAE7C;EACA5C,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B;IACA,IAAI5C,IAAI,IAAI,CAACH,QAAQ,IAAIuB,WAAW,CAACyB,MAAM,EAAE;MAC3C,IAAIC,gBAAgB;MACpB,CAACA,gBAAgB,GAAGV,OAAO,CAACW,OAAO,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACE,QAAQ,CAAC;QACxGC,GAAG,EAAE7B,WAAW,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;;EAEV;EACA,IAAIkD,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpDA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACzD,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IACpB,IAAIrC,SAAS,IAAItC,eAAe,CAAC2E,IAAI,CAAC,EAAE;MACtC;IACF;IACA9C,QAAQ,CAAC8C,IAAI,CAACP,GAAG,EAAE;MACjBQ,QAAQ,EAAE,CAACrC,WAAW,CAACsC,QAAQ,CAACF,IAAI,CAACP,GAAG;IAC1C,CAAC,CAAC;IACF,IAAI,CAACpD,QAAQ,EAAE;MACbE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAI4D,eAAe,GAAGlF,KAAK,CAACmF,QAAQ,CAACpC,uBAAuB,CAAC;IAC3DqC,gBAAgB,GAAG1F,cAAc,CAACwF,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,gBAAgB,GAAGvF,KAAK,CAACmF,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAG9F,cAAc,CAAC6F,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,kBAAkB,GAAG3F,KAAK,CAACD,OAAO,CAAC,YAAY;IACjD,IAAI8C,gBAAgB,EAAE;MACpB,OAAOpD,kBAAkB,CAACoD,gBAAgB,CAAC;IAC7C;IACA,OAAOxB,WAAW,GAAGoE,kBAAkB,GAAGJ,YAAY;EACxD,CAAC,EAAE,CAACA,YAAY,EAAEI,kBAAkB,EAAE5C,gBAAgB,EAAExB,WAAW,CAAC,CAAC;EACrE,IAAIuE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrDP,eAAe,CAACO,IAAI,CAAC;IACrBH,qBAAqB,CAACG,IAAI,CAAC;IAC3B,IAAI7C,YAAY,EAAE;MAChBA,YAAY,CAAC6C,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,IAAIC,gBAAgB,GAAGC,MAAM,CAAC1E,WAAW,CAAC,CAAC2E,WAAW,CAAC,CAAC;EACxD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,QAAQ,EAAE;IACrD,IAAI,CAACJ,gBAAgB,EAAE;MACrB,OAAO,KAAK;IACd;IACA,OAAOC,MAAM,CAACG,QAAQ,CAAC7C,kBAAkB,CAAC,CAAC,CAAC2C,WAAW,CAAC,CAAC,CAACf,QAAQ,CAACa,gBAAgB,CAAC;EACtF,CAAC;EACD9F,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAI9C,WAAW,EAAE;MACfqE,qBAAqB,CAACvF,UAAU,CAAC4B,QAAQ,EAAEC,UAAU,CAAC,CAAC;IACzD;IACA;EACF,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;;EAEjB;EACA;EACA,IAAI8E,gBAAgB,GAAGnG,KAAK,CAACmF,QAAQ,CAAC,YAAY;MAC9C,OAAO,IAAIiB,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC;IACFC,gBAAgB,GAAG3G,cAAc,CAACyG,gBAAgB,EAAE,CAAC,CAAC;IACtDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxCrG,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAI7B,YAAY,EAAE;MAChBiE,gBAAgB,CAAC,IAAIH,GAAG,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC9D,YAAY,CAAC,CAAC;EAClB,SAASkE,oBAAoBA,CAACzB,IAAI,EAAE;IAClC,IAAI0B,KAAK,GAAG1B,IAAI,CAAC/C,UAAU,CAACyE,KAAK,CAAC;IAClC,IAAI,CAACH,aAAa,CAACI,GAAG,CAACD,KAAK,CAAC,EAAE;MAC7B,IAAIE,MAAM,GAAGnE,aAAa,CAACoE,GAAG,CAACH,KAAK,CAAC;MACrC,IAAII,MAAM,GAAG,CAACF,MAAM,CAACG,QAAQ,IAAI,EAAE,EAAE1C,MAAM,KAAK,CAAC;MACjD,IAAI,CAACyC,MAAM,EAAE;QACX,IAAIE,iBAAiB,GAAGJ,MAAM,CAACG,QAAQ,CAACE,MAAM,CAAC,UAAUC,aAAa,EAAE;UACtE,OAAO,CAACA,aAAa,CAAClC,IAAI,CAACmC,QAAQ,IAAI,CAACD,aAAa,CAAClC,IAAI,CAACoC,eAAe,IAAI,CAACxE,WAAW,CAACsC,QAAQ,CAACgC,aAAa,CAAClC,IAAI,CAAC/C,UAAU,CAACyE,KAAK,CAAC,CAAC;QAC3I,CAAC,CAAC;QACF,IAAIW,sBAAsB,GAAGL,iBAAiB,CAAC3C,MAAM;QACrDkC,aAAa,CAACe,GAAG,CAACZ,KAAK,EAAEW,sBAAsB,GAAG9E,YAAY,CAAC;MACjE,CAAC,MAAM;QACLgE,aAAa,CAACe,GAAG,CAACZ,KAAK,EAAE,KAAK,CAAC;MACjC;IACF;IACA,OAAOH,aAAa,CAACM,GAAG,CAACH,KAAK,CAAC;EACjC;EACA,IAAIa,YAAY,GAAGjH,QAAQ,CAAC,UAAU0E,IAAI,EAAE;IAC1C,IAAIwC,SAAS,GAAGxC,IAAI,CAAC/C,UAAU,CAACyE,KAAK,CAAC;IACtC,IAAI9D,WAAW,CAACsC,QAAQ,CAACsC,SAAS,CAAC,EAAE;MACnC,OAAO,KAAK;IACd;IACA,IAAIjF,YAAY,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IACd;IACA,IAAIA,YAAY,IAAI,CAAC,EAAE;MACrB,OAAO,IAAI;IACb;;IAEA;IACA,IAAIC,aAAa,IAAID,YAAY,EAAE;MACjC,OAAOkE,oBAAoB,CAACzB,IAAI,CAAC;IACnC;IACA,OAAO,KAAK;EACd,CAAC,CAAC;;EAEF;EACA,IAAIyC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAE;IAC9D,IAAIC,SAAS,GAAGlI,0BAA0B,CAACiI,KAAK,CAAC;MAC/CE,KAAK;IACP,IAAI;MACF,KAAKD,SAAS,CAACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAACD,KAAK,GAAGD,SAAS,CAACG,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;QAClD,IAAI/C,IAAI,GAAG4C,KAAK,CAAClB,KAAK;QACtB,IAAI1B,IAAI,CAACmC,QAAQ,IAAInC,IAAI,CAACgD,UAAU,KAAK,KAAK,EAAE;UAC9C;QACF;QACA,IAAI1G,WAAW,EAAE;UACf,IAAI4E,cAAc,CAAClB,IAAI,CAAC,EAAE;YACxB,OAAOA,IAAI;UACb;QACF,CAAC,MAAM;UACL,OAAOA,IAAI;QACb;QACA,IAAIA,IAAI,CAAC/C,UAAU,CAAC8E,QAAQ,CAAC,EAAE;UAC7B,IAAIkB,eAAe,GAAGR,oBAAoB,CAACzC,IAAI,CAAC/C,UAAU,CAAC8E,QAAQ,CAAC,CAAC;UACrE,IAAIkB,eAAe,EAAE;YACnB,OAAOA,eAAe;UACxB;QACF;MACF;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZP,SAAS,CAACQ,CAAC,CAACD,GAAG,CAAC;IAClB,CAAC,SAAS;MACRP,SAAS,CAACS,CAAC,CAAC,CAAC;IACf;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,IAAIC,gBAAgB,GAAGpI,KAAK,CAACmF,QAAQ,CAAC,IAAI,CAAC;IACzCkD,gBAAgB,GAAG3I,cAAc,CAAC0I,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,YAAY,GAAG9E,WAAW,CAAC4E,SAAS,CAAC;EACzCtI,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC5C,IAAI,EAAE;MACT;IACF;IACA,IAAIkH,aAAa,GAAG,IAAI;IACxB,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;MACzC,IAAIC,SAAS,GAAGnB,oBAAoB,CAAC3D,YAAY,CAAC;MAClD,OAAO8E,SAAS,GAAGA,SAAS,CAAC3G,UAAU,CAACyE,KAAK,CAAC,GAAG,IAAI;IACvD,CAAC;;IAED;IACA,IAAI,CAACrF,QAAQ,IAAIuB,WAAW,CAACyB,MAAM,IAAI,CAAC/C,WAAW,EAAE;MACnDoH,aAAa,GAAG9F,WAAW,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM;MACL8F,aAAa,GAAGC,YAAY,CAAC,CAAC;IAChC;IACAH,YAAY,CAACE,aAAa,CAAC;EAC7B,CAAC,EAAE,CAAClH,IAAI,EAAEF,WAAW,CAAC,CAAC;;EAEvB;EACArB,KAAK,CAAC4I,mBAAmB,CAAC3H,GAAG,EAAE,YAAY;IACzC,IAAI4H,iBAAiB;IACrB,OAAO;MACLtE,QAAQ,EAAE,CAACsE,iBAAiB,GAAGlF,OAAO,CAACW,OAAO,MAAM,IAAI,IAAIuE,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACtE,QAAQ;MAC9HuE,SAAS,EAAE,SAASA,SAASA,CAACpE,KAAK,EAAE;QACnC,IAAIqE,iBAAiB;QACrB,IAAIC,KAAK,GAAGtE,KAAK,CAACsE,KAAK;QACvB,QAAQA,KAAK;UACX;UACA,KAAKlJ,OAAO,CAACmJ,EAAE;UACf,KAAKnJ,OAAO,CAACoJ,IAAI;UACjB,KAAKpJ,OAAO,CAACqJ,IAAI;UACjB,KAAKrJ,OAAO,CAACsJ,KAAK;YAChB,CAACL,iBAAiB,GAAGpF,OAAO,CAACW,OAAO,MAAM,IAAI,IAAIyE,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACD,SAAS,CAACpE,KAAK,CAAC;YACpH;;UAEF;UACA,KAAK5E,OAAO,CAACuJ,KAAK;YAChB;cACE,IAAIb,YAAY,EAAE;gBAChB,IAAIc,cAAc,GAAGhC,YAAY,CAACkB,YAAY,CAACzD,IAAI,CAAC;gBACpD,IAAIwE,IAAI,GAAG,CAACf,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACzD,IAAI,KAAK,CAAC,CAAC;kBAC9FgD,UAAU,GAAGwB,IAAI,CAACxB,UAAU;kBAC5BtB,KAAK,GAAG8C,IAAI,CAAC9C,KAAK;kBAClBS,QAAQ,GAAGqC,IAAI,CAACrC,QAAQ;gBAC1B,IAAIa,UAAU,KAAK,KAAK,IAAI,CAACb,QAAQ,IAAI,CAACoC,cAAc,EAAE;kBACxD1E,gBAAgB,CAAC,IAAI,EAAE;oBACrBG,IAAI,EAAE;sBACJP,GAAG,EAAE8D;oBACP,CAAC;oBACDtD,QAAQ,EAAE,CAACrC,WAAW,CAACsC,QAAQ,CAACwB,KAAK;kBACvC,CAAC,CAAC;gBACJ;cACF;cACA;YACF;;UAEF;UACA,KAAK3G,OAAO,CAAC0J,GAAG;YACd;cACElI,UAAU,CAAC,KAAK,CAAC;YACnB;QACJ;MACF,CAAC;MACDmI,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;IAC/B,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,aAAa,GAAG3J,OAAO,CAAC,YAAY;IACtC,OAAOsB,WAAW,GAAG,KAAK,GAAG,IAAI;EACnC,CAAC,EAAE,CAACA,WAAW,EAAEwB,gBAAgB,IAAIwC,YAAY,CAAC,EAAE,UAAUsE,KAAK,EAAEC,KAAK,EAAE;IAC1E,IAAIC,KAAK,GAAGnK,cAAc,CAACiK,KAAK,EAAE,CAAC,CAAC;MAClCG,cAAc,GAAGD,KAAK,CAAC,CAAC,CAAC;IAC3B,IAAIE,KAAK,GAAGrK,cAAc,CAACkK,KAAK,EAAE,CAAC,CAAC;MAClCI,eAAe,GAAGD,KAAK,CAAC,CAAC,CAAC;MAC1BE,6BAA6B,GAAGF,KAAK,CAAC,CAAC,CAAC;IAC1C,OAAOD,cAAc,KAAKE,eAAe,IAAI,CAAC,EAAEA,eAAe,IAAIC,6BAA6B,CAAC;EACnG,CAAC,CAAC;EACF,IAAIC,YAAY,GAAGR,aAAa,GAAGpG,QAAQ,GAAG,IAAI;;EAElD;EACA,IAAIO,YAAY,CAACO,MAAM,KAAK,CAAC,EAAE;IAC7B,OAAO,aAAapE,KAAK,CAACmK,aAAa,CAAC,KAAK,EAAE;MAC7CC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACnJ,SAAS,EAAE,QAAQ,CAAC;MACzCoJ,WAAW,EAAE9F;IACf,CAAC,EAAEjD,eAAe,CAAC;EACrB;EACA,IAAIgJ,SAAS,GAAG;IACdxI,UAAU,EAAEA;EACd,CAAC;EACD,IAAIuB,cAAc,EAAE;IAClBiH,SAAS,CAACC,UAAU,GAAGlH,cAAc;EACvC;EACA,IAAIoC,kBAAkB,EAAE;IACtB6E,SAAS,CAACnF,YAAY,GAAGM,kBAAkB;EAC7C;EACA,OAAO,aAAa3F,KAAK,CAACmK,aAAa,CAAC,KAAK,EAAE;IAC7CI,WAAW,EAAE9F;EACf,CAAC,EAAE+D,YAAY,IAAIjH,IAAI,IAAI,aAAavB,KAAK,CAACmK,aAAa,CAAC,MAAM,EAAE;IAClEO,KAAK,EAAEpK,YAAY;IACnB,WAAW,EAAE;EACf,CAAC,EAAEkI,YAAY,CAACzD,IAAI,CAAC0B,KAAK,CAAC,EAAE,aAAazG,KAAK,CAACmK,aAAa,CAACtK,eAAe,CAAC8K,QAAQ,EAAE;IACtFlE,KAAK,EAAE;MACLa,YAAY,EAAEA;IAChB;EACF,CAAC,EAAE,aAAatH,KAAK,CAACmK,aAAa,CAACvK,IAAI,EAAEL,QAAQ,CAAC;IACjD0B,GAAG,EAAE0C,OAAO;IACZiH,SAAS,EAAE,KAAK;IAChBzJ,SAAS,EAAE,EAAE,CAACmJ,MAAM,CAACnJ,SAAS,EAAE,OAAO,CAAC;IACxCY,QAAQ,EAAE8B,YAAY;IACtBrD,MAAM,EAAEoB,UAAU;IAClBiJ,UAAU,EAAEhJ,cAAc;IAC1BiJ,gBAAgB,EAAEhJ,oBAAoB;IACtCH,OAAO,EAAEA,OAAO,KAAK,KAAK,IAAIO,wBAAwB,KAAK,KAAK;IAChEd,QAAQ,EAAEA,QAAQ;IAClB2J,IAAI,EAAE9H,QAAQ;IACd+H,QAAQ,EAAE9H,YAAY;IACtBC,YAAY,EAAEA,YAAY;IAC1B8H,QAAQ,EAAE7H,QAAQ;IAClBE,QAAQ,EAAE4G,YAAY;IACtBgB,MAAM,EAAE1H,UAAU;IAClB8E,SAAS,EAAEA;IACX;IAAA;;IAEA5F,SAAS,EAAEA,SAAS;IACpByI,aAAa,EAAE,IAAI;IACnBxI,WAAW,EAAEqB,iBAAiB;IAC9BoH,YAAY,EAAE,CAAC1I,SAAS,GAAGC,WAAW,GAAG,EAAE;IAC3C0I,gBAAgB,EAAEvI,oBAAoB;IACtCwI,WAAW,EAAElJ;EACf,CAAC,EAAEoI,SAAS,EAAE;IACZ;IACAe,cAAc,EAAEhD,YAAY;IAC5BtG,QAAQ,EAAE2C,gBAAgB;IAC1B4G,OAAO,EAAE5G,gBAAgB;IACzB6G,QAAQ,EAAE7F,gBAAgB;IAC1B8F,MAAM,EAAEjI,UAAU;IAClBwC,cAAc,EAAEA,cAAc;IAC9B0F,YAAY,EAAExJ,gBAAgB;IAC9ByJ,QAAQ,EAAEvJ;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAIwJ,aAAa,GAAG,aAAa7L,KAAK,CAAC8L,UAAU,CAAC/K,UAAU,CAAC;AAC7D,IAAIgL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,aAAa,CAACK,WAAW,GAAG,YAAY;AAC1C;AACA,eAAeL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
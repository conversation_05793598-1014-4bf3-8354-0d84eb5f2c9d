{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = ({\n  enableEllipsis,\n  isEllipsis,\n  children,\n  tooltipProps\n}) => {\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enableEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "EllipsisTooltip", "enableEllipsis", "isEllipsis", "children", "tooltipProps", "title", "createElement", "Object", "assign", "open", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/typography/Base/EllipsisTooltip.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = ({\n  enableEllipsis,\n  isEllipsis,\n  children,\n  tooltipProps\n}) => {\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enableEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AACnC,MAAMC,eAAe,GAAGA,CAAC;EACvBC,cAAc;EACdC,UAAU;EACVC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,IAAI,EAAEA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACC,KAAK,CAAC,IAAI,CAACJ,cAAc,EAAE;IACxG,OAAOE,QAAQ;EACjB;EACA,OAAO,aAAaL,KAAK,CAACQ,aAAa,CAACP,OAAO,EAAEQ,MAAM,CAACC,MAAM,CAAC;IAC7DC,IAAI,EAAEP,UAAU,GAAGQ,SAAS,GAAG;EACjC,CAAC,EAAEN,YAAY,CAAC,EAAED,QAAQ,CAAC;AAC7B,CAAC;AACD,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCb,eAAe,CAACc,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAed,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
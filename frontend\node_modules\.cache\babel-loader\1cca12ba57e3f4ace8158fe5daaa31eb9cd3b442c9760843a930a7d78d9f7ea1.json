{"ast": null, "code": "import * as React from 'react';\nimport useEntities from \"./useEntities\";\nexport default function useOptions(mergedFieldNames, options) {\n  var mergedOptions = React.useMemo(function () {\n    return options || [];\n  }, [options]);\n\n  // Only used in multiple mode, this fn will not call in single mode\n  var getPathKeyEntities = useEntities(mergedOptions, mergedFieldNames);\n\n  /** Convert path key back to value format */\n  var getValueByKeyPath = React.useCallback(function (pathKeys) {\n    var keyPathEntities = getPathKeyEntities();\n    return pathKeys.map(function (pathKey) {\n      var nodes = keyPathEntities[pathKey].nodes;\n      return nodes.map(function (node) {\n        return node[mergedFieldNames.value];\n      });\n    });\n  }, [getPathKeyEntities, mergedFieldNames]);\n  return [mergedOptions, getPathKeyEntities, getValueByKeyPath];\n}", "map": {"version": 3, "names": ["React", "useEntities", "useOptions", "mergedFieldNames", "options", "mergedOptions", "useMemo", "getPathKeyEntities", "getValueByKeyPath", "useCallback", "pathKeys", "keyPathEntities", "map", "path<PERSON><PERSON>", "nodes", "node", "value"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-cascader@3.34.0_react-do_81eee13bae352416aaa466308a981cc5/node_modules/rc-cascader/es/hooks/useOptions.js"], "sourcesContent": ["import * as React from 'react';\nimport useEntities from \"./useEntities\";\nexport default function useOptions(mergedFieldNames, options) {\n  var mergedOptions = React.useMemo(function () {\n    return options || [];\n  }, [options]);\n\n  // Only used in multiple mode, this fn will not call in single mode\n  var getPathKeyEntities = useEntities(mergedOptions, mergedFieldNames);\n\n  /** Convert path key back to value format */\n  var getValueByKeyPath = React.useCallback(function (pathKeys) {\n    var keyPathEntities = getPathKeyEntities();\n    return pathKeys.map(function (pathKey) {\n      var nodes = keyPathEntities[pathKey].nodes;\n      return nodes.map(function (node) {\n        return node[mergedFieldNames.value];\n      });\n    });\n  }, [getPathKeyEntities, mergedFieldNames]);\n  return [mergedOptions, getPathKeyEntities, getValueByKeyPath];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,UAAUA,CAACC,gBAAgB,EAAEC,OAAO,EAAE;EAC5D,IAAIC,aAAa,GAAGL,KAAK,CAACM,OAAO,CAAC,YAAY;IAC5C,OAAOF,OAAO,IAAI,EAAE;EACtB,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;;EAEb;EACA,IAAIG,kBAAkB,GAAGN,WAAW,CAACI,aAAa,EAAEF,gBAAgB,CAAC;;EAErE;EACA,IAAIK,iBAAiB,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,QAAQ,EAAE;IAC5D,IAAIC,eAAe,GAAGJ,kBAAkB,CAAC,CAAC;IAC1C,OAAOG,QAAQ,CAACE,GAAG,CAAC,UAAUC,OAAO,EAAE;MACrC,IAAIC,KAAK,GAAGH,eAAe,CAACE,OAAO,CAAC,CAACC,KAAK;MAC1C,OAAOA,KAAK,CAACF,GAAG,CAAC,UAAUG,IAAI,EAAE;QAC/B,OAAOA,IAAI,CAACZ,gBAAgB,CAACa,KAAK,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,kBAAkB,EAAEJ,gBAAgB,CAAC,CAAC;EAC1C,OAAO,CAACE,aAAa,EAAEE,kBAAkB,EAAEC,iBAAiB,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\n// https://github.com/ant-design/ant-design/issues/50080\nvar traverseFileTree = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(files, isAccepted) {\n    var flattenFileList, progressFileList, readDirectory, _readDirectory, readFile, _readFile, _traverseFileTree, wipIndex;\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _readFile = function _readFile3() {\n            _readFile = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(item) {\n              return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n                while (1) switch (_context3.prev = _context3.next) {\n                  case 0:\n                    return _context3.abrupt(\"return\", new Promise(function (reslove) {\n                      item.file(function (file) {\n                        if (isAccepted(file)) {\n                          // https://github.com/ant-design/ant-design/issues/16426\n                          if (item.fullPath && !file.webkitRelativePath) {\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: true\n                              }\n                            });\n                            // eslint-disable-next-line no-param-reassign\n                            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: false\n                              }\n                            });\n                          }\n                          reslove(file);\n                        } else {\n                          reslove(null);\n                        }\n                      });\n                    }));\n                  case 1:\n                  case \"end\":\n                    return _context3.stop();\n                }\n              }, _callee3);\n            }));\n            return _readFile.apply(this, arguments);\n          };\n          readFile = function _readFile2(_x4) {\n            return _readFile.apply(this, arguments);\n          };\n          _readDirectory = function _readDirectory3() {\n            _readDirectory = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(directory) {\n              var dirReader, entries, results, n, i;\n              return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                while (1) switch (_context2.prev = _context2.next) {\n                  case 0:\n                    dirReader = directory.createReader();\n                    entries = [];\n                  case 2:\n                    if (!true) {\n                      _context2.next = 12;\n                      break;\n                    }\n                    _context2.next = 5;\n                    return new Promise(function (resolve) {\n                      dirReader.readEntries(resolve, function () {\n                        return resolve([]);\n                      });\n                    });\n                  case 5:\n                    results = _context2.sent;\n                    n = results.length;\n                    if (n) {\n                      _context2.next = 9;\n                      break;\n                    }\n                    return _context2.abrupt(\"break\", 12);\n                  case 9:\n                    for (i = 0; i < n; i++) {\n                      entries.push(results[i]);\n                    }\n                    _context2.next = 2;\n                    break;\n                  case 12:\n                    return _context2.abrupt(\"return\", entries);\n                  case 13:\n                  case \"end\":\n                    return _context2.stop();\n                }\n              }, _callee2);\n            }));\n            return _readDirectory.apply(this, arguments);\n          };\n          readDirectory = function _readDirectory2(_x3) {\n            return _readDirectory.apply(this, arguments);\n          };\n          flattenFileList = [];\n          progressFileList = [];\n          files.forEach(function (file) {\n            return progressFileList.push(file.webkitGetAsEntry());\n          });\n\n          // eslint-disable-next-line @typescript-eslint/naming-convention\n          _traverseFileTree = /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(item, path) {\n              var _file, entries;\n              return _regeneratorRuntime().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    if (item) {\n                      _context.next = 2;\n                      break;\n                    }\n                    return _context.abrupt(\"return\");\n                  case 2:\n                    // eslint-disable-next-line no-param-reassign\n                    item.path = path || '';\n                    if (!item.isFile) {\n                      _context.next = 10;\n                      break;\n                    }\n                    _context.next = 6;\n                    return readFile(item);\n                  case 6:\n                    _file = _context.sent;\n                    if (_file) {\n                      flattenFileList.push(_file);\n                    }\n                    _context.next = 15;\n                    break;\n                  case 10:\n                    if (!item.isDirectory) {\n                      _context.next = 15;\n                      break;\n                    }\n                    _context.next = 13;\n                    return readDirectory(item);\n                  case 13:\n                    entries = _context.sent;\n                    progressFileList.push.apply(progressFileList, _toConsumableArray(entries));\n                  case 15:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            }));\n            return function _traverseFileTree(_x5, _x6) {\n              return _ref2.apply(this, arguments);\n            };\n          }();\n          wipIndex = 0;\n        case 9:\n          if (!(wipIndex < progressFileList.length)) {\n            _context4.next = 15;\n            break;\n          }\n          _context4.next = 12;\n          return _traverseFileTree(progressFileList[wipIndex]);\n        case 12:\n          wipIndex++;\n          _context4.next = 9;\n          break;\n        case 15:\n          return _context4.abrupt(\"return\", flattenFileList);\n        case 16:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return function traverseFileTree(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport default traverseFileTree;", "map": {"version": 3, "names": ["_regeneratorRuntime", "_toConsumableArray", "_asyncToGenerator", "traverseFileTree", "_ref", "mark", "_callee4", "files", "isAccepted", "flattenFileList", "progressFileList", "readDirectory", "_readDirectory", "readFile", "_readFile", "_traverseFileTree", "wipIndex", "wrap", "_callee4$", "_context4", "prev", "next", "_readFile3", "_callee3", "item", "_callee3$", "_context3", "abrupt", "Promise", "reslove", "file", "fullPath", "webkitRelativePath", "Object", "defineProperties", "writable", "replace", "stop", "apply", "arguments", "_readFile2", "_x4", "_readDirectory3", "_callee2", "directory", "<PERSON><PERSON><PERSON><PERSON>", "entries", "results", "n", "i", "_callee2$", "_context2", "createReader", "resolve", "readEntries", "sent", "length", "push", "_readDirectory2", "_x3", "for<PERSON>ach", "webkitGetAsEntry", "_ref2", "_callee", "path", "_file", "_callee$", "_context", "isFile", "isDirectory", "_x5", "_x6", "_x", "_x2"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-upload@4.9.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-upload/es/traverseFileTree.js"], "sourcesContent": ["import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\n// https://github.com/ant-design/ant-design/issues/50080\nvar traverseFileTree = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(files, isAccepted) {\n    var flattenFileList, progressFileList, readDirectory, _readDirectory, readFile, _readFile, _traverseFileTree, wipIndex;\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _readFile = function _readFile3() {\n            _readFile = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(item) {\n              return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n                while (1) switch (_context3.prev = _context3.next) {\n                  case 0:\n                    return _context3.abrupt(\"return\", new Promise(function (reslove) {\n                      item.file(function (file) {\n                        if (isAccepted(file)) {\n                          // https://github.com/ant-design/ant-design/issues/16426\n                          if (item.fullPath && !file.webkitRelativePath) {\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: true\n                              }\n                            });\n                            // eslint-disable-next-line no-param-reassign\n                            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: false\n                              }\n                            });\n                          }\n                          reslove(file);\n                        } else {\n                          reslove(null);\n                        }\n                      });\n                    }));\n                  case 1:\n                  case \"end\":\n                    return _context3.stop();\n                }\n              }, _callee3);\n            }));\n            return _readFile.apply(this, arguments);\n          };\n          readFile = function _readFile2(_x4) {\n            return _readFile.apply(this, arguments);\n          };\n          _readDirectory = function _readDirectory3() {\n            _readDirectory = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(directory) {\n              var dirReader, entries, results, n, i;\n              return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                while (1) switch (_context2.prev = _context2.next) {\n                  case 0:\n                    dirReader = directory.createReader();\n                    entries = [];\n                  case 2:\n                    if (!true) {\n                      _context2.next = 12;\n                      break;\n                    }\n                    _context2.next = 5;\n                    return new Promise(function (resolve) {\n                      dirReader.readEntries(resolve, function () {\n                        return resolve([]);\n                      });\n                    });\n                  case 5:\n                    results = _context2.sent;\n                    n = results.length;\n                    if (n) {\n                      _context2.next = 9;\n                      break;\n                    }\n                    return _context2.abrupt(\"break\", 12);\n                  case 9:\n                    for (i = 0; i < n; i++) {\n                      entries.push(results[i]);\n                    }\n                    _context2.next = 2;\n                    break;\n                  case 12:\n                    return _context2.abrupt(\"return\", entries);\n                  case 13:\n                  case \"end\":\n                    return _context2.stop();\n                }\n              }, _callee2);\n            }));\n            return _readDirectory.apply(this, arguments);\n          };\n          readDirectory = function _readDirectory2(_x3) {\n            return _readDirectory.apply(this, arguments);\n          };\n          flattenFileList = [];\n          progressFileList = [];\n          files.forEach(function (file) {\n            return progressFileList.push(file.webkitGetAsEntry());\n          });\n\n          // eslint-disable-next-line @typescript-eslint/naming-convention\n          _traverseFileTree = /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(item, path) {\n              var _file, entries;\n              return _regeneratorRuntime().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    if (item) {\n                      _context.next = 2;\n                      break;\n                    }\n                    return _context.abrupt(\"return\");\n                  case 2:\n                    // eslint-disable-next-line no-param-reassign\n                    item.path = path || '';\n                    if (!item.isFile) {\n                      _context.next = 10;\n                      break;\n                    }\n                    _context.next = 6;\n                    return readFile(item);\n                  case 6:\n                    _file = _context.sent;\n                    if (_file) {\n                      flattenFileList.push(_file);\n                    }\n                    _context.next = 15;\n                    break;\n                  case 10:\n                    if (!item.isDirectory) {\n                      _context.next = 15;\n                      break;\n                    }\n                    _context.next = 13;\n                    return readDirectory(item);\n                  case 13:\n                    entries = _context.sent;\n                    progressFileList.push.apply(progressFileList, _toConsumableArray(entries));\n                  case 15:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            }));\n            return function _traverseFileTree(_x5, _x6) {\n              return _ref2.apply(this, arguments);\n            };\n          }();\n          wipIndex = 0;\n        case 9:\n          if (!(wipIndex < progressFileList.length)) {\n            _context4.next = 15;\n            break;\n          }\n          _context4.next = 12;\n          return _traverseFileTree(progressFileList[wipIndex]);\n        case 12:\n          wipIndex++;\n          _context4.next = 9;\n          break;\n        case 15:\n          return _context4.abrupt(\"return\", flattenFileList);\n        case 16:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return function traverseFileTree(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport default traverseFileTree;"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E;AACA,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EAC9C,IAAIC,IAAI,GAAGF,iBAAiB,CAAE,aAAaF,mBAAmB,CAAC,CAAC,CAACK,IAAI,CAAC,SAASC,QAAQA,CAACC,KAAK,EAAEC,UAAU,EAAE;IACzG,IAAIC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ;IACtH,OAAOhB,mBAAmB,CAAC,CAAC,CAACiB,IAAI,CAAC,SAASC,SAASA,CAACC,SAAS,EAAE;MAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACC,IAAI,GAAGD,SAAS,CAACE,IAAI;QAC/C,KAAK,CAAC;UACJP,SAAS,GAAG,SAASQ,UAAUA,CAAA,EAAG;YAChCR,SAAS,GAAGZ,iBAAiB,CAAE,aAAaF,mBAAmB,CAAC,CAAC,CAACK,IAAI,CAAC,SAASkB,QAAQA,CAACC,IAAI,EAAE;cAC7F,OAAOxB,mBAAmB,CAAC,CAAC,CAACiB,IAAI,CAAC,SAASQ,SAASA,CAACC,SAAS,EAAE;gBAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACN,IAAI,GAAGM,SAAS,CAACL,IAAI;kBAC/C,KAAK,CAAC;oBACJ,OAAOK,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;sBAC/DL,IAAI,CAACM,IAAI,CAAC,UAAUA,IAAI,EAAE;wBACxB,IAAItB,UAAU,CAACsB,IAAI,CAAC,EAAE;0BACpB;0BACA,IAAIN,IAAI,CAACO,QAAQ,IAAI,CAACD,IAAI,CAACE,kBAAkB,EAAE;4BAC7CC,MAAM,CAACC,gBAAgB,CAACJ,IAAI,EAAE;8BAC5BE,kBAAkB,EAAE;gCAClBG,QAAQ,EAAE;8BACZ;4BACF,CAAC,CAAC;4BACF;4BACAL,IAAI,CAACE,kBAAkB,GAAGR,IAAI,CAACO,QAAQ,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;4BAC1DH,MAAM,CAACC,gBAAgB,CAACJ,IAAI,EAAE;8BAC5BE,kBAAkB,EAAE;gCAClBG,QAAQ,EAAE;8BACZ;4BACF,CAAC,CAAC;0BACJ;0BACAN,OAAO,CAACC,IAAI,CAAC;wBACf,CAAC,MAAM;0BACLD,OAAO,CAAC,IAAI,CAAC;wBACf;sBACF,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC;kBACL,KAAK,CAAC;kBACN,KAAK,KAAK;oBACR,OAAOH,SAAS,CAACW,IAAI,CAAC,CAAC;gBAC3B;cACF,CAAC,EAAEd,QAAQ,CAAC;YACd,CAAC,CAAC,CAAC;YACH,OAAOT,SAAS,CAACwB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UACzC,CAAC;UACD1B,QAAQ,GAAG,SAAS2B,UAAUA,CAACC,GAAG,EAAE;YAClC,OAAO3B,SAAS,CAACwB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UACzC,CAAC;UACD3B,cAAc,GAAG,SAAS8B,eAAeA,CAAA,EAAG;YAC1C9B,cAAc,GAAGV,iBAAiB,CAAE,aAAaF,mBAAmB,CAAC,CAAC,CAACK,IAAI,CAAC,SAASsC,QAAQA,CAACC,SAAS,EAAE;cACvG,IAAIC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,CAAC,EAAEC,CAAC;cACrC,OAAOjD,mBAAmB,CAAC,CAAC,CAACiB,IAAI,CAAC,SAASiC,SAASA,CAACC,SAAS,EAAE;gBAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAAC/B,IAAI,GAAG+B,SAAS,CAAC9B,IAAI;kBAC/C,KAAK,CAAC;oBACJwB,SAAS,GAAGD,SAAS,CAACQ,YAAY,CAAC,CAAC;oBACpCN,OAAO,GAAG,EAAE;kBACd,KAAK,CAAC;oBACJ,IAAI,CAAC,IAAI,EAAE;sBACTK,SAAS,CAAC9B,IAAI,GAAG,EAAE;sBACnB;oBACF;oBACA8B,SAAS,CAAC9B,IAAI,GAAG,CAAC;oBAClB,OAAO,IAAIO,OAAO,CAAC,UAAUyB,OAAO,EAAE;sBACpCR,SAAS,CAACS,WAAW,CAACD,OAAO,EAAE,YAAY;wBACzC,OAAOA,OAAO,CAAC,EAAE,CAAC;sBACpB,CAAC,CAAC;oBACJ,CAAC,CAAC;kBACJ,KAAK,CAAC;oBACJN,OAAO,GAAGI,SAAS,CAACI,IAAI;oBACxBP,CAAC,GAAGD,OAAO,CAACS,MAAM;oBAClB,IAAIR,CAAC,EAAE;sBACLG,SAAS,CAAC9B,IAAI,GAAG,CAAC;sBAClB;oBACF;oBACA,OAAO8B,SAAS,CAACxB,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;kBACtC,KAAK,CAAC;oBACJ,KAAKsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;sBACtBH,OAAO,CAACW,IAAI,CAACV,OAAO,CAACE,CAAC,CAAC,CAAC;oBAC1B;oBACAE,SAAS,CAAC9B,IAAI,GAAG,CAAC;oBAClB;kBACF,KAAK,EAAE;oBACL,OAAO8B,SAAS,CAACxB,MAAM,CAAC,QAAQ,EAAEmB,OAAO,CAAC;kBAC5C,KAAK,EAAE;kBACP,KAAK,KAAK;oBACR,OAAOK,SAAS,CAACd,IAAI,CAAC,CAAC;gBAC3B;cACF,CAAC,EAAEM,QAAQ,CAAC;YACd,CAAC,CAAC,CAAC;YACH,OAAO/B,cAAc,CAAC0B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAC9C,CAAC;UACD5B,aAAa,GAAG,SAAS+C,eAAeA,CAACC,GAAG,EAAE;YAC5C,OAAO/C,cAAc,CAAC0B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAC9C,CAAC;UACD9B,eAAe,GAAG,EAAE;UACpBC,gBAAgB,GAAG,EAAE;UACrBH,KAAK,CAACqD,OAAO,CAAC,UAAU9B,IAAI,EAAE;YAC5B,OAAOpB,gBAAgB,CAAC+C,IAAI,CAAC3B,IAAI,CAAC+B,gBAAgB,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC;;UAEF;UACA9C,iBAAiB,GAAG,aAAa,YAAY;YAC3C,IAAI+C,KAAK,GAAG5D,iBAAiB,CAAE,aAAaF,mBAAmB,CAAC,CAAC,CAACK,IAAI,CAAC,SAAS0D,OAAOA,CAACvC,IAAI,EAAEwC,IAAI,EAAE;cAClG,IAAIC,KAAK,EAAEnB,OAAO;cAClB,OAAO9C,mBAAmB,CAAC,CAAC,CAACiB,IAAI,CAAC,SAASiD,QAAQA,CAACC,QAAQ,EAAE;gBAC5D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAAC/C,IAAI,GAAG+C,QAAQ,CAAC9C,IAAI;kBAC7C,KAAK,CAAC;oBACJ,IAAIG,IAAI,EAAE;sBACR2C,QAAQ,CAAC9C,IAAI,GAAG,CAAC;sBACjB;oBACF;oBACA,OAAO8C,QAAQ,CAACxC,MAAM,CAAC,QAAQ,CAAC;kBAClC,KAAK,CAAC;oBACJ;oBACAH,IAAI,CAACwC,IAAI,GAAGA,IAAI,IAAI,EAAE;oBACtB,IAAI,CAACxC,IAAI,CAAC4C,MAAM,EAAE;sBAChBD,QAAQ,CAAC9C,IAAI,GAAG,EAAE;sBAClB;oBACF;oBACA8C,QAAQ,CAAC9C,IAAI,GAAG,CAAC;oBACjB,OAAOR,QAAQ,CAACW,IAAI,CAAC;kBACvB,KAAK,CAAC;oBACJyC,KAAK,GAAGE,QAAQ,CAACZ,IAAI;oBACrB,IAAIU,KAAK,EAAE;sBACTxD,eAAe,CAACgD,IAAI,CAACQ,KAAK,CAAC;oBAC7B;oBACAE,QAAQ,CAAC9C,IAAI,GAAG,EAAE;oBAClB;kBACF,KAAK,EAAE;oBACL,IAAI,CAACG,IAAI,CAAC6C,WAAW,EAAE;sBACrBF,QAAQ,CAAC9C,IAAI,GAAG,EAAE;sBAClB;oBACF;oBACA8C,QAAQ,CAAC9C,IAAI,GAAG,EAAE;oBAClB,OAAOV,aAAa,CAACa,IAAI,CAAC;kBAC5B,KAAK,EAAE;oBACLsB,OAAO,GAAGqB,QAAQ,CAACZ,IAAI;oBACvB7C,gBAAgB,CAAC+C,IAAI,CAACnB,KAAK,CAAC5B,gBAAgB,EAAET,kBAAkB,CAAC6C,OAAO,CAAC,CAAC;kBAC5E,KAAK,EAAE;kBACP,KAAK,KAAK;oBACR,OAAOqB,QAAQ,CAAC9B,IAAI,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE0B,OAAO,CAAC;YACb,CAAC,CAAC,CAAC;YACH,OAAO,SAAShD,iBAAiBA,CAACuD,GAAG,EAAEC,GAAG,EAAE;cAC1C,OAAOT,KAAK,CAACxB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;YACrC,CAAC;UACH,CAAC,CAAC,CAAC;UACHvB,QAAQ,GAAG,CAAC;QACd,KAAK,CAAC;UACJ,IAAI,EAAEA,QAAQ,GAAGN,gBAAgB,CAAC8C,MAAM,CAAC,EAAE;YACzCrC,SAAS,CAACE,IAAI,GAAG,EAAE;YACnB;UACF;UACAF,SAAS,CAACE,IAAI,GAAG,EAAE;UACnB,OAAON,iBAAiB,CAACL,gBAAgB,CAACM,QAAQ,CAAC,CAAC;QACtD,KAAK,EAAE;UACLA,QAAQ,EAAE;UACVG,SAAS,CAACE,IAAI,GAAG,CAAC;UAClB;QACF,KAAK,EAAE;UACL,OAAOF,SAAS,CAACQ,MAAM,CAAC,QAAQ,EAAElB,eAAe,CAAC;QACpD,KAAK,EAAE;QACP,KAAK,KAAK;UACR,OAAOU,SAAS,CAACkB,IAAI,CAAC,CAAC;MAC3B;IACF,CAAC,EAAE/B,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;EACH,OAAO,SAASH,gBAAgBA,CAACqE,EAAE,EAAEC,GAAG,EAAE;IACxC,OAAOrE,IAAI,CAACkC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACpC,CAAC;AACH,CAAC,CAAC,CAAC;AACH,eAAepC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"],\n  _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction convertNodeToOption(node) {\n  var _ref = node,\n    key = _ref.key,\n    _ref$props = _ref.props,\n    children = _ref$props.children,\n    value = _ref$props.value,\n    restProps = _objectWithoutProperties(_ref$props, _excluded);\n  return _objectSpread({\n    key: key,\n    value: value !== undefined ? value : key,\n    children: children\n  }, restProps);\n}\nexport function convertChildrenToData(nodes) {\n  var optionOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return toArray(nodes).map(function (node, index) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref2 = node,\n      isSelectOptGroup = _ref2.type.isSelectOptGroup,\n      key = _ref2.key,\n      _ref2$props = _ref2.props,\n      children = _ref2$props.children,\n      restProps = _objectWithoutProperties(_ref2$props, _excluded2);\n    if (optionOnly || !isSelectOptGroup) {\n      return convertNodeToOption(node);\n    }\n    return _objectSpread(_objectSpread({\n      key: \"__RC_SELECT_GRP__\".concat(key === null ? index : key, \"__\"),\n      label: key\n    }, restProps), {}, {\n      options: convertChildrenToData(children)\n    });\n  }).filter(function (data) {\n    return data;\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "toArray", "convertNodeToOption", "node", "_ref", "key", "_ref$props", "props", "children", "value", "restProps", "undefined", "convertChildrenToData", "nodes", "optionOnly", "arguments", "length", "map", "index", "isValidElement", "type", "_ref2", "isSelectOptGroup", "_ref2$props", "concat", "label", "options", "filter", "data"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-select@14.16.8_react-dom_dcba6f14d7eb7e8a7564f8966e06ae09/node_modules/rc-select/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"],\n  _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction convertNodeToOption(node) {\n  var _ref = node,\n    key = _ref.key,\n    _ref$props = _ref.props,\n    children = _ref$props.children,\n    value = _ref$props.value,\n    restProps = _objectWithoutProperties(_ref$props, _excluded);\n  return _objectSpread({\n    key: key,\n    value: value !== undefined ? value : key,\n    children: children\n  }, restProps);\n}\nexport function convertChildrenToData(nodes) {\n  var optionOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return toArray(nodes).map(function (node, index) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref2 = node,\n      isSelectOptGroup = _ref2.type.isSelectOptGroup,\n      key = _ref2.key,\n      _ref2$props = _ref2.props,\n      children = _ref2$props.children,\n      restProps = _objectWithoutProperties(_ref2$props, _excluded2);\n    if (optionOnly || !isSelectOptGroup) {\n      return convertNodeToOption(node);\n    }\n    return _objectSpread(_objectSpread({\n      key: \"__RC_SELECT_GRP__\".concat(key === null ? index : key, \"__\"),\n      label: key\n    }, restProps), {}, {\n      options: convertChildrenToData(children)\n    });\n  }).filter(function (data) {\n    return data;\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;EACnCC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,IAAIC,IAAI,GAAGD,IAAI;IACbE,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,UAAU,GAAGF,IAAI,CAACG,KAAK;IACvBC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;IAC9BC,KAAK,GAAGH,UAAU,CAACG,KAAK;IACxBC,SAAS,GAAGb,wBAAwB,CAACS,UAAU,EAAER,SAAS,CAAC;EAC7D,OAAOF,aAAa,CAAC;IACnBS,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAEA,KAAK,KAAKE,SAAS,GAAGF,KAAK,GAAGJ,GAAG;IACxCG,QAAQ,EAAEA;EACZ,CAAC,EAAEE,SAAS,CAAC;AACf;AACA,OAAO,SAASE,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKJ,SAAS,GAAGI,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1F,OAAOd,OAAO,CAACY,KAAK,CAAC,CAACI,GAAG,CAAC,UAAUd,IAAI,EAAEe,KAAK,EAAE;IAC/C,IAAI,EAAE,aAAalB,KAAK,CAACmB,cAAc,CAAChB,IAAI,CAAC,IAAI,CAACA,IAAI,CAACiB,IAAI,EAAE;MAC3D,OAAO,IAAI;IACb;IACA,IAAIC,KAAK,GAAGlB,IAAI;MACdmB,gBAAgB,GAAGD,KAAK,CAACD,IAAI,CAACE,gBAAgB;MAC9CjB,GAAG,GAAGgB,KAAK,CAAChB,GAAG;MACfkB,WAAW,GAAGF,KAAK,CAACd,KAAK;MACzBC,QAAQ,GAAGe,WAAW,CAACf,QAAQ;MAC/BE,SAAS,GAAGb,wBAAwB,CAAC0B,WAAW,EAAExB,UAAU,CAAC;IAC/D,IAAIe,UAAU,IAAI,CAACQ,gBAAgB,EAAE;MACnC,OAAOpB,mBAAmB,CAACC,IAAI,CAAC;IAClC;IACA,OAAOP,aAAa,CAACA,aAAa,CAAC;MACjCS,GAAG,EAAE,mBAAmB,CAACmB,MAAM,CAACnB,GAAG,KAAK,IAAI,GAAGa,KAAK,GAAGb,GAAG,EAAE,IAAI,CAAC;MACjEoB,KAAK,EAAEpB;IACT,CAAC,EAAEK,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjBgB,OAAO,EAAEd,qBAAqB,CAACJ,QAAQ;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC,CAACmB,MAAM,CAAC,UAAUC,IAAI,EAAE;IACxB,OAAOA,IAAI;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
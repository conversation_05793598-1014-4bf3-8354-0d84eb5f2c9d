{"ast": null, "code": "const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;", "map": {"version": 3, "names": ["genCollapseMotion", "token", "componentCls", "antCls", "overflow", "transition", "motionDurationMid", "motionEaseInOut"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/style/motion/collapse.js"], "sourcesContent": ["const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGC,KAAK,KAAK;EAClC,CAACA,KAAK,CAACC,YAAY,GAAG;IACpB;IACA,CAAC,GAAGD,KAAK,CAACE,MAAM,yBAAyB,GAAG;MAC1CC,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE;QACVC,UAAU,EAAE,UAAUJ,KAAK,CAACK,iBAAiB,IAAIL,KAAK,CAACM,eAAe;AAC9E,kBAAkBN,KAAK,CAACK,iBAAiB,IAAIL,KAAK,CAACM,eAAe;MAC5D;IACF,CAAC;IACD,CAAC,GAAGN,KAAK,CAACE,MAAM,kBAAkB,GAAG;MACnCC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,UAAUJ,KAAK,CAACK,iBAAiB,IAAIL,KAAK,CAACM,eAAe;AAC5E,kBAAkBN,KAAK,CAACK,iBAAiB,IAAIL,KAAK,CAACM,eAAe;IAC9D;EACF;AACF,CAAC,CAAC;AACF,eAAeP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
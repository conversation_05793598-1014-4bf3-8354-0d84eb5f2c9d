{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst getRTLStyle = ({\n  componentCls,\n  menuArrowOffset,\n  calc\n}) => ({\n  [`${componentCls}-rtl`]: {\n    direction: 'rtl'\n  },\n  [`${componentCls}-submenu-rtl`]: {\n    transformOrigin: '100% 0'\n  },\n  // Vertical Arrow\n  [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n    [`${componentCls}-submenu-arrow`]: {\n      '&::before': {\n        transform: `rotate(-45deg) translateY(${unit(calc(menuArrowOffset).mul(-1).equal())})`\n      },\n      '&::after': {\n        transform: `rotate(45deg) translateY(${unit(menuArrowOffset)})`\n      }\n    }\n  }\n});\nexport default getRTLStyle;", "map": {"version": 3, "names": ["unit", "getRTLStyle", "componentCls", "menuArrowOffset", "calc", "direction", "transform<PERSON><PERSON>in", "transform", "mul", "equal"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/menu/style/rtl.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst getRTLStyle = ({\n  componentCls,\n  menuArrowOffset,\n  calc\n}) => ({\n  [`${componentCls}-rtl`]: {\n    direction: 'rtl'\n  },\n  [`${componentCls}-submenu-rtl`]: {\n    transformOrigin: '100% 0'\n  },\n  // Vertical Arrow\n  [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n    [`${componentCls}-submenu-arrow`]: {\n      '&::before': {\n        transform: `rotate(-45deg) translateY(${unit(calc(menuArrowOffset).mul(-1).equal())})`\n      },\n      '&::after': {\n        transform: `rotate(45deg) translateY(${unit(menuArrowOffset)})`\n      }\n    }\n  }\n});\nexport default getRTLStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,WAAW,GAAGA,CAAC;EACnBC,YAAY;EACZC,eAAe;EACfC;AACF,CAAC,MAAM;EACL,CAAC,GAAGF,YAAY,MAAM,GAAG;IACvBG,SAAS,EAAE;EACb,CAAC;EACD,CAAC,GAAGH,YAAY,cAAc,GAAG;IAC/BI,eAAe,EAAE;EACnB,CAAC;EACD;EACA,CAAC,GAAGJ,YAAY,OAAOA,YAAY;AACrC,MAAMA,YAAY,gBAAgBA,YAAY,WAAW,GAAG;IACxD,CAAC,GAAGA,YAAY,gBAAgB,GAAG;MACjC,WAAW,EAAE;QACXK,SAAS,EAAE,6BAA6BP,IAAI,CAACI,IAAI,CAACD,eAAe,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MACrF,CAAC;MACD,UAAU,EAAE;QACVF,SAAS,EAAE,4BAA4BP,IAAI,CAACG,eAAe,CAAC;MAC9D;IACF;EACF;AACF,CAAC,CAAC;AACF,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
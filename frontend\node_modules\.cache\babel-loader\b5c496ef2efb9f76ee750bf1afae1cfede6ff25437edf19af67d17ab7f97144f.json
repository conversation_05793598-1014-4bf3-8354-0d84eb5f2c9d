{"ast": null, "code": "import React from 'react';\nexport const PanelPickerContext = /*#__PURE__*/React.createContext({});\nexport const PanelPresetsContext = /*#__PURE__*/React.createContext({});", "map": {"version": 3, "names": ["React", "PanelPickerContext", "createContext", "PanelPresetsContext"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/color-picker/context.js"], "sourcesContent": ["import React from 'react';\nexport const PanelPickerContext = /*#__PURE__*/React.createContext({});\nexport const PanelPresetsContext = /*#__PURE__*/React.createContext({});"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACtE,OAAO,MAAMC,mBAAmB,GAAG,aAAaH,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport ListContext from \"./ListContext\";\nimport { toArray } from \"./utils/typeUtil\";\nimport { validateRules } from \"./utils/validateUtil\";\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from \"./utils/valueUtil\";\nvar EMPTY_ERRORS = [];\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n  return prevValue !== nextValue;\n}\n\n// eslint-disable-next-line @typescript-eslint/consistent-indexed-object-style\n// We use Class instead of Hooks here since it will cost much code by using Hooks.\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n  var _super = _createSuper(Field);\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n    _classCallCheck(this, Field);\n    _this = _super.call(this, props);\n\n    // Register on init\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      resetCount: 0\n    });\n    _defineProperty(_assertThisInitialized(_this), \"cancelRegisterFunc\", null);\n    _defineProperty(_assertThisInitialized(_this), \"mounted\", false);\n    /**\n     * Follow state should not management in State since it will async update by React.\n     * This makes first render of form can not get correct state value.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"touched\", false);\n    /**\n     * Mark when touched & validated. Currently only used for `dependencies`.\n     * Note that we do not think field with `initialValue` is dirty\n     * but this will be by `isFieldDirty` func.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"dirty\", false);\n    _defineProperty(_assertThisInitialized(_this), \"validatePromise\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"prevValidating\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"errors\", EMPTY_ERRORS);\n    _defineProperty(_assertThisInitialized(_this), \"warnings\", EMPTY_ERRORS);\n    _defineProperty(_assertThisInitialized(_this), \"cancelRegister\", function () {\n      var _this$props = _this.props,\n        preserve = _this$props.preserve,\n        isListField = _this$props.isListField,\n        name = _this$props.name;\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n      _this.cancelRegisterFunc = null;\n    });\n    // ================================== Utils ==================================\n    _defineProperty(_assertThisInitialized(_this), \"getNamePath\", function () {\n      var _this$props2 = _this.props,\n        name = _this$props2.name,\n        fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n        prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getRules\", function () {\n      var _this$props3 = _this.props,\n        _this$props3$rules = _this$props3.rules,\n        rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n        fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n        return rule;\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"refresh\", function () {\n      if (!_this.mounted) return;\n\n      /**\n       * Clean up current node.\n       */\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    });\n    // Event should only trigger when meta changed\n    _defineProperty(_assertThisInitialized(_this), \"metaCache\", null);\n    _defineProperty(_assertThisInitialized(_this), \"triggerMetaEvent\", function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      if (onMetaChange) {\n        var _meta = _objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n          destroy: destroy\n        });\n        if (!isEqual(_this.metaCache, _meta)) {\n          onMetaChange(_meta);\n        }\n        _this.metaCache = _meta;\n      } else {\n        _this.metaCache = null;\n      }\n    });\n    // ========================= Field Entity Interfaces =========================\n    // Trigger by store update. Check if need update the component\n    _defineProperty(_assertThisInitialized(_this), \"onStoreChange\", function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n        shouldUpdate = _this$props4.shouldUpdate,\n        _this$props4$dependen = _this$props4.dependencies,\n        dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n        onReset = _this$props4.onReset;\n      var store = info.store;\n      var namePath = _this.getNamePath();\n      var prevValue = _this.getValue(prevStore);\n      var curValue = _this.getValue(store);\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath);\n\n      // `setFieldsValue` is a quick access to update related status\n      if (info.type === 'valueUpdate' && info.source === 'external' && !isEqual(prevValue, curValue)) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n        _this.triggerMetaEvent();\n      }\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = undefined;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n            _this.triggerMetaEvent();\n            onReset === null || onReset === void 0 || onReset();\n            _this.refresh();\n            return;\n          }\n          break;\n\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n        case 'remove':\n          {\n            if (shouldUpdate && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'setField':\n          {\n            var data = info.data;\n            if (namePathMatch) {\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n              _this.dirty = true;\n              _this.triggerMetaEvent();\n              _this.reRender();\n              return;\n            } else if ('value' in data && containsNamePath(namePathList, namePath, true)) {\n              // Contains path with value should also check\n              _this.reRender();\n              return;\n            }\n\n            // Handle update by `setField` with `shouldUpdate`\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath);\n            // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n            return;\n          }\n          break;\n      }\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"validateRules\", function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n      var currentValue = _this.getValue();\n      var _ref2 = options || {},\n        triggerName = _ref2.triggerName,\n        _ref2$validateOnly = _ref2.validateOnly,\n        validateOnly = _ref2$validateOnly === void 0 ? false : _ref2$validateOnly;\n\n      // Force change to async to avoid rule OOD under renderProps field\n      var rootPromise = Promise.resolve().then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _this$props5, _this$props5$validate, validateFirst, messageVariables, validateDebounce, filteredRules, promise;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (_this.mounted) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\", []);\n            case 2:\n              _this$props5 = _this.props, _this$props5$validate = _this$props5.validateFirst, validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate, messageVariables = _this$props5.messageVariables, validateDebounce = _this$props5.validateDebounce; // Start validate\n              filteredRules = _this.getRules();\n              if (triggerName) {\n                filteredRules = filteredRules.filter(function (rule) {\n                  return rule;\n                }).filter(function (rule) {\n                  var validateTrigger = rule.validateTrigger;\n                  if (!validateTrigger) {\n                    return true;\n                  }\n                  var triggerList = toArray(validateTrigger);\n                  return triggerList.includes(triggerName);\n                });\n              }\n\n              // Wait for debounce. Skip if no `triggerName` since its from `validateFields / submit`\n              if (!(validateDebounce && triggerName)) {\n                _context.next = 10;\n                break;\n              }\n              _context.next = 8;\n              return new Promise(function (resolve) {\n                setTimeout(resolve, validateDebounce);\n              });\n            case 8:\n              if (!(_this.validatePromise !== rootPromise)) {\n                _context.next = 10;\n                break;\n              }\n              return _context.abrupt(\"return\", []);\n            case 10:\n              promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n              promise.catch(function (e) {\n                return e;\n              }).then(function () {\n                var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n                if (_this.validatePromise === rootPromise) {\n                  var _ruleErrors$forEach;\n                  _this.validatePromise = null;\n\n                  // Get errors & warnings\n                  var nextErrors = [];\n                  var nextWarnings = [];\n                  (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 || _ruleErrors$forEach.call(ruleErrors, function (_ref4) {\n                    var warningOnly = _ref4.rule.warningOnly,\n                      _ref4$errors = _ref4.errors,\n                      errors = _ref4$errors === void 0 ? EMPTY_ERRORS : _ref4$errors;\n                    if (warningOnly) {\n                      nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n                    } else {\n                      nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n                    }\n                  });\n                  _this.errors = nextErrors;\n                  _this.warnings = nextWarnings;\n                  _this.triggerMetaEvent();\n                  _this.reRender();\n                }\n              });\n              return _context.abrupt(\"return\", promise);\n            case 13:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      })));\n      if (validateOnly) {\n        return rootPromise;\n      }\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n      _this.triggerMetaEvent();\n\n      // Force trigger re-render since we need sync renderProps with new meta\n      _this.reRender();\n      return rootPromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldValidating\", function () {\n      return !!_this.validatePromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldTouched\", function () {\n      return _this.touched;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldDirty\", function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      }\n\n      // Form set initialValue\n      var fieldContext = _this.props.fieldContext;\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n        getInitialValue = _fieldContext$getInte.getInitialValue;\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n      return false;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getErrors\", function () {\n      return _this.errors;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getWarnings\", function () {\n      return _this.warnings;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isListField\", function () {\n      return _this.props.isListField;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isList\", function () {\n      return _this.props.isList;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isPreserve\", function () {\n      return _this.props.preserve;\n    });\n    // ============================= Child Component =============================\n    _defineProperty(_assertThisInitialized(_this), \"getMeta\", function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath(),\n        validated: _this.validatePromise === null\n      };\n      return meta;\n    });\n    // Only return validate child node. If invalidate, will do nothing about field.\n    _defineProperty(_assertThisInitialized(_this), \"getOnlyChild\", function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var _meta2 = _this.getMeta();\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), _meta2, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      }\n\n      // Filed element only\n      var childList = toChildrenArray(children);\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    });\n    // ============================== Field Control ==============================\n    _defineProperty(_assertThisInitialized(_this), \"getValue\", function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n      var namePath = _this.getNamePath();\n      return getValue(store || getFieldsValue(true), namePath);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getControlled\", function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n        name = _this$props6.name,\n        trigger = _this$props6.trigger,\n        validateTrigger = _this$props6.validateTrigger,\n        getValueFromEvent = _this$props6.getValueFromEvent,\n        normalize = _this$props6.normalize,\n        valuePropName = _this$props6.valuePropName,\n        getValueProps = _this$props6.getValueProps,\n        fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n      var namePath = _this.getNamePath();\n      var getInternalHooks = fieldContext.getInternalHooks,\n        getFieldsValue = fieldContext.getFieldsValue;\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        dispatch = _getInternalHooks.dispatch;\n      var value = _this.getValue();\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      };\n      var originTriggerFunc = childProps[trigger];\n      var valueProps = name !== undefined ? mergedGetValueProps(value) : {};\n\n      // warning when prop value is function\n      if (process.env.NODE_ENV !== 'production' && valueProps) {\n        Object.keys(valueProps).forEach(function (key) {\n          warning(typeof valueProps[key] !== 'function', \"It's not recommended to generate dynamic function prop by `getValueProps`. Please pass it to child component directly (prop: \".concat(key, \")\"));\n        });\n      }\n      var control = _objectSpread(_objectSpread({}, childProps), valueProps);\n\n      // Add trigger\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n        _this.triggerMetaEvent();\n        var newValue;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n        if (newValue !== value) {\n          dispatch({\n            type: 'updateValue',\n            namePath: namePath,\n            value: newValue\n          });\n        }\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      };\n\n      // Add validateTrigger\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          }\n\n          // Always use latest rules\n          var rules = _this.props.rules;\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    });\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n        initEntityValue = _getInternalHooks2.initEntityValue;\n      initEntityValue(_assertThisInitialized(_this));\n    }\n    return _this;\n  }\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n        shouldUpdate = _this$props7.shouldUpdate,\n        fieldContext = _this$props7.fieldContext;\n      this.mounted = true;\n\n      // Register on init\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n          registerField = _getInternalHooks3.registerField;\n        this.cancelRegisterFunc = registerField(this);\n      }\n\n      // One more render for component in case fields not ready\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n      var _this$getOnlyChild = this.getOnlyChild(children),\n        child = _this$getOnlyChild.child,\n        isFunction = _this$getOnlyChild.isFunction;\n\n      // Not need to `cloneElement` since user can handle this in render function self\n      var returnChildNode;\n      if (isFunction) {\n        returnChildNode = child;\n      } else if (/*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n  return Field;\n}(React.Component);\n_defineProperty(Field, \"contextType\", FieldContext);\n_defineProperty(Field, \"defaultProps\", {\n  trigger: 'onChange',\n  valuePropName: 'value'\n});\nfunction WrapperField(_ref6) {\n  var _restProps$isListFiel;\n  var name = _ref6.name,\n    restProps = _objectWithoutProperties(_ref6, _excluded);\n  var fieldContext = React.useContext(FieldContext);\n  var listContext = React.useContext(ListContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var isMergedListField = (_restProps$isListFiel = restProps.isListField) !== null && _restProps$isListFiel !== void 0 ? _restProps$isListFiel : !!listContext;\n  var key = 'keep';\n  if (!isMergedListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  }\n\n  // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && isMergedListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath,\n    isListField: isMergedListField\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\nexport default WrapperField;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_regeneratorRuntime", "_asyncToGenerator", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "_excluded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEqual", "warning", "React", "FieldContext", "HOOK_MARK", "ListContext", "toArray", "validateRules", "containsNamePath", "defaultGetValueFromEvent", "getNamePath", "getValue", "EMPTY_ERRORS", "requireUpdate", "shouldUpdate", "prev", "next", "prevValue", "nextValue", "info", "source", "Field", "_React$Component", "_super", "props", "_this", "call", "resetCount", "_this$props", "preserve", "isListField", "name", "cancelRegisterFunc", "_this$props2", "fieldContext", "_fieldContext$prefixN", "prefixName", "undefined", "concat", "_this$props3", "_this$props3$rules", "rules", "map", "rule", "mounted", "setState", "_ref", "destroy", "onMetaChange", "_meta", "getMeta", "metaCache", "prevStore", "namePathList", "_this$props4", "_this$props4$dependen", "dependencies", "onReset", "store", "namePath", "curValue", "namePathMatch", "type", "touched", "dirty", "validatePromise", "errors", "warnings", "triggerMetaEvent", "refresh", "reRender", "data", "validating", "Promise", "resolve", "length", "dependencyList", "some", "dependency", "relatedFields", "options", "currentValue", "_ref2", "triggerName", "_ref2$validateOnly", "validateOnly", "rootPromise", "then", "mark", "_callee", "_this$props5", "_this$props5$validate", "validate<PERSON><PERSON><PERSON>", "messageVariables", "validateDebounce", "filteredRules", "promise", "wrap", "_callee$", "_context", "abrupt", "getRules", "filter", "validate<PERSON><PERSON>ger", "triggerList", "includes", "setTimeout", "catch", "e", "ruleErrors", "arguments", "_ruleErrors$forEach", "nextErrors", "nextWarnings", "for<PERSON>ach", "_ref4", "warningOnly", "_ref4$errors", "push", "apply", "stop", "initialValue", "_fieldContext$getInte", "getInternalHooks", "getInitialValue", "isList", "prevValidating", "isFieldValidating", "meta", "isFieldTouched", "validated", "children", "_meta2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getControlled", "isFunction", "childList", "isValidElement", "child", "getFieldsValue", "childProps", "_this$props6", "trigger", "getValueFromEvent", "normalize", "valuePropName", "getValueProps", "mergedValidateTrigger", "_getInternalHooks", "dispatch", "value", "mergedGetValueProps", "val", "originTriggerFunc", "valueProps", "process", "env", "NODE_ENV", "Object", "keys", "key", "control", "newValue", "_len", "args", "Array", "_key", "validateTriggerList", "originTrigger", "_getInternalHooks2", "initEntityValue", "componentDidMount", "_this$props7", "_getInternalHooks3", "registerField", "componentWillUnmount", "cancelRegister", "forceUpdate", "render", "state", "_this$getOnlyChild", "returnChildNode", "cloneElement", "createElement", "Fragment", "Component", "WrapperField", "_ref6", "_restProps$isListFiel", "restProps", "useContext", "listContext", "isMergedListField", "join"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-field-form@2.7.0_react-d_64f5b93340a107ccce19d223ada77c46/node_modules/rc-field-form/es/Field.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport ListContext from \"./ListContext\";\nimport { toArray } from \"./utils/typeUtil\";\nimport { validateRules } from \"./utils/validateUtil\";\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from \"./utils/valueUtil\";\nvar EMPTY_ERRORS = [];\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n  return prevValue !== nextValue;\n}\n\n// eslint-disable-next-line @typescript-eslint/consistent-indexed-object-style\n// We use Class instead of Hooks here since it will cost much code by using Hooks.\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n  var _super = _createSuper(Field);\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n    _classCallCheck(this, Field);\n    _this = _super.call(this, props);\n\n    // Register on init\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      resetCount: 0\n    });\n    _defineProperty(_assertThisInitialized(_this), \"cancelRegisterFunc\", null);\n    _defineProperty(_assertThisInitialized(_this), \"mounted\", false);\n    /**\n     * Follow state should not management in State since it will async update by React.\n     * This makes first render of form can not get correct state value.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"touched\", false);\n    /**\n     * Mark when touched & validated. Currently only used for `dependencies`.\n     * Note that we do not think field with `initialValue` is dirty\n     * but this will be by `isFieldDirty` func.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"dirty\", false);\n    _defineProperty(_assertThisInitialized(_this), \"validatePromise\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"prevValidating\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"errors\", EMPTY_ERRORS);\n    _defineProperty(_assertThisInitialized(_this), \"warnings\", EMPTY_ERRORS);\n    _defineProperty(_assertThisInitialized(_this), \"cancelRegister\", function () {\n      var _this$props = _this.props,\n        preserve = _this$props.preserve,\n        isListField = _this$props.isListField,\n        name = _this$props.name;\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n      _this.cancelRegisterFunc = null;\n    });\n    // ================================== Utils ==================================\n    _defineProperty(_assertThisInitialized(_this), \"getNamePath\", function () {\n      var _this$props2 = _this.props,\n        name = _this$props2.name,\n        fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n        prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getRules\", function () {\n      var _this$props3 = _this.props,\n        _this$props3$rules = _this$props3.rules,\n        rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n        fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n        return rule;\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"refresh\", function () {\n      if (!_this.mounted) return;\n\n      /**\n       * Clean up current node.\n       */\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    });\n    // Event should only trigger when meta changed\n    _defineProperty(_assertThisInitialized(_this), \"metaCache\", null);\n    _defineProperty(_assertThisInitialized(_this), \"triggerMetaEvent\", function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      if (onMetaChange) {\n        var _meta = _objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n          destroy: destroy\n        });\n        if (!isEqual(_this.metaCache, _meta)) {\n          onMetaChange(_meta);\n        }\n        _this.metaCache = _meta;\n      } else {\n        _this.metaCache = null;\n      }\n    });\n    // ========================= Field Entity Interfaces =========================\n    // Trigger by store update. Check if need update the component\n    _defineProperty(_assertThisInitialized(_this), \"onStoreChange\", function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n        shouldUpdate = _this$props4.shouldUpdate,\n        _this$props4$dependen = _this$props4.dependencies,\n        dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n        onReset = _this$props4.onReset;\n      var store = info.store;\n      var namePath = _this.getNamePath();\n      var prevValue = _this.getValue(prevStore);\n      var curValue = _this.getValue(store);\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath);\n\n      // `setFieldsValue` is a quick access to update related status\n      if (info.type === 'valueUpdate' && info.source === 'external' && !isEqual(prevValue, curValue)) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n        _this.triggerMetaEvent();\n      }\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = undefined;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n            _this.triggerMetaEvent();\n            onReset === null || onReset === void 0 || onReset();\n            _this.refresh();\n            return;\n          }\n          break;\n\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n        case 'remove':\n          {\n            if (shouldUpdate && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'setField':\n          {\n            var data = info.data;\n            if (namePathMatch) {\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n              _this.dirty = true;\n              _this.triggerMetaEvent();\n              _this.reRender();\n              return;\n            } else if ('value' in data && containsNamePath(namePathList, namePath, true)) {\n              // Contains path with value should also check\n              _this.reRender();\n              return;\n            }\n\n            // Handle update by `setField` with `shouldUpdate`\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath);\n            // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n            return;\n          }\n          break;\n      }\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"validateRules\", function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n      var currentValue = _this.getValue();\n      var _ref2 = options || {},\n        triggerName = _ref2.triggerName,\n        _ref2$validateOnly = _ref2.validateOnly,\n        validateOnly = _ref2$validateOnly === void 0 ? false : _ref2$validateOnly;\n\n      // Force change to async to avoid rule OOD under renderProps field\n      var rootPromise = Promise.resolve().then( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _this$props5, _this$props5$validate, validateFirst, messageVariables, validateDebounce, filteredRules, promise;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (_this.mounted) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\", []);\n            case 2:\n              _this$props5 = _this.props, _this$props5$validate = _this$props5.validateFirst, validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate, messageVariables = _this$props5.messageVariables, validateDebounce = _this$props5.validateDebounce; // Start validate\n              filteredRules = _this.getRules();\n              if (triggerName) {\n                filteredRules = filteredRules.filter(function (rule) {\n                  return rule;\n                }).filter(function (rule) {\n                  var validateTrigger = rule.validateTrigger;\n                  if (!validateTrigger) {\n                    return true;\n                  }\n                  var triggerList = toArray(validateTrigger);\n                  return triggerList.includes(triggerName);\n                });\n              }\n\n              // Wait for debounce. Skip if no `triggerName` since its from `validateFields / submit`\n              if (!(validateDebounce && triggerName)) {\n                _context.next = 10;\n                break;\n              }\n              _context.next = 8;\n              return new Promise(function (resolve) {\n                setTimeout(resolve, validateDebounce);\n              });\n            case 8:\n              if (!(_this.validatePromise !== rootPromise)) {\n                _context.next = 10;\n                break;\n              }\n              return _context.abrupt(\"return\", []);\n            case 10:\n              promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n              promise.catch(function (e) {\n                return e;\n              }).then(function () {\n                var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n                if (_this.validatePromise === rootPromise) {\n                  var _ruleErrors$forEach;\n                  _this.validatePromise = null;\n\n                  // Get errors & warnings\n                  var nextErrors = [];\n                  var nextWarnings = [];\n                  (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 || _ruleErrors$forEach.call(ruleErrors, function (_ref4) {\n                    var warningOnly = _ref4.rule.warningOnly,\n                      _ref4$errors = _ref4.errors,\n                      errors = _ref4$errors === void 0 ? EMPTY_ERRORS : _ref4$errors;\n                    if (warningOnly) {\n                      nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n                    } else {\n                      nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n                    }\n                  });\n                  _this.errors = nextErrors;\n                  _this.warnings = nextWarnings;\n                  _this.triggerMetaEvent();\n                  _this.reRender();\n                }\n              });\n              return _context.abrupt(\"return\", promise);\n            case 13:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      })));\n      if (validateOnly) {\n        return rootPromise;\n      }\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n      _this.triggerMetaEvent();\n\n      // Force trigger re-render since we need sync renderProps with new meta\n      _this.reRender();\n      return rootPromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldValidating\", function () {\n      return !!_this.validatePromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldTouched\", function () {\n      return _this.touched;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isFieldDirty\", function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      }\n\n      // Form set initialValue\n      var fieldContext = _this.props.fieldContext;\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n        getInitialValue = _fieldContext$getInte.getInitialValue;\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n      return false;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getErrors\", function () {\n      return _this.errors;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getWarnings\", function () {\n      return _this.warnings;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isListField\", function () {\n      return _this.props.isListField;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isList\", function () {\n      return _this.props.isList;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"isPreserve\", function () {\n      return _this.props.preserve;\n    });\n    // ============================= Child Component =============================\n    _defineProperty(_assertThisInitialized(_this), \"getMeta\", function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath(),\n        validated: _this.validatePromise === null\n      };\n      return meta;\n    });\n    // Only return validate child node. If invalidate, will do nothing about field.\n    _defineProperty(_assertThisInitialized(_this), \"getOnlyChild\", function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var _meta2 = _this.getMeta();\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), _meta2, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      }\n\n      // Filed element only\n      var childList = toChildrenArray(children);\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    });\n    // ============================== Field Control ==============================\n    _defineProperty(_assertThisInitialized(_this), \"getValue\", function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n      var namePath = _this.getNamePath();\n      return getValue(store || getFieldsValue(true), namePath);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getControlled\", function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n        name = _this$props6.name,\n        trigger = _this$props6.trigger,\n        validateTrigger = _this$props6.validateTrigger,\n        getValueFromEvent = _this$props6.getValueFromEvent,\n        normalize = _this$props6.normalize,\n        valuePropName = _this$props6.valuePropName,\n        getValueProps = _this$props6.getValueProps,\n        fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n      var namePath = _this.getNamePath();\n      var getInternalHooks = fieldContext.getInternalHooks,\n        getFieldsValue = fieldContext.getFieldsValue;\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        dispatch = _getInternalHooks.dispatch;\n      var value = _this.getValue();\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      };\n      var originTriggerFunc = childProps[trigger];\n      var valueProps = name !== undefined ? mergedGetValueProps(value) : {};\n\n      // warning when prop value is function\n      if (process.env.NODE_ENV !== 'production' && valueProps) {\n        Object.keys(valueProps).forEach(function (key) {\n          warning(typeof valueProps[key] !== 'function', \"It's not recommended to generate dynamic function prop by `getValueProps`. Please pass it to child component directly (prop: \".concat(key, \")\"));\n        });\n      }\n      var control = _objectSpread(_objectSpread({}, childProps), valueProps);\n\n      // Add trigger\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n        _this.triggerMetaEvent();\n        var newValue;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n        if (newValue !== value) {\n          dispatch({\n            type: 'updateValue',\n            namePath: namePath,\n            value: newValue\n          });\n        }\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      };\n\n      // Add validateTrigger\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          }\n\n          // Always use latest rules\n          var rules = _this.props.rules;\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    });\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n        initEntityValue = _getInternalHooks2.initEntityValue;\n      initEntityValue(_assertThisInitialized(_this));\n    }\n    return _this;\n  }\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n        shouldUpdate = _this$props7.shouldUpdate,\n        fieldContext = _this$props7.fieldContext;\n      this.mounted = true;\n\n      // Register on init\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n          registerField = _getInternalHooks3.registerField;\n        this.cancelRegisterFunc = registerField(this);\n      }\n\n      // One more render for component in case fields not ready\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n      var _this$getOnlyChild = this.getOnlyChild(children),\n        child = _this$getOnlyChild.child,\n        isFunction = _this$getOnlyChild.isFunction;\n\n      // Not need to `cloneElement` since user can handle this in render function self\n      var returnChildNode;\n      if (isFunction) {\n        returnChildNode = child;\n      } else if ( /*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n  return Field;\n}(React.Component);\n_defineProperty(Field, \"contextType\", FieldContext);\n_defineProperty(Field, \"defaultProps\", {\n  trigger: 'onChange',\n  valuePropName: 'value'\n});\nfunction WrapperField(_ref6) {\n  var _restProps$isListFiel;\n  var name = _ref6.name,\n    restProps = _objectWithoutProperties(_ref6, _excluded);\n  var fieldContext = React.useContext(FieldContext);\n  var listContext = React.useContext(ListContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var isMergedListField = (_restProps$isListFiel = restProps.isListField) !== null && _restProps$isListFiel !== void 0 ? _restProps$isListFiel : !!listContext;\n  var key = 'keep';\n  if (!isMergedListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  }\n\n  // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && isMergedListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath,\n    isListField: isMergedListField\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\nexport default WrapperField;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AACrG,IAAIC,YAAY,GAAG,EAAE;AACrB,SAASC,aAAaA,CAACC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC3E,IAAI,OAAOL,YAAY,KAAK,UAAU,EAAE;IACtC,OAAOA,YAAY,CAACC,IAAI,EAAEC,IAAI,EAAE,QAAQ,IAAIG,IAAI,GAAG;MACjDC,MAAM,EAAED,IAAI,CAACC;IACf,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,OAAOH,SAAS,KAAKC,SAAS;AAChC;;AAEA;AACA;AACA,IAAIG,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnD3B,SAAS,CAAC0B,KAAK,EAAEC,gBAAgB,CAAC;EAClC,IAAIC,MAAM,GAAG3B,YAAY,CAACyB,KAAK,CAAC;EAChC;EACA,SAASA,KAAKA,CAACG,KAAK,EAAE;IACpB,IAAIC,KAAK;IACTjC,eAAe,CAAC,IAAI,EAAE6B,KAAK,CAAC;IAC5BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;;IAEhC;IACA3B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDE,UAAU,EAAE;IACd,CAAC,CAAC;IACF9B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,oBAAoB,EAAE,IAAI,CAAC;IAC1E5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC;IAChE;AACJ;AACA;AACA;IACI5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC;IAChE;AACJ;AACA;AACA;AACA;IACI5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC;IAC9D5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IACzE5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACxE5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,QAAQ,EAAEb,YAAY,CAAC;IACtEf,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,UAAU,EAAEb,YAAY,CAAC;IACxEf,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,gBAAgB,EAAE,YAAY;MAC3E,IAAIG,WAAW,GAAGH,KAAK,CAACD,KAAK;QAC3BK,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,IAAI,GAAGH,WAAW,CAACG,IAAI;MACzB,IAAIN,KAAK,CAACO,kBAAkB,EAAE;QAC5BP,KAAK,CAACO,kBAAkB,CAACF,WAAW,EAAED,QAAQ,EAAEnB,WAAW,CAACqB,IAAI,CAAC,CAAC;MACpE;MACAN,KAAK,CAACO,kBAAkB,GAAG,IAAI;IACjC,CAAC,CAAC;IACF;IACAnC,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,aAAa,EAAE,YAAY;MACxE,IAAIQ,YAAY,GAAGR,KAAK,CAACD,KAAK;QAC5BO,IAAI,GAAGE,YAAY,CAACF,IAAI;QACxBG,YAAY,GAAGD,YAAY,CAACC,YAAY;MAC1C,IAAIC,qBAAqB,GAAGD,YAAY,CAACE,UAAU;QACjDA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;MAC5E,OAAOJ,IAAI,KAAKM,SAAS,GAAG,EAAE,CAACC,MAAM,CAAC/C,kBAAkB,CAAC6C,UAAU,CAAC,EAAE7C,kBAAkB,CAACwC,IAAI,CAAC,CAAC,GAAG,EAAE;IACtG,CAAC,CAAC;IACFlC,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,UAAU,EAAE,YAAY;MACrE,IAAIc,YAAY,GAAGd,KAAK,CAACD,KAAK;QAC5BgB,kBAAkB,GAAGD,YAAY,CAACE,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;QAC/DN,YAAY,GAAGK,YAAY,CAACL,YAAY;MAC1C,OAAOO,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;UAC9B,OAAOA,IAAI,CAACT,YAAY,CAAC;QAC3B;QACA,OAAOS,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;IACF9C,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY;MACpE,IAAI,CAACA,KAAK,CAACmB,OAAO,EAAE;;MAEpB;AACN;AACA;MACMnB,KAAK,CAACoB,QAAQ,CAAC,UAAUC,IAAI,EAAE;QAC7B,IAAInB,UAAU,GAAGmB,IAAI,CAACnB,UAAU;QAChC,OAAO;UACLA,UAAU,EAAEA,UAAU,GAAG;QAC3B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA9B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC;IACjE5B,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUsB,OAAO,EAAE;MACpF,IAAIC,YAAY,GAAGvB,KAAK,CAACD,KAAK,CAACwB,YAAY;MAC3C,IAAIA,YAAY,EAAE;QAChB,IAAIC,KAAK,GAAG3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAChEH,OAAO,EAAEA;QACX,CAAC,CAAC;QACF,IAAI,CAAC/C,OAAO,CAACyB,KAAK,CAAC0B,SAAS,EAAEF,KAAK,CAAC,EAAE;UACpCD,YAAY,CAACC,KAAK,CAAC;QACrB;QACAxB,KAAK,CAAC0B,SAAS,GAAGF,KAAK;MACzB,CAAC,MAAM;QACLxB,KAAK,CAAC0B,SAAS,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACF;IACA;IACAtD,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,eAAe,EAAE,UAAU2B,SAAS,EAAEC,YAAY,EAAElC,IAAI,EAAE;MACvG,IAAImC,YAAY,GAAG7B,KAAK,CAACD,KAAK;QAC5BV,YAAY,GAAGwC,YAAY,CAACxC,YAAY;QACxCyC,qBAAqB,GAAGD,YAAY,CAACE,YAAY;QACjDA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC5EE,OAAO,GAAGH,YAAY,CAACG,OAAO;MAChC,IAAIC,KAAK,GAAGvC,IAAI,CAACuC,KAAK;MACtB,IAAIC,QAAQ,GAAGlC,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAIO,SAAS,GAAGQ,KAAK,CAACd,QAAQ,CAACyC,SAAS,CAAC;MACzC,IAAIQ,QAAQ,GAAGnC,KAAK,CAACd,QAAQ,CAAC+C,KAAK,CAAC;MACpC,IAAIG,aAAa,GAAGR,YAAY,IAAI7C,gBAAgB,CAAC6C,YAAY,EAAEM,QAAQ,CAAC;;MAE5E;MACA,IAAIxC,IAAI,CAAC2C,IAAI,KAAK,aAAa,IAAI3C,IAAI,CAACC,MAAM,KAAK,UAAU,IAAI,CAACpB,OAAO,CAACiB,SAAS,EAAE2C,QAAQ,CAAC,EAAE;QAC9FnC,KAAK,CAACsC,OAAO,GAAG,IAAI;QACpBtC,KAAK,CAACuC,KAAK,GAAG,IAAI;QAClBvC,KAAK,CAACwC,eAAe,GAAG,IAAI;QAC5BxC,KAAK,CAACyC,MAAM,GAAGtD,YAAY;QAC3Ba,KAAK,CAAC0C,QAAQ,GAAGvD,YAAY;QAC7Ba,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;MAC1B;MACA,QAAQjD,IAAI,CAAC2C,IAAI;QACf,KAAK,OAAO;UACV,IAAI,CAACT,YAAY,IAAIQ,aAAa,EAAE;YAClC;YACApC,KAAK,CAACsC,OAAO,GAAG,KAAK;YACrBtC,KAAK,CAACuC,KAAK,GAAG,KAAK;YACnBvC,KAAK,CAACwC,eAAe,GAAG5B,SAAS;YACjCZ,KAAK,CAACyC,MAAM,GAAGtD,YAAY;YAC3Ba,KAAK,CAAC0C,QAAQ,GAAGvD,YAAY;YAC7Ba,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;YACxBX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAC,CAAC;YACnDhC,KAAK,CAAC4C,OAAO,CAAC,CAAC;YACf;UACF;UACA;;QAEF;AACR;AACA;AACA;AACA;AACA;QACQ,KAAK,QAAQ;UACX;YACE,IAAIvD,YAAY,IAAID,aAAa,CAACC,YAAY,EAAEsC,SAAS,EAAEM,KAAK,EAAEzC,SAAS,EAAE2C,QAAQ,EAAEzC,IAAI,CAAC,EAAE;cAC5FM,KAAK,CAAC6C,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF,KAAK,UAAU;UACb;YACE,IAAIC,IAAI,GAAGpD,IAAI,CAACoD,IAAI;YACpB,IAAIV,aAAa,EAAE;cACjB,IAAI,SAAS,IAAIU,IAAI,EAAE;gBACrB9C,KAAK,CAACsC,OAAO,GAAGQ,IAAI,CAACR,OAAO;cAC9B;cACA,IAAI,YAAY,IAAIQ,IAAI,IAAI,EAAE,eAAe,IAAIA,IAAI,CAAC,EAAE;gBACtD9C,KAAK,CAACwC,eAAe,GAAGM,IAAI,CAACC,UAAU,GAAGC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI;cACtE;cACA,IAAI,QAAQ,IAAIH,IAAI,EAAE;gBACpB9C,KAAK,CAACyC,MAAM,GAAGK,IAAI,CAACL,MAAM,IAAItD,YAAY;cAC5C;cACA,IAAI,UAAU,IAAI2D,IAAI,EAAE;gBACtB9C,KAAK,CAAC0C,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIvD,YAAY;cAChD;cACAa,KAAK,CAACuC,KAAK,GAAG,IAAI;cAClBvC,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;cACxB3C,KAAK,CAAC6C,QAAQ,CAAC,CAAC;cAChB;YACF,CAAC,MAAM,IAAI,OAAO,IAAIC,IAAI,IAAI/D,gBAAgB,CAAC6C,YAAY,EAAEM,QAAQ,EAAE,IAAI,CAAC,EAAE;cAC5E;cACAlC,KAAK,CAAC6C,QAAQ,CAAC,CAAC;cAChB;YACF;;YAEA;YACA,IAAIxD,YAAY,IAAI,CAAC6C,QAAQ,CAACgB,MAAM,IAAI9D,aAAa,CAACC,YAAY,EAAEsC,SAAS,EAAEM,KAAK,EAAEzC,SAAS,EAAE2C,QAAQ,EAAEzC,IAAI,CAAC,EAAE;cAChHM,KAAK,CAAC6C,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF,KAAK,oBAAoB;UACvB;YACE;AACZ;AACA;YACY,IAAIM,cAAc,GAAGpB,YAAY,CAACd,GAAG,CAAChC,WAAW,CAAC;YAClD;YACA;YACA;YACA,IAAIkE,cAAc,CAACC,IAAI,CAAC,UAAUC,UAAU,EAAE;cAC5C,OAAOtE,gBAAgB,CAACW,IAAI,CAAC4D,aAAa,EAAED,UAAU,CAAC;YACzD,CAAC,CAAC,EAAE;cACFrD,KAAK,CAAC6C,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF;UACE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIT,aAAa,IAAI,CAAC,CAACL,YAAY,CAACmB,MAAM,IAAIhB,QAAQ,CAACgB,MAAM,IAAI7D,YAAY,KAAKD,aAAa,CAACC,YAAY,EAAEsC,SAAS,EAAEM,KAAK,EAAEzC,SAAS,EAAE2C,QAAQ,EAAEzC,IAAI,CAAC,EAAE;YAC1JM,KAAK,CAAC6C,QAAQ,CAAC,CAAC;YAChB;UACF;UACA;MACJ;MACA,IAAIxD,YAAY,KAAK,IAAI,EAAE;QACzBW,KAAK,CAAC6C,QAAQ,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFzE,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,eAAe,EAAE,UAAUuD,OAAO,EAAE;MACjF;MACA,IAAIrB,QAAQ,GAAGlC,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAIuE,YAAY,GAAGxD,KAAK,CAACd,QAAQ,CAAC,CAAC;MACnC,IAAIuE,KAAK,GAAGF,OAAO,IAAI,CAAC,CAAC;QACvBG,WAAW,GAAGD,KAAK,CAACC,WAAW;QAC/BC,kBAAkB,GAAGF,KAAK,CAACG,YAAY;QACvCA,YAAY,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,kBAAkB;;MAE3E;MACA,IAAIE,WAAW,GAAGb,OAAO,CAACC,OAAO,CAAC,CAAC,CAACa,IAAI,CAAE,aAAalG,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACoG,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;QACnI,IAAIC,YAAY,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO;QAClH,OAAO5G,mBAAmB,CAAC,CAAC,CAAC6G,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAACpF,IAAI,GAAGoF,QAAQ,CAACnF,IAAI;YAC7C,KAAK,CAAC;cACJ,IAAIS,KAAK,CAACmB,OAAO,EAAE;gBACjBuD,QAAQ,CAACnF,IAAI,GAAG,CAAC;gBACjB;cACF;cACA,OAAOmF,QAAQ,CAACC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtC,KAAK,CAAC;cACJV,YAAY,GAAGjE,KAAK,CAACD,KAAK,EAAEmE,qBAAqB,GAAGD,YAAY,CAACE,aAAa,EAAEA,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB,EAAEE,gBAAgB,GAAGH,YAAY,CAACG,gBAAgB,EAAEC,gBAAgB,GAAGJ,YAAY,CAACI,gBAAgB,CAAC,CAAC;cACtQC,aAAa,GAAGtE,KAAK,CAAC4E,QAAQ,CAAC,CAAC;cAChC,IAAIlB,WAAW,EAAE;gBACfY,aAAa,GAAGA,aAAa,CAACO,MAAM,CAAC,UAAU3D,IAAI,EAAE;kBACnD,OAAOA,IAAI;gBACb,CAAC,CAAC,CAAC2D,MAAM,CAAC,UAAU3D,IAAI,EAAE;kBACxB,IAAI4D,eAAe,GAAG5D,IAAI,CAAC4D,eAAe;kBAC1C,IAAI,CAACA,eAAe,EAAE;oBACpB,OAAO,IAAI;kBACb;kBACA,IAAIC,WAAW,GAAGlG,OAAO,CAACiG,eAAe,CAAC;kBAC1C,OAAOC,WAAW,CAACC,QAAQ,CAACtB,WAAW,CAAC;gBAC1C,CAAC,CAAC;cACJ;;cAEA;cACA,IAAI,EAAEW,gBAAgB,IAAIX,WAAW,CAAC,EAAE;gBACtCgB,QAAQ,CAACnF,IAAI,GAAG,EAAE;gBAClB;cACF;cACAmF,QAAQ,CAACnF,IAAI,GAAG,CAAC;cACjB,OAAO,IAAIyD,OAAO,CAAC,UAAUC,OAAO,EAAE;gBACpCgC,UAAU,CAAChC,OAAO,EAAEoB,gBAAgB,CAAC;cACvC,CAAC,CAAC;YACJ,KAAK,CAAC;cACJ,IAAI,EAAErE,KAAK,CAACwC,eAAe,KAAKqB,WAAW,CAAC,EAAE;gBAC5Ca,QAAQ,CAACnF,IAAI,GAAG,EAAE;gBAClB;cACF;cACA,OAAOmF,QAAQ,CAACC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtC,KAAK,EAAE;cACLJ,OAAO,GAAGzF,aAAa,CAACoD,QAAQ,EAAEsB,YAAY,EAAEc,aAAa,EAAEf,OAAO,EAAEY,aAAa,EAAEC,gBAAgB,CAAC;cACxGG,OAAO,CAACW,KAAK,CAAC,UAAUC,CAAC,EAAE;gBACzB,OAAOA,CAAC;cACV,CAAC,CAAC,CAACrB,IAAI,CAAC,YAAY;gBAClB,IAAIsB,UAAU,GAAGC,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKzE,SAAS,GAAGyE,SAAS,CAAC,CAAC,CAAC,GAAGlG,YAAY;gBACjG,IAAIa,KAAK,CAACwC,eAAe,KAAKqB,WAAW,EAAE;kBACzC,IAAIyB,mBAAmB;kBACvBtF,KAAK,CAACwC,eAAe,GAAG,IAAI;;kBAE5B;kBACA,IAAI+C,UAAU,GAAG,EAAE;kBACnB,IAAIC,YAAY,GAAG,EAAE;kBACrB,CAACF,mBAAmB,GAAGF,UAAU,CAACK,OAAO,MAAM,IAAI,IAAIH,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACrF,IAAI,CAACmF,UAAU,EAAE,UAAUM,KAAK,EAAE;oBAC7I,IAAIC,WAAW,GAAGD,KAAK,CAACxE,IAAI,CAACyE,WAAW;sBACtCC,YAAY,GAAGF,KAAK,CAACjD,MAAM;sBAC3BA,MAAM,GAAGmD,YAAY,KAAK,KAAK,CAAC,GAAGzG,YAAY,GAAGyG,YAAY;oBAChE,IAAID,WAAW,EAAE;sBACfH,YAAY,CAACK,IAAI,CAACC,KAAK,CAACN,YAAY,EAAE1H,kBAAkB,CAAC2E,MAAM,CAAC,CAAC;oBACnE,CAAC,MAAM;sBACL8C,UAAU,CAACM,IAAI,CAACC,KAAK,CAACP,UAAU,EAAEzH,kBAAkB,CAAC2E,MAAM,CAAC,CAAC;oBAC/D;kBACF,CAAC,CAAC;kBACFzC,KAAK,CAACyC,MAAM,GAAG8C,UAAU;kBACzBvF,KAAK,CAAC0C,QAAQ,GAAG8C,YAAY;kBAC7BxF,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;kBACxB3C,KAAK,CAAC6C,QAAQ,CAAC,CAAC;gBAClB;cACF,CAAC,CAAC;cACF,OAAO6B,QAAQ,CAACC,MAAM,CAAC,QAAQ,EAAEJ,OAAO,CAAC;YAC3C,KAAK,EAAE;YACP,KAAK,KAAK;cACR,OAAOG,QAAQ,CAACqB,IAAI,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE/B,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,CAAC;MACJ,IAAIJ,YAAY,EAAE;QAChB,OAAOC,WAAW;MACpB;MACA7D,KAAK,CAACwC,eAAe,GAAGqB,WAAW;MACnC7D,KAAK,CAACuC,KAAK,GAAG,IAAI;MAClBvC,KAAK,CAACyC,MAAM,GAAGtD,YAAY;MAC3Ba,KAAK,CAAC0C,QAAQ,GAAGvD,YAAY;MAC7Ba,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;;MAExB;MACA3C,KAAK,CAAC6C,QAAQ,CAAC,CAAC;MAChB,OAAOgB,WAAW;IACpB,CAAC,CAAC;IACFzF,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,mBAAmB,EAAE,YAAY;MAC9E,OAAO,CAAC,CAACA,KAAK,CAACwC,eAAe;IAChC,CAAC,CAAC;IACFpE,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,gBAAgB,EAAE,YAAY;MAC3E,OAAOA,KAAK,CAACsC,OAAO;IACtB,CAAC,CAAC;IACFlE,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;MACzE;MACA,IAAIA,KAAK,CAACuC,KAAK,IAAIvC,KAAK,CAACD,KAAK,CAACiG,YAAY,KAAKpF,SAAS,EAAE;QACzD,OAAO,IAAI;MACb;;MAEA;MACA,IAAIH,YAAY,GAAGT,KAAK,CAACD,KAAK,CAACU,YAAY;MAC3C,IAAIwF,qBAAqB,GAAGxF,YAAY,CAACyF,gBAAgB,CAACvH,SAAS,CAAC;QAClEwH,eAAe,GAAGF,qBAAqB,CAACE,eAAe;MACzD,IAAIA,eAAe,CAACnG,KAAK,CAACf,WAAW,CAAC,CAAC,CAAC,KAAK2B,SAAS,EAAE;QACtD,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC;IACFxC,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,WAAW,EAAE,YAAY;MACtE,OAAOA,KAAK,CAACyC,MAAM;IACrB,CAAC,CAAC;IACFrE,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,aAAa,EAAE,YAAY;MACxE,OAAOA,KAAK,CAAC0C,QAAQ;IACvB,CAAC,CAAC;IACFtE,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,aAAa,EAAE,YAAY;MACxE,OAAOA,KAAK,CAACD,KAAK,CAACM,WAAW;IAChC,CAAC,CAAC;IACFjC,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,QAAQ,EAAE,YAAY;MACnE,OAAOA,KAAK,CAACD,KAAK,CAACqG,MAAM;IAC3B,CAAC,CAAC;IACFhI,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,YAAY,EAAE,YAAY;MACvE,OAAOA,KAAK,CAACD,KAAK,CAACK,QAAQ;IAC7B,CAAC,CAAC;IACF;IACAhC,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY;MACpE;MACAA,KAAK,CAACqG,cAAc,GAAGrG,KAAK,CAACsG,iBAAiB,CAAC,CAAC;MAChD,IAAIC,IAAI,GAAG;QACTjE,OAAO,EAAEtC,KAAK,CAACwG,cAAc,CAAC,CAAC;QAC/BzD,UAAU,EAAE/C,KAAK,CAACqG,cAAc;QAChC5D,MAAM,EAAEzC,KAAK,CAACyC,MAAM;QACpBC,QAAQ,EAAE1C,KAAK,CAAC0C,QAAQ;QACxBpC,IAAI,EAAEN,KAAK,CAACf,WAAW,CAAC,CAAC;QACzBwH,SAAS,EAAEzG,KAAK,CAACwC,eAAe,KAAK;MACvC,CAAC;MACD,OAAO+D,IAAI;IACb,CAAC,CAAC;IACF;IACAnI,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,cAAc,EAAE,UAAU0G,QAAQ,EAAE;MACjF;MACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAIC,MAAM,GAAG3G,KAAK,CAACyB,OAAO,CAAC,CAAC;QAC5B,OAAO5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC4G,YAAY,CAACF,QAAQ,CAAC1G,KAAK,CAAC6G,aAAa,CAAC,CAAC,EAAEF,MAAM,EAAE3G,KAAK,CAACD,KAAK,CAACU,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACjIqG,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIC,SAAS,GAAGzI,eAAe,CAACoI,QAAQ,CAAC;MACzC,IAAIK,SAAS,CAAC7D,MAAM,KAAK,CAAC,IAAI,EAAE,aAAazE,KAAK,CAACuI,cAAc,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/E,OAAO;UACLE,KAAK,EAAEF,SAAS;UAChBD,UAAU,EAAE;QACd,CAAC;MACH;MACA,OAAO;QACLG,KAAK,EAAEF,SAAS,CAAC,CAAC,CAAC;QACnBD,UAAU,EAAE;MACd,CAAC;IACH,CAAC,CAAC;IACF;IACA1I,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,UAAU,EAAE,UAAUiC,KAAK,EAAE;MAC1E,IAAIiF,cAAc,GAAGlH,KAAK,CAACD,KAAK,CAACU,YAAY,CAACyG,cAAc;MAC5D,IAAIhF,QAAQ,GAAGlC,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,OAAOC,QAAQ,CAAC+C,KAAK,IAAIiF,cAAc,CAAC,IAAI,CAAC,EAAEhF,QAAQ,CAAC;IAC1D,CAAC,CAAC;IACF9D,eAAe,CAACH,sBAAsB,CAAC+B,KAAK,CAAC,EAAE,eAAe,EAAE,YAAY;MAC1E,IAAImH,UAAU,GAAG9B,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKzE,SAAS,GAAGyE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvF,IAAI+B,YAAY,GAAGpH,KAAK,CAACD,KAAK;QAC5BO,IAAI,GAAG8G,YAAY,CAAC9G,IAAI;QACxB+G,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BvC,eAAe,GAAGsC,YAAY,CAACtC,eAAe;QAC9CwC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,aAAa,GAAGJ,YAAY,CAACI,aAAa;QAC1CC,aAAa,GAAGL,YAAY,CAACK,aAAa;QAC1ChH,YAAY,GAAG2G,YAAY,CAAC3G,YAAY;MAC1C,IAAIiH,qBAAqB,GAAG5C,eAAe,KAAKlE,SAAS,GAAGkE,eAAe,GAAGrE,YAAY,CAACqE,eAAe;MAC1G,IAAI5C,QAAQ,GAAGlC,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAIiH,gBAAgB,GAAGzF,YAAY,CAACyF,gBAAgB;QAClDgB,cAAc,GAAGzG,YAAY,CAACyG,cAAc;MAC9C,IAAIS,iBAAiB,GAAGzB,gBAAgB,CAACvH,SAAS,CAAC;QACjDiJ,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;MACvC,IAAIC,KAAK,GAAG7H,KAAK,CAACd,QAAQ,CAAC,CAAC;MAC5B,IAAI4I,mBAAmB,GAAGL,aAAa,IAAI,UAAUM,GAAG,EAAE;QACxD,OAAO3J,eAAe,CAAC,CAAC,CAAC,EAAEoJ,aAAa,EAAEO,GAAG,CAAC;MAChD,CAAC;MACD,IAAIC,iBAAiB,GAAGb,UAAU,CAACE,OAAO,CAAC;MAC3C,IAAIY,UAAU,GAAG3H,IAAI,KAAKM,SAAS,GAAGkH,mBAAmB,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC;;MAErE;MACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,UAAU,EAAE;QACvDI,MAAM,CAACC,IAAI,CAACL,UAAU,CAAC,CAACxC,OAAO,CAAC,UAAU8C,GAAG,EAAE;UAC7C/J,OAAO,CAAC,OAAOyJ,UAAU,CAACM,GAAG,CAAC,KAAK,UAAU,EAAE,+HAA+H,CAAC1H,MAAM,CAAC0H,GAAG,EAAE,GAAG,CAAC,CAAC;QAClM,CAAC,CAAC;MACJ;MACA,IAAIC,OAAO,GAAG3K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsJ,UAAU,CAAC,EAAEc,UAAU,CAAC;;MAEtE;MACAO,OAAO,CAACnB,OAAO,CAAC,GAAG,YAAY;QAC7B;QACArH,KAAK,CAACsC,OAAO,GAAG,IAAI;QACpBtC,KAAK,CAACuC,KAAK,GAAG,IAAI;QAClBvC,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;QACxB,IAAI8F,QAAQ;QACZ,KAAK,IAAIC,IAAI,GAAGrD,SAAS,CAACnC,MAAM,EAAEyF,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;UACvFF,IAAI,CAACE,IAAI,CAAC,GAAGxD,SAAS,CAACwD,IAAI,CAAC;QAC9B;QACA,IAAIvB,iBAAiB,EAAE;UACrBmB,QAAQ,GAAGnB,iBAAiB,CAACxB,KAAK,CAAC,KAAK,CAAC,EAAE6C,IAAI,CAAC;QAClD,CAAC,MAAM;UACLF,QAAQ,GAAGzJ,wBAAwB,CAAC8G,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC0B,aAAa,CAAC,CAAC3G,MAAM,CAAC8H,IAAI,CAAC,CAAC;QACjF;QACA,IAAIpB,SAAS,EAAE;UACbkB,QAAQ,GAAGlB,SAAS,CAACkB,QAAQ,EAAEZ,KAAK,EAAEX,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7D;QACA,IAAIuB,QAAQ,KAAKZ,KAAK,EAAE;UACtBD,QAAQ,CAAC;YACPvF,IAAI,EAAE,aAAa;YACnBH,QAAQ,EAAEA,QAAQ;YAClB2F,KAAK,EAAEY;UACT,CAAC,CAAC;QACJ;QACA,IAAIT,iBAAiB,EAAE;UACrBA,iBAAiB,CAAClC,KAAK,CAAC,KAAK,CAAC,EAAE6C,IAAI,CAAC;QACvC;MACF,CAAC;;MAED;MACA,IAAIG,mBAAmB,GAAGjK,OAAO,CAAC6I,qBAAqB,IAAI,EAAE,CAAC;MAC9DoB,mBAAmB,CAACrD,OAAO,CAAC,UAAU/B,WAAW,EAAE;QACjD;QACA,IAAIqF,aAAa,GAAGP,OAAO,CAAC9E,WAAW,CAAC;QACxC8E,OAAO,CAAC9E,WAAW,CAAC,GAAG,YAAY;UACjC,IAAIqF,aAAa,EAAE;YACjBA,aAAa,CAACjD,KAAK,CAAC,KAAK,CAAC,EAAET,SAAS,CAAC;UACxC;;UAEA;UACA,IAAIrE,KAAK,GAAGhB,KAAK,CAACD,KAAK,CAACiB,KAAK;UAC7B,IAAIA,KAAK,IAAIA,KAAK,CAACkC,MAAM,EAAE;YACzB;YACA;YACA0E,QAAQ,CAAC;cACPvF,IAAI,EAAE,eAAe;cACrBH,QAAQ,EAAEA,QAAQ;cAClBwB,WAAW,EAAEA;YACf,CAAC,CAAC;UACJ;QACF,CAAC;MACH,CAAC,CAAC;MACF,OAAO8E,OAAO;IAChB,CAAC,CAAC;IACF,IAAIzI,KAAK,CAACU,YAAY,EAAE;MACtB,IAAIyF,gBAAgB,GAAGnG,KAAK,CAACU,YAAY,CAACyF,gBAAgB;MAC1D,IAAI8C,kBAAkB,GAAG9C,gBAAgB,CAACvH,SAAS,CAAC;QAClDsK,eAAe,GAAGD,kBAAkB,CAACC,eAAe;MACtDA,eAAe,CAAChL,sBAAsB,CAAC+B,KAAK,CAAC,CAAC;IAChD;IACA,OAAOA,KAAK;EACd;EACAhC,YAAY,CAAC4B,KAAK,EAAE,CAAC;IACnB2I,GAAG,EAAE,mBAAmB;IACxBV,KAAK,EAAE,SAASqB,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,YAAY,GAAG,IAAI,CAACpJ,KAAK;QAC3BV,YAAY,GAAG8J,YAAY,CAAC9J,YAAY;QACxCoB,YAAY,GAAG0I,YAAY,CAAC1I,YAAY;MAC1C,IAAI,CAACU,OAAO,GAAG,IAAI;;MAEnB;MACA,IAAIV,YAAY,EAAE;QAChB,IAAIyF,gBAAgB,GAAGzF,YAAY,CAACyF,gBAAgB;QACpD,IAAIkD,kBAAkB,GAAGlD,gBAAgB,CAACvH,SAAS,CAAC;UAClD0K,aAAa,GAAGD,kBAAkB,CAACC,aAAa;QAClD,IAAI,CAAC9I,kBAAkB,GAAG8I,aAAa,CAAC,IAAI,CAAC;MAC/C;;MAEA;MACA,IAAIhK,YAAY,KAAK,IAAI,EAAE;QACzB,IAAI,CAACwD,QAAQ,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE;IACD0F,GAAG,EAAE,sBAAsB;IAC3BV,KAAK,EAAE,SAASyB,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC5G,gBAAgB,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACxB,OAAO,GAAG,KAAK;IACtB;EACF,CAAC,EAAE;IACDoH,GAAG,EAAE,UAAU;IACfV,KAAK,EAAE,SAAShF,QAAQA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE;MACnB,IAAI,CAACqI,WAAW,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDjB,GAAG,EAAE,QAAQ;IACbV,KAAK,EAAE,SAAS4B,MAAMA,CAAA,EAAG;MACvB,IAAIvJ,UAAU,GAAG,IAAI,CAACwJ,KAAK,CAACxJ,UAAU;MACtC,IAAIwG,QAAQ,GAAG,IAAI,CAAC3G,KAAK,CAAC2G,QAAQ;MAClC,IAAIiD,kBAAkB,GAAG,IAAI,CAAC/C,YAAY,CAACF,QAAQ,CAAC;QAClDO,KAAK,GAAG0C,kBAAkB,CAAC1C,KAAK;QAChCH,UAAU,GAAG6C,kBAAkB,CAAC7C,UAAU;;MAE5C;MACA,IAAI8C,eAAe;MACnB,IAAI9C,UAAU,EAAE;QACd8C,eAAe,GAAG3C,KAAK;MACzB,CAAC,MAAM,IAAK,aAAaxI,KAAK,CAACuI,cAAc,CAACC,KAAK,CAAC,EAAE;QACpD2C,eAAe,GAAG,aAAanL,KAAK,CAACoL,YAAY,CAAC5C,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK,CAAClH,KAAK,CAAC,CAAC;MAC3F,CAAC,MAAM;QACLvB,OAAO,CAAC,CAACyI,KAAK,EAAE,mDAAmD,CAAC;QACpE2C,eAAe,GAAG3C,KAAK;MACzB;MACA,OAAO,aAAaxI,KAAK,CAACqL,aAAa,CAACrL,KAAK,CAACsL,QAAQ,EAAE;QACtDxB,GAAG,EAAErI;MACP,CAAC,EAAE0J,eAAe,CAAC;IACrB;EACF,CAAC,CAAC,CAAC;EACH,OAAOhK,KAAK;AACd,CAAC,CAACnB,KAAK,CAACuL,SAAS,CAAC;AAClB5L,eAAe,CAACwB,KAAK,EAAE,aAAa,EAAElB,YAAY,CAAC;AACnDN,eAAe,CAACwB,KAAK,EAAE,cAAc,EAAE;EACrCyH,OAAO,EAAE,UAAU;EACnBG,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,SAASyC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,qBAAqB;EACzB,IAAI7J,IAAI,GAAG4J,KAAK,CAAC5J,IAAI;IACnB8J,SAAS,GAAG1M,wBAAwB,CAACwM,KAAK,EAAE7L,SAAS,CAAC;EACxD,IAAIoC,YAAY,GAAGhC,KAAK,CAAC4L,UAAU,CAAC3L,YAAY,CAAC;EACjD,IAAI4L,WAAW,GAAG7L,KAAK,CAAC4L,UAAU,CAACzL,WAAW,CAAC;EAC/C,IAAIsD,QAAQ,GAAG5B,IAAI,KAAKM,SAAS,GAAG3B,WAAW,CAACqB,IAAI,CAAC,GAAGM,SAAS;EACjE,IAAI2J,iBAAiB,GAAG,CAACJ,qBAAqB,GAAGC,SAAS,CAAC/J,WAAW,MAAM,IAAI,IAAI8J,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAACG,WAAW;EAC5J,IAAI/B,GAAG,GAAG,MAAM;EAChB,IAAI,CAACgC,iBAAiB,EAAE;IACtBhC,GAAG,GAAG,GAAG,CAAC1H,MAAM,CAAC,CAACqB,QAAQ,IAAI,EAAE,EAAEsI,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9C;;EAEA;EACA;EACA,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIgC,SAAS,CAAChK,QAAQ,KAAK,KAAK,IAAImK,iBAAiB,IAAIrI,QAAQ,CAACgB,MAAM,IAAI,CAAC,EAAE;IACtH1E,OAAO,CAAC,KAAK,EAAE,kDAAkD,CAAC;EACpE;EACA,OAAO,aAAaC,KAAK,CAACqL,aAAa,CAAClK,KAAK,EAAEnC,QAAQ,CAAC;IACtD8K,GAAG,EAAEA,GAAG;IACRjI,IAAI,EAAE4B,QAAQ;IACd7B,WAAW,EAAEkK;EACf,CAAC,EAAEH,SAAS,EAAE;IACZ3J,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC;AACL;AACA,eAAewJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
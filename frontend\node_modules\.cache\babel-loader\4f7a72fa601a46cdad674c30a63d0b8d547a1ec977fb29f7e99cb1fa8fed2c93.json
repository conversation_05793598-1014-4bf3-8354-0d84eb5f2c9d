{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport classNames from 'classnames';\nimport Portal from '@rc-component/portal';\nimport useId from \"rc-util/es/hooks/useId\";\nvar COVER_PROPS = {\n  fill: 'transparent',\n  pointerEvents: 'auto'\n};\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    rootClassName = props.rootClassName,\n    pos = props.pos,\n    showMask = props.showMask,\n    _props$style = props.style,\n    style = _props$style === void 0 ? {} : _props$style,\n    _props$fill = props.fill,\n    fill = _props$fill === void 0 ? \"rgba(0,0,0,0.5)\" : _props$fill,\n    open = props.open,\n    animated = props.animated,\n    zIndex = props.zIndex,\n    disabledInteraction = props.disabledInteraction;\n  var id = useId();\n  var maskId = \"\".concat(prefixCls, \"-mask-\").concat(id);\n  var mergedAnimated = _typeof(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;\n  var isSafari = typeof navigator !== 'undefined' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  var maskRectSize = isSafari ? {\n    width: '100%',\n    height: '100%'\n  } : {\n    width: '100vw',\n    height: '100vh'\n  };\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open,\n    autoLock: true\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-mask\"), rootClassName),\n    style: _objectSpread({\n      position: 'fixed',\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      zIndex: zIndex,\n      pointerEvents: pos && !disabledInteraction ? 'none' : 'auto'\n    }, style)\n  }, showMask ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, /*#__PURE__*/React.createElement(\"rect\", _extends({\n    x: \"0\",\n    y: \"0\"\n  }, maskRectSize, {\n    fill: \"white\"\n  })), pos && /*#__PURE__*/React.createElement(\"rect\", {\n    x: pos.left,\n    y: pos.top,\n    rx: pos.radius,\n    width: pos.width,\n    height: pos.height,\n    fill: \"black\",\n    className: mergedAnimated ? \"\".concat(prefixCls, \"-placeholder-animated\") : ''\n  }))), /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    fill: fill,\n    mask: \"url(#\".concat(maskId, \")\")\n  }), pos && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: pos.top\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: pos.left,\n    height: \"100%\"\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: pos.top + pos.height,\n    width: \"100%\",\n    height: \"calc(100vh - \".concat(pos.top + pos.height, \"px)\")\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: pos.left + pos.width,\n    y: \"0\",\n    width: \"calc(100vw - \".concat(pos.left + pos.width, \"px)\"),\n    height: \"100%\"\n  })))) : null));\n};\nexport default Mask;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_typeof", "React", "classNames", "Portal", "useId", "COVER_PROPS", "fill", "pointerEvents", "Mask", "props", "prefixCls", "rootClassName", "pos", "showMask", "_props$style", "style", "_props$fill", "open", "animated", "zIndex", "disabledInteraction", "id", "maskId", "concat", "mergedAnimated", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "test", "userAgent", "maskRectSize", "width", "height", "createElement", "autoLock", "className", "position", "left", "right", "top", "bottom", "x", "y", "rx", "radius", "mask", "Fragment"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@rc-component+tour@1.15.1_r_14d992225ad622c42cfc1bfce16fe219/node_modules/@rc-component/tour/es/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport classNames from 'classnames';\nimport Portal from '@rc-component/portal';\nimport useId from \"rc-util/es/hooks/useId\";\nvar COVER_PROPS = {\n  fill: 'transparent',\n  pointerEvents: 'auto'\n};\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    rootClassName = props.rootClassName,\n    pos = props.pos,\n    showMask = props.showMask,\n    _props$style = props.style,\n    style = _props$style === void 0 ? {} : _props$style,\n    _props$fill = props.fill,\n    fill = _props$fill === void 0 ? \"rgba(0,0,0,0.5)\" : _props$fill,\n    open = props.open,\n    animated = props.animated,\n    zIndex = props.zIndex,\n    disabledInteraction = props.disabledInteraction;\n  var id = useId();\n  var maskId = \"\".concat(prefixCls, \"-mask-\").concat(id);\n  var mergedAnimated = _typeof(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;\n  var isSafari = typeof navigator !== 'undefined' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  var maskRectSize = isSafari ? {\n    width: '100%',\n    height: '100%'\n  } : {\n    width: '100vw',\n    height: '100vh'\n  };\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open,\n    autoLock: true\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-mask\"), rootClassName),\n    style: _objectSpread({\n      position: 'fixed',\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      zIndex: zIndex,\n      pointerEvents: pos && !disabledInteraction ? 'none' : 'auto'\n    }, style)\n  }, showMask ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, /*#__PURE__*/React.createElement(\"rect\", _extends({\n    x: \"0\",\n    y: \"0\"\n  }, maskRectSize, {\n    fill: \"white\"\n  })), pos && /*#__PURE__*/React.createElement(\"rect\", {\n    x: pos.left,\n    y: pos.top,\n    rx: pos.radius,\n    width: pos.width,\n    height: pos.height,\n    fill: \"black\",\n    className: mergedAnimated ? \"\".concat(prefixCls, \"-placeholder-animated\") : ''\n  }))), /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    fill: fill,\n    mask: \"url(#\".concat(maskId, \")\")\n  }), pos && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: pos.top\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: pos.left,\n    height: \"100%\"\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: pos.top + pos.height,\n    width: \"100%\",\n    height: \"calc(100vh - \".concat(pos.top + pos.height, \"px)\")\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: pos.left + pos.width,\n    y: \"0\",\n    width: \"calc(100vw - \".concat(pos.left + pos.width, \"px)\"),\n    height: \"100%\"\n  })))) : null));\n};\nexport default Mask;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE;AACjB,CAAC;AACD,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,GAAG,GAAGH,KAAK,CAACG,GAAG;IACfC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,YAAY,GAAGL,KAAK,CAACM,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,WAAW,GAAGP,KAAK,CAACH,IAAI;IACxBA,IAAI,GAAGU,WAAW,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,WAAW;IAC/DC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,mBAAmB,GAAGX,KAAK,CAACW,mBAAmB;EACjD,IAAIC,EAAE,GAAGjB,KAAK,CAAC,CAAC;EAChB,IAAIkB,MAAM,GAAG,EAAE,CAACC,MAAM,CAACb,SAAS,EAAE,QAAQ,CAAC,CAACa,MAAM,CAACF,EAAE,CAAC;EACtD,IAAIG,cAAc,GAAGxB,OAAO,CAACkB,QAAQ,CAAC,KAAK,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACO,WAAW,GAAGP,QAAQ;EACzI,IAAIQ,QAAQ,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,gCAAgC,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;EAC7G,IAAIC,YAAY,GAAGJ,QAAQ,GAAG;IAC5BK,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC,GAAG;IACFD,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE;EACV,CAAC;EACD,OAAO,aAAa/B,KAAK,CAACgC,aAAa,CAAC9B,MAAM,EAAE;IAC9Cc,IAAI,EAAEA,IAAI;IACViB,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAajC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACzCE,SAAS,EAAEjC,UAAU,CAAC,EAAE,CAACqB,MAAM,CAACb,SAAS,EAAE,OAAO,CAAC,EAAEC,aAAa,CAAC;IACnEI,KAAK,EAAEhB,aAAa,CAAC;MACnBqC,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTrB,MAAM,EAAEA,MAAM;MACdZ,aAAa,EAAEK,GAAG,IAAI,CAACQ,mBAAmB,GAAG,MAAM,GAAG;IACxD,CAAC,EAAEL,KAAK;EACV,CAAC,EAAEF,QAAQ,GAAG,aAAaZ,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACpDlB,KAAK,EAAE;MACLgB,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAa/B,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAahC,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAE;IACzFZ,EAAE,EAAEC;EACN,CAAC,EAAE,aAAarB,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC;IACnD2C,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE;EACL,CAAC,EAAEZ,YAAY,EAAE;IACfxB,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,EAAEM,GAAG,IAAI,aAAaX,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAE;IACnDQ,CAAC,EAAE7B,GAAG,CAACyB,IAAI;IACXK,CAAC,EAAE9B,GAAG,CAAC2B,GAAG;IACVI,EAAE,EAAE/B,GAAG,CAACgC,MAAM;IACdb,KAAK,EAAEnB,GAAG,CAACmB,KAAK;IAChBC,MAAM,EAAEpB,GAAG,CAACoB,MAAM;IAClB1B,IAAI,EAAE,OAAO;IACb6B,SAAS,EAAEX,cAAc,GAAG,EAAE,CAACD,MAAM,CAACb,SAAS,EAAE,uBAAuB,CAAC,GAAG;EAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAE;IAC7CQ,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNX,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd1B,IAAI,EAAEA,IAAI;IACVuC,IAAI,EAAE,OAAO,CAACtB,MAAM,CAACD,MAAM,EAAE,GAAG;EAClC,CAAC,CAAC,EAAEV,GAAG,IAAI,aAAaX,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAAC6C,QAAQ,EAAE,IAAI,EAAE,aAAa7C,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACnIoC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNX,KAAK,EAAE,MAAM;IACbC,MAAM,EAAEpB,GAAG,CAAC2B;EACd,CAAC,CAAC,CAAC,EAAE,aAAatC,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtEoC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNX,KAAK,EAAEnB,GAAG,CAACyB,IAAI;IACfL,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtEoC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE9B,GAAG,CAAC2B,GAAG,GAAG3B,GAAG,CAACoB,MAAM;IACvBD,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,eAAe,CAACT,MAAM,CAACX,GAAG,CAAC2B,GAAG,GAAG3B,GAAG,CAACoB,MAAM,EAAE,KAAK;EAC5D,CAAC,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtEoC,CAAC,EAAE7B,GAAG,CAACyB,IAAI,GAAGzB,GAAG,CAACmB,KAAK;IACvBW,CAAC,EAAE,GAAG;IACNX,KAAK,EAAE,eAAe,CAACR,MAAM,CAACX,GAAG,CAACyB,IAAI,GAAGzB,GAAG,CAACmB,KAAK,EAAE,KAAK,CAAC;IAC1DC,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAChB,CAAC;AACD,eAAexB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
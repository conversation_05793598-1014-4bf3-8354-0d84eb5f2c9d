{"ast": null, "code": "import * as React from 'react';\nexport default function usePickerRef(ref) {\n  var selectorRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current;\n    return {\n      nativeElement: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.nativeElement,\n      focus: function focus(options) {\n        var _selectorRef$current2;\n        (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 || _selectorRef$current2.focus(options);\n      },\n      blur: function blur() {\n        var _selectorRef$current3;\n        (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 || _selectorRef$current3.blur();\n      }\n    };\n  });\n  return selectorRef;\n}", "map": {"version": 3, "names": ["React", "usePickerRef", "ref", "selectorRef", "useRef", "useImperativeHandle", "_selectorRef$current", "nativeElement", "current", "focus", "options", "_selectorRef$current2", "blur", "_selectorRef$current3"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/hooks/usePickerRef.js"], "sourcesContent": ["import * as React from 'react';\nexport default function usePickerRef(ref) {\n  var selectorRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current;\n    return {\n      nativeElement: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.nativeElement,\n      focus: function focus(options) {\n        var _selectorRef$current2;\n        (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 || _selectorRef$current2.focus(options);\n      },\n      blur: function blur() {\n        var _selectorRef$current3;\n        (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 || _selectorRef$current3.blur();\n      }\n    };\n  });\n  return selectorRef;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,GAAG,EAAE;EACxC,IAAIC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC;EAChCJ,KAAK,CAACK,mBAAmB,CAACH,GAAG,EAAE,YAAY;IACzC,IAAII,oBAAoB;IACxB,OAAO;MACLC,aAAa,EAAE,CAACD,oBAAoB,GAAGH,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIF,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACC,aAAa;MACrJE,KAAK,EAAE,SAASA,KAAKA,CAACC,OAAO,EAAE;QAC7B,IAAIC,qBAAqB;QACzB,CAACA,qBAAqB,GAAGR,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIG,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACF,KAAK,CAACC,OAAO,CAAC;MACpI,CAAC;MACDE,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIC,qBAAqB;QACzB,CAACA,qBAAqB,GAAGV,WAAW,CAACK,OAAO,MAAM,IAAI,IAAIK,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACD,IAAI,CAAC,CAAC;MAC5H;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAOT,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\n// TODO: Remove when use `responsiveImmutable`\nvar PerfContext = /*#__PURE__*/React.createContext({\n  renderWithProps: false\n});\nexport default PerfContext;", "map": {"version": 3, "names": ["React", "PerfContext", "createContext", "renderWithProps"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/context/PerfContext.js"], "sourcesContent": ["import * as React from 'react';\n// TODO: Remove when use `responsiveImmutable`\nvar PerfContext = /*#__PURE__*/React.createContext({\n  renderWithProps: false\n});\nexport default PerfContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACjDC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
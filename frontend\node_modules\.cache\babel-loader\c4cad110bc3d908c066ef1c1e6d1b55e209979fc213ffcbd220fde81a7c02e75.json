{"ast": null, "code": "export default function useContainerWidth(prefixCls) {\n  const getContainerWidth = (ele, width) => {\n    const container = ele.querySelector(`.${prefixCls}-container`);\n    let returnWidth = width;\n    if (container) {\n      const style = getComputedStyle(container);\n      const borderLeft = parseInt(style.borderLeftWidth, 10);\n      const borderRight = parseInt(style.borderRightWidth, 10);\n      returnWidth = width - borderLeft - borderRight;\n    }\n    return returnWidth;\n  };\n  return getContainerWidth;\n}", "map": {"version": 3, "names": ["useContainerWidth", "prefixCls", "getContainer<PERSON>idth", "ele", "width", "container", "querySelector", "returnWidth", "style", "getComputedStyle", "borderLeft", "parseInt", "borderLeftWidth", "borderRight", "borderRightWidth"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/hooks/useContainerWidth.js"], "sourcesContent": ["export default function useContainerWidth(prefixCls) {\n  const getContainerWidth = (ele, width) => {\n    const container = ele.querySelector(`.${prefixCls}-container`);\n    let returnWidth = width;\n    if (container) {\n      const style = getComputedStyle(container);\n      const borderLeft = parseInt(style.borderLeftWidth, 10);\n      const borderRight = parseInt(style.borderRightWidth, 10);\n      returnWidth = width - borderLeft - borderRight;\n    }\n    return returnWidth;\n  };\n  return getContainerWidth;\n}"], "mappings": "AAAA,eAAe,SAASA,iBAAiBA,CAACC,SAAS,EAAE;EACnD,MAAMC,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxC,MAAMC,SAAS,GAAGF,GAAG,CAACG,aAAa,CAAC,IAAIL,SAAS,YAAY,CAAC;IAC9D,IAAIM,WAAW,GAAGH,KAAK;IACvB,IAAIC,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,gBAAgB,CAACJ,SAAS,CAAC;MACzC,MAAMK,UAAU,GAAGC,QAAQ,CAACH,KAAK,CAACI,eAAe,EAAE,EAAE,CAAC;MACtD,MAAMC,WAAW,GAAGF,QAAQ,CAACH,KAAK,CAACM,gBAAgB,EAAE,EAAE,CAAC;MACxDP,WAAW,GAAGH,KAAK,GAAGM,UAAU,GAAGG,WAAW;IAChD;IACA,OAAON,WAAW;EACpB,CAAC;EACD,OAAOL,iBAAiB;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
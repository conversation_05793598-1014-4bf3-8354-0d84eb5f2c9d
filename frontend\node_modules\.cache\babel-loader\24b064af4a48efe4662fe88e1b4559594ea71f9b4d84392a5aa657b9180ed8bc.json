{"ast": null, "code": "export function toArray(value) {\n  if (value === undefined || value === null) {\n    return [];\n  }\n  return Array.isArray(value) ? value : [value];\n}\nexport function isFormInstance(form) {\n  return form && !!form._init;\n}", "map": {"version": 3, "names": ["toArray", "value", "undefined", "Array", "isArray", "isFormInstance", "form", "_init"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-field-form@2.7.0_react-d_64f5b93340a107ccce19d223ada77c46/node_modules/rc-field-form/es/utils/typeUtil.js"], "sourcesContent": ["export function toArray(value) {\n  if (value === undefined || value === null) {\n    return [];\n  }\n  return Array.isArray(value) ? value : [value];\n}\nexport function isFormInstance(form) {\n  return form && !!form._init;\n}"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAO,EAAE;EACX;EACA,OAAOE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AAC/C;AACA,OAAO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOA,IAAI,IAAI,CAAC,CAACA,IAAI,CAACC,KAAK;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
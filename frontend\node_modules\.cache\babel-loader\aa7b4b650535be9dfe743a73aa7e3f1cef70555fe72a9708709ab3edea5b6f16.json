{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"showArrow\", \"headerClass\", \"isActive\", \"onItemClick\", \"forceRender\", \"className\", \"classNames\", \"styles\", \"prefixCls\", \"collapsible\", \"accordion\", \"panelKey\", \"extra\", \"header\", \"expandIcon\", \"openMotion\", \"destroyInactivePanel\", \"children\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport React from 'react';\nimport PanelContent from \"./PanelContent\";\nvar CollapsePanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    headerClass = props.headerClass,\n    isActive = props.isActive,\n    onItemClick = props.onItemClick,\n    forceRender = props.forceRender,\n    className = props.className,\n    _props$classNames = props.classNames,\n    customizeClassNames = _props$classNames === void 0 ? {} : _props$classNames,\n    _props$styles = props.styles,\n    styles = _props$styles === void 0 ? {} : _props$styles,\n    prefixCls = props.prefixCls,\n    collapsible = props.collapsible,\n    accordion = props.accordion,\n    panelKey = props.panelKey,\n    extra = props.extra,\n    header = props.header,\n    expandIcon = props.expandIcon,\n    openMotion = props.openMotion,\n    destroyInactivePanel = props.destroyInactivePanel,\n    children = props.children,\n    resetProps = _objectWithoutProperties(props, _excluded);\n  var disabled = collapsible === 'disabled';\n  var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n  var collapsibleProps = _defineProperty(_defineProperty(_defineProperty({\n    onClick: function onClick() {\n      onItemClick === null || onItemClick === void 0 || onItemClick(panelKey);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if (e.key === 'Enter' || e.keyCode === KeyCode.ENTER || e.which === KeyCode.ENTER) {\n        onItemClick === null || onItemClick === void 0 || onItemClick(panelKey);\n      }\n    },\n    role: accordion ? 'tab' : 'button'\n  }, 'aria-expanded', isActive), 'aria-disabled', disabled), \"tabIndex\", disabled ? -1 : 0);\n\n  // ======================== Icon ========================\n  var iconNodeInner = typeof expandIcon === 'function' ? expandIcon(props) : /*#__PURE__*/React.createElement(\"i\", {\n    className: \"arrow\"\n  });\n  var iconNode = iconNodeInner && /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-expand-icon\")\n  }, ['header', 'icon'].includes(collapsible) ? collapsibleProps : {}), iconNodeInner);\n  var collapsePanelClassNames = classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-item-active\"), isActive), \"\".concat(prefixCls, \"-item-disabled\"), disabled), className);\n  var headerClassName = classNames(headerClass, \"\".concat(prefixCls, \"-header\"), _defineProperty({}, \"\".concat(prefixCls, \"-collapsible-\").concat(collapsible), !!collapsible), customizeClassNames.header);\n\n  // ======================== HeaderProps ========================\n  var headerProps = _objectSpread({\n    className: headerClassName,\n    style: styles.header\n  }, ['header', 'icon'].includes(collapsible) ? {} : collapsibleProps);\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, resetProps, {\n    ref: ref,\n    className: collapsePanelClassNames\n  }), /*#__PURE__*/React.createElement(\"div\", headerProps, showArrow && iconNode, /*#__PURE__*/React.createElement(\"span\", _extends({\n    className: \"\".concat(prefixCls, \"-header-text\")\n  }, collapsible === 'header' ? collapsibleProps : {}), header), ifExtraExist && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: isActive,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  }, openMotion, {\n    forceRender: forceRender,\n    removeOnLeave: destroyInactivePanel\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(PanelContent, {\n      ref: motionRef,\n      prefixCls: prefixCls,\n      className: motionClassName,\n      classNames: customizeClassNames,\n      style: motionStyle,\n      styles: styles,\n      isActive: isActive,\n      forceRender: forceRender,\n      role: accordion ? 'tabpanel' : void 0\n    }, children);\n  }));\n});\nexport default CollapsePanel;", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_defineProperty", "_objectWithoutProperties", "_excluded", "classNames", "CSSMotion", "KeyCode", "React", "PanelContent", "CollapsePanel", "forwardRef", "props", "ref", "_props$showArrow", "showArrow", "headerClass", "isActive", "onItemClick", "forceRender", "className", "_props$classNames", "customizeClassNames", "_props$styles", "styles", "prefixCls", "collapsible", "accordion", "<PERSON><PERSON><PERSON>", "extra", "header", "expandIcon", "openMotion", "destroyInactivePanel", "children", "resetProps", "disabled", "ifExtraExist", "undefined", "collapsibleProps", "onClick", "onKeyDown", "e", "key", "keyCode", "ENTER", "which", "role", "iconNodeInner", "createElement", "iconNode", "concat", "includes", "collapsePanelClassNames", "headerClassName", "headerProps", "style", "visible", "leavedClassName", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-collapse@3.9.0_react-dom_fc0a782d916d1a18010b31eb7cc9b486/node_modules/rc-collapse/es/Panel.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"showArrow\", \"headerClass\", \"isActive\", \"onItemClick\", \"forceRender\", \"className\", \"classNames\", \"styles\", \"prefixCls\", \"collapsible\", \"accordion\", \"panelKey\", \"extra\", \"header\", \"expandIcon\", \"openMotion\", \"destroyInactivePanel\", \"children\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport React from 'react';\nimport PanelContent from \"./PanelContent\";\nvar CollapsePanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    headerClass = props.headerClass,\n    isActive = props.isActive,\n    onItemClick = props.onItemClick,\n    forceRender = props.forceRender,\n    className = props.className,\n    _props$classNames = props.classNames,\n    customizeClassNames = _props$classNames === void 0 ? {} : _props$classNames,\n    _props$styles = props.styles,\n    styles = _props$styles === void 0 ? {} : _props$styles,\n    prefixCls = props.prefixCls,\n    collapsible = props.collapsible,\n    accordion = props.accordion,\n    panelKey = props.panelKey,\n    extra = props.extra,\n    header = props.header,\n    expandIcon = props.expandIcon,\n    openMotion = props.openMotion,\n    destroyInactivePanel = props.destroyInactivePanel,\n    children = props.children,\n    resetProps = _objectWithoutProperties(props, _excluded);\n  var disabled = collapsible === 'disabled';\n  var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n  var collapsibleProps = _defineProperty(_defineProperty(_defineProperty({\n    onClick: function onClick() {\n      onItemClick === null || onItemClick === void 0 || onItemClick(panelKey);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if (e.key === 'Enter' || e.keyCode === KeyCode.ENTER || e.which === KeyCode.ENTER) {\n        onItemClick === null || onItemClick === void 0 || onItemClick(panelKey);\n      }\n    },\n    role: accordion ? 'tab' : 'button'\n  }, 'aria-expanded', isActive), 'aria-disabled', disabled), \"tabIndex\", disabled ? -1 : 0);\n\n  // ======================== Icon ========================\n  var iconNodeInner = typeof expandIcon === 'function' ? expandIcon(props) : /*#__PURE__*/React.createElement(\"i\", {\n    className: \"arrow\"\n  });\n  var iconNode = iconNodeInner && /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-expand-icon\")\n  }, ['header', 'icon'].includes(collapsible) ? collapsibleProps : {}), iconNodeInner);\n  var collapsePanelClassNames = classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-item-active\"), isActive), \"\".concat(prefixCls, \"-item-disabled\"), disabled), className);\n  var headerClassName = classNames(headerClass, \"\".concat(prefixCls, \"-header\"), _defineProperty({}, \"\".concat(prefixCls, \"-collapsible-\").concat(collapsible), !!collapsible), customizeClassNames.header);\n\n  // ======================== HeaderProps ========================\n  var headerProps = _objectSpread({\n    className: headerClassName,\n    style: styles.header\n  }, ['header', 'icon'].includes(collapsible) ? {} : collapsibleProps);\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, resetProps, {\n    ref: ref,\n    className: collapsePanelClassNames\n  }), /*#__PURE__*/React.createElement(\"div\", headerProps, showArrow && iconNode, /*#__PURE__*/React.createElement(\"span\", _extends({\n    className: \"\".concat(prefixCls, \"-header-text\")\n  }, collapsible === 'header' ? collapsibleProps : {}), header), ifExtraExist && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: isActive,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  }, openMotion, {\n    forceRender: forceRender,\n    removeOnLeave: destroyInactivePanel\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(PanelContent, {\n      ref: motionRef,\n      prefixCls: prefixCls,\n      className: motionClassName,\n      classNames: customizeClassNames,\n      style: motionStyle,\n      styles: styles,\n      isActive: isActive,\n      forceRender: forceRender,\n      role: accordion ? 'tabpanel' : void 0\n    }, children);\n  }));\n});\nexport default CollapsePanel;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,sBAAsB,EAAE,UAAU,CAAC;AACnQ,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,aAAa,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,iBAAiB,GAAGT,KAAK,CAACP,UAAU;IACpCiB,mBAAmB,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;IAC3EE,aAAa,GAAGX,KAAK,CAACY,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;IACtDE,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,WAAW,GAAGd,KAAK,CAACc,WAAW;IAC/BC,SAAS,GAAGf,KAAK,CAACe,SAAS;IAC3BC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,KAAK,GAAGjB,KAAK,CAACiB,KAAK;IACnBC,MAAM,GAAGlB,KAAK,CAACkB,MAAM;IACrBC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,UAAU,GAAGpB,KAAK,CAACoB,UAAU;IAC7BC,oBAAoB,GAAGrB,KAAK,CAACqB,oBAAoB;IACjDC,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,UAAU,GAAGhC,wBAAwB,CAACS,KAAK,EAAER,SAAS,CAAC;EACzD,IAAIgC,QAAQ,GAAGV,WAAW,KAAK,UAAU;EACzC,IAAIW,YAAY,GAAGR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKS,SAAS,IAAI,OAAOT,KAAK,KAAK,SAAS;EACtF,IAAIU,gBAAgB,GAAGrC,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC;IACrEsC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1BtB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACU,QAAQ,CAAC;IACzE,CAAC;IACDa,SAAS,EAAE,SAASA,SAASA,CAACC,CAAC,EAAE;MAC/B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKrC,OAAO,CAACsC,KAAK,IAAIH,CAAC,CAACI,KAAK,KAAKvC,OAAO,CAACsC,KAAK,EAAE;QACjF3B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACU,QAAQ,CAAC;MACzE;IACF,CAAC;IACDmB,IAAI,EAAEpB,SAAS,GAAG,KAAK,GAAG;EAC5B,CAAC,EAAE,eAAe,EAAEV,QAAQ,CAAC,EAAE,eAAe,EAAEmB,QAAQ,CAAC,EAAE,UAAU,EAAEA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;EAEzF;EACA,IAAIY,aAAa,GAAG,OAAOjB,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACnB,KAAK,CAAC,GAAG,aAAaJ,KAAK,CAACyC,aAAa,CAAC,GAAG,EAAE;IAC/G7B,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAI8B,QAAQ,GAAGF,aAAa,IAAI,aAAaxC,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAEhD,QAAQ,CAAC;IAC/EmB,SAAS,EAAE,EAAE,CAAC+B,MAAM,CAAC1B,SAAS,EAAE,cAAc;EAChD,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC2B,QAAQ,CAAC1B,WAAW,CAAC,GAAGa,gBAAgB,GAAG,CAAC,CAAC,CAAC,EAAES,aAAa,CAAC;EACpF,IAAIK,uBAAuB,GAAGhD,UAAU,CAAC,EAAE,CAAC8C,MAAM,CAAC1B,SAAS,EAAE,OAAO,CAAC,EAAEvB,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACiD,MAAM,CAAC1B,SAAS,EAAE,cAAc,CAAC,EAAER,QAAQ,CAAC,EAAE,EAAE,CAACkC,MAAM,CAAC1B,SAAS,EAAE,gBAAgB,CAAC,EAAEW,QAAQ,CAAC,EAAEhB,SAAS,CAAC;EAC1N,IAAIkC,eAAe,GAAGjD,UAAU,CAACW,WAAW,EAAE,EAAE,CAACmC,MAAM,CAAC1B,SAAS,EAAE,SAAS,CAAC,EAAEvB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACiD,MAAM,CAAC1B,SAAS,EAAE,eAAe,CAAC,CAAC0B,MAAM,CAACzB,WAAW,CAAC,EAAE,CAAC,CAACA,WAAW,CAAC,EAAEJ,mBAAmB,CAACQ,MAAM,CAAC;;EAEzM;EACA,IAAIyB,WAAW,GAAGvD,aAAa,CAAC;IAC9BoB,SAAS,EAAEkC,eAAe;IAC1BE,KAAK,EAAEhC,MAAM,CAACM;EAChB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACsB,QAAQ,CAAC1B,WAAW,CAAC,GAAG,CAAC,CAAC,GAAGa,gBAAgB,CAAC;;EAEpE;EACA,OAAO,aAAa/B,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,UAAU,EAAE;IACtEtB,GAAG,EAAEA,GAAG;IACRO,SAAS,EAAEiC;EACb,CAAC,CAAC,EAAE,aAAa7C,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAEM,WAAW,EAAExC,SAAS,IAAImC,QAAQ,EAAE,aAAa1C,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAEhD,QAAQ,CAAC;IAChImB,SAAS,EAAE,EAAE,CAAC+B,MAAM,CAAC1B,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEC,WAAW,KAAK,QAAQ,GAAGa,gBAAgB,GAAG,CAAC,CAAC,CAAC,EAAET,MAAM,CAAC,EAAEO,YAAY,IAAI,aAAa7B,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAE;IACrH7B,SAAS,EAAE,EAAE,CAAC+B,MAAM,CAAC1B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEI,KAAK,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACyC,aAAa,CAAC3C,SAAS,EAAEL,QAAQ,CAAC;IAC/DwD,OAAO,EAAExC,QAAQ;IACjByC,eAAe,EAAE,EAAE,CAACP,MAAM,CAAC1B,SAAS,EAAE,iBAAiB;EACzD,CAAC,EAAEO,UAAU,EAAE;IACbb,WAAW,EAAEA,WAAW;IACxBwC,aAAa,EAAE1B;EACjB,CAAC,CAAC,EAAE,UAAU2B,IAAI,EAAEC,SAAS,EAAE;IAC7B,IAAIC,eAAe,GAAGF,IAAI,CAACxC,SAAS;MAClC2C,WAAW,GAAGH,IAAI,CAACJ,KAAK;IAC1B,OAAO,aAAahD,KAAK,CAACyC,aAAa,CAACxC,YAAY,EAAE;MACpDI,GAAG,EAAEgD,SAAS;MACdpC,SAAS,EAAEA,SAAS;MACpBL,SAAS,EAAE0C,eAAe;MAC1BzD,UAAU,EAAEiB,mBAAmB;MAC/BkC,KAAK,EAAEO,WAAW;MAClBvC,MAAM,EAAEA,MAAM;MACdP,QAAQ,EAAEA,QAAQ;MAClBE,WAAW,EAAEA,WAAW;MACxB4B,IAAI,EAAEpB,SAAS,GAAG,UAAU,GAAG,KAAK;IACtC,CAAC,EAAEO,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAexB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
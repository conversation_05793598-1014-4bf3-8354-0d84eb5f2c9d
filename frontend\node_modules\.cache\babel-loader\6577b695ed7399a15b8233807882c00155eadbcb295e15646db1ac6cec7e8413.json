{"ast": null, "code": "/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;", "map": {"version": 3, "names": ["Column", "_"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-table@7.51.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-table/es/sugar/Column.js"], "sourcesContent": ["/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,IAAI;AACb;AACA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
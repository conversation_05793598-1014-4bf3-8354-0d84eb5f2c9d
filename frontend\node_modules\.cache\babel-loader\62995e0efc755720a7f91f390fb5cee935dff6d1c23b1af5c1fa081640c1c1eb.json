{"ast": null, "code": "import CSSCalculator from \"./CSSCalculator\";\nimport NumCalculator from \"./NumCalculator\";\nvar genCalc = function genCalc(type, unitlessCssVar) {\n  var Calculator = type === 'css' ? CSSCalculator : NumCalculator;\n  return function (num) {\n    return new Calculator(num, unitlessCssVar);\n  };\n};\nexport default genCalc;", "map": {"version": 3, "names": ["CSSCalculator", "NumCalculator", "genCalc", "type", "unitlessCssVar", "Calculator", "num"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/theme/calc/index.js"], "sourcesContent": ["import CSSCalculator from \"./CSSCalculator\";\nimport NumCalculator from \"./NumCalculator\";\nvar genCalc = function genCalc(type, unitlessCssVar) {\n  var Calculator = type === 'css' ? CSSCalculator : NumCalculator;\n  return function (num) {\n    return new Calculator(num, unitlessCssVar);\n  };\n};\nexport default genCalc;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAEC,cAAc,EAAE;EACnD,IAAIC,UAAU,GAAGF,IAAI,KAAK,KAAK,GAAGH,aAAa,GAAGC,aAAa;EAC/D,OAAO,UAAUK,GAAG,EAAE;IACpB,OAAO,IAAID,UAAU,CAACC,GAAG,EAAEF,cAAc,CAAC;EAC5C,CAAC;AACH,CAAC;AACD,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
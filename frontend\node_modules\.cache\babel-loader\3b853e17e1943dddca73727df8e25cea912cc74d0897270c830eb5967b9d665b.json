{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nconst viewSize = 100;\nconst borderWidth = viewSize / 5;\nconst radius = viewSize / 2 - borderWidth / 2;\nconst circumference = radius * 2 * Math.PI;\nconst position = 50;\nconst CustomCircle = props => {\n  const {\n    dotClassName,\n    style,\n    hasCircleCls\n  } = props;\n  return /*#__PURE__*/React.createElement(\"circle\", {\n    className: classNames(`${dotClassName}-circle`, {\n      [`${dotClassName}-circle-bg`]: hasCircleCls\n    }),\n    r: radius,\n    cx: position,\n    cy: position,\n    strokeWidth: borderWidth,\n    style: style\n  });\n};\nconst Progress = ({\n  percent,\n  prefixCls\n}) => {\n  const dotClassName = `${prefixCls}-dot`;\n  const holderClassName = `${dotClassName}-holder`;\n  const hideClassName = `${holderClassName}-hidden`;\n  const [render, setRender] = React.useState(false);\n  // ==================== Visible =====================\n  useLayoutEffect(() => {\n    if (percent !== 0) {\n      setRender(true);\n    }\n  }, [percent !== 0]);\n  // ==================== Progress ====================\n  const safePtg = Math.max(Math.min(percent, 100), 0);\n  // ===================== Render =====================\n  if (!render) {\n    return null;\n  }\n  const circleStyle = {\n    strokeDashoffset: `${circumference / 4}`,\n    strokeDasharray: `${circumference * safePtg / 100} ${circumference * (100 - safePtg) / 100}`\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(holderClassName, `${dotClassName}-progress`, safePtg <= 0 && hideClassName)\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: `0 0 ${viewSize} ${viewSize}`,\n    role: \"progressbar\",\n    \"aria-valuemin\": 0,\n    \"aria-valuemax\": 100,\n    \"aria-valuenow\": safePtg\n  }, /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    hasCircleCls: true\n  }), /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    style: circleStyle\n  })));\n};\nexport default Progress;", "map": {"version": 3, "names": ["React", "classNames", "useLayoutEffect", "viewSize", "borderWidth", "radius", "circumference", "Math", "PI", "position", "CustomCircle", "props", "dotClassName", "style", "hasCircleCls", "createElement", "className", "r", "cx", "cy", "strokeWidth", "Progress", "percent", "prefixCls", "holderClassName", "hideClassName", "render", "setRender", "useState", "safePtg", "max", "min", "circleStyle", "strokeDashoffset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewBox", "role"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/spin/Indicator/Progress.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nconst viewSize = 100;\nconst borderWidth = viewSize / 5;\nconst radius = viewSize / 2 - borderWidth / 2;\nconst circumference = radius * 2 * Math.PI;\nconst position = 50;\nconst CustomCircle = props => {\n  const {\n    dotClassName,\n    style,\n    hasCircleCls\n  } = props;\n  return /*#__PURE__*/React.createElement(\"circle\", {\n    className: classNames(`${dotClassName}-circle`, {\n      [`${dotClassName}-circle-bg`]: hasCircleCls\n    }),\n    r: radius,\n    cx: position,\n    cy: position,\n    strokeWidth: borderWidth,\n    style: style\n  });\n};\nconst Progress = ({\n  percent,\n  prefixCls\n}) => {\n  const dotClassName = `${prefixCls}-dot`;\n  const holderClassName = `${dotClassName}-holder`;\n  const hideClassName = `${holderClassName}-hidden`;\n  const [render, setRender] = React.useState(false);\n  // ==================== Visible =====================\n  useLayoutEffect(() => {\n    if (percent !== 0) {\n      setRender(true);\n    }\n  }, [percent !== 0]);\n  // ==================== Progress ====================\n  const safePtg = Math.max(Math.min(percent, 100), 0);\n  // ===================== Render =====================\n  if (!render) {\n    return null;\n  }\n  const circleStyle = {\n    strokeDashoffset: `${circumference / 4}`,\n    strokeDasharray: `${circumference * safePtg / 100} ${circumference * (100 - safePtg) / 100}`\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(holderClassName, `${dotClassName}-progress`, safePtg <= 0 && hideClassName)\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: `0 0 ${viewSize} ${viewSize}`,\n    role: \"progressbar\",\n    \"aria-valuemin\": 0,\n    \"aria-valuemax\": 100,\n    \"aria-valuenow\": safePtg\n  }, /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    hasCircleCls: true\n  }), /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    style: circleStyle\n  })));\n};\nexport default Progress;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,MAAMC,QAAQ,GAAG,GAAG;AACpB,MAAMC,WAAW,GAAGD,QAAQ,GAAG,CAAC;AAChC,MAAME,MAAM,GAAGF,QAAQ,GAAG,CAAC,GAAGC,WAAW,GAAG,CAAC;AAC7C,MAAME,aAAa,GAAGD,MAAM,GAAG,CAAC,GAAGE,IAAI,CAACC,EAAE;AAC1C,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,KAAK;IACLC;EACF,CAAC,GAAGH,KAAK;EACT,OAAO,aAAaX,KAAK,CAACe,aAAa,CAAC,QAAQ,EAAE;IAChDC,SAAS,EAAEf,UAAU,CAAC,GAAGW,YAAY,SAAS,EAAE;MAC9C,CAAC,GAAGA,YAAY,YAAY,GAAGE;IACjC,CAAC,CAAC;IACFG,CAAC,EAAEZ,MAAM;IACTa,EAAE,EAAET,QAAQ;IACZU,EAAE,EAAEV,QAAQ;IACZW,WAAW,EAAEhB,WAAW;IACxBS,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AACD,MAAMQ,QAAQ,GAAGA,CAAC;EAChBC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMX,YAAY,GAAG,GAAGW,SAAS,MAAM;EACvC,MAAMC,eAAe,GAAG,GAAGZ,YAAY,SAAS;EAChD,MAAMa,aAAa,GAAG,GAAGD,eAAe,SAAS;EACjD,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA1B,eAAe,CAAC,MAAM;IACpB,IAAIoB,OAAO,KAAK,CAAC,EAAE;MACjBK,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,OAAO,KAAK,CAAC,CAAC,CAAC;EACnB;EACA,MAAMO,OAAO,GAAGtB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,GAAG,CAACT,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;EACnD;EACA,IAAI,CAACI,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EACA,MAAMM,WAAW,GAAG;IAClBC,gBAAgB,EAAE,GAAG3B,aAAa,GAAG,CAAC,EAAE;IACxC4B,eAAe,EAAE,GAAG5B,aAAa,GAAGuB,OAAO,GAAG,GAAG,IAAIvB,aAAa,IAAI,GAAG,GAAGuB,OAAO,CAAC,GAAG,GAAG;EAC5F,CAAC;EACD,OAAO,aAAa7B,KAAK,CAACe,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEf,UAAU,CAACuB,eAAe,EAAE,GAAGZ,YAAY,WAAW,EAAEiB,OAAO,IAAI,CAAC,IAAIJ,aAAa;EAClG,CAAC,EAAE,aAAazB,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACzCoB,OAAO,EAAE,OAAOhC,QAAQ,IAAIA,QAAQ,EAAE;IACtCiC,IAAI,EAAE,aAAa;IACnB,eAAe,EAAE,CAAC;IAClB,eAAe,EAAE,GAAG;IACpB,eAAe,EAAEP;EACnB,CAAC,EAAE,aAAa7B,KAAK,CAACe,aAAa,CAACL,YAAY,EAAE;IAChDE,YAAY,EAAEA,YAAY;IAC1BE,YAAY,EAAE;EAChB,CAAC,CAAC,EAAE,aAAad,KAAK,CAACe,aAAa,CAACL,YAAY,EAAE;IACjDE,YAAY,EAAEA,YAAY;IAC1BC,KAAK,EAAEmB;EACT,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAeX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
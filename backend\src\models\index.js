const { sequelize, testConnection } = require('../config/database');

// 导入所有模型
const User = require('./User');
const Customer = require('./Customer');
const Supplier = require('./Supplier');
const Product = require('./Product');
const Order = require('./Order');
const OrderItem = require('./OrderItem');
const PurchaseOrder = require('./PurchaseOrder');
const PurchaseOrderItem = require('./PurchaseOrderItem');

// 定义模型关联关系

// 用户关联
User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(PurchaseOrder, { foreignKey: 'user_id', as: 'purchaseOrders' });
PurchaseOrder.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 客户关联
Customer.hasMany(Order, { foreignKey: 'customer_id', as: 'orders' });
Order.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });

// 供应商关联
Supplier.hasMany(PurchaseOrder, { foreignKey: 'supplier_id', as: 'purchaseOrders' });
PurchaseOrder.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });

// 订单和订单明细关联
Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items', onDelete: 'CASCADE' });
OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// 采购订单和采购订单明细关联
PurchaseOrder.hasMany(PurchaseOrderItem, { 
  foreignKey: 'purchase_order_id', 
  as: 'items', 
  onDelete: 'CASCADE' 
});
PurchaseOrderItem.belongsTo(PurchaseOrder, { 
  foreignKey: 'purchase_order_id', 
  as: 'purchaseOrder' 
});

// 产品关联
Product.hasMany(OrderItem, { foreignKey: 'product_id', as: 'orderItems' });
OrderItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

Product.hasMany(PurchaseOrderItem, { foreignKey: 'product_id', as: 'purchaseOrderItems' });
PurchaseOrderItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

// 导出所有模型和sequelize实例
module.exports = {
  sequelize,
  User,
  Customer,
  Supplier,
  Product,
  Order,
  OrderItem,
  PurchaseOrder,
  PurchaseOrderItem
};

// 同步数据库（仅在开发环境）
const syncDatabase = async () => {
  try {
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('数据库同步成功');
    }
  } catch (error) {
    console.error('数据库同步失败:', error);
  }
};

module.exports.syncDatabase = syncDatabase;
module.exports.testConnection = testConnection;

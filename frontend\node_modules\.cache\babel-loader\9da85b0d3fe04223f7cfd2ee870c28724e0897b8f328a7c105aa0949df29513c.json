{"ast": null, "code": "const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky,\n    calc\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`\n        ${componentCls}-cell-fix-left,\n        ${componentCls}-cell-fix-right\n      `]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: calc(lineWidth).mul(-1).equal(),\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-cell-fix-left-all::after`]: {\n        display: 'none'\n      },\n      [`\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        bottom: calc(lineWidth).mul(-1).equal(),\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-container`]: {\n        position: 'relative',\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: calc(zIndexTableSticky).add(1).equal({\n            unit: false\n          }),\n          width: 30,\n          transition: `box-shadow ${motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [`${componentCls}-ping-left`]: {\n        [`&:not(${componentCls}-has-fix-left) ${componentCls}-container::before`]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-left-first::after,\n          ${componentCls}-cell-fix-left-last::after\n        `]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`${componentCls}-cell-fix-left-last::before`]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`${componentCls}-ping-right`]: {\n        [`&:not(${componentCls}-has-fix-right) ${componentCls}-container::after`]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-right-first::after,\n          ${componentCls}-cell-fix-right-last::after\n        `]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        }\n      },\n      // Gapped fixed Columns do not show the shadow\n      [`${componentCls}-fixed-column-gapped`]: {\n        [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after,\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n          boxShadow: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;", "map": {"version": 3, "names": ["genFixedStyle", "token", "componentCls", "lineWidth", "colorSplit", "motionDurationSlow", "zIndexTableFixed", "tableBg", "zIndexTableSticky", "calc", "shadowColor", "position", "zIndex", "background", "top", "right", "_skip_check_", "value", "bottom", "mul", "equal", "width", "transform", "transition", "content", "pointerEvents", "display", "left", "add", "unit", "insetInlineStart", "insetInlineEnd", "boxShadow", "backgroundColor"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/style/fixed.js"], "sourcesContent": ["const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky,\n    calc\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`\n        ${componentCls}-cell-fix-left,\n        ${componentCls}-cell-fix-right\n      `]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: calc(lineWidth).mul(-1).equal(),\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-cell-fix-left-all::after`]: {\n        display: 'none'\n      },\n      [`\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        bottom: calc(lineWidth).mul(-1).equal(),\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-container`]: {\n        position: 'relative',\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: calc(zIndexTableSticky).add(1).equal({\n            unit: false\n          }),\n          width: 30,\n          transition: `box-shadow ${motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [`${componentCls}-ping-left`]: {\n        [`&:not(${componentCls}-has-fix-left) ${componentCls}-container::before`]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-left-first::after,\n          ${componentCls}-cell-fix-left-last::after\n        `]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`${componentCls}-cell-fix-left-last::before`]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`${componentCls}-ping-right`]: {\n        [`&:not(${componentCls}-has-fix-right) ${componentCls}-container::after`]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-right-first::after,\n          ${componentCls}-cell-fix-right-last::after\n        `]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        }\n      },\n      // Gapped fixed Columns do not show the shadow\n      [`${componentCls}-fixed-column-gapped`]: {\n        [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after,\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n          boxShadow: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;"], "mappings": "AAAA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,UAAU;IACVC,kBAAkB;IAClBC,gBAAgB;IAChBC,OAAO;IACPC,iBAAiB;IACjBC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,WAAW,GAAGN,UAAU;EAC9B;EACA,OAAO;IACL,CAAC,GAAGF,YAAY,UAAU,GAAG;MAC3B,CAAC;AACP,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,OAAO,GAAG;QACFS,QAAQ,EAAE,mBAAmB;QAC7BC,MAAM,EAAEN,gBAAgB;QACxBO,UAAU,EAAEN;MACd,CAAC;MACD,CAAC;AACP,UAAUL,YAAY;AACtB,UAAUA,YAAY;AACtB,OAAO,GAAG;QACFS,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE;UACLC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDC,MAAM,EAAET,IAAI,CAACN,SAAS,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACvCC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,EAAE,cAAclB,kBAAkB,EAAE;QAC9CmB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAGvB,YAAY,2BAA2B,GAAG;QAC5CwB,OAAO,EAAE;MACX,CAAC;MACD,CAAC;AACP,UAAUxB,YAAY;AACtB,UAAUA,YAAY;AACtB,OAAO,GAAG;QACFS,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNI,MAAM,EAAET,IAAI,CAACN,SAAS,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACvCO,IAAI,EAAE;UACJX,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDI,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,EAAE,cAAclB,kBAAkB,EAAE;QAC9CmB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAGvB,YAAY,YAAY,GAAG;QAC7BS,QAAQ,EAAE,UAAU;QACpB,qBAAqB,EAAE;UACrBA,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNI,MAAM,EAAE,CAAC;UACTN,MAAM,EAAEH,IAAI,CAACD,iBAAiB,CAAC,CAACoB,GAAG,CAAC,CAAC,CAAC,CAACR,KAAK,CAAC;YAC3CS,IAAI,EAAE;UACR,CAAC,CAAC;UACFR,KAAK,EAAE,EAAE;UACTE,UAAU,EAAE,cAAclB,kBAAkB,EAAE;UAC9CmB,OAAO,EAAE,IAAI;UACbC,aAAa,EAAE;QACjB,CAAC;QACD,WAAW,EAAE;UACXK,gBAAgB,EAAE;QACpB,CAAC;QACD,UAAU,EAAE;UACVC,cAAc,EAAE;QAClB;MACF,CAAC;MACD,CAAC,GAAG7B,YAAY,YAAY,GAAG;QAC7B,CAAC,SAASA,YAAY,kBAAkBA,YAAY,oBAAoB,GAAG;UACzE8B,SAAS,EAAE,yBAAyBtB,WAAW;QACjD,CAAC;QACD,CAAC;AACT,YAAYR,YAAY;AACxB,YAAYA,YAAY;AACxB,SAAS,GAAG;UACF8B,SAAS,EAAE,yBAAyBtB,WAAW;QACjD,CAAC;QACD,CAAC,GAAGR,YAAY,6BAA6B,GAAG;UAC9C+B,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,GAAG/B,YAAY,aAAa,GAAG;QAC9B,CAAC,SAASA,YAAY,mBAAmBA,YAAY,mBAAmB,GAAG;UACzE8B,SAAS,EAAE,0BAA0BtB,WAAW;QAClD,CAAC;QACD,CAAC;AACT,YAAYR,YAAY;AACxB,YAAYA,YAAY;AACxB,SAAS,GAAG;UACF8B,SAAS,EAAE,0BAA0BtB,WAAW;QAClD;MACF,CAAC;MACD;MACA,CAAC,GAAGR,YAAY,sBAAsB,GAAG;QACvC,CAAC;AACT,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,OAAO,GAAG;UACA8B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAehC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
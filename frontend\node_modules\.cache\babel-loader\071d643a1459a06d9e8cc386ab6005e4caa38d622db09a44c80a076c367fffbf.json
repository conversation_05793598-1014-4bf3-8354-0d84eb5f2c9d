{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    headerIconColor,\n    headerIconHoverColor,\n    tableSelectionColumnWidth,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableRowHoverBg,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).equal()\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: calc(tableSelectionColumnWidth).add(calc(paddingXS).mul(2)).equal(),\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).add(calc(paddingXS).mul(2)).equal()\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column,\n        ${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: calc(token.zIndexTableFixed).add(1).equal({\n          unit: false\n        })\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: unit(calc(tablePaddingHorizontal).div(4).equal()),\n        [iconCls]: {\n          color: headerIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: headerIconHoverColor\n          }\n        }\n      },\n      // ============================= Rows =============================\n      [`${componentCls}-tbody`]: {\n        [`${componentCls}-row`]: {\n          [`&${componentCls}-row-selected`]: {\n            [`> ${componentCls}-cell`]: {\n              background: tableSelectedRowBg,\n              '&-row-hover': {\n                background: tableSelectedRowHoverBg\n              }\n            }\n          },\n          [`> ${componentCls}-cell-row-hover`]: {\n            background: tableRowHoverBg\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;", "map": {"version": 3, "names": ["unit", "genSelectionStyle", "token", "componentCls", "antCls", "iconCls", "fontSizeIcon", "padding", "paddingXS", "headerIconColor", "headerIconHoverColor", "tableSelectionColumnWidth", "tableSelectedRowBg", "tableSelectedRowHoverBg", "tableRowHoverBg", "tablePaddingHorizontal", "calc", "width", "add", "div", "equal", "mul", "paddingInlineEnd", "paddingInlineStart", "textAlign", "marginInlineEnd", "zIndex", "zIndexTableFixed", "backgroundColor", "position", "display", "flexDirection", "top", "cursor", "transition", "motionDurationSlow", "marginInlineStart", "color", "fontSize", "verticalAlign", "background"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/style/selection.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    headerIconColor,\n    headerIconHoverColor,\n    tableSelectionColumnWidth,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableRowHoverBg,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).equal()\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: calc(tableSelectionColumnWidth).add(calc(paddingXS).mul(2)).equal(),\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).add(calc(paddingXS).mul(2)).equal()\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column,\n        ${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: calc(token.zIndexTableFixed).add(1).equal({\n          unit: false\n        })\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: unit(calc(tablePaddingHorizontal).div(4).equal()),\n        [iconCls]: {\n          color: headerIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: headerIconHoverColor\n          }\n        }\n      },\n      // ============================= Rows =============================\n      [`${componentCls}-tbody`]: {\n        [`${componentCls}-row`]: {\n          [`&${componentCls}-row-selected`]: {\n            [`> ${componentCls}-cell`]: {\n              background: tableSelectedRowBg,\n              '&-row-hover': {\n                background: tableSelectedRowHoverBg\n              }\n            }\n          },\n          [`> ${componentCls}-cell-row-hover`]: {\n            background: tableRowHoverBg\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,OAAO;IACPC,YAAY;IACZC,OAAO;IACPC,SAAS;IACTC,eAAe;IACfC,oBAAoB;IACpBC,yBAAyB;IACzBC,kBAAkB;IAClBC,uBAAuB;IACvBC,eAAe;IACfC,sBAAsB;IACtBC;EACF,CAAC,GAAGd,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B;MACA,CAAC,GAAGA,YAAY,gBAAgB,GAAG;QACjCc,KAAK,EAAEN,yBAAyB;QAChC,CAAC,IAAIR,YAAY,8BAA8B,GAAG;UAChDc,KAAK,EAAED,IAAI,CAACL,yBAAyB,CAAC,CAACO,GAAG,CAACZ,YAAY,CAAC,CAACY,GAAG,CAACF,IAAI,CAACT,OAAO,CAAC,CAACY,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QAC3F;MACF,CAAC;MACD,CAAC,GAAGjB,YAAY,aAAaA,YAAY,gBAAgB,GAAG;QAC1Dc,KAAK,EAAED,IAAI,CAACL,yBAAyB,CAAC,CAACO,GAAG,CAACF,IAAI,CAACR,SAAS,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QAC1E,CAAC,IAAIjB,YAAY,8BAA8B,GAAG;UAChDc,KAAK,EAAED,IAAI,CAACL,yBAAyB,CAAC,CAACO,GAAG,CAACZ,YAAY,CAAC,CAACY,GAAG,CAACF,IAAI,CAACT,OAAO,CAAC,CAACY,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,GAAG,CAACF,IAAI,CAACR,SAAS,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QACvH;MACF,CAAC;MACD,CAAC;AACP,qBAAqBjB,YAAY;AACjC,qBAAqBA,YAAY;AACjC,UAAUA,YAAY;AACtB,OAAO,GAAG;QACFmB,gBAAgB,EAAEpB,KAAK,CAACM,SAAS;QACjCe,kBAAkB,EAAErB,KAAK,CAACM,SAAS;QACnCgB,SAAS,EAAE,QAAQ;QACnB,CAAC,GAAGpB,MAAM,gBAAgB,GAAG;UAC3BqB,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,cAActB,YAAY,oBAAoBA,YAAY,gBAAgB,GAAG;QAC5EuB,MAAM,EAAEV,IAAI,CAACd,KAAK,CAACyB,gBAAgB,CAAC,CAACT,GAAG,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;UAChDpB,IAAI,EAAE;QACR,CAAC;MACH,CAAC;MACD,CAAC,cAAcG,YAAY,0BAA0B,GAAG;QACtDyB,eAAe,EAAE;MACnB,CAAC;MACD,CAAC,GAAGzB,YAAY,YAAY,GAAG;QAC7B0B,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,aAAa;QACtBC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAG5B,YAAY,kBAAkB,GAAG;QACnC0B,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNN,MAAM,EAAE,CAAC;QACTO,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,OAAOhC,KAAK,CAACiC,kBAAkB,EAAE;QAC7CC,iBAAiB,EAAE,MAAM;QACzBb,kBAAkB,EAAEvB,IAAI,CAACgB,IAAI,CAACD,sBAAsB,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QACrE,CAACf,OAAO,GAAG;UACTgC,KAAK,EAAE5B,eAAe;UACtB6B,QAAQ,EAAEhC,YAAY;UACtBiC,aAAa,EAAE,UAAU;UACzB,SAAS,EAAE;YACTF,KAAK,EAAE3B;UACT;QACF;MACF,CAAC;MACD;MACA,CAAC,GAAGP,YAAY,QAAQ,GAAG;QACzB,CAAC,GAAGA,YAAY,MAAM,GAAG;UACvB,CAAC,IAAIA,YAAY,eAAe,GAAG;YACjC,CAAC,KAAKA,YAAY,OAAO,GAAG;cAC1BqC,UAAU,EAAE5B,kBAAkB;cAC9B,aAAa,EAAE;gBACb4B,UAAU,EAAE3B;cACd;YACF;UACF,CAAC;UACD,CAAC,KAAKV,YAAY,iBAAiB,GAAG;YACpCqC,UAAU,EAAE1B;UACd;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
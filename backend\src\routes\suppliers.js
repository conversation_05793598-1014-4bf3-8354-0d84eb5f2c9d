const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const supplierController = require('../controllers/supplierController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createSupplierValidation = [
  body('company_name')
    .isLength({ min: 2, max: 200 })
    .withMessage('公司名称长度必须在2-200个字符之间')
    .trim(),
  body('contact_person')
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('supplier_type')
    .isIn(['manufacturer', 'distributor', 'service'])
    .withMessage('供应商类型必须是: manufacturer, distributor, service'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('付款期限必须是非负整数'),
  body('rating')
    .optional()
    .isFloat({ min: 1, max: 5 })
    .withMessage('评级必须在1-5之间')
];

const updateSupplierValidation = [
  body('company_name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('公司名称长度必须在2-200个字符之间')
    .trim(),
  body('contact_person')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('联系人姓名长度必须在2-100个字符之间')
    .trim(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址长度不能超过500个字符')
    .trim(),
  body('supplier_type')
    .optional()
    .isIn(['manufacturer', 'distributor', 'service'])
    .withMessage('供应商类型必须是: manufacturer, distributor, service'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('付款期限必须是非负整数'),
  body('rating')
    .optional()
    .isFloat({ min: 1, max: 5 })
    .withMessage('评级必须在1-5之间')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取供应商列表
router.get('/', 
  auth,
  paginationValidation,
  supplierController.getSuppliers
);

// 获取供应商详情
router.get('/:id', 
  auth,
  supplierController.getSupplierById
);

// 创建供应商
router.post('/', 
  auth,
  authorize('admin', 'manager', 'employee'),
  createSupplierValidation,
  supplierController.createSupplier
);

// 更新供应商
router.put('/:id', 
  auth,
  authorize('admin', 'manager', 'employee'),
  updateSupplierValidation,
  supplierController.updateSupplier
);

// 删除供应商
router.delete('/:id', 
  auth,
  authorize('admin', 'manager'),
  supplierController.deleteSupplier
);

// 更新供应商状态
router.patch('/:id/status', 
  auth,
  authorize('admin', 'manager'),
  body('status').isIn(['active', 'inactive']).withMessage('状态必须是: active, inactive'),
  supplierController.updateSupplierStatus
);

module.exports = router;

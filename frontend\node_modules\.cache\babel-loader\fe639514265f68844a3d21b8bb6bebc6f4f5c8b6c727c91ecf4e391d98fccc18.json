{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    list.push(/*#__PURE__*/React.createElement(\"span\", {\n      key: i,\n      className: classNames(baseClassName, _defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-start\"), isStart[i]), \"\".concat(baseClassName, \"-end\"), isEnd[i]))\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\nexport default /*#__PURE__*/React.memo(Indent);", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "Indent", "_ref", "prefixCls", "level", "isStart", "isEnd", "baseClassName", "concat", "list", "i", "push", "createElement", "key", "className", "memo"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-tree@5.13.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/rc-tree/es/Indent.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    list.push( /*#__PURE__*/React.createElement(\"span\", {\n      key: i,\n      className: classNames(baseClassName, _defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-start\"), isStart[i]), \"\".concat(baseClassName, \"-end\"), isEnd[i]))\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\nexport default /*#__PURE__*/React.memo(Indent);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,cAAc,CAAC;EACxD,IAAIM,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,IAAI,CAAC,EAAE;IACjCD,IAAI,CAACE,IAAI,CAAE,aAAaX,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;MAClDC,GAAG,EAAEH,CAAC;MACNI,SAAS,EAAEf,UAAU,CAACQ,aAAa,EAAET,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACU,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC,EAAEF,OAAO,CAACK,CAAC,CAAC,CAAC,EAAE,EAAE,CAACF,MAAM,CAACD,aAAa,EAAE,MAAM,CAAC,EAAED,KAAK,CAACI,CAAC,CAAC,CAAC;IACvK,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAaV,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IAC9C,aAAa,EAAE,MAAM;IACrBE,SAAS,EAAE,EAAE,CAACN,MAAM,CAACL,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEM,IAAI,CAAC;AACV,CAAC;AACD,eAAe,aAAaT,KAAK,CAACe,IAAI,CAACd,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
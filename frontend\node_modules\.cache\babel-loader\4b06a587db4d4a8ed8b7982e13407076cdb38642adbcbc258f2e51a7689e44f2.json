{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable no-param-reassign */\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { warning } from 'rc-util';\nvar MAX_TIMES = 10;\nexport default function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {\n  var scrollRef = React.useRef();\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    syncState = _React$useState2[0],\n    setSyncState = _React$useState2[1];\n\n  // ========================== Sync Scroll ==========================\n  useLayoutEffect(function () {\n    if (syncState && syncState.times < MAX_TIMES) {\n      // Never reach\n      if (!containerRef.current) {\n        setSyncState(function (ori) {\n          return _objectSpread({}, ori);\n        });\n        return;\n      }\n      collectHeight();\n      var targetAlign = syncState.targetAlign,\n        originAlign = syncState.originAlign,\n        index = syncState.index,\n        offset = syncState.offset;\n      var height = containerRef.current.clientHeight;\n      var needCollectHeight = false;\n      var newTargetAlign = targetAlign;\n      var targetTop = null;\n\n      // Go to next frame if height not exist\n      if (height) {\n        var mergedAlign = targetAlign || originAlign;\n\n        // Get top & bottom\n        var stackTop = 0;\n        var itemTop = 0;\n        var itemBottom = 0;\n        var maxLen = Math.min(data.length - 1, index);\n        for (var i = 0; i <= maxLen; i += 1) {\n          var key = getKey(data[i]);\n          itemTop = stackTop;\n          var cacheHeight = heights.get(key);\n          itemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n          stackTop = itemBottom;\n        }\n\n        // Check if need sync height (visible range has item not record height)\n        var leftHeight = mergedAlign === 'top' ? offset : height - offset;\n        for (var _i = maxLen; _i >= 0; _i -= 1) {\n          var _key = getKey(data[_i]);\n          var _cacheHeight = heights.get(_key);\n          if (_cacheHeight === undefined) {\n            needCollectHeight = true;\n            break;\n          }\n          leftHeight -= _cacheHeight;\n          if (leftHeight <= 0) {\n            break;\n          }\n        }\n\n        // Scroll to\n        switch (mergedAlign) {\n          case 'top':\n            targetTop = itemTop - offset;\n            break;\n          case 'bottom':\n            targetTop = itemBottom - height + offset;\n            break;\n          default:\n            {\n              var scrollTop = containerRef.current.scrollTop;\n              var scrollBottom = scrollTop + height;\n              if (itemTop < scrollTop) {\n                newTargetAlign = 'top';\n              } else if (itemBottom > scrollBottom) {\n                newTargetAlign = 'bottom';\n              }\n            }\n        }\n        if (targetTop !== null) {\n          syncScrollTop(targetTop);\n        }\n\n        // One more time for sync\n        if (targetTop !== syncState.lastTop) {\n          needCollectHeight = true;\n        }\n      }\n\n      // Trigger next effect\n      if (needCollectHeight) {\n        setSyncState(_objectSpread(_objectSpread({}, syncState), {}, {\n          times: syncState.times + 1,\n          targetAlign: newTargetAlign,\n          lastTop: targetTop\n        }));\n      }\n    } else if (process.env.NODE_ENV !== 'production' && (syncState === null || syncState === void 0 ? void 0 : syncState.times) === MAX_TIMES) {\n      warning(false, 'Seems `scrollTo` with `rc-virtual-list` reach the max limitation. Please fire issue for us. Thanks.');\n    }\n  }, [syncState, containerRef.current]);\n\n  // =========================== Scroll To ===========================\n  return function (arg) {\n    // When not argument provided, we think dev may want to show the scrollbar\n    if (arg === null || arg === undefined) {\n      triggerFlash();\n      return;\n    }\n\n    // Normal scroll logic\n    raf.cancel(scrollRef.current);\n    if (typeof arg === 'number') {\n      syncScrollTop(arg);\n    } else if (arg && _typeof(arg) === 'object') {\n      var index;\n      var align = arg.align;\n      if ('index' in arg) {\n        index = arg.index;\n      } else {\n        index = data.findIndex(function (item) {\n          return getKey(item) === arg.key;\n        });\n      }\n      var _arg$offset = arg.offset,\n        offset = _arg$offset === void 0 ? 0 : _arg$offset;\n      setSyncState({\n        times: 0,\n        index: index,\n        offset: offset,\n        originAlign: align\n      });\n    }\n  };\n}", "map": {"version": 3, "names": ["_typeof", "_objectSpread", "_slicedToArray", "React", "raf", "useLayoutEffect", "warning", "MAX_TIMES", "useScrollTo", "containerRef", "data", "heights", "itemHeight", "<PERSON><PERSON><PERSON>", "collectHeight", "syncScrollTop", "triggerFlash", "scrollRef", "useRef", "_React$useState", "useState", "_React$useState2", "syncState", "setSyncState", "times", "current", "ori", "targetAlign", "originAlign", "index", "offset", "height", "clientHeight", "needCollectHeight", "newTargetAlign", "targetTop", "mergedAlign", "stackTop", "itemTop", "itemBottom", "maxLen", "Math", "min", "length", "i", "key", "cacheHeight", "get", "undefined", "leftHeight", "_i", "_key", "_cacheHeight", "scrollTop", "scrollBottom", "lastTop", "process", "env", "NODE_ENV", "arg", "cancel", "align", "findIndex", "item", "_arg$offset"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-virtual-list@3.19.1_reac_faad33af14cf2eb0b57167af327c7e91/node_modules/rc-virtual-list/es/hooks/useScrollTo.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable no-param-reassign */\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { warning } from 'rc-util';\nvar MAX_TIMES = 10;\nexport default function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {\n  var scrollRef = React.useRef();\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    syncState = _React$useState2[0],\n    setSyncState = _React$useState2[1];\n\n  // ========================== Sync Scroll ==========================\n  useLayoutEffect(function () {\n    if (syncState && syncState.times < MAX_TIMES) {\n      // Never reach\n      if (!containerRef.current) {\n        setSyncState(function (ori) {\n          return _objectSpread({}, ori);\n        });\n        return;\n      }\n      collectHeight();\n      var targetAlign = syncState.targetAlign,\n        originAlign = syncState.originAlign,\n        index = syncState.index,\n        offset = syncState.offset;\n      var height = containerRef.current.clientHeight;\n      var needCollectHeight = false;\n      var newTargetAlign = targetAlign;\n      var targetTop = null;\n\n      // Go to next frame if height not exist\n      if (height) {\n        var mergedAlign = targetAlign || originAlign;\n\n        // Get top & bottom\n        var stackTop = 0;\n        var itemTop = 0;\n        var itemBottom = 0;\n        var maxLen = Math.min(data.length - 1, index);\n        for (var i = 0; i <= maxLen; i += 1) {\n          var key = getKey(data[i]);\n          itemTop = stackTop;\n          var cacheHeight = heights.get(key);\n          itemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n          stackTop = itemBottom;\n        }\n\n        // Check if need sync height (visible range has item not record height)\n        var leftHeight = mergedAlign === 'top' ? offset : height - offset;\n        for (var _i = maxLen; _i >= 0; _i -= 1) {\n          var _key = getKey(data[_i]);\n          var _cacheHeight = heights.get(_key);\n          if (_cacheHeight === undefined) {\n            needCollectHeight = true;\n            break;\n          }\n          leftHeight -= _cacheHeight;\n          if (leftHeight <= 0) {\n            break;\n          }\n        }\n\n        // Scroll to\n        switch (mergedAlign) {\n          case 'top':\n            targetTop = itemTop - offset;\n            break;\n          case 'bottom':\n            targetTop = itemBottom - height + offset;\n            break;\n          default:\n            {\n              var scrollTop = containerRef.current.scrollTop;\n              var scrollBottom = scrollTop + height;\n              if (itemTop < scrollTop) {\n                newTargetAlign = 'top';\n              } else if (itemBottom > scrollBottom) {\n                newTargetAlign = 'bottom';\n              }\n            }\n        }\n        if (targetTop !== null) {\n          syncScrollTop(targetTop);\n        }\n\n        // One more time for sync\n        if (targetTop !== syncState.lastTop) {\n          needCollectHeight = true;\n        }\n      }\n\n      // Trigger next effect\n      if (needCollectHeight) {\n        setSyncState(_objectSpread(_objectSpread({}, syncState), {}, {\n          times: syncState.times + 1,\n          targetAlign: newTargetAlign,\n          lastTop: targetTop\n        }));\n      }\n    } else if (process.env.NODE_ENV !== 'production' && (syncState === null || syncState === void 0 ? void 0 : syncState.times) === MAX_TIMES) {\n      warning(false, 'Seems `scrollTo` with `rc-virtual-list` reach the max limitation. Please fire issue for us. Thanks.');\n    }\n  }, [syncState, containerRef.current]);\n\n  // =========================== Scroll To ===========================\n  return function (arg) {\n    // When not argument provided, we think dev may want to show the scrollbar\n    if (arg === null || arg === undefined) {\n      triggerFlash();\n      return;\n    }\n\n    // Normal scroll logic\n    raf.cancel(scrollRef.current);\n    if (typeof arg === 'number') {\n      syncScrollTop(arg);\n    } else if (arg && _typeof(arg) === 'object') {\n      var index;\n      var align = arg.align;\n      if ('index' in arg) {\n        index = arg.index;\n      } else {\n        index = data.findIndex(function (item) {\n          return getKey(item) === arg.key;\n        });\n      }\n      var _arg$offset = arg.offset,\n        offset = _arg$offset === void 0 ? 0 : _arg$offset;\n      setSyncState({\n        times: 0,\n        index: index,\n        offset: offset,\n        originAlign: align\n      });\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,OAAO,QAAQ,SAAS;AACjC,IAAIC,SAAS,GAAG,EAAE;AAClB,eAAe,SAASC,WAAWA,CAACC,YAAY,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAE;EAC/H,IAAIC,SAAS,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;EAC9B,IAAIC,eAAe,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGnB,cAAc,CAACiB,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEpC;EACAhB,eAAe,CAAC,YAAY;IAC1B,IAAIiB,SAAS,IAAIA,SAAS,CAACE,KAAK,GAAGjB,SAAS,EAAE;MAC5C;MACA,IAAI,CAACE,YAAY,CAACgB,OAAO,EAAE;QACzBF,YAAY,CAAC,UAAUG,GAAG,EAAE;UAC1B,OAAOzB,aAAa,CAAC,CAAC,CAAC,EAAEyB,GAAG,CAAC;QAC/B,CAAC,CAAC;QACF;MACF;MACAZ,aAAa,CAAC,CAAC;MACf,IAAIa,WAAW,GAAGL,SAAS,CAACK,WAAW;QACrCC,WAAW,GAAGN,SAAS,CAACM,WAAW;QACnCC,KAAK,GAAGP,SAAS,CAACO,KAAK;QACvBC,MAAM,GAAGR,SAAS,CAACQ,MAAM;MAC3B,IAAIC,MAAM,GAAGtB,YAAY,CAACgB,OAAO,CAACO,YAAY;MAC9C,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,IAAIC,cAAc,GAAGP,WAAW;MAChC,IAAIQ,SAAS,GAAG,IAAI;;MAEpB;MACA,IAAIJ,MAAM,EAAE;QACV,IAAIK,WAAW,GAAGT,WAAW,IAAIC,WAAW;;QAE5C;QACA,IAAIS,QAAQ,GAAG,CAAC;QAChB,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAChC,IAAI,CAACiC,MAAM,GAAG,CAAC,EAAEd,KAAK,CAAC;QAC7C,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;UACnC,IAAIC,GAAG,GAAGhC,MAAM,CAACH,IAAI,CAACkC,CAAC,CAAC,CAAC;UACzBN,OAAO,GAAGD,QAAQ;UAClB,IAAIS,WAAW,GAAGnC,OAAO,CAACoC,GAAG,CAACF,GAAG,CAAC;UAClCN,UAAU,GAAGD,OAAO,IAAIQ,WAAW,KAAKE,SAAS,GAAGpC,UAAU,GAAGkC,WAAW,CAAC;UAC7ET,QAAQ,GAAGE,UAAU;QACvB;;QAEA;QACA,IAAIU,UAAU,GAAGb,WAAW,KAAK,KAAK,GAAGN,MAAM,GAAGC,MAAM,GAAGD,MAAM;QACjE,KAAK,IAAIoB,EAAE,GAAGV,MAAM,EAAEU,EAAE,IAAI,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;UACtC,IAAIC,IAAI,GAAGtC,MAAM,CAACH,IAAI,CAACwC,EAAE,CAAC,CAAC;UAC3B,IAAIE,YAAY,GAAGzC,OAAO,CAACoC,GAAG,CAACI,IAAI,CAAC;UACpC,IAAIC,YAAY,KAAKJ,SAAS,EAAE;YAC9Bf,iBAAiB,GAAG,IAAI;YACxB;UACF;UACAgB,UAAU,IAAIG,YAAY;UAC1B,IAAIH,UAAU,IAAI,CAAC,EAAE;YACnB;UACF;QACF;;QAEA;QACA,QAAQb,WAAW;UACjB,KAAK,KAAK;YACRD,SAAS,GAAGG,OAAO,GAAGR,MAAM;YAC5B;UACF,KAAK,QAAQ;YACXK,SAAS,GAAGI,UAAU,GAAGR,MAAM,GAAGD,MAAM;YACxC;UACF;YACE;cACE,IAAIuB,SAAS,GAAG5C,YAAY,CAACgB,OAAO,CAAC4B,SAAS;cAC9C,IAAIC,YAAY,GAAGD,SAAS,GAAGtB,MAAM;cACrC,IAAIO,OAAO,GAAGe,SAAS,EAAE;gBACvBnB,cAAc,GAAG,KAAK;cACxB,CAAC,MAAM,IAAIK,UAAU,GAAGe,YAAY,EAAE;gBACpCpB,cAAc,GAAG,QAAQ;cAC3B;YACF;QACJ;QACA,IAAIC,SAAS,KAAK,IAAI,EAAE;UACtBpB,aAAa,CAACoB,SAAS,CAAC;QAC1B;;QAEA;QACA,IAAIA,SAAS,KAAKb,SAAS,CAACiC,OAAO,EAAE;UACnCtB,iBAAiB,GAAG,IAAI;QAC1B;MACF;;MAEA;MACA,IAAIA,iBAAiB,EAAE;QACrBV,YAAY,CAACtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3DE,KAAK,EAAEF,SAAS,CAACE,KAAK,GAAG,CAAC;UAC1BG,WAAW,EAAEO,cAAc;UAC3BqB,OAAO,EAAEpB;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,MAAM,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACpC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK,MAAMjB,SAAS,EAAE;MACzID,OAAO,CAAC,KAAK,EAAE,qGAAqG,CAAC;IACvH;EACF,CAAC,EAAE,CAACgB,SAAS,EAAEb,YAAY,CAACgB,OAAO,CAAC,CAAC;;EAErC;EACA,OAAO,UAAUkC,GAAG,EAAE;IACpB;IACA,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKX,SAAS,EAAE;MACrChC,YAAY,CAAC,CAAC;MACd;IACF;;IAEA;IACAZ,GAAG,CAACwD,MAAM,CAAC3C,SAAS,CAACQ,OAAO,CAAC;IAC7B,IAAI,OAAOkC,GAAG,KAAK,QAAQ,EAAE;MAC3B5C,aAAa,CAAC4C,GAAG,CAAC;IACpB,CAAC,MAAM,IAAIA,GAAG,IAAI3D,OAAO,CAAC2D,GAAG,CAAC,KAAK,QAAQ,EAAE;MAC3C,IAAI9B,KAAK;MACT,IAAIgC,KAAK,GAAGF,GAAG,CAACE,KAAK;MACrB,IAAI,OAAO,IAAIF,GAAG,EAAE;QAClB9B,KAAK,GAAG8B,GAAG,CAAC9B,KAAK;MACnB,CAAC,MAAM;QACLA,KAAK,GAAGnB,IAAI,CAACoD,SAAS,CAAC,UAAUC,IAAI,EAAE;UACrC,OAAOlD,MAAM,CAACkD,IAAI,CAAC,KAAKJ,GAAG,CAACd,GAAG;QACjC,CAAC,CAAC;MACJ;MACA,IAAImB,WAAW,GAAGL,GAAG,CAAC7B,MAAM;QAC1BA,MAAM,GAAGkC,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;MACnDzC,YAAY,CAAC;QACXC,KAAK,EAAE,CAAC;QACRK,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA,MAAM;QACdF,WAAW,EAAEiC;MACf,CAAC,CAAC;IACJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
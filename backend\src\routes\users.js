const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const userController = require('../controllers/userController');
const { auth, authorize } = require('../middleware/auth');

// 验证规则
const createUserValidation = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('full_name')
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名长度必须在2-100个字符之间')
    .trim(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('role')
    .isIn(['admin', 'manager', 'employee'])
    .withMessage('角色必须是: admin, manager, employee')
];

const updateUserValidation = [
  body('username')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('full_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名长度必须在2-100个字符之间')
    .trim(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('role')
    .optional()
    .isIn(['admin', 'manager', 'employee'])
    .withMessage('角色必须是: admin, manager, employee')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

// 获取用户列表（仅管理员）
router.get('/', 
  auth, 
  authorize('admin'),
  paginationValidation,
  userController.getUsers
);

// 获取用户详情（仅管理员）
router.get('/:id', 
  auth, 
  authorize('admin'),
  userController.getUserById
);

// 创建用户（仅管理员）
router.post('/', 
  auth, 
  authorize('admin'),
  createUserValidation,
  userController.createUser
);

// 更新用户（仅管理员）
router.put('/:id', 
  auth, 
  authorize('admin'),
  updateUserValidation,
  userController.updateUser
);

// 删除用户（仅管理员）
router.delete('/:id', 
  auth, 
  authorize('admin'),
  userController.deleteUser
);

// 更新用户状态（仅管理员）
router.patch('/:id/status', 
  auth, 
  authorize('admin'),
  body('status').isIn(['active', 'inactive']).withMessage('状态必须是: active, inactive'),
  userController.updateUserStatus
);

module.exports = router;

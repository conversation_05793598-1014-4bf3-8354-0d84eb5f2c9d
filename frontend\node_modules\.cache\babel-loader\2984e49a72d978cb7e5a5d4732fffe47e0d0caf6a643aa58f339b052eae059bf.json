{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nfunction renderExpandIcon(locale) {\n  return props => {\n    const {\n      prefixCls,\n      onExpand,\n      record,\n      expanded,\n      expandable\n    } = props;\n    const iconPrefix = `${prefixCls}-row-expand-icon`;\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, {\n        [`${iconPrefix}-spaced`]: !expandable,\n        [`${iconPrefix}-expanded`]: expandable && expanded,\n        [`${iconPrefix}-collapsed`]: expandable && !expanded\n      }),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;", "map": {"version": 3, "names": ["React", "classNames", "renderExpandIcon", "locale", "props", "prefixCls", "onExpand", "record", "expanded", "expandable", "iconPrefix", "createElement", "type", "onClick", "e", "stopPropagation", "className", "collapse", "expand"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/antd@5.26.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/table/ExpandIcon.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nfunction renderExpandIcon(locale) {\n  return props => {\n    const {\n      prefixCls,\n      onExpand,\n      record,\n      expanded,\n      expandable\n    } = props;\n    const iconPrefix = `${prefixCls}-row-expand-icon`;\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, {\n        [`${iconPrefix}-spaced`]: !expandable,\n        [`${iconPrefix}-expanded`]: expandable && expanded,\n        [`${iconPrefix}-collapsed`]: expandable && !expanded\n      }),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChC,OAAOC,KAAK,IAAI;IACd,MAAM;MACJC,SAAS;MACTC,QAAQ;MACRC,MAAM;MACNC,QAAQ;MACRC;IACF,CAAC,GAAGL,KAAK;IACT,MAAMM,UAAU,GAAG,GAAGL,SAAS,kBAAkB;IACjD,OAAO,aAAaL,KAAK,CAACW,aAAa,CAAC,QAAQ,EAAE;MAChDC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAEC,CAAC,IAAI;QACZR,QAAQ,CAACC,MAAM,EAAEO,CAAC,CAAC;QACnBA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB,CAAC;MACDC,SAAS,EAAEf,UAAU,CAACS,UAAU,EAAE;QAChC,CAAC,GAAGA,UAAU,SAAS,GAAG,CAACD,UAAU;QACrC,CAAC,GAAGC,UAAU,WAAW,GAAGD,UAAU,IAAID,QAAQ;QAClD,CAAC,GAAGE,UAAU,YAAY,GAAGD,UAAU,IAAI,CAACD;MAC9C,CAAC,CAAC;MACF,YAAY,EAAEA,QAAQ,GAAGL,MAAM,CAACc,QAAQ,GAAGd,MAAM,CAACe,MAAM;MACxD,eAAe,EAAEV;IACnB,CAAC,CAAC;EACJ,CAAC;AACH;AACA,eAAeN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport PickerTrigger from \"../PickerTrigger\";\nimport { pickTriggerProps } from \"../PickerTrigger/util\";\nimport { fillIndex, getFromDate, toArray } from \"../utils/miscUtil\";\nimport PickerContext from \"./context\";\nimport useCellRender from \"./hooks/useCellRender\";\nimport useFieldsInvalidate from \"./hooks/useFieldsInvalidate\";\nimport useFilledProps from \"./hooks/useFilledProps\";\nimport useOpen from \"./hooks/useOpen\";\nimport usePickerRef from \"./hooks/usePickerRef\";\nimport usePresets from \"./hooks/usePresets\";\nimport useRangeActive from \"./hooks/useRangeActive\";\nimport useRangeDisabledDate from \"./hooks/useRangeDisabledDate\";\nimport useRangePickerValue from \"./hooks/useRangePickerValue\";\nimport useRangeValue, { useInnerValue } from \"./hooks/useRangeValue\";\nimport useShowNow from \"./hooks/useShowNow\";\nimport Popup from \"./Popup\";\nimport RangeSelector from \"./Selector/RangeSelector\";\nfunction separateConfig(config, defaultConfig) {\n  var singleConfig = config !== null && config !== void 0 ? config : defaultConfig;\n  if (Array.isArray(singleConfig)) {\n    return singleConfig;\n  }\n  return [singleConfig, singleConfig];\n}\n\n/** Used for change event, it should always be not undefined */\n\nfunction getActiveRange(activeIndex) {\n  return activeIndex === 1 ? 'end' : 'start';\n}\nfunction RangePicker(props, ref) {\n  // ========================= Prop =========================\n  var _useFilledProps = useFilledProps(props, function () {\n      var disabled = props.disabled,\n        allowEmpty = props.allowEmpty;\n      var mergedDisabled = separateConfig(disabled, false);\n      var mergedAllowEmpty = separateConfig(allowEmpty, false);\n      return {\n        disabled: mergedDisabled,\n        allowEmpty: mergedAllowEmpty\n      };\n    }),\n    _useFilledProps2 = _slicedToArray(_useFilledProps, 6),\n    filledProps = _useFilledProps2[0],\n    internalPicker = _useFilledProps2[1],\n    complexPicker = _useFilledProps2[2],\n    formatList = _useFilledProps2[3],\n    maskFormat = _useFilledProps2[4],\n    isInvalidateDate = _useFilledProps2[5];\n  var prefixCls = filledProps.prefixCls,\n    styles = filledProps.styles,\n    classNames = filledProps.classNames,\n    defaultValue = filledProps.defaultValue,\n    value = filledProps.value,\n    needConfirm = filledProps.needConfirm,\n    onKeyDown = filledProps.onKeyDown,\n    disabled = filledProps.disabled,\n    allowEmpty = filledProps.allowEmpty,\n    disabledDate = filledProps.disabledDate,\n    minDate = filledProps.minDate,\n    maxDate = filledProps.maxDate,\n    defaultOpen = filledProps.defaultOpen,\n    open = filledProps.open,\n    onOpenChange = filledProps.onOpenChange,\n    locale = filledProps.locale,\n    generateConfig = filledProps.generateConfig,\n    picker = filledProps.picker,\n    showNow = filledProps.showNow,\n    showToday = filledProps.showToday,\n    showTime = filledProps.showTime,\n    mode = filledProps.mode,\n    onPanelChange = filledProps.onPanelChange,\n    onCalendarChange = filledProps.onCalendarChange,\n    onOk = filledProps.onOk,\n    defaultPickerValue = filledProps.defaultPickerValue,\n    pickerValue = filledProps.pickerValue,\n    onPickerValueChange = filledProps.onPickerValueChange,\n    inputReadOnly = filledProps.inputReadOnly,\n    suffixIcon = filledProps.suffixIcon,\n    onFocus = filledProps.onFocus,\n    onBlur = filledProps.onBlur,\n    presets = filledProps.presets,\n    ranges = filledProps.ranges,\n    components = filledProps.components,\n    cellRender = filledProps.cellRender,\n    dateRender = filledProps.dateRender,\n    monthCellRender = filledProps.monthCellRender,\n    onClick = filledProps.onClick;\n\n  // ========================= Refs =========================\n  var selectorRef = usePickerRef(ref);\n\n  // ========================= Open =========================\n  var _useOpen = useOpen(open, defaultOpen, disabled, onOpenChange),\n    _useOpen2 = _slicedToArray(_useOpen, 2),\n    mergedOpen = _useOpen2[0],\n    setMergeOpen = _useOpen2[1];\n  var triggerOpen = function triggerOpen(nextOpen, config) {\n    // No need to open if all disabled\n    if (disabled.some(function (fieldDisabled) {\n      return !fieldDisabled;\n    }) || !nextOpen) {\n      setMergeOpen(nextOpen, config);\n    }\n  };\n\n  // ======================== Values ========================\n  var _useInnerValue = useInnerValue(generateConfig, locale, formatList, true, false, defaultValue, value, onCalendarChange, onOk),\n    _useInnerValue2 = _slicedToArray(_useInnerValue, 5),\n    mergedValue = _useInnerValue2[0],\n    setInnerValue = _useInnerValue2[1],\n    getCalendarValue = _useInnerValue2[2],\n    triggerCalendarChange = _useInnerValue2[3],\n    triggerOk = _useInnerValue2[4];\n  var calendarValue = getCalendarValue();\n\n  // ======================== Active ========================\n  var _useRangeActive = useRangeActive(disabled, allowEmpty, mergedOpen),\n    _useRangeActive2 = _slicedToArray(_useRangeActive, 9),\n    focused = _useRangeActive2[0],\n    triggerFocus = _useRangeActive2[1],\n    lastOperation = _useRangeActive2[2],\n    activeIndex = _useRangeActive2[3],\n    setActiveIndex = _useRangeActive2[4],\n    nextActiveIndex = _useRangeActive2[5],\n    activeIndexList = _useRangeActive2[6],\n    updateSubmitIndex = _useRangeActive2[7],\n    hasActiveSubmitValue = _useRangeActive2[8];\n  var onSharedFocus = function onSharedFocus(event, index) {\n    triggerFocus(true);\n    onFocus === null || onFocus === void 0 || onFocus(event, {\n      range: getActiveRange(index !== null && index !== void 0 ? index : activeIndex)\n    });\n  };\n  var onSharedBlur = function onSharedBlur(event, index) {\n    triggerFocus(false);\n    onBlur === null || onBlur === void 0 || onBlur(event, {\n      range: getActiveRange(index !== null && index !== void 0 ? index : activeIndex)\n    });\n  };\n\n  // ======================= ShowTime =======================\n  /** Used for Popup panel */\n  var mergedShowTime = React.useMemo(function () {\n    if (!showTime) {\n      return null;\n    }\n    var disabledTime = showTime.disabledTime;\n    var proxyDisabledTime = disabledTime ? function (date) {\n      var range = getActiveRange(activeIndex);\n      var fromDate = getFromDate(calendarValue, activeIndexList, activeIndex);\n      return disabledTime(date, range, {\n        from: fromDate\n      });\n    } : undefined;\n    return _objectSpread(_objectSpread({}, showTime), {}, {\n      disabledTime: proxyDisabledTime\n    });\n  }, [showTime, activeIndex, calendarValue, activeIndexList]);\n\n  // ========================= Mode =========================\n  var _useMergedState = useMergedState([picker, picker], {\n      value: mode\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    modes = _useMergedState2[0],\n    setModes = _useMergedState2[1];\n  var mergedMode = modes[activeIndex] || picker;\n\n  /** Extends from `mergedMode` to patch `datetime` mode */\n  var internalMode = mergedMode === 'date' && mergedShowTime ? 'datetime' : mergedMode;\n\n  // ====================== PanelCount ======================\n  var multiplePanel = internalMode === picker && internalMode !== 'time';\n\n  // ======================= Show Now =======================\n  var mergedShowNow = useShowNow(picker, mergedMode, showNow, showToday, true);\n\n  // ======================== Value =========================\n  var _useRangeValue = useRangeValue(filledProps, mergedValue, setInnerValue, getCalendarValue, triggerCalendarChange, disabled, formatList, focused, mergedOpen, isInvalidateDate),\n    _useRangeValue2 = _slicedToArray(_useRangeValue, 2),\n    /** Trigger `onChange` by check `disabledDate` */\n    flushSubmit = _useRangeValue2[0],\n    /** Trigger `onChange` directly without check `disabledDate` */\n    triggerSubmitChange = _useRangeValue2[1];\n\n  // ===================== DisabledDate =====================\n  var mergedDisabledDate = useRangeDisabledDate(calendarValue, disabled, activeIndexList, generateConfig, locale, disabledDate);\n\n  // ======================= Validate =======================\n  var _useFieldsInvalidate = useFieldsInvalidate(calendarValue, isInvalidateDate, allowEmpty),\n    _useFieldsInvalidate2 = _slicedToArray(_useFieldsInvalidate, 2),\n    submitInvalidates = _useFieldsInvalidate2[0],\n    onSelectorInvalid = _useFieldsInvalidate2[1];\n\n  // ===================== Picker Value =====================\n  var _useRangePickerValue = useRangePickerValue(generateConfig, locale, calendarValue, modes, mergedOpen, activeIndex, internalPicker, multiplePanel, defaultPickerValue, pickerValue, mergedShowTime === null || mergedShowTime === void 0 ? void 0 : mergedShowTime.defaultOpenValue, onPickerValueChange, minDate, maxDate),\n    _useRangePickerValue2 = _slicedToArray(_useRangePickerValue, 2),\n    currentPickerValue = _useRangePickerValue2[0],\n    setCurrentPickerValue = _useRangePickerValue2[1];\n\n  // >>> Mode need wait for `pickerValue`\n  var triggerModeChange = useEvent(function (nextPickerValue, nextMode, triggerEvent) {\n    var clone = fillIndex(modes, activeIndex, nextMode);\n    if (clone[0] !== modes[0] || clone[1] !== modes[1]) {\n      setModes(clone);\n    }\n\n    // Compatible with `onPanelChange`\n    if (onPanelChange && triggerEvent !== false) {\n      var clonePickerValue = _toConsumableArray(calendarValue);\n      if (nextPickerValue) {\n        clonePickerValue[activeIndex] = nextPickerValue;\n      }\n      onPanelChange(clonePickerValue, clone);\n    }\n  });\n\n  // ======================== Change ========================\n  var fillCalendarValue = function fillCalendarValue(date, index) {\n    return (\n      // Trigger change only when date changed\n      fillIndex(calendarValue, index, date)\n    );\n  };\n\n  // ======================== Submit ========================\n  /**\n   * Trigger by confirm operation.\n   * This function has already handle the `needConfirm` check logic.\n   * - Selector: enter key\n   * - Panel: OK button\n   */\n  var triggerPartConfirm = function triggerPartConfirm(date, skipFocus) {\n    var nextValue = calendarValue;\n    if (date) {\n      nextValue = fillCalendarValue(date, activeIndex);\n    }\n    updateSubmitIndex(activeIndex);\n    // Get next focus index\n    var nextIndex = nextActiveIndex(nextValue);\n\n    // Change calendar value and tell flush it\n    triggerCalendarChange(nextValue);\n    flushSubmit(activeIndex, nextIndex === null);\n    if (nextIndex === null) {\n      triggerOpen(false, {\n        force: true\n      });\n    } else if (!skipFocus) {\n      selectorRef.current.focus({\n        index: nextIndex\n      });\n    }\n  };\n\n  // ======================== Click =========================\n  var onSelectorClick = function onSelectorClick(event) {\n    var _activeElement;\n    var rootNode = event.target.getRootNode();\n    if (!selectorRef.current.nativeElement.contains((_activeElement = rootNode.activeElement) !== null && _activeElement !== void 0 ? _activeElement : document.activeElement)) {\n      // Click to focus the enabled input\n      var enabledIndex = disabled.findIndex(function (d) {\n        return !d;\n      });\n      if (enabledIndex >= 0) {\n        selectorRef.current.focus({\n          index: enabledIndex\n        });\n      }\n    }\n    triggerOpen(true);\n    onClick === null || onClick === void 0 || onClick(event);\n  };\n  var onSelectorClear = function onSelectorClear() {\n    triggerSubmitChange(null);\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Hover =========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hoverSource = _React$useState2[0],\n    setHoverSource = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalHoverValues = _React$useState4[0],\n    setInternalHoverValues = _React$useState4[1];\n  var hoverValues = React.useMemo(function () {\n    return internalHoverValues || calendarValue;\n  }, [calendarValue, internalHoverValues]);\n\n  // Clean up `internalHoverValues` when closed\n  React.useEffect(function () {\n    if (!mergedOpen) {\n      setInternalHoverValues(null);\n    }\n  }, [mergedOpen]);\n\n  // ========================================================\n  // ==                       Panels                       ==\n  // ========================================================\n  // Save the offset with active bar position\n  // const [activeOffset, setActiveOffset] = React.useState(0);\n  var _React$useState5 = React.useState([0, 0, 0]),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    activeInfo = _React$useState6[0],\n    setActiveInfo = _React$useState6[1];\n\n  // ======================= Presets ========================\n  var presetList = usePresets(presets, ranges);\n  var onPresetHover = function onPresetHover(nextValues) {\n    setInternalHoverValues(nextValues);\n    setHoverSource('preset');\n  };\n  var onPresetSubmit = function onPresetSubmit(nextValues) {\n    var passed = triggerSubmitChange(nextValues);\n    if (passed) {\n      triggerOpen(false, {\n        force: true\n      });\n    }\n  };\n  var onNow = function onNow(now) {\n    triggerPartConfirm(now);\n  };\n\n  // ======================== Panel =========================\n  var onPanelHover = function onPanelHover(date) {\n    setInternalHoverValues(date ? fillCalendarValue(date, activeIndex) : null);\n    setHoverSource('cell');\n  };\n\n  // >>> Focus\n  var onPanelFocus = function onPanelFocus(event) {\n    triggerOpen(true);\n    onSharedFocus(event);\n  };\n\n  // >>> MouseDown\n  var onPanelMouseDown = function onPanelMouseDown() {\n    lastOperation('panel');\n  };\n\n  // >>> Calendar\n  var onPanelSelect = function onPanelSelect(date) {\n    var clone = fillIndex(calendarValue, activeIndex, date);\n\n    // Only trigger calendar event but not update internal `calendarValue` state\n    triggerCalendarChange(clone);\n\n    // >>> Trigger next active if !needConfirm\n    // Fully logic check `useRangeValue` hook\n    if (!needConfirm && !complexPicker && internalPicker === internalMode) {\n      triggerPartConfirm(date);\n    }\n  };\n\n  // >>> Close\n  var onPopupClose = function onPopupClose() {\n    // Close popup\n    triggerOpen(false);\n  };\n\n  // >>> cellRender\n  var onInternalCellRender = useCellRender(cellRender, dateRender, monthCellRender, getActiveRange(activeIndex));\n\n  // >>> Value\n  var panelValue = calendarValue[activeIndex] || null;\n\n  // >>> invalid\n  var isPopupInvalidateDate = useEvent(function (date) {\n    return isInvalidateDate(date, {\n      activeIndex: activeIndex\n    });\n  });\n  var panelProps = React.useMemo(function () {\n    var domProps = pickAttrs(filledProps, false);\n    var restProps = omit(filledProps, [].concat(_toConsumableArray(Object.keys(domProps)), ['onChange', 'onCalendarChange', 'style', 'className', 'onPanelChange', 'disabledTime']));\n    return restProps;\n  }, [filledProps]);\n\n  // >>> Render\n  var panel = /*#__PURE__*/React.createElement(Popup, _extends({}, panelProps, {\n    showNow: mergedShowNow,\n    showTime: mergedShowTime\n    // Range\n    ,\n\n    range: true,\n    multiplePanel: multiplePanel,\n    activeInfo: activeInfo\n    // Disabled\n    ,\n\n    disabledDate: mergedDisabledDate\n    // Focus\n    ,\n\n    onFocus: onPanelFocus,\n    onBlur: onSharedBlur,\n    onPanelMouseDown: onPanelMouseDown\n    // Mode\n    ,\n\n    picker: picker,\n    mode: mergedMode,\n    internalMode: internalMode,\n    onPanelChange: triggerModeChange\n    // Value\n    ,\n\n    format: maskFormat,\n    value: panelValue,\n    isInvalid: isPopupInvalidateDate,\n    onChange: null,\n    onSelect: onPanelSelect\n    // PickerValue\n    ,\n\n    pickerValue: currentPickerValue,\n    defaultOpenValue: toArray(showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue)[activeIndex],\n    onPickerValueChange: setCurrentPickerValue\n    // Hover\n    ,\n\n    hoverValue: hoverValues,\n    onHover: onPanelHover\n    // Submit\n    ,\n\n    needConfirm: needConfirm,\n    onSubmit: triggerPartConfirm,\n    onOk: triggerOk\n    // Preset\n    ,\n\n    presets: presetList,\n    onPresetHover: onPresetHover,\n    onPresetSubmit: onPresetSubmit\n    // Now\n    ,\n\n    onNow: onNow\n    // Render\n    ,\n\n    cellRender: onInternalCellRender\n  }));\n\n  // ========================================================\n  // ==                      Selector                      ==\n  // ========================================================\n\n  // ======================== Change ========================\n  var onSelectorChange = function onSelectorChange(date, index) {\n    var clone = fillCalendarValue(date, index);\n    triggerCalendarChange(clone);\n  };\n  var onSelectorInputChange = function onSelectorInputChange() {\n    lastOperation('input');\n  };\n\n  // ======================= Selector =======================\n  var onSelectorFocus = function onSelectorFocus(event, index) {\n    // Check if `needConfirm` but user not submit yet\n    var activeListLen = activeIndexList.length;\n    var lastActiveIndex = activeIndexList[activeListLen - 1];\n    if (activeListLen && lastActiveIndex !== index && needConfirm &&\n    // Not change index if is not filled\n    !allowEmpty[lastActiveIndex] && !hasActiveSubmitValue(lastActiveIndex) && calendarValue[lastActiveIndex]) {\n      selectorRef.current.focus({\n        index: lastActiveIndex\n      });\n      return;\n    }\n    lastOperation('input');\n    triggerOpen(true, {\n      inherit: true\n    });\n\n    // When click input to switch the field, it will not trigger close.\n    // Which means it will lose the part confirm and we need fill back.\n    // ref: https://github.com/ant-design/ant-design/issues/49512\n    if (activeIndex !== index && mergedOpen && !needConfirm && complexPicker) {\n      triggerPartConfirm(null, true);\n    }\n    setActiveIndex(index);\n    onSharedFocus(event, index);\n  };\n  var onSelectorBlur = function onSelectorBlur(event, index) {\n    triggerOpen(false);\n    if (!needConfirm && lastOperation() === 'input') {\n      var nextIndex = nextActiveIndex(calendarValue);\n      flushSubmit(activeIndex, nextIndex === null);\n    }\n    onSharedBlur(event, index);\n  };\n  var onSelectorKeyDown = function onSelectorKeyDown(event, preventDefault) {\n    if (event.key === 'Tab') {\n      triggerPartConfirm(null, true);\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event, preventDefault);\n  };\n\n  // ======================= Context ========================\n  var context = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      locale: locale,\n      generateConfig: generateConfig,\n      button: components.button,\n      input: components.input\n    };\n  }, [prefixCls, locale, generateConfig, components.button, components.input]);\n\n  // ======================== Effect ========================\n  // >>> Mode\n  // Reset for every active\n  useLayoutEffect(function () {\n    if (mergedOpen && activeIndex !== undefined) {\n      // Legacy compatible. This effect update should not trigger `onPanelChange`\n      triggerModeChange(null, picker, false);\n    }\n  }, [mergedOpen, activeIndex, picker]);\n\n  // >>> For complex picker, we need check if need to focus next one\n  useLayoutEffect(function () {\n    var lastOp = lastOperation();\n\n    // Trade as confirm on field leave\n    if (!mergedOpen && lastOp === 'input') {\n      triggerOpen(false);\n      triggerPartConfirm(null, true);\n    }\n\n    // Submit with complex picker\n    if (!mergedOpen && complexPicker && !needConfirm && lastOp === 'panel') {\n      triggerOpen(true);\n      triggerPartConfirm();\n    }\n  }, [mergedOpen]);\n\n  // ====================== DevWarning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    var isIndexEmpty = function isIndexEmpty(index) {\n      return (\n        // Value is empty\n        !(value !== null && value !== void 0 && value[index]) &&\n        // DefaultValue is empty\n        !(defaultValue !== null && defaultValue !== void 0 && defaultValue[index])\n      );\n    };\n    if (disabled.some(function (fieldDisabled, index) {\n      return fieldDisabled && isIndexEmpty(index) && !allowEmpty[index];\n    })) {\n      warning(false, '`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.');\n    }\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(PickerContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(PickerTrigger, _extends({}, pickTriggerProps(filledProps), {\n    popupElement: panel,\n    popupStyle: styles.popup,\n    popupClassName: classNames.popup\n    // Visible\n    ,\n\n    visible: mergedOpen,\n    onClose: onPopupClose\n    // Range\n    ,\n\n    range: true\n  }), /*#__PURE__*/React.createElement(RangeSelector\n  // Shared\n  , _extends({}, filledProps, {\n    // Ref\n    ref: selectorRef\n    // Icon\n    ,\n\n    suffixIcon: suffixIcon\n    // Active\n    ,\n\n    activeIndex: focused || mergedOpen ? activeIndex : null,\n    activeHelp: !!internalHoverValues,\n    allHelp: !!internalHoverValues && hoverSource === 'preset',\n    focused: focused,\n    onFocus: onSelectorFocus,\n    onBlur: onSelectorBlur,\n    onKeyDown: onSelectorKeyDown,\n    onSubmit: triggerPartConfirm\n    // Change\n    ,\n\n    value: hoverValues,\n    maskFormat: maskFormat,\n    onChange: onSelectorChange,\n    onInputChange: onSelectorInputChange\n    // Format\n    ,\n\n    format: formatList,\n    inputReadOnly: inputReadOnly\n    // Disabled\n    ,\n\n    disabled: disabled\n    // Open\n    ,\n\n    open: mergedOpen,\n    onOpenChange: triggerOpen\n    // Click\n    ,\n\n    onClick: onSelectorClick,\n    onClear: onSelectorClear\n    // Invalid\n    ,\n\n    invalid: submitInvalidates,\n    onInvalid: onSelectorInvalid\n    // Offset\n    ,\n\n    onActiveInfo: setActiveInfo\n  }))));\n}\nvar RefRangePicker = /*#__PURE__*/React.forwardRef(RangePicker);\nif (process.env.NODE_ENV !== 'production') {\n  RefRangePicker.displayName = 'RefRangePicker';\n}\nexport default RefRangePicker;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_objectSpread", "_slicedToArray", "useEvent", "useMergedState", "useLayoutEffect", "omit", "pickAttrs", "warning", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickTriggerProps", "fillIndex", "getFromDate", "toArray", "<PERSON>er<PERSON>ontext", "useCellRender", "useFieldsInvalidate", "useFilledProps", "useOpen", "usePickerRef", "usePresets", "useRangeActive", "useRangeDisabledDate", "useRangePickerValue", "useRangeValue", "useInnerValue", "useShowNow", "Popup", "RangeSelector", "separateConfig", "config", "defaultConfig", "singleConfig", "Array", "isArray", "getActiveRange", "activeIndex", "RangePicker", "props", "ref", "_useFilledProps", "disabled", "allowEmpty", "mergedDisabled", "mergedAllowEmpty", "_useFilledProps2", "filledProps", "internalPicker", "complexPicker", "formatList", "maskFormat", "isInvalidateDate", "prefixCls", "styles", "classNames", "defaultValue", "value", "needConfirm", "onKeyDown", "disabledDate", "minDate", "maxDate", "defaultOpen", "open", "onOpenChange", "locale", "generateConfig", "picker", "showNow", "showToday", "showTime", "mode", "onPanelChange", "onCalendarChange", "onOk", "defaultPickerValue", "picker<PERSON><PERSON><PERSON>", "onPickerValueChange", "inputReadOnly", "suffixIcon", "onFocus", "onBlur", "presets", "ranges", "components", "cellRender", "dateRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "selectorRef", "_useOpen", "_useOpen2", "mergedOpen", "setMergeOpen", "triggerOpen", "nextOpen", "some", "fieldDisabled", "_useInnerValue", "_useInnerValue2", "mergedValue", "setInnerValue", "getCalendarValue", "triggerCalendarChange", "triggerOk", "calendarValue", "_useRangeActive", "_useRangeActive2", "focused", "triggerFocus", "lastOperation", "setActiveIndex", "nextActiveIndex", "activeIndexList", "updateSubmitIndex", "hasActiveSubmitValue", "onSharedFocus", "event", "index", "range", "onSharedBlur", "mergedShowTime", "useMemo", "disabledTime", "proxyDisabledTime", "date", "fromDate", "from", "undefined", "_useMergedState", "_useMergedState2", "modes", "setModes", "mergedMode", "internalMode", "multiplePanel", "mergedShowNow", "_useRangeValue", "_useRangeValue2", "flushSubmit", "triggerSubmitChange", "mergedDisabledDate", "_useFieldsInvalidate", "_useFieldsInvalidate2", "submitInvalidates", "onSelectorInvalid", "_useRangePickerValue", "defaultOpenValue", "_useRangePickerValue2", "currentPickerValue", "setCurrentPickerValue", "triggerModeChange", "nextPickerValue", "nextMode", "triggerEvent", "clone", "clonePickerValue", "fillCalendarValue", "triggerPartConfirm", "skipFocus", "nextValue", "nextIndex", "force", "current", "focus", "onSelectorClick", "_activeElement", "rootNode", "target", "getRootNode", "nativeElement", "contains", "activeElement", "document", "enabledIndex", "findIndex", "d", "onSelectorClear", "_React$useState", "useState", "_React$useState2", "hoverSource", "setHoverSource", "_React$useState3", "_React$useState4", "internalHoverValues", "setInternalHoverValues", "hoverValues", "useEffect", "_React$useState5", "_React$useState6", "activeInfo", "setActiveInfo", "presetList", "onPresetHover", "nextV<PERSON>ues", "onPresetSubmit", "passed", "onNow", "now", "onPanelHover", "onPanelFocus", "onPanelMouseDown", "onPanelSelect", "onPopupClose", "onInternalCellRender", "panelValue", "isPopupInvalidateDate", "panelProps", "domProps", "restProps", "concat", "Object", "keys", "panel", "createElement", "format", "isInvalid", "onChange", "onSelect", "hoverValue", "onHover", "onSubmit", "onSelectorChange", "onSelectorInputChange", "onSelectorFocus", "activeListLen", "length", "lastActiveIndex", "inherit", "onSelectorBlur", "onSelectorKeyDown", "preventDefault", "key", "context", "button", "input", "lastOp", "process", "env", "NODE_ENV", "isIndexEmpty", "Provider", "popupElement", "popupStyle", "popup", "popupClassName", "visible", "onClose", "activeHelp", "allHelp", "onInputChange", "onClear", "invalid", "onInvalid", "onActiveInfo", "RefRangePicker", "forwardRef", "displayName"], "sources": ["D:/code/erp1/frontend/node_modules/.pnpm/rc-picker@4.11.3_dayjs@1.11_f443140093555408914807522c826bb9/node_modules/rc-picker/es/PickerInput/RangePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent, useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport PickerTrigger from \"../PickerTrigger\";\nimport { pickTriggerProps } from \"../PickerTrigger/util\";\nimport { fillIndex, getFromDate, toArray } from \"../utils/miscUtil\";\nimport PickerContext from \"./context\";\nimport useCellRender from \"./hooks/useCellRender\";\nimport useFieldsInvalidate from \"./hooks/useFieldsInvalidate\";\nimport useFilledProps from \"./hooks/useFilledProps\";\nimport useOpen from \"./hooks/useOpen\";\nimport usePickerRef from \"./hooks/usePickerRef\";\nimport usePresets from \"./hooks/usePresets\";\nimport useRangeActive from \"./hooks/useRangeActive\";\nimport useRangeDisabledDate from \"./hooks/useRangeDisabledDate\";\nimport useRangePickerValue from \"./hooks/useRangePickerValue\";\nimport useRangeValue, { useInnerValue } from \"./hooks/useRangeValue\";\nimport useShowNow from \"./hooks/useShowNow\";\nimport Popup from \"./Popup\";\nimport RangeSelector from \"./Selector/RangeSelector\";\nfunction separateConfig(config, defaultConfig) {\n  var singleConfig = config !== null && config !== void 0 ? config : defaultConfig;\n  if (Array.isArray(singleConfig)) {\n    return singleConfig;\n  }\n  return [singleConfig, singleConfig];\n}\n\n/** Used for change event, it should always be not undefined */\n\nfunction getActiveRange(activeIndex) {\n  return activeIndex === 1 ? 'end' : 'start';\n}\nfunction RangePicker(props, ref) {\n  // ========================= Prop =========================\n  var _useFilledProps = useFilledProps(props, function () {\n      var disabled = props.disabled,\n        allowEmpty = props.allowEmpty;\n      var mergedDisabled = separateConfig(disabled, false);\n      var mergedAllowEmpty = separateConfig(allowEmpty, false);\n      return {\n        disabled: mergedDisabled,\n        allowEmpty: mergedAllowEmpty\n      };\n    }),\n    _useFilledProps2 = _slicedToArray(_useFilledProps, 6),\n    filledProps = _useFilledProps2[0],\n    internalPicker = _useFilledProps2[1],\n    complexPicker = _useFilledProps2[2],\n    formatList = _useFilledProps2[3],\n    maskFormat = _useFilledProps2[4],\n    isInvalidateDate = _useFilledProps2[5];\n  var prefixCls = filledProps.prefixCls,\n    styles = filledProps.styles,\n    classNames = filledProps.classNames,\n    defaultValue = filledProps.defaultValue,\n    value = filledProps.value,\n    needConfirm = filledProps.needConfirm,\n    onKeyDown = filledProps.onKeyDown,\n    disabled = filledProps.disabled,\n    allowEmpty = filledProps.allowEmpty,\n    disabledDate = filledProps.disabledDate,\n    minDate = filledProps.minDate,\n    maxDate = filledProps.maxDate,\n    defaultOpen = filledProps.defaultOpen,\n    open = filledProps.open,\n    onOpenChange = filledProps.onOpenChange,\n    locale = filledProps.locale,\n    generateConfig = filledProps.generateConfig,\n    picker = filledProps.picker,\n    showNow = filledProps.showNow,\n    showToday = filledProps.showToday,\n    showTime = filledProps.showTime,\n    mode = filledProps.mode,\n    onPanelChange = filledProps.onPanelChange,\n    onCalendarChange = filledProps.onCalendarChange,\n    onOk = filledProps.onOk,\n    defaultPickerValue = filledProps.defaultPickerValue,\n    pickerValue = filledProps.pickerValue,\n    onPickerValueChange = filledProps.onPickerValueChange,\n    inputReadOnly = filledProps.inputReadOnly,\n    suffixIcon = filledProps.suffixIcon,\n    onFocus = filledProps.onFocus,\n    onBlur = filledProps.onBlur,\n    presets = filledProps.presets,\n    ranges = filledProps.ranges,\n    components = filledProps.components,\n    cellRender = filledProps.cellRender,\n    dateRender = filledProps.dateRender,\n    monthCellRender = filledProps.monthCellRender,\n    onClick = filledProps.onClick;\n\n  // ========================= Refs =========================\n  var selectorRef = usePickerRef(ref);\n\n  // ========================= Open =========================\n  var _useOpen = useOpen(open, defaultOpen, disabled, onOpenChange),\n    _useOpen2 = _slicedToArray(_useOpen, 2),\n    mergedOpen = _useOpen2[0],\n    setMergeOpen = _useOpen2[1];\n  var triggerOpen = function triggerOpen(nextOpen, config) {\n    // No need to open if all disabled\n    if (disabled.some(function (fieldDisabled) {\n      return !fieldDisabled;\n    }) || !nextOpen) {\n      setMergeOpen(nextOpen, config);\n    }\n  };\n\n  // ======================== Values ========================\n  var _useInnerValue = useInnerValue(generateConfig, locale, formatList, true, false, defaultValue, value, onCalendarChange, onOk),\n    _useInnerValue2 = _slicedToArray(_useInnerValue, 5),\n    mergedValue = _useInnerValue2[0],\n    setInnerValue = _useInnerValue2[1],\n    getCalendarValue = _useInnerValue2[2],\n    triggerCalendarChange = _useInnerValue2[3],\n    triggerOk = _useInnerValue2[4];\n  var calendarValue = getCalendarValue();\n\n  // ======================== Active ========================\n  var _useRangeActive = useRangeActive(disabled, allowEmpty, mergedOpen),\n    _useRangeActive2 = _slicedToArray(_useRangeActive, 9),\n    focused = _useRangeActive2[0],\n    triggerFocus = _useRangeActive2[1],\n    lastOperation = _useRangeActive2[2],\n    activeIndex = _useRangeActive2[3],\n    setActiveIndex = _useRangeActive2[4],\n    nextActiveIndex = _useRangeActive2[5],\n    activeIndexList = _useRangeActive2[6],\n    updateSubmitIndex = _useRangeActive2[7],\n    hasActiveSubmitValue = _useRangeActive2[8];\n  var onSharedFocus = function onSharedFocus(event, index) {\n    triggerFocus(true);\n    onFocus === null || onFocus === void 0 || onFocus(event, {\n      range: getActiveRange(index !== null && index !== void 0 ? index : activeIndex)\n    });\n  };\n  var onSharedBlur = function onSharedBlur(event, index) {\n    triggerFocus(false);\n    onBlur === null || onBlur === void 0 || onBlur(event, {\n      range: getActiveRange(index !== null && index !== void 0 ? index : activeIndex)\n    });\n  };\n\n  // ======================= ShowTime =======================\n  /** Used for Popup panel */\n  var mergedShowTime = React.useMemo(function () {\n    if (!showTime) {\n      return null;\n    }\n    var disabledTime = showTime.disabledTime;\n    var proxyDisabledTime = disabledTime ? function (date) {\n      var range = getActiveRange(activeIndex);\n      var fromDate = getFromDate(calendarValue, activeIndexList, activeIndex);\n      return disabledTime(date, range, {\n        from: fromDate\n      });\n    } : undefined;\n    return _objectSpread(_objectSpread({}, showTime), {}, {\n      disabledTime: proxyDisabledTime\n    });\n  }, [showTime, activeIndex, calendarValue, activeIndexList]);\n\n  // ========================= Mode =========================\n  var _useMergedState = useMergedState([picker, picker], {\n      value: mode\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    modes = _useMergedState2[0],\n    setModes = _useMergedState2[1];\n  var mergedMode = modes[activeIndex] || picker;\n\n  /** Extends from `mergedMode` to patch `datetime` mode */\n  var internalMode = mergedMode === 'date' && mergedShowTime ? 'datetime' : mergedMode;\n\n  // ====================== PanelCount ======================\n  var multiplePanel = internalMode === picker && internalMode !== 'time';\n\n  // ======================= Show Now =======================\n  var mergedShowNow = useShowNow(picker, mergedMode, showNow, showToday, true);\n\n  // ======================== Value =========================\n  var _useRangeValue = useRangeValue(filledProps, mergedValue, setInnerValue, getCalendarValue, triggerCalendarChange, disabled, formatList, focused, mergedOpen, isInvalidateDate),\n    _useRangeValue2 = _slicedToArray(_useRangeValue, 2),\n    /** Trigger `onChange` by check `disabledDate` */\n    flushSubmit = _useRangeValue2[0],\n    /** Trigger `onChange` directly without check `disabledDate` */\n    triggerSubmitChange = _useRangeValue2[1];\n\n  // ===================== DisabledDate =====================\n  var mergedDisabledDate = useRangeDisabledDate(calendarValue, disabled, activeIndexList, generateConfig, locale, disabledDate);\n\n  // ======================= Validate =======================\n  var _useFieldsInvalidate = useFieldsInvalidate(calendarValue, isInvalidateDate, allowEmpty),\n    _useFieldsInvalidate2 = _slicedToArray(_useFieldsInvalidate, 2),\n    submitInvalidates = _useFieldsInvalidate2[0],\n    onSelectorInvalid = _useFieldsInvalidate2[1];\n\n  // ===================== Picker Value =====================\n  var _useRangePickerValue = useRangePickerValue(generateConfig, locale, calendarValue, modes, mergedOpen, activeIndex, internalPicker, multiplePanel, defaultPickerValue, pickerValue, mergedShowTime === null || mergedShowTime === void 0 ? void 0 : mergedShowTime.defaultOpenValue, onPickerValueChange, minDate, maxDate),\n    _useRangePickerValue2 = _slicedToArray(_useRangePickerValue, 2),\n    currentPickerValue = _useRangePickerValue2[0],\n    setCurrentPickerValue = _useRangePickerValue2[1];\n\n  // >>> Mode need wait for `pickerValue`\n  var triggerModeChange = useEvent(function (nextPickerValue, nextMode, triggerEvent) {\n    var clone = fillIndex(modes, activeIndex, nextMode);\n    if (clone[0] !== modes[0] || clone[1] !== modes[1]) {\n      setModes(clone);\n    }\n\n    // Compatible with `onPanelChange`\n    if (onPanelChange && triggerEvent !== false) {\n      var clonePickerValue = _toConsumableArray(calendarValue);\n      if (nextPickerValue) {\n        clonePickerValue[activeIndex] = nextPickerValue;\n      }\n      onPanelChange(clonePickerValue, clone);\n    }\n  });\n\n  // ======================== Change ========================\n  var fillCalendarValue = function fillCalendarValue(date, index) {\n    return (\n      // Trigger change only when date changed\n      fillIndex(calendarValue, index, date)\n    );\n  };\n\n  // ======================== Submit ========================\n  /**\n   * Trigger by confirm operation.\n   * This function has already handle the `needConfirm` check logic.\n   * - Selector: enter key\n   * - Panel: OK button\n   */\n  var triggerPartConfirm = function triggerPartConfirm(date, skipFocus) {\n    var nextValue = calendarValue;\n    if (date) {\n      nextValue = fillCalendarValue(date, activeIndex);\n    }\n    updateSubmitIndex(activeIndex);\n    // Get next focus index\n    var nextIndex = nextActiveIndex(nextValue);\n\n    // Change calendar value and tell flush it\n    triggerCalendarChange(nextValue);\n    flushSubmit(activeIndex, nextIndex === null);\n    if (nextIndex === null) {\n      triggerOpen(false, {\n        force: true\n      });\n    } else if (!skipFocus) {\n      selectorRef.current.focus({\n        index: nextIndex\n      });\n    }\n  };\n\n  // ======================== Click =========================\n  var onSelectorClick = function onSelectorClick(event) {\n    var _activeElement;\n    var rootNode = event.target.getRootNode();\n    if (!selectorRef.current.nativeElement.contains((_activeElement = rootNode.activeElement) !== null && _activeElement !== void 0 ? _activeElement : document.activeElement)) {\n      // Click to focus the enabled input\n      var enabledIndex = disabled.findIndex(function (d) {\n        return !d;\n      });\n      if (enabledIndex >= 0) {\n        selectorRef.current.focus({\n          index: enabledIndex\n        });\n      }\n    }\n    triggerOpen(true);\n    onClick === null || onClick === void 0 || onClick(event);\n  };\n  var onSelectorClear = function onSelectorClear() {\n    triggerSubmitChange(null);\n    triggerOpen(false, {\n      force: true\n    });\n  };\n\n  // ======================== Hover =========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hoverSource = _React$useState2[0],\n    setHoverSource = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalHoverValues = _React$useState4[0],\n    setInternalHoverValues = _React$useState4[1];\n  var hoverValues = React.useMemo(function () {\n    return internalHoverValues || calendarValue;\n  }, [calendarValue, internalHoverValues]);\n\n  // Clean up `internalHoverValues` when closed\n  React.useEffect(function () {\n    if (!mergedOpen) {\n      setInternalHoverValues(null);\n    }\n  }, [mergedOpen]);\n\n  // ========================================================\n  // ==                       Panels                       ==\n  // ========================================================\n  // Save the offset with active bar position\n  // const [activeOffset, setActiveOffset] = React.useState(0);\n  var _React$useState5 = React.useState([0, 0, 0]),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    activeInfo = _React$useState6[0],\n    setActiveInfo = _React$useState6[1];\n\n  // ======================= Presets ========================\n  var presetList = usePresets(presets, ranges);\n  var onPresetHover = function onPresetHover(nextValues) {\n    setInternalHoverValues(nextValues);\n    setHoverSource('preset');\n  };\n  var onPresetSubmit = function onPresetSubmit(nextValues) {\n    var passed = triggerSubmitChange(nextValues);\n    if (passed) {\n      triggerOpen(false, {\n        force: true\n      });\n    }\n  };\n  var onNow = function onNow(now) {\n    triggerPartConfirm(now);\n  };\n\n  // ======================== Panel =========================\n  var onPanelHover = function onPanelHover(date) {\n    setInternalHoverValues(date ? fillCalendarValue(date, activeIndex) : null);\n    setHoverSource('cell');\n  };\n\n  // >>> Focus\n  var onPanelFocus = function onPanelFocus(event) {\n    triggerOpen(true);\n    onSharedFocus(event);\n  };\n\n  // >>> MouseDown\n  var onPanelMouseDown = function onPanelMouseDown() {\n    lastOperation('panel');\n  };\n\n  // >>> Calendar\n  var onPanelSelect = function onPanelSelect(date) {\n    var clone = fillIndex(calendarValue, activeIndex, date);\n\n    // Only trigger calendar event but not update internal `calendarValue` state\n    triggerCalendarChange(clone);\n\n    // >>> Trigger next active if !needConfirm\n    // Fully logic check `useRangeValue` hook\n    if (!needConfirm && !complexPicker && internalPicker === internalMode) {\n      triggerPartConfirm(date);\n    }\n  };\n\n  // >>> Close\n  var onPopupClose = function onPopupClose() {\n    // Close popup\n    triggerOpen(false);\n  };\n\n  // >>> cellRender\n  var onInternalCellRender = useCellRender(cellRender, dateRender, monthCellRender, getActiveRange(activeIndex));\n\n  // >>> Value\n  var panelValue = calendarValue[activeIndex] || null;\n\n  // >>> invalid\n  var isPopupInvalidateDate = useEvent(function (date) {\n    return isInvalidateDate(date, {\n      activeIndex: activeIndex\n    });\n  });\n  var panelProps = React.useMemo(function () {\n    var domProps = pickAttrs(filledProps, false);\n    var restProps = omit(filledProps, [].concat(_toConsumableArray(Object.keys(domProps)), ['onChange', 'onCalendarChange', 'style', 'className', 'onPanelChange', 'disabledTime']));\n    return restProps;\n  }, [filledProps]);\n\n  // >>> Render\n  var panel = /*#__PURE__*/React.createElement(Popup, _extends({}, panelProps, {\n    showNow: mergedShowNow,\n    showTime: mergedShowTime\n    // Range\n    ,\n    range: true,\n    multiplePanel: multiplePanel,\n    activeInfo: activeInfo\n    // Disabled\n    ,\n    disabledDate: mergedDisabledDate\n    // Focus\n    ,\n    onFocus: onPanelFocus,\n    onBlur: onSharedBlur,\n    onPanelMouseDown: onPanelMouseDown\n    // Mode\n    ,\n    picker: picker,\n    mode: mergedMode,\n    internalMode: internalMode,\n    onPanelChange: triggerModeChange\n    // Value\n    ,\n    format: maskFormat,\n    value: panelValue,\n    isInvalid: isPopupInvalidateDate,\n    onChange: null,\n    onSelect: onPanelSelect\n    // PickerValue\n    ,\n    pickerValue: currentPickerValue,\n    defaultOpenValue: toArray(showTime === null || showTime === void 0 ? void 0 : showTime.defaultOpenValue)[activeIndex],\n    onPickerValueChange: setCurrentPickerValue\n    // Hover\n    ,\n    hoverValue: hoverValues,\n    onHover: onPanelHover\n    // Submit\n    ,\n    needConfirm: needConfirm,\n    onSubmit: triggerPartConfirm,\n    onOk: triggerOk\n    // Preset\n    ,\n    presets: presetList,\n    onPresetHover: onPresetHover,\n    onPresetSubmit: onPresetSubmit\n    // Now\n    ,\n    onNow: onNow\n    // Render\n    ,\n    cellRender: onInternalCellRender\n  }));\n\n  // ========================================================\n  // ==                      Selector                      ==\n  // ========================================================\n\n  // ======================== Change ========================\n  var onSelectorChange = function onSelectorChange(date, index) {\n    var clone = fillCalendarValue(date, index);\n    triggerCalendarChange(clone);\n  };\n  var onSelectorInputChange = function onSelectorInputChange() {\n    lastOperation('input');\n  };\n\n  // ======================= Selector =======================\n  var onSelectorFocus = function onSelectorFocus(event, index) {\n    // Check if `needConfirm` but user not submit yet\n    var activeListLen = activeIndexList.length;\n    var lastActiveIndex = activeIndexList[activeListLen - 1];\n    if (activeListLen && lastActiveIndex !== index && needConfirm &&\n    // Not change index if is not filled\n    !allowEmpty[lastActiveIndex] && !hasActiveSubmitValue(lastActiveIndex) && calendarValue[lastActiveIndex]) {\n      selectorRef.current.focus({\n        index: lastActiveIndex\n      });\n      return;\n    }\n    lastOperation('input');\n    triggerOpen(true, {\n      inherit: true\n    });\n\n    // When click input to switch the field, it will not trigger close.\n    // Which means it will lose the part confirm and we need fill back.\n    // ref: https://github.com/ant-design/ant-design/issues/49512\n    if (activeIndex !== index && mergedOpen && !needConfirm && complexPicker) {\n      triggerPartConfirm(null, true);\n    }\n    setActiveIndex(index);\n    onSharedFocus(event, index);\n  };\n  var onSelectorBlur = function onSelectorBlur(event, index) {\n    triggerOpen(false);\n    if (!needConfirm && lastOperation() === 'input') {\n      var nextIndex = nextActiveIndex(calendarValue);\n      flushSubmit(activeIndex, nextIndex === null);\n    }\n    onSharedBlur(event, index);\n  };\n  var onSelectorKeyDown = function onSelectorKeyDown(event, preventDefault) {\n    if (event.key === 'Tab') {\n      triggerPartConfirm(null, true);\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event, preventDefault);\n  };\n\n  // ======================= Context ========================\n  var context = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      locale: locale,\n      generateConfig: generateConfig,\n      button: components.button,\n      input: components.input\n    };\n  }, [prefixCls, locale, generateConfig, components.button, components.input]);\n\n  // ======================== Effect ========================\n  // >>> Mode\n  // Reset for every active\n  useLayoutEffect(function () {\n    if (mergedOpen && activeIndex !== undefined) {\n      // Legacy compatible. This effect update should not trigger `onPanelChange`\n      triggerModeChange(null, picker, false);\n    }\n  }, [mergedOpen, activeIndex, picker]);\n\n  // >>> For complex picker, we need check if need to focus next one\n  useLayoutEffect(function () {\n    var lastOp = lastOperation();\n\n    // Trade as confirm on field leave\n    if (!mergedOpen && lastOp === 'input') {\n      triggerOpen(false);\n      triggerPartConfirm(null, true);\n    }\n\n    // Submit with complex picker\n    if (!mergedOpen && complexPicker && !needConfirm && lastOp === 'panel') {\n      triggerOpen(true);\n      triggerPartConfirm();\n    }\n  }, [mergedOpen]);\n\n  // ====================== DevWarning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    var isIndexEmpty = function isIndexEmpty(index) {\n      return (\n        // Value is empty\n        !(value !== null && value !== void 0 && value[index]) &&\n        // DefaultValue is empty\n        !(defaultValue !== null && defaultValue !== void 0 && defaultValue[index])\n      );\n    };\n    if (disabled.some(function (fieldDisabled, index) {\n      return fieldDisabled && isIndexEmpty(index) && !allowEmpty[index];\n    })) {\n      warning(false, '`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.');\n    }\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(PickerContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(PickerTrigger, _extends({}, pickTriggerProps(filledProps), {\n    popupElement: panel,\n    popupStyle: styles.popup,\n    popupClassName: classNames.popup\n    // Visible\n    ,\n    visible: mergedOpen,\n    onClose: onPopupClose\n    // Range\n    ,\n    range: true\n  }), /*#__PURE__*/React.createElement(RangeSelector\n  // Shared\n  , _extends({}, filledProps, {\n    // Ref\n    ref: selectorRef\n    // Icon\n    ,\n    suffixIcon: suffixIcon\n    // Active\n    ,\n    activeIndex: focused || mergedOpen ? activeIndex : null,\n    activeHelp: !!internalHoverValues,\n    allHelp: !!internalHoverValues && hoverSource === 'preset',\n    focused: focused,\n    onFocus: onSelectorFocus,\n    onBlur: onSelectorBlur,\n    onKeyDown: onSelectorKeyDown,\n    onSubmit: triggerPartConfirm\n    // Change\n    ,\n    value: hoverValues,\n    maskFormat: maskFormat,\n    onChange: onSelectorChange,\n    onInputChange: onSelectorInputChange\n    // Format\n    ,\n    format: formatList,\n    inputReadOnly: inputReadOnly\n    // Disabled\n    ,\n    disabled: disabled\n    // Open\n    ,\n    open: mergedOpen,\n    onOpenChange: triggerOpen\n    // Click\n    ,\n    onClick: onSelectorClick,\n    onClear: onSelectorClear\n    // Invalid\n    ,\n    invalid: submitInvalidates,\n    onInvalid: onSelectorInvalid\n    // Offset\n    ,\n    onActiveInfo: setActiveInfo\n  }))));\n}\nvar RefRangePicker = /*#__PURE__*/React.forwardRef(RangePicker);\nif (process.env.NODE_ENV !== 'production') {\n  RefRangePicker.displayName = 'RefRangePicker';\n}\nexport default RefRangePicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,cAAc,QAAQ,SAAS;AAClD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,mBAAmB;AACnE,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,aAAa,IAAIC,aAAa,QAAQ,uBAAuB;AACpE,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,cAAcA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAC7C,IAAIC,YAAY,GAAGF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGC,aAAa;EAChF,IAAIE,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY;EACrB;EACA,OAAO,CAACA,YAAY,EAAEA,YAAY,CAAC;AACrC;;AAEA;;AAEA,SAASG,cAAcA,CAACC,WAAW,EAAE;EACnC,OAAOA,WAAW,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;AAC5C;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/B;EACA,IAAIC,eAAe,GAAGvB,cAAc,CAACqB,KAAK,EAAE,YAAY;MACpD,IAAIG,QAAQ,GAAGH,KAAK,CAACG,QAAQ;QAC3BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;MAC/B,IAAIC,cAAc,GAAGd,cAAc,CAACY,QAAQ,EAAE,KAAK,CAAC;MACpD,IAAIG,gBAAgB,GAAGf,cAAc,CAACa,UAAU,EAAE,KAAK,CAAC;MACxD,OAAO;QACLD,QAAQ,EAAEE,cAAc;QACxBD,UAAU,EAAEE;MACd,CAAC;IACH,CAAC,CAAC;IACFC,gBAAgB,GAAG5C,cAAc,CAACuC,eAAe,EAAE,CAAC,CAAC;IACrDM,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACpCG,aAAa,GAAGH,gBAAgB,CAAC,CAAC,CAAC;IACnCI,UAAU,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;IAChCK,UAAU,GAAGL,gBAAgB,CAAC,CAAC,CAAC;IAChCM,gBAAgB,GAAGN,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIO,SAAS,GAAGN,WAAW,CAACM,SAAS;IACnCC,MAAM,GAAGP,WAAW,CAACO,MAAM;IAC3BC,UAAU,GAAGR,WAAW,CAACQ,UAAU;IACnCC,YAAY,GAAGT,WAAW,CAACS,YAAY;IACvCC,KAAK,GAAGV,WAAW,CAACU,KAAK;IACzBC,WAAW,GAAGX,WAAW,CAACW,WAAW;IACrCC,SAAS,GAAGZ,WAAW,CAACY,SAAS;IACjCjB,QAAQ,GAAGK,WAAW,CAACL,QAAQ;IAC/BC,UAAU,GAAGI,WAAW,CAACJ,UAAU;IACnCiB,YAAY,GAAGb,WAAW,CAACa,YAAY;IACvCC,OAAO,GAAGd,WAAW,CAACc,OAAO;IAC7BC,OAAO,GAAGf,WAAW,CAACe,OAAO;IAC7BC,WAAW,GAAGhB,WAAW,CAACgB,WAAW;IACrCC,IAAI,GAAGjB,WAAW,CAACiB,IAAI;IACvBC,YAAY,GAAGlB,WAAW,CAACkB,YAAY;IACvCC,MAAM,GAAGnB,WAAW,CAACmB,MAAM;IAC3BC,cAAc,GAAGpB,WAAW,CAACoB,cAAc;IAC3CC,MAAM,GAAGrB,WAAW,CAACqB,MAAM;IAC3BC,OAAO,GAAGtB,WAAW,CAACsB,OAAO;IAC7BC,SAAS,GAAGvB,WAAW,CAACuB,SAAS;IACjCC,QAAQ,GAAGxB,WAAW,CAACwB,QAAQ;IAC/BC,IAAI,GAAGzB,WAAW,CAACyB,IAAI;IACvBC,aAAa,GAAG1B,WAAW,CAAC0B,aAAa;IACzCC,gBAAgB,GAAG3B,WAAW,CAAC2B,gBAAgB;IAC/CC,IAAI,GAAG5B,WAAW,CAAC4B,IAAI;IACvBC,kBAAkB,GAAG7B,WAAW,CAAC6B,kBAAkB;IACnDC,WAAW,GAAG9B,WAAW,CAAC8B,WAAW;IACrCC,mBAAmB,GAAG/B,WAAW,CAAC+B,mBAAmB;IACrDC,aAAa,GAAGhC,WAAW,CAACgC,aAAa;IACzCC,UAAU,GAAGjC,WAAW,CAACiC,UAAU;IACnCC,OAAO,GAAGlC,WAAW,CAACkC,OAAO;IAC7BC,MAAM,GAAGnC,WAAW,CAACmC,MAAM;IAC3BC,OAAO,GAAGpC,WAAW,CAACoC,OAAO;IAC7BC,MAAM,GAAGrC,WAAW,CAACqC,MAAM;IAC3BC,UAAU,GAAGtC,WAAW,CAACsC,UAAU;IACnCC,UAAU,GAAGvC,WAAW,CAACuC,UAAU;IACnCC,UAAU,GAAGxC,WAAW,CAACwC,UAAU;IACnCC,eAAe,GAAGzC,WAAW,CAACyC,eAAe;IAC7CC,OAAO,GAAG1C,WAAW,CAAC0C,OAAO;;EAE/B;EACA,IAAIC,WAAW,GAAGtE,YAAY,CAACoB,GAAG,CAAC;;EAEnC;EACA,IAAImD,QAAQ,GAAGxE,OAAO,CAAC6C,IAAI,EAAED,WAAW,EAAErB,QAAQ,EAAEuB,YAAY,CAAC;IAC/D2B,SAAS,GAAG1F,cAAc,CAACyF,QAAQ,EAAE,CAAC,CAAC;IACvCE,UAAU,GAAGD,SAAS,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;EAC7B,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACC,QAAQ,EAAEjE,MAAM,EAAE;IACvD;IACA,IAAIW,QAAQ,CAACuD,IAAI,CAAC,UAAUC,aAAa,EAAE;MACzC,OAAO,CAACA,aAAa;IACvB,CAAC,CAAC,IAAI,CAACF,QAAQ,EAAE;MACfF,YAAY,CAACE,QAAQ,EAAEjE,MAAM,CAAC;IAChC;EACF,CAAC;;EAED;EACA,IAAIoE,cAAc,GAAGzE,aAAa,CAACyC,cAAc,EAAED,MAAM,EAAEhB,UAAU,EAAE,IAAI,EAAE,KAAK,EAAEM,YAAY,EAAEC,KAAK,EAAEiB,gBAAgB,EAAEC,IAAI,CAAC;IAC9HyB,eAAe,GAAGlG,cAAc,CAACiG,cAAc,EAAE,CAAC,CAAC;IACnDE,WAAW,GAAGD,eAAe,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;IAClCG,gBAAgB,GAAGH,eAAe,CAAC,CAAC,CAAC;IACrCI,qBAAqB,GAAGJ,eAAe,CAAC,CAAC,CAAC;IAC1CK,SAAS,GAAGL,eAAe,CAAC,CAAC,CAAC;EAChC,IAAIM,aAAa,GAAGH,gBAAgB,CAAC,CAAC;;EAEtC;EACA,IAAII,eAAe,GAAGrF,cAAc,CAACoB,QAAQ,EAAEC,UAAU,EAAEkD,UAAU,CAAC;IACpEe,gBAAgB,GAAG1G,cAAc,CAACyG,eAAe,EAAE,CAAC,CAAC;IACrDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAClCG,aAAa,GAAGH,gBAAgB,CAAC,CAAC,CAAC;IACnCvE,WAAW,GAAGuE,gBAAgB,CAAC,CAAC,CAAC;IACjCI,cAAc,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;IACpCK,eAAe,GAAGL,gBAAgB,CAAC,CAAC,CAAC;IACrCM,eAAe,GAAGN,gBAAgB,CAAC,CAAC,CAAC;IACrCO,iBAAiB,GAAGP,gBAAgB,CAAC,CAAC,CAAC;IACvCQ,oBAAoB,GAAGR,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAIS,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACvDT,YAAY,CAAC,IAAI,CAAC;IAClB7B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACqC,KAAK,EAAE;MACvDE,KAAK,EAAEpF,cAAc,CAACmF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGlF,WAAW;IAChF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIoF,YAAY,GAAG,SAASA,YAAYA,CAACH,KAAK,EAAEC,KAAK,EAAE;IACrDT,YAAY,CAAC,KAAK,CAAC;IACnB5B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACoC,KAAK,EAAE;MACpDE,KAAK,EAAEpF,cAAc,CAACmF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGlF,WAAW;IAChF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA;EACA,IAAIqF,cAAc,GAAGjH,KAAK,CAACkH,OAAO,CAAC,YAAY;IAC7C,IAAI,CAACpD,QAAQ,EAAE;MACb,OAAO,IAAI;IACb;IACA,IAAIqD,YAAY,GAAGrD,QAAQ,CAACqD,YAAY;IACxC,IAAIC,iBAAiB,GAAGD,YAAY,GAAG,UAAUE,IAAI,EAAE;MACrD,IAAIN,KAAK,GAAGpF,cAAc,CAACC,WAAW,CAAC;MACvC,IAAI0F,QAAQ,GAAGlH,WAAW,CAAC6F,aAAa,EAAEQ,eAAe,EAAE7E,WAAW,CAAC;MACvE,OAAOuF,YAAY,CAACE,IAAI,EAAEN,KAAK,EAAE;QAC/BQ,IAAI,EAAED;MACR,CAAC,CAAC;IACJ,CAAC,GAAGE,SAAS;IACb,OAAOhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;MACpDqD,YAAY,EAAEC;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtD,QAAQ,EAAElC,WAAW,EAAEqE,aAAa,EAAEQ,eAAe,CAAC,CAAC;;EAE3D;EACA,IAAIgB,eAAe,GAAG9H,cAAc,CAAC,CAACgE,MAAM,EAAEA,MAAM,CAAC,EAAE;MACnDX,KAAK,EAAEe;IACT,CAAC,CAAC;IACF2D,gBAAgB,GAAGjI,cAAc,CAACgI,eAAe,EAAE,CAAC,CAAC;IACrDE,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,UAAU,GAAGF,KAAK,CAAC/F,WAAW,CAAC,IAAI+B,MAAM;;EAE7C;EACA,IAAImE,YAAY,GAAGD,UAAU,KAAK,MAAM,IAAIZ,cAAc,GAAG,UAAU,GAAGY,UAAU;;EAEpF;EACA,IAAIE,aAAa,GAAGD,YAAY,KAAKnE,MAAM,IAAImE,YAAY,KAAK,MAAM;;EAEtE;EACA,IAAIE,aAAa,GAAG9G,UAAU,CAACyC,MAAM,EAAEkE,UAAU,EAAEjE,OAAO,EAAEC,SAAS,EAAE,IAAI,CAAC;;EAE5E;EACA,IAAIoE,cAAc,GAAGjH,aAAa,CAACsB,WAAW,EAAEsD,WAAW,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAE9D,QAAQ,EAAEQ,UAAU,EAAE2D,OAAO,EAAEhB,UAAU,EAAEzC,gBAAgB,CAAC;IAC/KuF,eAAe,GAAGzI,cAAc,CAACwI,cAAc,EAAE,CAAC,CAAC;IACnD;IACAE,WAAW,GAAGD,eAAe,CAAC,CAAC,CAAC;IAChC;IACAE,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;;EAE1C;EACA,IAAIG,kBAAkB,GAAGvH,oBAAoB,CAACmF,aAAa,EAAEhE,QAAQ,EAAEwE,eAAe,EAAE/C,cAAc,EAAED,MAAM,EAAEN,YAAY,CAAC;;EAE7H;EACA,IAAImF,oBAAoB,GAAG9H,mBAAmB,CAACyF,aAAa,EAAEtD,gBAAgB,EAAET,UAAU,CAAC;IACzFqG,qBAAqB,GAAG9I,cAAc,CAAC6I,oBAAoB,EAAE,CAAC,CAAC;IAC/DE,iBAAiB,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IAC5CE,iBAAiB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;;EAE9C;EACA,IAAIG,oBAAoB,GAAG3H,mBAAmB,CAAC2C,cAAc,EAAED,MAAM,EAAEwC,aAAa,EAAE0B,KAAK,EAAEvC,UAAU,EAAExD,WAAW,EAAEW,cAAc,EAAEwF,aAAa,EAAE5D,kBAAkB,EAAEC,WAAW,EAAE6C,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC0B,gBAAgB,EAAEtE,mBAAmB,EAAEjB,OAAO,EAAEC,OAAO,CAAC;IAC3TuF,qBAAqB,GAAGnJ,cAAc,CAACiJ,oBAAoB,EAAE,CAAC,CAAC;IAC/DG,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IAC7CE,qBAAqB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;;EAElD;EACA,IAAIG,iBAAiB,GAAGrJ,QAAQ,CAAC,UAAUsJ,eAAe,EAAEC,QAAQ,EAAEC,YAAY,EAAE;IAClF,IAAIC,KAAK,GAAGhJ,SAAS,CAACwH,KAAK,EAAE/F,WAAW,EAAEqH,QAAQ,CAAC;IACnD,IAAIE,KAAK,CAAC,CAAC,CAAC,KAAKxB,KAAK,CAAC,CAAC,CAAC,IAAIwB,KAAK,CAAC,CAAC,CAAC,KAAKxB,KAAK,CAAC,CAAC,CAAC,EAAE;MAClDC,QAAQ,CAACuB,KAAK,CAAC;IACjB;;IAEA;IACA,IAAInF,aAAa,IAAIkF,YAAY,KAAK,KAAK,EAAE;MAC3C,IAAIE,gBAAgB,GAAG7J,kBAAkB,CAAC0G,aAAa,CAAC;MACxD,IAAI+C,eAAe,EAAE;QACnBI,gBAAgB,CAACxH,WAAW,CAAC,GAAGoH,eAAe;MACjD;MACAhF,aAAa,CAACoF,gBAAgB,EAAED,KAAK,CAAC;IACxC;EACF,CAAC,CAAC;;EAEF;EACA,IAAIE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAChC,IAAI,EAAEP,KAAK,EAAE;IAC9D;MACE;MACA3G,SAAS,CAAC8F,aAAa,EAAEa,KAAK,EAAEO,IAAI;IAAC;EAEzC,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIiC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACjC,IAAI,EAAEkC,SAAS,EAAE;IACpE,IAAIC,SAAS,GAAGvD,aAAa;IAC7B,IAAIoB,IAAI,EAAE;MACRmC,SAAS,GAAGH,iBAAiB,CAAChC,IAAI,EAAEzF,WAAW,CAAC;IAClD;IACA8E,iBAAiB,CAAC9E,WAAW,CAAC;IAC9B;IACA,IAAI6H,SAAS,GAAGjD,eAAe,CAACgD,SAAS,CAAC;;IAE1C;IACAzD,qBAAqB,CAACyD,SAAS,CAAC;IAChCrB,WAAW,CAACvG,WAAW,EAAE6H,SAAS,KAAK,IAAI,CAAC;IAC5C,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtBnE,WAAW,CAAC,KAAK,EAAE;QACjBoE,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACH,SAAS,EAAE;MACrBtE,WAAW,CAAC0E,OAAO,CAACC,KAAK,CAAC;QACxB9C,KAAK,EAAE2C;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAII,eAAe,GAAG,SAASA,eAAeA,CAAChD,KAAK,EAAE;IACpD,IAAIiD,cAAc;IAClB,IAAIC,QAAQ,GAAGlD,KAAK,CAACmD,MAAM,CAACC,WAAW,CAAC,CAAC;IACzC,IAAI,CAAChF,WAAW,CAAC0E,OAAO,CAACO,aAAa,CAACC,QAAQ,CAAC,CAACL,cAAc,GAAGC,QAAQ,CAACK,aAAa,MAAM,IAAI,IAAIN,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGO,QAAQ,CAACD,aAAa,CAAC,EAAE;MAC1K;MACA,IAAIE,YAAY,GAAGrI,QAAQ,CAACsI,SAAS,CAAC,UAAUC,CAAC,EAAE;QACjD,OAAO,CAACA,CAAC;MACX,CAAC,CAAC;MACF,IAAIF,YAAY,IAAI,CAAC,EAAE;QACrBrF,WAAW,CAAC0E,OAAO,CAACC,KAAK,CAAC;UACxB9C,KAAK,EAAEwD;QACT,CAAC,CAAC;MACJ;IACF;IACAhF,WAAW,CAAC,IAAI,CAAC;IACjBN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAC6B,KAAK,CAAC;EAC1D,CAAC;EACD,IAAI4D,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CrC,mBAAmB,CAAC,IAAI,CAAC;IACzB9C,WAAW,CAAC,KAAK,EAAE;MACjBoE,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIgB,eAAe,GAAG1K,KAAK,CAAC2K,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGnL,cAAc,CAACiL,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG/K,KAAK,CAAC2K,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAGvL,cAAc,CAACsL,gBAAgB,EAAE,CAAC,CAAC;IACtDE,mBAAmB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACzCE,sBAAsB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC9C,IAAIG,WAAW,GAAGnL,KAAK,CAACkH,OAAO,CAAC,YAAY;IAC1C,OAAO+D,mBAAmB,IAAIhF,aAAa;EAC7C,CAAC,EAAE,CAACA,aAAa,EAAEgF,mBAAmB,CAAC,CAAC;;EAExC;EACAjL,KAAK,CAACoL,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAChG,UAAU,EAAE;MACf8F,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC9F,UAAU,CAAC,CAAC;;EAEhB;EACA;EACA;EACA;EACA;EACA,IAAIiG,gBAAgB,GAAGrL,KAAK,CAAC2K,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9CW,gBAAgB,GAAG7L,cAAc,CAAC4L,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAErC;EACA,IAAIG,UAAU,GAAG7K,UAAU,CAAC8D,OAAO,EAAEC,MAAM,CAAC;EAC5C,IAAI+G,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAE;IACrDT,sBAAsB,CAACS,UAAU,CAAC;IAClCb,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EACD,IAAIc,cAAc,GAAG,SAASA,cAAcA,CAACD,UAAU,EAAE;IACvD,IAAIE,MAAM,GAAGzD,mBAAmB,CAACuD,UAAU,CAAC;IAC5C,IAAIE,MAAM,EAAE;MACVvG,WAAW,CAAC,KAAK,EAAE;QACjBoE,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIoC,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;IAC9BzC,kBAAkB,CAACyC,GAAG,CAAC;EACzB,CAAC;;EAED;EACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAC3E,IAAI,EAAE;IAC7C6D,sBAAsB,CAAC7D,IAAI,GAAGgC,iBAAiB,CAAChC,IAAI,EAAEzF,WAAW,CAAC,GAAG,IAAI,CAAC;IAC1EkJ,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;;EAED;EACA,IAAImB,YAAY,GAAG,SAASA,YAAYA,CAACpF,KAAK,EAAE;IAC9CvB,WAAW,CAAC,IAAI,CAAC;IACjBsB,aAAa,CAACC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,IAAIqF,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD5F,aAAa,CAAC,OAAO,CAAC;EACxB,CAAC;;EAED;EACA,IAAI6F,aAAa,GAAG,SAASA,aAAaA,CAAC9E,IAAI,EAAE;IAC/C,IAAI8B,KAAK,GAAGhJ,SAAS,CAAC8F,aAAa,EAAErE,WAAW,EAAEyF,IAAI,CAAC;;IAEvD;IACAtB,qBAAqB,CAACoD,KAAK,CAAC;;IAE5B;IACA;IACA,IAAI,CAAClG,WAAW,IAAI,CAACT,aAAa,IAAID,cAAc,KAAKuF,YAAY,EAAE;MACrEwB,kBAAkB,CAACjC,IAAI,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,IAAI+E,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC;IACA9G,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI+G,oBAAoB,GAAG9L,aAAa,CAACsE,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEpD,cAAc,CAACC,WAAW,CAAC,CAAC;;EAE9G;EACA,IAAI0K,UAAU,GAAGrG,aAAa,CAACrE,WAAW,CAAC,IAAI,IAAI;;EAEnD;EACA,IAAI2K,qBAAqB,GAAG7M,QAAQ,CAAC,UAAU2H,IAAI,EAAE;IACnD,OAAO1E,gBAAgB,CAAC0E,IAAI,EAAE;MAC5BzF,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI4K,UAAU,GAAGxM,KAAK,CAACkH,OAAO,CAAC,YAAY;IACzC,IAAIuF,QAAQ,GAAG3M,SAAS,CAACwC,WAAW,EAAE,KAAK,CAAC;IAC5C,IAAIoK,SAAS,GAAG7M,IAAI,CAACyC,WAAW,EAAE,EAAE,CAACqK,MAAM,CAACpN,kBAAkB,CAACqN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,kBAAkB,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC;IAChL,OAAOC,SAAS;EAClB,CAAC,EAAE,CAACpK,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIwK,KAAK,GAAG,aAAa9M,KAAK,CAAC+M,aAAa,CAAC5L,KAAK,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAEkN,UAAU,EAAE;IAC3E5I,OAAO,EAAEoE,aAAa;IACtBlE,QAAQ,EAAEmD;IACV;IAAA;;IAEAF,KAAK,EAAE,IAAI;IACXgB,aAAa,EAAEA,aAAa;IAC5BwD,UAAU,EAAEA;IACZ;IAAA;;IAEApI,YAAY,EAAEkF;IACd;IAAA;;IAEA7D,OAAO,EAAEyH,YAAY;IACrBxH,MAAM,EAAEuC,YAAY;IACpBkF,gBAAgB,EAAEA;IAClB;IAAA;;IAEAvI,MAAM,EAAEA,MAAM;IACdI,IAAI,EAAE8D,UAAU;IAChBC,YAAY,EAAEA,YAAY;IAC1B9D,aAAa,EAAE+E;IACf;IAAA;;IAEAiE,MAAM,EAAEtK,UAAU;IAClBM,KAAK,EAAEsJ,UAAU;IACjBW,SAAS,EAAEV,qBAAqB;IAChCW,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAEhB;IACV;IAAA;;IAEA/H,WAAW,EAAEyE,kBAAkB;IAC/BF,gBAAgB,EAAEtI,OAAO,CAACyD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6E,gBAAgB,CAAC,CAAC/G,WAAW,CAAC;IACrHyC,mBAAmB,EAAEyE;IACrB;IAAA;;IAEAsE,UAAU,EAAEjC,WAAW;IACvBkC,OAAO,EAAErB;IACT;IAAA;;IAEA/I,WAAW,EAAEA,WAAW;IACxBqK,QAAQ,EAAEhE,kBAAkB;IAC5BpF,IAAI,EAAE8B;IACN;IAAA;;IAEAtB,OAAO,EAAE+G,UAAU;IACnBC,aAAa,EAAEA,aAAa;IAC5BE,cAAc,EAAEA;IAChB;IAAA;;IAEAE,KAAK,EAAEA;IACP;IAAA;;IAEAjH,UAAU,EAAEwH;EACd,CAAC,CAAC,CAAC;;EAEH;EACA;EACA;;EAEA;EACA,IAAIkB,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClG,IAAI,EAAEP,KAAK,EAAE;IAC5D,IAAIqC,KAAK,GAAGE,iBAAiB,CAAChC,IAAI,EAAEP,KAAK,CAAC;IAC1Cf,qBAAqB,CAACoD,KAAK,CAAC;EAC9B,CAAC;EACD,IAAIqE,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3DlH,aAAa,CAAC,OAAO,CAAC;EACxB,CAAC;;EAED;EACA,IAAImH,eAAe,GAAG,SAASA,eAAeA,CAAC5G,KAAK,EAAEC,KAAK,EAAE;IAC3D;IACA,IAAI4G,aAAa,GAAGjH,eAAe,CAACkH,MAAM;IAC1C,IAAIC,eAAe,GAAGnH,eAAe,CAACiH,aAAa,GAAG,CAAC,CAAC;IACxD,IAAIA,aAAa,IAAIE,eAAe,KAAK9G,KAAK,IAAI7D,WAAW;IAC7D;IACA,CAACf,UAAU,CAAC0L,eAAe,CAAC,IAAI,CAACjH,oBAAoB,CAACiH,eAAe,CAAC,IAAI3H,aAAa,CAAC2H,eAAe,CAAC,EAAE;MACxG3I,WAAW,CAAC0E,OAAO,CAACC,KAAK,CAAC;QACxB9C,KAAK,EAAE8G;MACT,CAAC,CAAC;MACF;IACF;IACAtH,aAAa,CAAC,OAAO,CAAC;IACtBhB,WAAW,CAAC,IAAI,EAAE;MAChBuI,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACA;IACA;IACA,IAAIjM,WAAW,KAAKkF,KAAK,IAAI1B,UAAU,IAAI,CAACnC,WAAW,IAAIT,aAAa,EAAE;MACxE8G,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChC;IACA/C,cAAc,CAACO,KAAK,CAAC;IACrBF,aAAa,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC7B,CAAC;EACD,IAAIgH,cAAc,GAAG,SAASA,cAAcA,CAACjH,KAAK,EAAEC,KAAK,EAAE;IACzDxB,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAACrC,WAAW,IAAIqD,aAAa,CAAC,CAAC,KAAK,OAAO,EAAE;MAC/C,IAAImD,SAAS,GAAGjD,eAAe,CAACP,aAAa,CAAC;MAC9CkC,WAAW,CAACvG,WAAW,EAAE6H,SAAS,KAAK,IAAI,CAAC;IAC9C;IACAzC,YAAY,CAACH,KAAK,EAAEC,KAAK,CAAC;EAC5B,CAAC;EACD,IAAIiH,iBAAiB,GAAG,SAASA,iBAAiBA,CAAClH,KAAK,EAAEmH,cAAc,EAAE;IACxE,IAAInH,KAAK,CAACoH,GAAG,KAAK,KAAK,EAAE;MACvB3E,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChC;IACApG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC2D,KAAK,EAAEmH,cAAc,CAAC;EAChF,CAAC;;EAED;EACA,IAAIE,OAAO,GAAGlO,KAAK,CAACkH,OAAO,CAAC,YAAY;IACtC,OAAO;MACLtE,SAAS,EAAEA,SAAS;MACpBa,MAAM,EAAEA,MAAM;MACdC,cAAc,EAAEA,cAAc;MAC9ByK,MAAM,EAAEvJ,UAAU,CAACuJ,MAAM;MACzBC,KAAK,EAAExJ,UAAU,CAACwJ;IACpB,CAAC;EACH,CAAC,EAAE,CAACxL,SAAS,EAAEa,MAAM,EAAEC,cAAc,EAAEkB,UAAU,CAACuJ,MAAM,EAAEvJ,UAAU,CAACwJ,KAAK,CAAC,CAAC;;EAE5E;EACA;EACA;EACAxO,eAAe,CAAC,YAAY;IAC1B,IAAIwF,UAAU,IAAIxD,WAAW,KAAK4F,SAAS,EAAE;MAC3C;MACAuB,iBAAiB,CAAC,IAAI,EAAEpF,MAAM,EAAE,KAAK,CAAC;IACxC;EACF,CAAC,EAAE,CAACyB,UAAU,EAAExD,WAAW,EAAE+B,MAAM,CAAC,CAAC;;EAErC;EACA/D,eAAe,CAAC,YAAY;IAC1B,IAAIyO,MAAM,GAAG/H,aAAa,CAAC,CAAC;;IAE5B;IACA,IAAI,CAAClB,UAAU,IAAIiJ,MAAM,KAAK,OAAO,EAAE;MACrC/I,WAAW,CAAC,KAAK,CAAC;MAClBgE,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChC;;IAEA;IACA,IAAI,CAAClE,UAAU,IAAI5C,aAAa,IAAI,CAACS,WAAW,IAAIoL,MAAM,KAAK,OAAO,EAAE;MACtE/I,WAAW,CAAC,IAAI,CAAC;MACjBgE,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAClE,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIkJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAC3H,KAAK,EAAE;MAC9C;QACE;QACA,EAAE9D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,CAAC8D,KAAK,CAAC,CAAC;QACrD;QACA,EAAE/D,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC+D,KAAK,CAAC;MAAC;IAE9E,CAAC;IACD,IAAI7E,QAAQ,CAACuD,IAAI,CAAC,UAAUC,aAAa,EAAEqB,KAAK,EAAE;MAChD,OAAOrB,aAAa,IAAIgJ,YAAY,CAAC3H,KAAK,CAAC,IAAI,CAAC5E,UAAU,CAAC4E,KAAK,CAAC;IACnE,CAAC,CAAC,EAAE;MACF/G,OAAO,CAAC,KAAK,EAAE,+FAA+F,CAAC;IACjH;EACF;;EAEA;EACA,OAAO,aAAaC,KAAK,CAAC+M,aAAa,CAACzM,aAAa,CAACoO,QAAQ,EAAE;IAC9D1L,KAAK,EAAEkL;EACT,CAAC,EAAE,aAAalO,KAAK,CAAC+M,aAAa,CAAC9M,aAAa,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEY,gBAAgB,CAACoC,WAAW,CAAC,EAAE;IAC7FqM,YAAY,EAAE7B,KAAK;IACnB8B,UAAU,EAAE/L,MAAM,CAACgM,KAAK;IACxBC,cAAc,EAAEhM,UAAU,CAAC+L;IAC3B;IAAA;;IAEAE,OAAO,EAAE3J,UAAU;IACnB4J,OAAO,EAAE5C;IACT;IAAA;;IAEArF,KAAK,EAAE;EACT,CAAC,CAAC,EAAE,aAAa/G,KAAK,CAAC+M,aAAa,CAAC3L;EACrC;EAAA,EACE9B,QAAQ,CAAC,CAAC,CAAC,EAAEgD,WAAW,EAAE;IAC1B;IACAP,GAAG,EAAEkD;IACL;IAAA;;IAEAV,UAAU,EAAEA;IACZ;IAAA;;IAEA3C,WAAW,EAAEwE,OAAO,IAAIhB,UAAU,GAAGxD,WAAW,GAAG,IAAI;IACvDqN,UAAU,EAAE,CAAC,CAAChE,mBAAmB;IACjCiE,OAAO,EAAE,CAAC,CAACjE,mBAAmB,IAAIJ,WAAW,KAAK,QAAQ;IAC1DzE,OAAO,EAAEA,OAAO;IAChB5B,OAAO,EAAEiJ,eAAe;IACxBhJ,MAAM,EAAEqJ,cAAc;IACtB5K,SAAS,EAAE6K,iBAAiB;IAC5BT,QAAQ,EAAEhE;IACV;IAAA;;IAEAtG,KAAK,EAAEmI,WAAW;IAClBzI,UAAU,EAAEA,UAAU;IACtBwK,QAAQ,EAAEK,gBAAgB;IAC1B4B,aAAa,EAAE3B;IACf;IAAA;;IAEAR,MAAM,EAAEvK,UAAU;IAClB6B,aAAa,EAAEA;IACf;IAAA;;IAEArC,QAAQ,EAAEA;IACV;IAAA;;IAEAsB,IAAI,EAAE6B,UAAU;IAChB5B,YAAY,EAAE8B;IACd;IAAA;;IAEAN,OAAO,EAAE6E,eAAe;IACxBuF,OAAO,EAAE3E;IACT;IAAA;;IAEA4E,OAAO,EAAE7G,iBAAiB;IAC1B8G,SAAS,EAAE7G;IACX;IAAA;;IAEA8G,YAAY,EAAE/D;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACA,IAAIgE,cAAc,GAAG,aAAaxP,KAAK,CAACyP,UAAU,CAAC5N,WAAW,CAAC;AAC/D,IAAIyM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCgB,cAAc,CAACE,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
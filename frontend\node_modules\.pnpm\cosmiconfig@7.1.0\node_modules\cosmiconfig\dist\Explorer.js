"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Explorer = void 0;

var _path = _interopRequireDefault(require("path"));

var _ExplorerBase = require("./ExplorerBase");

var _readFile = require("./readFile");

var _cacheWrapper = require("./cacheWrapper");

var _getDirectory = require("./getDirectory");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class Explorer extends _ExplorerBase.ExplorerBase {
  constructor(options) {
    super(options);
  }

  async search(searchFrom = process.cwd()) {
    const startDirectory = await (0, _getDirectory.getDirectory)(searchFrom);
    const result = await this.searchFromDirectory(startDirectory);
    return result;
  }

  async searchFromDirectory(dir) {
    const absoluteDir = _path.default.resolve(process.cwd(), dir);

    const run = async () => {
      const result = await this.searchDirectory(absoluteDir);
      const nextDir = this.nextDirectoryToSearch(absoluteDir, result);

      if (nextDir) {
        return this.searchFromDirectory(nextDir);
      }

      const transformResult = await this.config.transform(result);
      return transformResult;
    };

    if (this.searchCache) {
      return (0, _cacheWrapper.cacheWrapper)(this.searchCache, absoluteDir, run);
    }

    return run();
  }

  async searchDirectory(dir) {
    for await (const place of this.config.searchPlaces) {
      const placeResult = await this.loadSearchPlace(dir, place);

      if (this.shouldSearchStopWithResult(placeResult) === true) {
        return placeResult;
      }
    } // config not found


    return null;
  }

  async loadSearchPlace(dir, place) {
    const filepath = _path.default.join(dir, place);

    const fileContents = await (0, _readFile.readFile)(filepath);
    const result = await this.createCosmiconfigResult(filepath, fileContents);
    return result;
  }

  async loadFileContent(filepath, content) {
    if (content === null) {
      return null;
    }

    if (content.trim() === '') {
      return undefined;
    }

    const loader = this.getLoaderEntryForFile(filepath);
    const loaderResult = await loader(filepath, content);
    return loaderResult;
  }

  async createCosmiconfigResult(filepath, content) {
    const fileContent = await this.loadFileContent(filepath, content);
    const result = this.loadedContentToCosmiconfigResult(filepath, fileContent);
    return result;
  }

  async load(filepath) {
    this.validateFilePath(filepath);

    const absoluteFilePath = _path.default.resolve(process.cwd(), filepath);

    const runLoad = async () => {
      const fileContents = await (0, _readFile.readFile)(absoluteFilePath, {
        throwNotFound: true
      });
      const result = await this.createCosmiconfigResult(absoluteFilePath, fileContents);
      const transformResult = await this.config.transform(result);
      return transformResult;
    };

    if (this.loadCache) {
      return (0, _cacheWrapper.cacheWrapper)(this.loadCache, absoluteFilePath, runLoad);
    }

    return runLoad();
  }

}

exports.Explorer = Explorer;
//# sourceMappingURL=Explorer.js.map